/*
01	AVEIRO                        	01	AGUEDA                        	03	AGUADA DE CIMA                                                  0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	09	FERMENTELOS                                                     0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	12	MACINHATA DO VOUGA                                              0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	19	VALONGO DO VOUGA                                                0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	21	�GUEDA E BORRALHA                                               0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	22	BARR� E AGUADA DE BAIXO                                         0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	23	BELAZAIMA DO CH�O, CASTANHEIRA DO VOUGA E AGAD�O                0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	24	RECARD�ES E ESPINHEL                                            0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	25	TRAVASS� E �IS DA RIBEIRA                                       0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	26	TROFA, SEGAD�ES E LAMAS DO VOUGA                                0019	AGUEDA
01	AVEIRO                        	01	AGUEDA                        	27	PR�STIMO E MACIEIRA DE ALCOBA                                   0019	AGUEDA
*/

INSERT INTO pcj.COMISSAO_PCJ values (1, null, 'AGUEDA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null, 
                                    1, 1, 'AGUEDA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1, 1, 'AGUADA DE CIMA', 3, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (2, 1, 'FERMENTELOS', 9, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3, 1, 'MACINHATA DO VOUGA', 12, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (4, 1, 'VALONGO DO VOUGA', 19, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (5, 1, '�GUEDA E BORRALHA', 21, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (6, 1, 'BARR� E AGUADA DE BAIXO', 22, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (7, 1, 'BELAZAIMA DO CH�O, CASTANHEIRA DO VOUGA E AGAD�O', 23, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (8, 1, 'RECARD�ES E ESPINHEL', 24, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (9, 1, 'TRAVASS� E �IS DA RIBEIRA', 25, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (10, 1, 'TROFA, SEGAD�ES E LAMAS DO VOUGA', 26, 1, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (11, 1, 'PR�STIMO E MACIEIRA DE ALCOBA', 27, 1, 1);

/*
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	02	ALQUERUBIM                          0027	ALBERGARIA-A-VELHA
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	03	ANGEJA                              0027	ALBERGARIA-A-VELHA
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	04	BRANCA                              0027	ALBERGARIA-A-VELHA
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	06	RIBEIRA DE FRAGUAS                  0027	ALBERGARIA-A-VELHA
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	09	ALBERGARIA-A-VELHA E VALMAIOR       0027	ALBERGARIA-A-VELHA       
01	AVEIRO                        	02	ALBERGARIA-A-VELHA            	10	S�O JO�O DE LOURE E FROSSOS         0027	ALBERGARIA-A-VELHA

*/


INSERT INTO pcj.COMISSAO_PCJ values (2, null, 'ALBERGARIA-A-VELHA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null, 
                                    1, 2, 'ALBERGARIA-A-VELHA', 'AVEIRO');


INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (12, 2, 'ALQUERUBIM', 2, 2, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (13, 2, 'ANGEJA', 3, 2, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (14, 2, 'BRANCA', 4, 2, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (15, 2, 'RIBEIRA DE FRAGUAS', 6, 2, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (16, 2, 'ALBERGARIA-A-VELHA E VALMAIOR', 9, 2, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (17, 2, 'S�O JO�O DE LOURE E FROSSOS', 10, 2, 1);

/*
01	AVEIRO                        	03	ANADIA                        	04	AVEL�S DE CAMINHO                                       0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	05	AVEL�S DE CIMA                                          0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	07	MOITA                                                   0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	09	SANGALHOS                                               0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	10	S. LOUREN�O DO BAIRRO                                   0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	12	VILA NOVA DE MONSARROS                                  0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	13	VILARINHO DO BAIRRO                                     0035	ANADIA    
01	AVEIRO                        	03	ANADIA                        	16	AMOREIRA DA G�NDARA, PAREDES DO BAIRRO E ANCAS          0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	17	ARCOS E MOGOFORES                                       0035	ANADIA
01	AVEIRO                        	03	ANADIA                        	18	TAMENGOS, AGUIM E �IS DO BAIRRO                         0035	ANADIA

*/

INSERT INTO pcj.COMISSAO_PCJ values (3, null, 'ANADIA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 3, 'ANADIA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (18, 3, 'AVEL�S DE CAMINHO', 4, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (19, 3, 'AVEL�S DE CIMA', 5, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (20, 3, 'MOITA', 7, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (21, 3, 'SANGALHOS', 9, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (22, 3, 'S. LOUREN�O DO BAIRRO', 10, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (23, 3, 'VILA NOVA DE MONSARROS', 12, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (24, 3, 'VILARINHO DO BAIRRO', 13, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (25, 3, 'AMOREIRA DA G�NDARA, PAREDES DO BAIRRO E ANCAS', 16, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (26, 3, 'ARCOS E MOGOFORES', 17, 3, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (27, 3, 'TAMENGOS, AGUIM E �IS DO BAIRRO', 18, 3, 1);

/*
01	AVEIRO                        	04	AROUCA                        	17	S. MIGUEL DO MATO	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	16	SANTA EULALIA	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	19	URRO	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	20	VARZEA	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	02	ALVARENGA	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	21	AROUCA E BURGO	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	22	CABREIROS E ALBERGARIA DA SERRA	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	23	CANELAS E ESPIUNCA	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	07	CHAVE	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	24	COVELO DE PAIV� E JANARDE	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	09	ESCARIZ	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	11	FERMEDO	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	13	MANSORES	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	14	MOLDES	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	15	ROSSAS	0043	AROUCA                        
01	AVEIRO                        	04	AROUCA                        	18	TROPE�O	0043	AROUCA                        

*/

INSERT INTO pcj.COMISSAO_PCJ values (4, null, 'AROUCA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 4, 'AROUCA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (28, 4, 'S. MIGUEL DO MATO', 17, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (29, 4, 'SANTA EULALIA', 16, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (30, 4, 'URRO', 19, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (31, 4, 'VARZEA', 20, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (32, 4, 'ALVARENGA', 2, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (33, 4, 'AROUCA E BURGO', 21, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (34, 4, 'CABREIROS E ALBERGARIA DA SERRA', 22, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (35, 4, 'CANELAS E ESPIUNCA', 23, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (36, 4, 'CHAVE', 07, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (37, 4, 'COVELO DE PAIV� E JANARDE', 24, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (38, 4, 'ESCARIZ', 09, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (39, 4, 'FERMEDO', 11, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (40, 4, 'MANSORES', 13, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (41, 4, 'MOLDES', 14, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (42, 4, 'ROSSAS', 15, 4, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (43, 4, 'TROPE�O', 18, 4, 1);

/*

01	AVEIRO                        	05	AVEIRO                        	10	S. BERNARDO	0051	AVEIRO-1.                     
01	AVEIRO                        	05	AVEIRO                        	11	S. JACINTO	0051	AVEIRO-1.                     
01	AVEIRO                        	05	AVEIRO                        	01	ARADAS	0051	AVEIRO-1.                     
01	AVEIRO                        	05	AVEIRO                        	02	CACIA	3417	AVEIRO-2.                     
01	AVEIRO                        	05	AVEIRO                        	15	EIXO E EIROL	3417	AVEIRO-2.                     
01	AVEIRO                        	05	AVEIRO                        	05	ESGUEIRA	3417	AVEIRO-2.                     
01	AVEIRO                        	05	AVEIRO                        	17	GL�RIA E VERA CRUZ	0051	AVEIRO-1.                     
01	AVEIRO                        	05	AVEIRO                        	08	OLIVEIRINHA	3417	AVEIRO-2.                     
01	AVEIRO                        	05	AVEIRO                        	16	REQUEIXO, NOSSA SENHORA DE F�TIMA E NARIZ	3417	AVEIRO-2.                     
01	AVEIRO                        	05	AVEIRO                        	13	SANTA JOANA	0051	AVEIRO-1.                     

*/

INSERT INTO pcj.COMISSAO_PCJ values (5, null, 'AVEIRO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 5, 'AVEIRO', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (44, 5, 'S. BERNARDO', 10, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (45, 5, 'S. JACINTO', 11, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (46, 5, 'ARADAS', 01, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (47, 5, 'CACIA', 02, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (48, 5, 'EIXO E EIROL', 15, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (49, 5, 'ESGUEIRA', 05, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (50, 5, 'GL�RIA E VERA CRUZ', 17, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (51, 5, 'OLIVEIRINHA', 08, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (52, 5, 'REQUEIXO, NOSSA SENHORA DE F�TIMA E NARIZ', 16, 5, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (53, 5, 'SANTA JOANA', 13, 5, 1);


/*

01	AVEIRO                        	06	CASTELO DE PAIVA              	08	S. MARTINHO DE SARDOURA	0060	CASTELO DE PAIVA              
01	AVEIRO                        	06	CASTELO DE PAIVA              	02	FORNOS	0060	CASTELO DE PAIVA              
01	AVEIRO                        	06	CASTELO DE PAIVA              	10	RAIVA, PEDORIDO E PARA�SO	0060	CASTELO DE PAIVA              
01	AVEIRO                        	06	CASTELO DE PAIVA              	06	REAL	0060	CASTELO DE PAIVA              
01	AVEIRO                        	06	CASTELO DE PAIVA              	07	SANTA MARIA DE SARDOURA	0060	CASTELO DE PAIVA              
01	AVEIRO                        	06	CASTELO DE PAIVA              	11	SOBRADO E BAIRROS	0060	CASTELO DE PAIVA              

*/

INSERT INTO pcj.COMISSAO_PCJ values (6, null, 'CASTELO DE PAIVA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 6, 'CASTELO DE PAIVA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (54, 6, 'S. MARTINHO DE SARDOURA', 08, 6, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (55, 6, 'FORNOS', 02, 6, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (56, 6, 'RAIVA, PEDORIDO E PARA�SO', 10, 6, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (57, 6, 'REAL', 06, 6, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (58, 6, 'SANTA MARIA DE SARDOURA', 07, 6, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (59, 6, 'SOBRADO E BAIRROS', 11, 6, 1);

/*

01	AVEIRO                        	07	ESPINHO                       	06	ANTA E GUETIM	0078	ESPINHO                       
01	AVEIRO                        	07	ESPINHO                       	02	ESPINHO	0078	ESPINHO                       
01	AVEIRO                        	07	ESPINHO                       	04	PARAMOS	0078	ESPINHO                       
01	AVEIRO                        	07	ESPINHO                       	05	SILVALDE	0078	ESPINHO                       

*/


INSERT INTO pcj.COMISSAO_PCJ values (7, null, 'ESPINHO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 7, 'ESPINHO', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (60, 7, 'ANTA E GUETIM', 06, 7, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (61, 7, 'ESPINHO', 02, 7, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (62, 7, 'PARAMOS', 04, 7, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (63, 7, 'SILVALDE', 05, 7, 1);


/*

01	AVEIRO                        	08	ESTARREJA                     	05	PARDILHO	0086	ESTARREJA                     
01	AVEIRO                        	08	ESTARREJA                     	01	AVANCA	0086	ESTARREJA                     
01	AVEIRO                        	08	ESTARREJA                     	08	BEDU�DO E VEIROS	0086	ESTARREJA                     
01	AVEIRO                        	08	ESTARREJA                     	09	CANELAS E FERMEL�	0086	ESTARREJA                     
01	AVEIRO                        	08	ESTARREJA                     	06	SALREU	0086	ESTARREJA                     

*/

INSERT INTO pcj.COMISSAO_PCJ values (8, null, 'ESTARREJA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 8, 'ESTARREJA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (64, 8, 'PARDILHO', 05, 8, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (65, 8, 'AVANCA', 01, 8, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (66, 8, 'BEDU�DO E VEIROS', 08, 8, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (67, 8, 'CANELAS E FERMEL�', 09, 8, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (68, 8, 'SALREU', 06, 8, 1);


/*

01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	14	MILHEIROS DE POIARES	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	26	S. JO�O DE VER	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	18	S. PAIO DE OLEIROS	4170	FEIRA-4                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	01	ARGONCILHE	3441	FEIRA-2                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	02	ARRIFANA	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	32	CALDAS DE S�O JORGE E PIGEIROS	3735	FEIRA-3                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	33	CANEDO, VALE E VILA MAIOR	3735	FEIRA-3                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	04	ESCAP�ES	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	07	FI�ES	3441	FEIRA-2                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	08	FORNOS	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	34	LOB�O, GI�O, LOUREDO E GUISANDE	3735	FEIRA-3                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	13	LOUROSA	3441	FEIRA-2                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	16	MOZELOS	3441	FEIRA-2                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	17	NOGUEIRA DA REGEDOURA	4170	FEIRA-4                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	19	PA�OS DE BRAND�O	4170	FEIRA-4                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	21	RIO ME�O	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	22	ROMARIZ	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	24	SANGUEDO	3441	FEIRA-2                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	35	SANTA MARIA DA FEIRA, TRAVANCA, SANFINS E ESPARGO	0094	FEIRA-1                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	25	SANTA MARIA DE LAMAS	4170	FEIRA-4                       
01	AVEIRO                        	09	SANTA MARIA DA FEIRA          	36	S�O MIGUEL DO SOUTO E MOSTEIR�	0094	FEIRA-1                       

*/


INSERT INTO pcj.COMISSAO_PCJ values (9, null, 'SANTA MARIA DA FEIRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 9, 'SANTA MARIA DA FEIRA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (69, 9, 'MILHEIROS DE POIARES', 14, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (70, 9, 'S. JO�O DE VER', 26, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (71, 9, 'S. PAIO DE OLEIROS', 18, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (72, 9, 'ARGONCILHE', 01, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (73, 9, 'ARRIFANA', 02, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (74, 9, 'CALDAS DE S�O JORGE E PIGEIROS', 32, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (75, 9, 'CANEDO, VALE E VILA MAIOR', 33, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (76, 9, 'ESCAP�ES', 04, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (77, 9, 'FI�ES', 07, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (78, 9, 'FORNOS', 08, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (79, 9, 'LOB�O, GI�O, LOUREDO E GUISANDE', 34, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (80, 9, 'LOUROSA', 13, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (81, 9, 'MOZELOS', 16, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (82, 9, 'NOGUEIRA DA REGEDOURA', 17, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (83, 9, 'PA�OS DE BRAND�O', 19, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (84, 9, 'RIO ME�O', 21, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (85, 9, 'ROMARIZ', 22, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (86, 9, 'SANGUEDO', 24, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (87, 9, 'SANTA MARIA DA FEIRA, TRAVANCA, SANFINS E ESPARGO', 35, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (88, 9, 'SANTA MARIA DE LAMAS', 25, 9, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (89, 9, 'S�O MIGUEL DO SOUTO E MOSTEIR�', 36, 9, 1);

/*

01	AVEIRO                        	10	ILHAVO                        	05	GAFANHA DA ENCARNA��O	0108	ILHAVO                        
01	AVEIRO                        	10	ILHAVO                        	06	GAFANHA DA NAZAR�	0108	ILHAVO                        
01	AVEIRO                        	10	ILHAVO                        	07	GAFANHA DO CARMO	0108	ILHAVO                        
01	AVEIRO                        	10	ILHAVO                        	08	�LHAVO (S�O SALVADOR)	0108	ILHAVO                        

*/

INSERT INTO pcj.COMISSAO_PCJ values (10, null, 'ILHAVO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 10, 'ILHAVO', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (90, 10, 'GAFANHA DA ENCARNA��O', 05, 10, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (91, 10, 'GAFANHA DA NAZAR�', 06, 10, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (92, 10, 'GAFANHA DO CARMO', 07, 10, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (93, 10, '�LHAVO (S�O SALVADOR)', 08, 10, 1);


/*

01	AVEIRO                        	11	MEALHADA                      	02	BARCOU�O	0116	MEALHADA                      
01	AVEIRO                        	11	MEALHADA                      	03	CASAL COMBA	0116	MEALHADA                      
01	AVEIRO                        	11	MEALHADA                      	04	LUSO	0116	MEALHADA                      
01	AVEIRO                        	11	MEALHADA                      	09	MEALHADA, VENTOSA DO BAIRRO E ANTES	0116	MEALHADA                      
01	AVEIRO                        	11	MEALHADA                      	06	PAMPILHOSA	0116	MEALHADA                      
01	AVEIRO                        	11	MEALHADA                      	07	VACARI�A	0116	MEALHADA                      

*/

INSERT INTO pcj.COMISSAO_PCJ values (11, null, 'MEALHADA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 11, 'MEALHADA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (94, 11, 'BARCOU�O', 02, 11, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (95, 11, 'CASAL COMBA', 03, 11, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (96, 11, 'LUSO', 04, 11, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (97, 11, 'MEALHADA, VENTOSA DO BAIRRO E ANTES', 09, 11, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (98, 11, 'PAMPILHOSA', 06, 11, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (99, 11, 'VACARI�A', 07, 11, 1);


/*

01	AVEIRO                        	12	MURTOSA                       	01	BUNHEIRO	0124	MURTOSA                       
01	AVEIRO                        	12	MURTOSA                       	02	MONTE	0124	MURTOSA                       
01	AVEIRO                        	12	MURTOSA                       	03	MURTOSA	0124	MURTOSA                       
01	AVEIRO                        	12	MURTOSA                       	04	TORREIRA	0124	MURTOSA                       

*/

INSERT INTO pcj.COMISSAO_PCJ values (12, null, 'MURTOSA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 12, 'MURTOSA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (100, 12, 'BUNHEIRO', 01, 12, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (101, 12, 'MONTE', 02, 12, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (102, 12, 'MURTOSA', 03, 12, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (103, 12, 'TORREIRA', 04, 12, 1);


/*

01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	15	S. MARTINHO DA GANDARA	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	01	CARREGOSA	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	02	CESAR	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	03	FAJ�ES	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	04	LOUREIRO	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	05	MACIEIRA DE SARNES	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	20	NOGUEIRA DO CRAVO E PINDELO	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	21	O. AZEM�IS, RIBA-UL, UL, MACINHATA SEIXA, MADAIL	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	10	OSSELA	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	22	PINHEIRO DA BEMPOSTA, TRAVANCA E PALMAZ	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	18	S�O ROQUE	0132	OLIVEIRA AZEMEIS              
01	AVEIRO                        	13	OLIVEIRA DE AZEMEIS           	19	VILA DE CUCUJ�ES	0132	OLIVEIRA AZEMEIS              

*/


INSERT INTO pcj.COMISSAO_PCJ values (13, null, 'OLIVEIRA DE AZEMEIS', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 13, 'OLIVEIRA DE AZEMEIS', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (104, 13, 'S. MARTINHO DA GANDARA', 15, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (105, 13, 'CARREGOSA', 01, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (106, 13, 'CESAR', 02, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (107, 13, 'FAJ�ES', 03, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (108, 13, 'LOUREIRO', 04, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (109, 13, 'MACIEIRA DE SARNES', 05, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (110, 13, 'NOGUEIRA DO CRAVO E PINDELO', 20, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (111, 13, 'O. AZEM�IS, RIBA-UL, UL, MACINHATA SEIXA, MADAIL', 21, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (112, 13, 'OSSELA', 10, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (113, 13, 'PINHEIRO DA BEMPOSTA, TRAVANCA E PALMAZ', 22, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (114, 13, 'S�O ROQUE', 18, 13, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (115, 13, 'VILA DE CUCUJ�ES', 19, 13, 1);


/*

01	AVEIRO                        	14	OLIVEIRA DO BAIRRO            	07	BUSTOS, TROVISCAL E MAMARROSA	0140	OLIVEIRA DO BAIRRO            
01	AVEIRO                        	14	OLIVEIRA DO BAIRRO            	03	OI�	0140	OLIVEIRA DO BAIRRO            
01	AVEIRO                        	14	OLIVEIRA DO BAIRRO            	04	OLIVEIRA DO BAIRRO	0140	OLIVEIRA DO BAIRRO            
01	AVEIRO                        	14	OLIVEIRA DO BAIRRO            	05	PALHA�A	0140	OLIVEIRA DO BAIRRO            

*/


INSERT INTO pcj.COMISSAO_PCJ values (14, null, 'OLIVEIRA DO BAIRRO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 14, 'OLIVEIRA DO BAIRRO', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (116, 14, 'BUSTOS, TROVISCAL E MAMARROSA', 07, 14, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (117, 14, 'OI�', 03, 14, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (118, 14, 'OLIVEIRA DO BAIRRO', 04, 14, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (119, 14, 'PALHA�A', 05, 14, 1);


/*

01	AVEIRO                        	15	OVAR                          	07	VALEGA	0159	OVAR                          
01	AVEIRO                        	15	OVAR                          	02	CORTEGA�A	0159	OVAR                          
01	AVEIRO                        	15	OVAR                          	03	ESMORIZ	0159	OVAR                          
01	AVEIRO                        	15	OVAR                          	04	MACEDA	0159	OVAR                          
01	AVEIRO                        	15	OVAR                          	09	OVAR, S.JO�O, ARADA E S.VICENTE DE PEREIRA JUS�	0159	OVAR                          

*/


INSERT INTO pcj.COMISSAO_PCJ values (15, null, 'OVAR', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 15, 'OVAR', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (120, 15, 'VALEGA', 07, 15, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (121, 15, 'CORTEGA�A', 02, 15, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (122, 15, 'ESMORIZ', 03, 15, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (123, 15, 'MACEDA', 04, 15, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (124, 15, 'OVAR, S.JO�O, ARADA E S.VICENTE DE PEREIRA JUS�', 09, 15, 1);

/*

01	AVEIRO                        	16	S. JO�O DA MADEIRA            	01	S. JO�O DA MADEIRA	0167	S. JOAO DA MADEIRA            

*/


INSERT INTO pcj.COMISSAO_PCJ values (16, null, 'S. JO�O DA MADEIRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 16, 'S. JO�O DA MADEIRA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (125, 16, 'S. JO�O DA MADEIRA', 01, 16, 1);

/*

01	AVEIRO                        	17	SEVER DO VOUGA                	10	CEDRIM E PARADELA	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	02	COUTO DE ESTEVES	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	04	PESSEGUEIRO DO VOUGA	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	05	ROCAS DO VOUGA	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	06	SEVER DO VOUGA	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	11	SILVA ESCURA E DORNELAS	0175	SEVER DO VOUGA                
01	AVEIRO                        	17	SEVER DO VOUGA                	08	TALHADAS	0175	SEVER DO VOUGA                

*/


INSERT INTO pcj.COMISSAO_PCJ values (17, null, 'SEVER DO VOUGA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 17, 'SEVER DO VOUGA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (126, 17, 'CEDRIM E PARADELA', 10, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (127, 17, 'COUTO DE ESTEVES', 02, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (128, 17, 'PESSEGUEIRO DO VOUGA', 04, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (129, 17, 'ROCAS DO VOUGA', 05, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (130, 17, 'SEVER DO VOUGA', 06, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (131, 17, 'SILVA ESCURA E DORNELAS', 11, 17, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (132, 17, 'TALHADAS', 08, 17, 1);


/*

01	AVEIRO                        	18	VAGOS                         	10	SANTO ANDRE DE VAGOS	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	07	SOZA	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	01	CALV�O	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	12	FONTE DE ANGE�O E COV�O DO LOBO	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	04	GAFANHA DA BOA HORA	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	05	OUCA	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	13	PONTE DE VAGOS E SANTA CATARINA	0183	VAGOS                         
01	AVEIRO                        	18	VAGOS                         	14	VAGOS E SANTO ANT�NIO	0183	VAGOS                         

*/


INSERT INTO pcj.COMISSAO_PCJ values (18, null, 'VAGOS', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 18, 'VAGOS', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (133, 18, 'SANTO ANDRE DE VAGOS', 10, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (134, 18, 'SOZA', 07, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (135, 18, 'CALV�O', 01, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (136, 18, 'FONTE DE ANGE�O E COV�O DO LOBO', 12, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (137, 18, 'GAFANHA DA BOA HORA', 04, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (138, 18, 'OUCA', 05, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (139, 18, 'PONTE DE VAGOS E SANTA CATARINA', 13, 18, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (140, 18, 'VAGOS E SANTO ANT�NIO', 14, 18, 1);


/*

01	AVEIRO                        	19	VALE DE CAMBRA                	01	AR�ES	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	03	CEPELOS	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	05	JUNQUEIRA	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	06	MACIEIRA DE CAMBRA	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	07	ROGE	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	02	S�O PEDRO DE CASTEL�ES	0191	VALE DE CAMBRA                
01	AVEIRO                        	19	VALE DE CAMBRA                	10	VILA CH�, CODAL E VILA COVA DE PERRINHO	0191	VALE DE CAMBRA                

*/


INSERT INTO pcj.COMISSAO_PCJ values (19, null, 'VALE DE CAMBRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    1, 19, 'VALE DE CAMBRA', 'AVEIRO');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (141, 19, 'AR�ES', 01, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (142, 19, 'CEPELOS', 03, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (143, 19, 'JUNQUEIRA', 05, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (144, 19, 'MACIEIRA DE CAMBRA', 06, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (145, 19, 'ROGE', 07, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (146, 19, 'S�O PEDRO DE CASTEL�ES', 02, 19, 1);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (147, 19, 'VILA CH�, CODAL E VILA COVA DE PERRINHO', 10, 19, 1);

commit;

