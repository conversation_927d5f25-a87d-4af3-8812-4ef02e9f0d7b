/*

03	BRAGA                         	01	AMARES                        	25	AMARES E FIGUEIREDO
03	BRAGA                         	01	AMARES                        	02	BARREIROS
03	BRAGA                         	01	AMARES                        	04	BICO
03	BRAGA                         	01	AMARES                        	19	BOURO (SANTA MARIA)
03	BRAGA                         	01	AMARES                        	20	BOURO (SANTA MARTA)
03	BRAGA                         	01	AMARES                        	05	CAIRES
03	BRAGA                         	01	AMARES                        	26	CALDELAS, SEQUEIROS E PARANHOS
03	BRAGA                         	01	AMARES                        	07	CARRAZEDO
03	BRAGA                         	01	AMARES                        	08	DORNELAS
03	BRAGA                         	01	AMARES                        	27	FERREIROS, PROZELO E BESTEIROS
03	BRAGA                         	01	AMARES                        	11	FISCAL
03	BRAGA                         	01	AMARES                        	12	GO�ES
03	BRAGA                         	01	AMARES                        	13	LAGO
03	BRAGA                         	01	AMARES                        	18	RENDUFE
03	BRAGA                         	01	AMARES                        	28	TORRE E PORTELA
03	BRAGA                         	01	AMARES                        	29	VILELA, SERAMIL E PAREDES SECAS

*/


INSERT INTO pcj.COMISSAO_PCJ values (34, null, 'AMARES', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    3, 01, 'AMARES', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (223, 34, 'AMARES E FIGUEIREDO', 25, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (224, 34, 'BARREIROS', 02, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (225, 34, 'BICO', 04, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (226, 34, 'BOURO (SANTA MARIA)', 19, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (227, 34, 'BOURO (SANTA MARTA)', 20, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (228, 34, 'CAIRES', 05, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (229, 34, 'CALDELAS, SEQUEIROS E PARANHOS', 26, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (230, 34, 'CARRAZEDO', 07, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (231, 34, 'DORNELAS', 08, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (232, 34, 'FERREIROS, PROZELO E BESTEIROS', 27, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (233, 34, 'FISCAL', 11, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (234, 34, 'GO�ES', 12, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (235, 34, 'LAGO', 13, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (236, 34, 'RENDUFE', 18, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (237, 34, 'TORRE E PORTELA', 28, 01, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (238, 34, 'VILELA, SERAMIL E PAREDES SECAS', 29, 01, 03);


/*

03	BRAGA                         	02	BARCELOS                      	05	AIRO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	21	CARVALHAS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	72	GALEGOS (S. MARTINHO)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	43	LIJO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	65	RIO COVO (SANTA EUGENIA)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	77	TAMEL (S. VERISSIMO)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	83	VARZEA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	01	ABADE DE NEIVA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	02	ABORIM	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	03	AD�ES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	06	ALDREU	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	90	ALHEIRA E IGREJA NOVA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	08	ALVELOS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	91	ALVITO (S�O PEDRO E S�O MARTINHO) E COUTO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	09	ARCOZELO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	10	AREIAS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	92	AREIAS DE VILAR E ENCOURADOS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	12	BALUG�ES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	13	BARCELINHOS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	93	BARCELOS, V.BOA, V.FRESCAINHA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	15	BARQUEIROS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	16	CAMBESES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	94	CAMPO E TAMEL (S�O PEDRO FINS)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	18	CARAPE�OS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	95	CARREIRA E FONTE COBERTA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	20	CARVALHAL	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	96	CHORENTE, G�IOS, COUREL, PEDRA FURADA E GUERAL	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	24	COSSOURADO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	97	CREIXOMIL E MARIZ	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	28	CRISTELO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	98	DURR�ES E TREGOSA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	34	FORNELOS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	35	FRAGOSO	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	68	GALEGOS (SANTA MARIA)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	99	GAMIL E MID�ES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	37	GILMONDE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	42	LAMA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	44	MACIEIRA DE RATES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	45	MANHENTE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	47	MARTIM	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FA	MILHAZES, VILAR DE FIGOS E FARIA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	52	MOURE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FB	NEGREIROS E CHAV�O	0353	BARCELOS   
03	BRAGA                         	02	BARCELOS                      	54	OLIVEIRA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	55	PALME	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	56	PANQUE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	57	PARADELA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	59	PEREIRA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	60	PERELHAL	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	61	POUSA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FC	QUINTI�ES E AGUIAR	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	63	REMELHE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	64	RORIZ	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FD	SEQUEADE E BASTU�O (S�O JO�O E SANTO ESTEV�O)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	79	SILVA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FE	SILVEIROS E RIO COVO (SANTA EUL�LIA)	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FF	TAMEL (SANTA LEOC�DIA) E VILAR DO MONTE	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	82	UCHA	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FG	VIATODOS, GRIMANCELOS, MINHOT�ES, MONTE FRAL�ES	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	FH	VILA COVA E FEITOS	0353	BARCELOS                      
03	BRAGA                         	02	BARCELOS                      	87	VILA SECA	0353	BARCELOS      

*/


INSERT INTO pcj.COMISSAO_PCJ values (35, null, 'BARCELOS', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 02, 'BARCELOS', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (239, 35, 'AIRO', 05, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (240, 35, 'CARVALHAS', 21, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (241, 35, 'GALEGOS (S. MARTINHO)', 72, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (242, 35, 'LIJO', 43, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (243, 35, 'RIO COVO (SANTA EUGENIA)', 65, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (244, 35, 'TAMEL (S. VERISSIMO)', 77, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (245, 35, 'VARZEA', 83, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (246, 35, 'ABADE DE NEIVA', 01, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (247, 35, 'ABORIM', 02, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (248, 35, 'AD�ES', 03, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (249, 35, 'ALDREU', 06, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (250, 35, 'ALHEIRA E IGREJA NOVA', 90, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (251, 35, 'ALVELOS', 08, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (252, 35, 'ALVITO (S�O PEDRO E S�O MARTINHO) E COUTO', 91, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (253, 35, 'ARCOZELO', 09, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (254, 35, 'AREIAS', 10, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (255, 35, 'AREIAS DE VILAR E ENCOURADOS', 92, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (256, 35, 'BALUG�ES', 12, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (257, 35, 'BARCELINHOS', 13, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (258, 35, 'BARCELOS, V.BOA, V.FRESCAINHA', 93, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (259, 35, 'BARQUEIROS', 15, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (260, 35, 'CAMBESES', 16, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (261, 35, 'CAMPO E TAMEL (S�O PEDRO FINS)', 94, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (262, 35, 'CARAPE�OS', 18, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (263, 35, 'CARREIRA E FONTE COBERTA', 95, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (264, 35, 'CARVALHAL', 20, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (265, 35, 'CHORENTE, G�IOS, COUREL, PEDRA FURADA E GUERAL', 96, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (266, 35, 'COSSOURADO', 24, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (267, 35, 'CREIXOMIL E MARIZ', 97, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (268, 35, 'CRISTELO', 28, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (269, 35, 'DURR�ES E TREGOSA', 98, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (270, 35, 'FORNELOS', 34, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (271, 35, 'FRAGOSO', 35, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (272, 35, 'GALEGOS (SANTA MARIA)', 68, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (273, 35, 'GAMIL E MID�ES', 99, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (274, 35, 'GILMONDE', 37, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (275, 35, 'LAMA', 42, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (276, 35, 'MACIEIRA DE RATES', 44, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (277, 35, 'MANHENTE', 45, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (278, 35, 'MARTIM', 47, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (279, 35, 'MILHAZES, VILAR DE FIGOS E FARIA', 200, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (280, 35, 'MOURE', 52, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (281, 35, 'NEGREIROS E CHAV�O', 201, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (282, 35, 'OLIVEIRA', 54, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (283, 35, 'PALME', 55, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (284, 35, 'PANQUE', 56, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (285, 35, 'PARADELA', 57, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (286, 35, 'PEREIRA', 59, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (287, 35, 'PERELHAL', 60, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (288, 35, 'POUSA', 61, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (289, 35, 'QUINTI�ES E AGUIAR', 202, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (290, 35, 'REMELHE', 63, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (291, 35, 'RORIZ', 64, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (292, 35, 'SEQUEADE E BASTU�O (S�O JO�O E SANTO ESTEV�O)', 203, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (293, 35, 'SILVA', 79, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (294, 35, 'SILVEIROS E RIO COVO (SANTA EUL�LIA)', 204, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (295, 35, 'TAMEL (SANTA LEOC�DIA) E VILAR DO MONTE', 205, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (296, 35, 'UCHA', 82, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (297, 35, 'VIATODOS, GRIMANCELOS, MINHOT�ES, MONTE FRAL�ES', 206, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (298, 35, 'VILA COVA E FEITOS', 207, 02, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (299, 35, 'VILA SECA', 87, 02, 03);



/*
                
03	BRAGA                         	03	BRAGA                         	01	ADAUFE	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	49	BRAGA (S. VICENTE)	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	51	BRAGA (S. VITOR)	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	63	ARENTIM E CUNHA	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	64	BRAGA (MAXIMINOS, S� E CIVIDADE)	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	65	BRAGA (S�O JOS� DE S�O L�ZARO E S�O JO�O DO SOUTO)	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	66	CABREIROS E PASSOS (S�O JULI�O)	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	67	CELEIR�S, AVELEDA E VIMIEIRO	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	68	CRESPOS E POUSADA	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	69	ESCUDEIROS E PENSO (SANTO EST�V�O E S�O VICENTE)	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	12	ESPINHO	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	13	ESPOR�ES	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	70	ESTE (S�O PEDRO E S�O MAMEDE)	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	71	FERREIROS E GONDIZALVES	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	15	FIGUEIREDO	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	19	GUALTAR	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	72	GUISANDE E OLIVEIRA (S�O PEDRO)	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	22	LAMAS	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	73	LOMAR E ARCOS	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	74	MERELIM (S�O PAIO), PANOIAS E PARADA DE TIB�ES	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	75	MERELIM (S�O PEDRO) E FROSSOS	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	25	MIRE DE TIB�ES	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	76	MORREIRA E TRANDEIRAS	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	77	NOGUEIRA, FRAI�O E LAMA��ES	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	78	NOGUEIR� E TEN�ES	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	30	PADIM DA GRA�A	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	31	PALMEIRA	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	34	PEDRALVA	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	36	PRISCOS	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	79	REAL, DUME E SEMELHE	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	38	RUILHE	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	80	SANTA LUCR�CIA DE ALGERIZ E NAVARRA	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	54	SEQUEIRA	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	55	SOBREPOSTA	0361	BRAGA-1.                      
03	BRAGA                         	03	BRAGA                         	56	TADIM	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	57	TEBOSA	3425	BRAGA-2.                      
03	BRAGA                         	03	BRAGA                         	81	VILA�A E FRADELOS	3425	BRAGA-2.                       
       
                 
*/

INSERT INTO pcj.COMISSAO_PCJ values (36, null, 'BRAGA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 03, 'BRAGA', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (300, 36, 'ADAUFE', 01, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (301, 36, 'BRAGA (S. VICENTE)', 49, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (302, 36, 'BRAGA (S. VITOR)', 51, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (303, 36, 'ARENTIM E CUNHA', 63, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (304, 36, 'BRAGA (MAXIMINOS, S� E CIVIDADE)', 64, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (305, 36, 'BRAGA (S�O JOS� DE S�O L�ZARO E S�O JO�O DO SOUTO)', 65, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (306, 36, 'CABREIROS E PASSOS (S�O JULI�O)', 66, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (307, 36, 'CELEIR�S, AVELEDA E VIMIEIRO', 67, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (308, 36, 'CRESPOS E POUSADA', 68, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (309, 36, 'ESCUDEIROS E PENSO (SANTO EST�V�O E S�O VICENTE)', 69, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (310, 36, 'ESPINHO', 12, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (311, 36, 'ESPOR�ES', 13, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (312, 36, 'ESTE (S�O PEDRO E S�O MAMEDE)', 70, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (313, 36, 'FERREIROS E GONDIZALVES', 71, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (314, 36, 'FIGUEIREDO', 15, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (315, 36, 'GUALTAR', 19, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (316, 36, 'GUISANDE E OLIVEIRA (S�O PEDRO)', 72, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (317, 36, 'LAMAS', 22, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (318, 36, 'LOMAR E ARCOS', 73, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (319, 36, 'MERELIM (S�O PAIO), PANOIAS E PARADA DE TIB�ES', 74, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (320, 36, 'MERELIM (S�O PEDRO) E FROSSOS', 75, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (321, 36, 'MIRE DE TIB�ES', 25, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (322, 36, 'MORREIRA E TRANDEIRAS', 76, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (323, 36, 'NOGUEIRA, FRAI�O E LAMA��ES', 77, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (324, 36, 'NOGUEIR� E TEN�ES', 78, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (325, 36, 'PADIM DA GRA�A', 30, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (326, 36, 'PALMEIRA', 31, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (327, 36, 'PEDRALVA', 34, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (328, 36, 'PRISCOS', 36, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (329, 36, 'REAL, DUME E SEMELHE', 79, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (330, 36, 'RUILHE', 38, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (331, 36, 'SANTA LUCR�CIA DE ALGERIZ E NAVARRA', 80, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (332, 36, 'SEQUEIRA', 54, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (333, 36, 'SOBREPOSTA', 55, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (334, 36, 'TADIM', 56, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (335, 36, 'TEBOSA', 57, 03, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (336, 36, 'VILA�A E FRADELOS', 81, 03, 03);



/*
 
03	BRAGA                         	04	CABECEIRAS DE BASTO           	01	ABADIM	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	18	ALVITE E PASSOS	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	19	ARCO DE BA�LHE E VILA NUNE	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	04	BASTO	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	05	BUCOS	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	06	CABECEIRAS DE BASTO	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	07	CAVEZ	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	08	FAIA	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	20	GONDI�ES E VILAR DE CUNHAS	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	13	PEDRA�A	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	21	REFOJOS DE BASTO, OUTEIRO E PAINZELA	0370	CABECEIRAS DE BASTO           
03	BRAGA                         	04	CABECEIRAS DE BASTO           	15	RIO DOURO	0370	CABECEIRAS DE BASTO           
                 
*/


INSERT INTO pcj.COMISSAO_PCJ values (37, null, 'CABECEIRAS DE BASTO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 04, 'CABECEIRAS DE BASTO', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (337, 37, 'ABADIM', 01, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (338, 37, 'ALVITE E PASSOS', 18, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (339, 37, 'ARCO DE BA�LHE E VILA NUNE', 19, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (340, 37, 'BASTO', 04, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (341, 37, 'BUCOS', 05, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (342, 37, 'CABECEIRAS DE BASTO', 06, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (343, 37, 'CAVEZ', 07, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (344, 37, 'FAIA', 08, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (345, 37, 'GONDI�ES E VILAR DE CUNHAS', 20, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (346, 37, 'PEDRA�A', 13, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (347, 37, 'REFOJOS DE BASTO, OUTEIRO E PAINZELA', 21, 04, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (348, 37, 'RIO DOURO', 15, 04, 03);


/*

03	BRAGA                         	05	CELORICO DE BASTO             	02	ARNOIA	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	20	BASTO (S. CLEMENTE)	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	03	BORBA DA MONTANHA	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	01	AGILDE	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	23	BRITELO, G�MEOS E OURILHE	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	24	CA�ARILHE E INFESTA	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	25	CANEDO DE BASTO E CORGO	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	26	CARVALHO E BASTO (SANTA TECLA)	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	08	CODE�OSO	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	10	FERVEN�A	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	15	MOREIRA DO CASTELO	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	17	REGO	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	18	CODE�OSORIBAS	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	21	VALE DE BOURO	0388	CELORICO DE BASTO             
03	BRAGA                         	05	CELORICO DE BASTO             	27	VEADE, GAGOS E MOLARES	0388	CELORICO DE BASTO             

*/


INSERT INTO pcj.COMISSAO_PCJ values (38, null, 'CELORICO DE BASTO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 05, 'CELORICO DE BASTO', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (349, 38, 'ARNOIA', 02, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (350, 38, 'BASTO (S. CLEMENTE)', 20, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (351, 38, 'BORBA DA MONTANHA', 03, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (352, 38, 'AGILDE', 01, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (353, 38, 'BRITELO, G�MEOS E OURILHE', 23, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (354, 38, 'CA�ARILHE E INFESTA', 24, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (355, 38, 'CANEDO DE BASTO E CORGO', 25, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (356, 38, 'CARVALHO E BASTO (SANTA TECLA)', 26, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (357, 38, 'CODE�OSO', 08, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (358, 38, 'FERVEN�A', 10, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (359, 38, 'MOREIRA DO CASTELO', 15, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (360, 38, 'REGO', 17, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (361, 38, 'CODE�OSORIBAS', 18, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (362, 38, 'VALE DE BOURO', 21, 05, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (363, 38, 'VEADE, GAGOS E MOLARES', 27, 05, 03);



/*

03	BRAGA                         	06	ESPOSENDE                     	01	ANTAS	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	16	AP�LIA E F�O	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	17	BELINHO E MAR	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	18	ESPOSENDE, MARINHAS E GANDRA	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	19	FONTE BOA E RIO TINTO	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	08	FORJ�ES	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	10	GEMESES	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	20	PALMEIRA DE FARO E CURVOS	0396	ESPOSENDE                     
03	BRAGA                         	06	ESPOSENDE                     	15	VILA CH�	0396	ESPOSENDE                     
 
*/


INSERT INTO pcj.COMISSAO_PCJ values (39, null, 'ESPOSENDE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 06, 'ESPOSENDE', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (364, 39, 'ANTAS', 01, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (365, 39, 'AP�LIA E F�O', 16, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (366, 39, 'BELINHO E MAR', 17, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (367, 39, 'ESPOSENDE, MARINHAS E GANDRA', 18, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (368, 39, 'FONTE BOA E RIO TINTO', 19, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (369, 39, 'FORJ�ES', 08, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (370, 39, 'GEMESES', 10, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (371, 39, 'PALMEIRA DE FARO E CURVOS', 20, 06, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (372, 39, 'VILA CH�', 15, 06, 03);


/*

03	BRAGA                         	07	FAFE                          	30	AR�ES (S. ROM�O)	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	28	S. GENS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	29	SILVARES (S. MARTINHO)	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	33	TRAVASSOS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	36	VINHOS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	37	ABOIM, FELGUEIRAS, GONTIM E PEDRA�DO	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	38	AGRELA E SERAF�O	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	39	ANTIME E SILVARES (S�O CLEMENTE)	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	40	ARDEG�O, ARNOZELA E SEID�ES	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	05	ARMIL	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	26	AR�ES (SANTA CRISTINA)	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	41	CEP�ES E FAREJA	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	08	ESTOR�OS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	09	FAFE	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	12	FORNELOS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	42	FREITAS E VILA COVA	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	14	GOL�ES	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	16	MEDELO	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	43	MONTE E QUEIMADELA	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	44	MOREIRA DO REI E V�RZEA COVA	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	19	PASSOS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	22	QUINCH�ES	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	23	REGADAS	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	24	REVELHE	0400	FAFE                          
03	BRAGA                         	07	FAFE                          	25	RIBEIROS	0400	FAFE                          

*/


INSERT INTO pcj.COMISSAO_PCJ values (40, null, 'FAFE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 07, 'FAFE', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (373, 40, 'AR�ES (S. ROM�O)', 30, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (374, 40, 'S. GENS', 28, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (375, 40, 'SILVARES (S. MARTINHO)', 29, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (376, 40, 'TRAVASSOS', 33, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (377, 40, 'VINHOS', 36, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (378, 40, 'ABOIM, FELGUEIRAS, GONTIM E PEDRA�DO', 37, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (379, 40, 'AGRELA E SERAF�O', 38, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (380, 40, 'ANTIME E SILVARES (S�O CLEMENTE)', 39, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (381, 40, 'ARDEG�O, ARNOZELA E SEID�ES', 40, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (382, 40, 'ARMIL', 05, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (383, 40, 'AR�ES (SANTA CRISTINA)', 26, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (384, 40, 'CEP�ES E FAREJA', 41, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (385, 40, 'ESTOR�OS', 08, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (386, 40, 'FAFE', 09, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (387, 40, 'FORNELOS', 12, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (388, 40, 'FREITAS E VILA COVA', 42, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (389, 40, 'GOL�ES', 14, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (390, 40, 'MEDELO', 16, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (391, 40, 'MONTE E QUEIMADELA', 43, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (392, 40, 'MOREIRA DO REI E V�RZEA COVA', 44, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (393, 40, 'PASSOS', 19, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (394, 40, 'QUINCH�ES', 22, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (395, 40, 'REGADAS', 23, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (396, 40, 'REVELHE', 24, 07, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (397, 40, 'RIBEIROS', 25, 07, 03);


/*

03	BRAGA                         	08	GUIMAR�ES                     	04	AZUREM	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	57	CANDOSO (S. MARTINHO)	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	31	MOREIRA DE CONEGOS	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	42	PRAZINS (SANTA EUFEMIA)	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	65	S. TORCATO	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	58	SANDE (S. MARTINHO)	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	50	SELHO (S. CRISTOV�O)	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	54	SELHO (S. JORGE)	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	75	ABA��O E G�MEOS	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	76	AIR�O SANTA MARIA, AIR�O S�O JO�O E VERMIL	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	01	ALD�O	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	77	AROSA E CASTEL�ES	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	78	AT�ES E RENDUFE	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	06	BARCO	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	79	BRITEIROS SANTO EST�V�O E DONIM	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	80	BRITEIROS S�O SALVADOR E BRITEIROS SANTA LEOC�DIA	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	07	BRITO	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	08	CALDELAS	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	81	CANDOSO S�O TIAGO E MASCOTELOS	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	82	CONDE E GANDARELA	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	12	COSTA	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	13	CREIXOMIL	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	15	FERMENT�ES	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	20	GON�A	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	21	GONDAR	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	23	GUARDIZELA	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	24	INFANTAS	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	83	LEIT�ES, OLEIROS E FIGUEIREDO	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	27	LONGOS	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	28	LORDELO	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	30	MES�O FRIO	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	32	NESPEREIRA	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	84	OLIVEIRA, S�O PAIO E S�O SEBASTI�O	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	35	PENCELO	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	36	PINHEIRO	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	37	POLVOREIRA	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	38	PONTE	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	85	PRAZINS SANTO TIRSO E CORVITE	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	40	RONFE	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	86	SANDE S�O LOUREN�O E BALAZAR	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	87	SANDE VILA NOVA E SANDE S�O CLEMENTE	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	88	SELHO S�O LOUREN�O E GOMINH�ES	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	66	SERZEDELO	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	89	SERZEDO E CALVOS	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	68	SILVARES	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	90	SOUTO SANTA MARIA, SOUTO S�O SALVADOR E GONDOMAR	0418	GUIMARAES-1.                  
03	BRAGA                         	08	GUIMAR�ES                     	91	TABUADELO E S�O FAUSTINO	3476	GUIMARAES-2.                  
03	BRAGA                         	08	GUIMAR�ES                     	71	URGEZES	3476	GUIMARAES-2.                  

*/


INSERT INTO pcj.COMISSAO_PCJ values (41, null, 'GUIMAR�ES', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 08, 'GUIMAR�ES', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (398, 41, 'AZUREM', 04, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (399, 41, 'CANDOSO (S. MARTINHO)', 57, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (400, 41, 'MOREIRA DE CONEGOS', 31, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (401, 41, 'PRAZINS (SANTA EUFEMIA)', 42, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (402, 41, 'S. TORCATO', 65, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (403, 41, 'SANDE (S. MARTINHO)', 58, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (404, 41, 'SELHO (S. CRISTOV�O)', 50, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (405, 41, 'SELHO (S. JORGE)', 54, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (406, 41, 'ABA��O E G�MEOS', 75, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (407, 41, 'AIR�O SANTA MARIA, AIR�O S�O JO�O E VERMIL', 76, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (408, 41, 'ALD�O', 01, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (409, 41, 'AROSA E CASTEL�ES', 77, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (410, 41, 'AT�ES E RENDUFE', 78, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (411, 41, 'BARCO', 06, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (412, 41, 'BRITEIROS SANTO EST�V�O E DONIM', 79, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (413, 41, 'BRITEIROS S�O SALVADOR E BRITEIROS SANTA LEOC�DIA', 80, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (414, 41, 'BRITO', 07, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (415, 41, 'CALDELAS', 08, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (416, 41, 'CANDOSO S�O TIAGO E MASCOTELOS', 81, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (417, 41, 'CONDE E GANDARELA', 82, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (418, 41, 'COSTA', 12, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (419, 41, 'CREIXOMIL', 13, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (420, 41, 'FERMENT�ES', 15, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (421, 41, 'GON�A', 20, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (422, 41, 'GONDAR', 21, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (423, 41, 'GUARDIZELA', 23, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (424, 41, 'INFANTAS', 24, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (425, 41, 'LEIT�ES, OLEIROS E FIGUEIREDO', 83, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (426, 41, 'LONGOS', 27, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (427, 41, 'LORDELO', 28, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (428, 41, 'MES�O FRIO', 30, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (429, 41, 'NESPEREIRA', 32, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (430, 41, 'OLIVEIRA, S�O PAIO E S�O SEBASTI�O', 84, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (431, 41, 'PENCELO', 35, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (432, 41, 'PINHEIRO', 36, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (433, 41, 'POLVOREIRA', 37, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (434, 41, 'PONTE', 38, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (435, 41, 'PRAZINS SANTO TIRSO E CORVITE', 85, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (436, 41, 'RONFE', 40, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (437, 41, 'SANDE S�O LOUREN�O E BALAZAR', 86, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (438, 41, 'SANDE VILA NOVA E SANDE S�O CLEMENTE', 87, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (439, 41, 'SELHO S�O LOUREN�O E GOMINH�ES', 88, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (440, 41, 'SERZEDELO', 66, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (441, 41, 'SERZEDO E CALVOS', 89, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (442, 41, 'SILVARES', 68, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (443, 41, 'SOUTO SANTA MARIA, SOUTO S�O SALVADOR E GONDOMAR', 90, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (444, 41, 'TABUADELO E S�O FAUSTINO', 91, 08, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (445, 41, 'URGEZES', 71, 08, 03);



/*

03	BRAGA                         	09	POVOA DE LANHOSO              	19	POVOA DE LANHOSO (N. SENHORA DO AMPARO)	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	23	S. JO�O DE REI	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	26	TAIDE	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	30	�GUAS SANTAS E MOURE	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	31	CALVOS E FRADES	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	32	CAMPOS E LOUREDO	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	06	COVELAS	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	33	ESPERAN�A E BRUNHAIS	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	08	FERREIROS	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	34	FONTE ARCADA E OLIVEIRA	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	12	GALEGOS	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	13	GARFE	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	14	GERAZ DO MINHO	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	15	LANHOSO	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	17	MONSUL	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	21	RENDUFINHO	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	22	SANTO EMILI�O	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	24	SERZEDELO	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	25	SOBRADELO DA GOMA	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	27	TRAVASSOS	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	35	VERIM, FRIANDE E AJUDE	0426	POVOA DE LANHOSO              
03	BRAGA                         	09	POVOA DE LANHOSO              	29	VILELA	0426	POVOA DE LANHOSO              

*/


INSERT INTO pcj.COMISSAO_PCJ values (42, null, 'POVOA DE LANHOSO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 09, 'POVOA DE LANHOSO', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (446, 42, 'POVOA DE LANHOSO (N. SENHORA DO AMPARO)', 19, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (447, 42, 'S. JO�O DE REI', 23, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (448, 42, 'TAIDE', 26, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (449, 42, '�GUAS SANTAS E MOURE', 30, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (450, 42, 'CALVOS E FRADES', 31, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (451, 42, 'CAMPOS E LOUREDO', 32, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (452, 42, 'COVELAS', 06, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (453, 42, 'ESPERAN�A E BRUNHAIS', 33, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (454, 42, 'FERREIROS', 08, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (455, 42, 'FONTE ARCADA E OLIVEIRA', 34, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (456, 42, 'GALEGOS', 12, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (457, 42, 'GARFE', 13, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (458, 42, 'GERAZ DO MINHO', 14, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (459, 42, 'LANHOSO', 15, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (460, 42, 'MONSUL', 17, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (461, 42, 'RENDUFINHO', 21, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (462, 42, 'SANTO EMILI�O', 22, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (463, 42, 'SERZEDELO', 24, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (464, 42, 'SOBRADELO DA GOMA', 25, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (465, 42, 'TRAVASSOS', 27, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (466, 42, 'VERIM, FRIANDE E AJUDE', 35, 09, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (467, 42, 'VILELA', 29, 09, 03);



/*

03	BRAGA                         	10	TERRAS DE BOURO               	03	CAMPO DO GERES	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	01	BALAN�A	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	04	CARVALHEIRA	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	18	CHAMOIM E VILAR	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	19	CHORENSE E MONTE	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	20	CIB�ES E BRUFE	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	08	COVIDE	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	09	GONDORIZ	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	10	MOIMENTA	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	12	RIBEIRA	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	13	RIO CALDO	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	14	SOUTO	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	15	VALDOSENDE	0434	TERRAS DE BOURO               
03	BRAGA                         	10	TERRAS DE BOURO               	17	VILAR DA VEIGA	0434	TERRAS DE BOURO               

*/


INSERT INTO pcj.COMISSAO_PCJ values (43, null, 'TERRAS DE BOURO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 10, 'TERRAS DE BOURO', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (468, 43, 'CAMPO DO GERES', 03, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (469, 43, 'BALAN�A', 01, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (470, 43, 'CARVALHEIRA', 04, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (471, 43, 'CHAMOIM E VILAR', 18, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (472, 43, 'CHORENSE E MONTE', 19, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (473, 43, 'CIB�ES E BRUFE', 20, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (474, 43, 'COVIDE', 08, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (475, 43, 'GONDORIZ', 09, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (476, 43, 'MOIMENTA', 10, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (477, 43, 'RIBEIRA', 12, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (478, 43, 'RIO CALDO', 13, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (479, 43, 'SOUTO', 14, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (480, 43, 'VALDOSENDE', 15, 10, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (481, 43, 'VILAR DA VEIGA', 17, 10, 03);


/*

03	BRAGA                         	11	VIEIRA DO MINHO               	22	ANISS� E SOUTELO	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	23	ANJOS E VILAR DO CH�O	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	24	CANI�ADA E SOENGAS	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	05	CANTEL�ES	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	07	EIRA VEDRA	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	08	GUILHOFREI	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	09	LOUREDO	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	10	MOSTEIRO	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	11	PARADA DO BOURO	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	12	PINHEIRO	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	13	ROSSAS	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	25	RUIV�ES E CAMPOS	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	15	SALAMONDE	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	18	TABUA�AS	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	26	VENTOSA E COVA	0442	VIEIRA DO MINHO               
03	BRAGA                         	11	VIEIRA DO MINHO               	20	VIEIRA DO MINHO	0442	VIEIRA DO MINHO               

*/


INSERT INTO pcj.COMISSAO_PCJ values (44, null, 'VIEIRA DO MINHO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 11, 'VIEIRA DO MINHO', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (482, 44, 'ANISS� E SOUTELO', 22, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (483, 44, 'ANJOS E VILAR DO CH�O', 23, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (484, 44, 'CANI�ADA E SOENGAS', 24, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (485, 44, 'CANTEL�ES', 05, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (486, 44, 'EIRA VEDRA', 07, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (487, 44, 'GUILHOFREI', 08, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (488, 44, 'LOUREDO', 09, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (489, 44, 'MOSTEIRO', 10, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (490, 44, 'PARADA DO BOURO', 11, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (491, 44, 'PINHEIRO', 12, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (492, 44, 'ROSSAS', 13, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (493, 44, 'RUIV�ES E CAMPOS', 25, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (494, 44, 'SALAMONDE', 15, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (495, 44, 'TABUA�AS', 18, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (496, 44, 'VENTOSA E COVA', 26, 11, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (497, 44, 'VIEIRA DO MINHO', 20, 11, 03);


/*

03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	42	OLIVEIRA (S. MATEUS)	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	41	VALE (S. MARTINHO)	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	50	ANTAS E ABADE DE VERMOIM	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	51	ARNOSO (SANTA MARIA E SANTA EUL�LIA) E SEZURES	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	52	AVIDOS E LAGOA	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	04	BAIRRO	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	06	BRUFE	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	53	CARREIRA E BENTE	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	10	CASTEL�ES	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	12	CRUZ	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	13	DEL�ES	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	54	ESMERIZ E CABE�UDOS	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	15	FRADELOS	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	16	GAVI�O	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	55	GONDIFELOS, CAVAL�ES E OUTIZ	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	19	JOANE	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	21	LANDIM	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	56	LEMENHE, MOUQUIM E JESUFREI	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	23	LOURO	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	24	LOUSADO	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	25	MOGEGE	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	27	NINE	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	39	OLIVEIRA (SANTA MARIA)	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	30	PEDOME	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	32	POUSADA DE SARAMAGOS	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	33	REQUI�O	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	34	RIBA DE AVE	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	35	RIBEIR�O	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	57	RUIV�ES E NOVAIS	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	58 RIBEIR�O	SEIDE	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	59	VALE (S�O COSME), TELHADO E PORTELA	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	47	VERMOIM	0450	VILA N.FAMALICAO-1.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	60	VILA NOVA DE FAMALIC�O E CALEND�RIO	3590	VILA N.FAMALICAO 2.           
03	BRAGA                         	12	VILA NOVA DE FAMALIC�O        	49	VILARINHO DAS CAMBAS	3590	VILA N.FAMALICAO 2.           

*/


INSERT INTO pcj.COMISSAO_PCJ values (45, null, 'VILA NOVA DE FAMALIC�O', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 12, 'VILA NOVA DE FAMALIC�O', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (498, 45, 'OLIVEIRA (S. MATEUS)', 42, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (499, 45, 'VALE (S. MARTINHO) ', 41, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (500, 45, 'ANTAS E ABADE DE VERMOIM ', 50, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (501, 45, 'ARNOSO (SANTA MARIA E SANTA EUL�LIA) E SEZURES ', 51, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (502, 45, 'AVIDOS E LAGOA ', 52, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (503, 45, 'BAIRRO ', 04, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (504, 45, 'BRUFE ', 06, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (505, 45, 'CARREIRA E BENTE ', 53, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (506, 45, 'CASTEL�ES ', 10, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (507, 45, 'CRUZ ', 12, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (508, 45, 'DEL�ES ', 13, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (509, 45, 'ESMERIZ E CABE�UDOS ', 54, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (510, 45, 'FRADELOS ', 15, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (511, 45, 'GAVI�O ', 16, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (512, 45, 'GONDIFELOS, CAVAL�ES E OUTIZ ', 55, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (513, 45, 'JOANE ', 19, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (514, 45, 'LANDIM ', 21, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (515, 45, 'LEMENHE, MOUQUIM E JESUFREI ', 56, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (516, 45, 'LOURO ', 23, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (517, 45, 'LOUSADO ', 24, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (518, 45, 'MOGEGE ', 25, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (519, 45, 'NINE ', 27, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (520, 45, 'OLIVEIRA (SANTA MARIA) ', 39, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (521, 45, 'PEDOME ', 30, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (522, 45, 'POUSADA DE SARAMAGOS ', 32, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (523, 45, 'REQUI�O ', 33, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (524, 45, 'RIBA DE AVE ', 34, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (525, 45, 'RIBEIR�O ', 35, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (526, 45, 'RUIV�ES E NOVAIS ', 57, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (527, 45, 'RIBEIR�O	SEIDE ', 58, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (528, 45, 'VALE (S�O COSME), TELHADO E PORTELA ', 59, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (529, 45, 'VERMOIM ', 47, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (530, 45, 'VILA NOVA DE FAMALIC�O E CALEND�RIO ', 60, 12, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (531, 45, 'VILARINHO DAS CAMBAS ', 49, 12, 03);



/*

03	BRAGA                         	13	VILA VERDE                    	17	G�ME	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	23	LAGE	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	50	PRADO (S. MIGUEL)	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	42	VILA DE PRADO	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	59	ABOIM DA N�BREGA E GONDOMAR	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	04	ATI�ES	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	08	CABANELAS	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	61	CARREIRAS (S�O MIGUEL) E CARREIRAS (SANTIAGO)	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	09	CERV�ES	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	11	COUCIEIRO	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	13	DOSS�OS	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	62	ESCARIZ (S�O MAMEDE) E ESCARIZ (S�O MARTINHO)	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	63	ESQUEIROS, NEVOGILDE E TRAVASS�S	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	16	FREIRIZ	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	24	LANHAS	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	25	LOUREIRA	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	64	MARRANCOS E ARCOZELO	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	28	MOURE	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	30	OLEIROS	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	65	ORIZ (SANTA MARINHA) E ORIZ (S�O MIGUEL)	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	31	PARADA DE GATIM	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	35	PICO	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	66	PICO DE REGALADOS, GONDI�ES E M�S	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	37	PONTE	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	60	RIBEIRA DO NEIVA	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	40	SABARIZ	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	67	SANDE, VILARINHO, BARROS E GOMIDE	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	52	SOUTELO	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	54	TURIZ	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	69	VADE	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	68	VALBOM (S�O PEDRO), PASS� E VALBOM (S�O MARTINHO)	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	55	VALDREU	0469	VILA VERDE                    
03	BRAGA                         	13	VILA VERDE                    	70	VILA VERDE E BARBUDO	0469	VILA VERDE                    
*/



INSERT INTO pcj.COMISSAO_PCJ values (46, null, 'VILA VERDE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 13, 'VILA VERDE', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (532, 46, 'G�ME', 17, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (533, 46, 'LAGE', 23, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (534, 46, 'PRADO (S. MIGUEL)', 50, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (535, 46, 'ILA DE PRADO', 42, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (536, 46, 'ABOIM DA N�BREGA E GONDOMAR', 59, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (537, 46, 'ATI�ES', 04, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (538, 46, 'CABANELAS', 08, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (539, 46, 'CARREIRAS (S�O MIGUEL) E CARREIRAS (SANTIAGO)', 61, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (540, 46, 'CERV�ES', 09, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (541, 46, 'COUCIEIRO', 11, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (542, 46, 'DOSS�OS', 13, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (543, 46, 'ESCARIZ (S�O MAMEDE) E ESCARIZ (S�O MARTINHO)', 62, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (544, 46, 'ESQUEIROS, NEVOGILDE E TRAVASS�S', 63, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (545, 46, 'FREIRIZ', 16, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (546, 46, 'LANHAS', 24, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (547, 46, 'LOUREIRA', 25, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (548, 46, 'MARRANCOS E ARCOZELO', 64, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (549, 46, 'MOURE', 28, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (550, 46, 'OLEIROS', 30, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (551, 46, 'ORIZ (SANTA MARINHA) E ORIZ (S�O MIGUEL)', 65, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (552, 46, 'PARADA DE GATIM', 31, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (553, 46, 'PICO', 35, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (554, 46, 'PICO DE REGALADOS, GONDI�ES E M�S', 66, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (555, 46, 'PONTE', 37, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (556, 46, 'RIBEIRA DO NEIVA', 60, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (557, 46, 'SABARIZ', 40, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (558, 46, 'SANDE, VILARINHO, BARROS E GOMIDE', 67, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (559, 46, 'SOUTELO', 52, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (560, 46, 'TURIZ', 54, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (561, 46, 'VADE', 69, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (562, 46, 'VALBOM (S�O PEDRO), PASS� E VALBOM (S�O MARTINHO)', 68, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (563, 46, 'VALDREU', 55, 13, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (564, 46, 'VILA VERDE E BARBUDO', 70, 13, 03);


/*

03	BRAGA                         	14	VIZELA                        	03	SANTO ADRI�O DE VIZELA	4200	VIZELA                        
03	BRAGA                         	14	VIZELA                        	08	CALDAS DE VIZELA (S�O MIGUEL E S�O JO�O)	4200	VIZELA                        
03	BRAGA                         	14	VIZELA                        	01	INFIAS	4200	VIZELA                        
03	BRAGA                         	14	VIZELA                        	02	SANTA EUL�LIA	4200	VIZELA                        
03	BRAGA                         	14	VIZELA                        	09	TAGILDE E VIZELA (S�O PAIO)	4200	VIZELA                        

*/


INSERT INTO pcj.COMISSAO_PCJ values (47, null, 'VIZELA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    03, 14, 'VIZELA', 'BRAGA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (565, 47, 'SANTO ADRI�O DE VIZELA', 03, 14, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (566, 47, 'CALDAS DE VIZELA (S�O MIGUEL E S�O JO�O)', 08, 14, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (567, 47, 'INFIAS', 01, 14, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (568, 47, 'SANTA EUL�LIA', 02, 14, 03);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (569, 47, 'TAGILDE E VIZELA (S�O PAIO)', 09, 14, 03);



commit;




