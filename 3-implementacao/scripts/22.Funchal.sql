/*Distrito do Funchal - 22*/

/*
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	02	CALHETA					2798	CALHETA - MADEIRA             	3014	300	CPCJ de Calheta (R.A.M.)	31	1	2
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	01	ARCO DA CALHETA			2798	CALHETA - MADEIRA             	3015	300	CPCJ de Calheta (R.A.M.)	31	1	1
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	03	ESTREITO DA CALHETA		2798	CALHETA - MADEIRA             	3016	300	CPCJ de Calheta (R.A.M.)	31	1	3
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	04	FAJ� DA OVELHA			2798	CALHETA - MADEIRA             	3017	300	CPCJ de Calheta (R.A.M.)	31	1	4
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	05	JARDIM DO MAR			2798	CALHETA - MADEIRA             	3018	300	CPCJ de Calheta (R.A.M.)	31	1	5
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	06	PAUL DO MAR				2798	CALHETA - MADEIRA             	3019	300	CPCJ de Calheta (R.A.M.)	31	1	6
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	07	PONTA DO PARGO			2798	CALHETA - MADEIRA             	3020	300	CPCJ de Calheta (R.A.M.)	31	1	7
22	FUNCHAL                       	01	CALHETA (MADEIRA)             	08	PRAZERES				2798	CALHETA - MADEIRA             	3021	300	CPCJ de Calheta (R.A.M.)	31	1	8

*/



INSERT INTO pcj.COMISSAO_PCJ values (300, null, 'CALHETA (R.A.M)', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null, 
                                    22, 1,'CALHETA (R.A.M)', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3014, 300, 'CALHETA', 2, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3015, 300, 'ARCO DA CALHETA', 1, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3016, 300, 'ESTREITO DA CALHETA', 3, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3017, 300, 'FAJ� DA OVELHA', 4, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3018, 300, 'JARDIM DO MAR', 5, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3019, 300, 'PAUL DO MAR', 6, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3020, 300, 'PONTA DO PARGO', 7, 1, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3021, 300, 'PRAZERES', 8, 1, 22);


/*
22	FUNCHAL                       	02	CAMARA DE LOBOS               	01	CAMARA DE LOBOS				2801	CAMARA DE LOBOS               	3022	301	CPCJ de C�mara de Lobos	31	2	1
22	FUNCHAL                       	02	CAMARA DE LOBOS               	03	ESTREITO DE CAMARA DE LOBOS	2801	CAMARA DE LOBOS               	3023	301	CPCJ de C�mara de Lobos	31	2	3
22	FUNCHAL                       	02	CAMARA DE LOBOS               	02	CURRAL DAS FREIRAS			2801	CAMARA DE LOBOS               	3024	301	CPCJ de C�mara de Lobos	31	2	2
22	FUNCHAL                       	02	CAMARA DE LOBOS               	05	JARDIM DA SERRA				2801	CAMARA DE LOBOS               	3025	301	CPCJ de C�mara de Lobos	31	2	5
22	FUNCHAL                       	02	CAMARA DE LOBOS               	04	QUINTA GRANDE				2801	CAMARA DE LOBOS               	3026	301	CPCJ de C�mara de Lobos	31	2	4


*/


INSERT INTO pcj.COMISSAO_PCJ values (301, null, 'C�MARA DE LOBOS', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null, 
                                    22, 2, 'C�MARA DE LOBOS', 'FUNCHAL');


INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3022, 301, 'C�MARA DE LOBOS', 1, 2, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3023, 301, 'ESTREITO DE CAMARA DE LOBOS', 3, 2, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3024, 301, 'CURRAL DAS FREIRAS', 2, 2, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3025, 301, 'JARDIM DA SERRA', 5, 2, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3026, 301, 'QUINTA GRANDE', 4, 2, 22);

/*
22	FUNCHAL                       	03	FUNCHAL                       	08	FUNCHAL (S. PEDRO)			2810	FUNCHAL-1.                    	3027	302	CPCJ de Funchal	31	3	8
22	FUNCHAL                       	03	FUNCHAL                       	10	FUNCHAL (SE)				2810	FUNCHAL-1.                    	3028	302	CPCJ de Funchal	31	3	10
22	FUNCHAL                       	03	FUNCHAL                       	06	S. GON�ALO					3450	FUNCHAL-2.                    	3029	302	CPCJ de Funchal	31	3	6
22	FUNCHAL                       	03	FUNCHAL                       	07	S. MARTINHO					2810	FUNCHAL-1.                    	3030	302	CPCJ de Funchal	31	3	7
22	FUNCHAL                       	03	FUNCHAL                       	09	S. ROQUE					3450	FUNCHAL-2.                    	3031	302	CPCJ de Funchal	31	3	9
22	FUNCHAL                       	03	FUNCHAL                       	05	SANTO ANTONIO				2810	FUNCHAL-1.                    	3032	302	CPCJ de Funchal	31	3	5
22	FUNCHAL                       	03	FUNCHAL                       	03	FUNCHAL (SANTA LUZIA)		3450	FUNCHAL-2.                    	3033	302	CPCJ de Funchal	31	3	3
22	FUNCHAL                       	03	FUNCHAL                       	04	FUNCHAL (SANTA MARIA MAIOR)	3450	FUNCHAL-2.                    	3034	302	CPCJ de Funchal	31	3	4
22	FUNCHAL                       	03	FUNCHAL                       	01	IMACULADO CORA��O DE MARIA	3450	FUNCHAL-2.                    	3035	302	CPCJ de Funchal	31	3	1
22	FUNCHAL                       	03	FUNCHAL                       	02	MONTE						3450	FUNCHAL-2.                    	3036	302	CPCJ de Funchal	31	3	2

*/

INSERT INTO pcj.COMISSAO_PCJ values (302, null, 'FUNCHAL', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 3,'FUNCHAL', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3027, 302, 'FUNCHAL (S. PEDRO)', 8, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3028, 302, 'FUNCHAL (SE)', 10, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3029, 302, 'S. GON�ALO', 6, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3030, 302, 'S. MARTINHO', 7, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3031, 302, 'S. ROQUE', 9, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3032, 302, 'SANTO ANTONIO', 5, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3033, 302, 'FUNCHAL (SANTA LUZIA)', 3, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3034,302, 'FUNCHAL (SANTA MARIA MAIOR)', 4, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3035, 302,  'IMACULADO CORA��O DE MARIA', 1, 3, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3036, 302,  'MONTE', 2, 3, 22);

/*
22	FUNCHAL                       	04	MACHICO                       	01	AGUA DE PENA			2828	MACHICO                       	3037	303	CPCJ de Machico	31	4	1
22	FUNCHAL                       	04	MACHICO                       	05	SANTO ANTONIO DA SERRA	2828	MACHICO                       	3038	303	CPCJ de Machico	31	4	5
22	FUNCHAL                       	04	MACHICO                       	02	CANI�AL					2828	MACHICO                       	3039	303	CPCJ de Machico	31	4	2
22	FUNCHAL                       	04	MACHICO                       	03	MACHICO					2828	MACHICO                       	3040	303	CPCJ de Machico	31	4	3
22	FUNCHAL                       	04	MACHICO                       	04	PORTO DA CRUZ			2828	MACHICO                       	3041	303	CPCJ de Machico	31	4	4


*/

INSERT INTO pcj.COMISSAO_PCJ values (303, null, 'MACHICO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 4,'MACHICO', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3037, 303, 'AGUA DE PENA', 1, 4, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3038, 303, 'SANTO ANTONIO DA SERRA', 5, 4, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3039, 303, 'CANI�AL', 2, 4, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3040, 303, 'MACHICO', 3, 4, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3041, 303, 'PORTO DA CRUZ', 4, 4, 22);

/*
22	FUNCHAL                       	05	PONTA DO SOL                  	01	CANHAS			2836	PONTA DO SOL                  	3042	304	CPCJ de Ponta do Sol	31	5	1
22	FUNCHAL                       	05	PONTA DO SOL                  	02	MADALENA DO MAR	2836	PONTA DO SOL                  	3043	304	CPCJ de Ponta do Sol	31	5	2
22	FUNCHAL                       	05	PONTA DO SOL                  	03	PONTA DO SOL	2836	PONTA DO SOL                  	3044	304	CPCJ de Ponta do Sol	31	5	3


*/

INSERT INTO pcj.COMISSAO_PCJ values (304, null, 'PONTA DO SOL', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 5,'PONTA DO SOL', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3042, 304, 'CANHAS', 1, 5, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3043, 304, 'MADALENA DO MAR', 2, 5, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3044, 304, 'PONTA DO SOL', 3, 5, 22);


/*
22	FUNCHAL                       	06	PORTO MONIZ                   	01	ACHADAS DA CRUZ		2844	PORTO MONIZ                   	3045	305	CPCJ de Porto Moniz	31	6	1
22	FUNCHAL                       	06	PORTO MONIZ                   	02	PORTO MONIZ			2844	PORTO MONIZ                   	3046	305	CPCJ de Porto Moniz	31	6	2
22	FUNCHAL                       	06	PORTO MONIZ                   	03	RIBEIRA DA JANELA	2844	PORTO MONIZ                   	3047	305	CPCJ de Porto Moniz	31	6	3
22	FUNCHAL                       	06	PORTO MONIZ                   	04	SEIXAL				2844	PORTO MONIZ                   	3048	305	CPCJ de Porto Moniz	31	6	4


*/

INSERT INTO pcj.COMISSAO_PCJ values (305, null, 'PORTO MONIZ', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 6,'PORTO MONIZ', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3045, 305, 'ACHADAS DA CRUZ', 1, 6, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3046, 305, 'PORTO MONIZ', 2, 6, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3047, 305, 'RIBEIRA DA JANELA', 3, 6, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3048, 305, 'SEIXAL', 4, 6, 22);

/*
22	FUNCHAL                       	07	PORTO SANTO                   	01	PORTO SANTO		2852	PORTO SANTO                   	3049	306	CPCJ de Porto Santo	32	1	1

*/

INSERT INTO pcj.COMISSAO_PCJ values (306, null, 'PORTO SANTO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 7,'PORTO SANTO', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3049, 306, 'PORTO SANTO', 1, 7, 22);

/*
22	FUNCHAL                       	08	RIBEIRA BRAVA                 	01	CAMPANARIO		2860	RIBEIRA BRAVA                 	3050	307	CPCJ de Ribeira Brava	31	7	1
22	FUNCHAL                       	08	RIBEIRA BRAVA                 	03	SERRA DE AGUA	2860	RIBEIRA BRAVA                 	3051	307	CPCJ de Ribeira Brava	31	7	3
22	FUNCHAL                       	08	RIBEIRA BRAVA                 	04	TABUA			2860	RIBEIRA BRAVA                 	3052	307	CPCJ de Ribeira Brava	31	7	4
22	FUNCHAL                       	08	RIBEIRA BRAVA                 	02	RIBEIRA BRAVA	2860	RIBEIRA BRAVA                 	3053	307	CPCJ de Ribeira Brava	31	7	2


*/

INSERT INTO pcj.COMISSAO_PCJ values (307, null, 'RIBEIRA BRAVA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 8, 'RIBEIRA BRAVA', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3050, 307, 'CAMPANARIO', 1, 8, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3051, 307, 'SERRA DE AGUA', 3, 8, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3052, 307, 'TABUA', 4, 8, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3053, 307, 'RIBEIRA BRAVA', 2, 8, 22);

/*
22	FUNCHAL                       	09	SANTA CRUZ                    	06	SANTO ANTONIO DA SERRA		2887	SANTA CRUZ (MADEIRA)          	3054	308	CPCJ de Santa Cruz	31	8	6
22	FUNCHAL                       	09	SANTA CRUZ                    	02	CAMACHA						2887	SANTA CRUZ (MADEIRA)          	3055	308	CPCJ de Santa Cruz	31	8	2
22	FUNCHAL                       	09	SANTA CRUZ                    	03	CANI�O						2887	SANTA CRUZ (MADEIRA)          	3056	308	CPCJ de Santa Cruz	31	8	3
22	FUNCHAL                       	09	SANTA CRUZ                    	04	GAULA						2887	SANTA CRUZ (MADEIRA)          	3057	308	CPCJ de Santa Cruz	31	8	4
22	FUNCHAL                       	09	SANTA CRUZ                    	05	SANTA CRUZ					2887	SANTA CRUZ (MADEIRA)          	3058	308	CPCJ de Santa Cruz	31	8	5


*/

INSERT INTO pcj.COMISSAO_PCJ values (308, null, 'SANTA CRUZ', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 9,'SANTA CRUZ', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3054, 308, 'SANTO ANTONIO DA SERRA', 6, 9, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3055, 308, 'CAMACHA', 2, 9, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3056, 308, 'CANI�O', 3, 9, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3057, 308, 'GAULA', 4, 9, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3058, 308, 'SANTA CRUZ', 5, 9, 22);

/*
22	FUNCHAL                       	10	SANTANA                       	01	ARCO DE S. JORGE	2895	SANTANA                       	3059	309	CPCJ de Santana	31	9	1
22	FUNCHAL                       	10	SANTANA                       	04	S. JORGE			2895	SANTANA                       	3060	309	CPCJ de Santana	31	9	4
22	FUNCHAL                       	10	SANTANA                       	05	S. ROQUE DO FAIAL	2895	SANTANA                       	3061	309	CPCJ de Santana	31	9	5
22	FUNCHAL                       	10	SANTANA                       	03	SANTANA				2895	SANTANA                       	3062	309	CPCJ de Santana	31	9	3
22	FUNCHAL                       	10	SANTANA                       	02	FAIAL				2895	SANTANA                       	3063	309	CPCJ de Santana	31	9	2
22	FUNCHAL                       	10	SANTANA                       	06	ILHA				2895	SANTANA                       	3064	309	CPCJ de Santana	31	9	6
*/

INSERT INTO pcj.COMISSAO_PCJ values (309, null, 'SANTANA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 10,'SANTANA', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3059, 309, 'ARCO DE S. JORGE', 1, 10, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3060, 309, 'S. JORGE', 4, 10, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3061, 309, 'S. ROQUE DO FAIAL', 5, 10, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3062, 309, 'SANTANA', 3, 10, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3063, 309, 'FAIAL', 2, 10, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3064, 309, 'ILHA', 6, 10, 22);

/*
22	FUNCHAL                       	11	S. VICENTE                    	03	S. VICENTE			2879	S.VICENTE (MADEIRA)           	3065	310	CPCJ de S�o Vicente	31	10	3
22	FUNCHAL                       	11	S. VICENTE                    	01	BOA VENTURA			2879	S.VICENTE (MADEIRA)           	3066	310	CPCJ de S�o Vicente	31	10	1
22	FUNCHAL                       	11	S. VICENTE                    	02	PONTA DELGADA		2879	S.VICENTE (MADEIRA)           	3067	310	CPCJ de S�o Vicente	31	10	2

*/

INSERT INTO pcj.COMISSAO_PCJ values (310, null, 'S. VICENTE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    22, 11,'S. VICENTE', 'FUNCHAL');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3065, 310, 'S. VICENTE', 3, 11, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3066, 310, 'BOA VENTURA', 1, 11, 22);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (3067, 310, 'PONTA DELGADA', 2, 11, 22);


COMMIT;



