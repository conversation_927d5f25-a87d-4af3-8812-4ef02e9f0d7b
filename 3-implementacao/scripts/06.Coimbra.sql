/*

06	COIMBRA                       	01	ARGANIL                       	11	PIOD�O	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	14	S. MARTINHO DA CORTI�A	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	02	ARGANIL	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	04	BENFEITA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	05	CELAVISA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	19	CEPOS E TEIXEIRA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	20	CERDEIRA E MOURA DA SERRA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	21	C�JA E BARRIL DE ALVA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	09	FOLQUES	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	12	POMARES	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	13	POMBEIRO DA BEIRA	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	15	SARZEDO	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	16	SECARIAS	0701	ARGANIL                       
06	COIMBRA                       	01	ARGANIL                       	22	VILA COVA DE ALVA E ANSERIZ	0701	ARGANIL                       

*/

INSERT INTO pcj.COMISSAO_PCJ values (71, null, 'ARGANIL', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 01, 'ARGANIL', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (916, 71, 'PIOD�O', 11 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (917, 71, 'S. MARTINHO DA CORTI�A', 14 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (918, 71, 'ARGANIL', 02 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (919, 71, 'BENFEITA', 04 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (920, 71, 'CELAVISA', 05 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (921, 71, 'CEPOS E TEIXEIRA', 19 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (922, 71, 'CERDEIRA E MOURA DA SERRA', 20 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (923, 71, 'C�JA E BARRIL DE ALVA', 21 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (924, 71, 'FOLQUES', 09 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (925, 71, 'POMARES', 12 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (926, 71, 'POMBEIRO DA BEIRA', 13 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (927, 71, 'SARZEDO', 15 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (928, 71, 'SECARIAS', 16 ,01, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (929, 71, 'VILA COVA DE ALVA E ANSERIZ', 22 ,01, 06);


/*

06	COIMBRA                       	02	CANTANHEDE                    	15	S. CAETANO	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	01	AN��	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	03	CADIMA	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	20	CANTANHEDE E POCARI�A	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	05	CORDINH�	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	21	COV�ES E CAMARNEIRA	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	07	FEBRES	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	08	MURTEDE	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	09	OURENT�	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	22	PORTUNHOS E OUTIL	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	18	SANGUINHEIRA	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	23	SEPINS E BOLHO	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	14	TOCHA	0710	CANTANHEDE                    
06	COIMBRA                       	02	CANTANHEDE                    	24	VILAMAR E CORTICEIRO DE CIMA	0710	CANTANHEDE                    

*/

INSERT INTO pcj.COMISSAO_PCJ values (72, null, 'CANTANHEDE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 02, 'CANTANHEDE', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (930, 72, 'S. CAETANO', 15 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (931, 72, 'AN��', 01 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (932, 72, 'CADIMA', 03 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (933, 72, 'CANTANHEDE E POCARI�A', 20 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (934, 72, 'CORDINH�', 05 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (935, 72, 'COV�ES E CAMARNEIRA', 21 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (936, 72, 'FEBRES', 07 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (937, 72, 'MURTEDE', 08 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (938, 72, 'OURENT�', 09 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (939, 72, 'PORTUNHOS E OUTIL', 22 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (940, 72, 'SANGUINHEIRA', 18 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (941, 72, 'SEPINS E BOLHO', 23 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (942, 72, 'TOCHA', 14 ,02, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (943, 72, 'VILAMAR E CORTICEIRO DE CIMA', 24 ,02, 06);


/*

06	COIMBRA                       	03	COIMBRA                       	01	ALMALAGUES	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	20	S. JO�O DO CAMPO	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	24	S. SILVESTRE	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	18	SANTO ANTONIO DOS OLIVAIS	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	32	ANTUZEDE E VIL DE MATOS	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	33	ASSAFARGE E ANTANHOL	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	09	BRASFEMES	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	11	CEIRA	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	12	CERNACHE	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	35	EIRAS E S�O PAULO DE FRADES	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	36	SANTA CLARA E CASTELO VIEGAS	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	37	S�O MARTINHO DE �RVORE E LAMAROSA	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	38	S�O MARTINHO DO BISPO E RIBEIRA DE FRADES	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	34	S� NOVA, SANTA CRUZ, ALMEDINA E S�O BARTOLOMEU	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	39	SOUSELAS E BOT�O	3050	COIMBRA-2.                    
06	COIMBRA                       	03	COIMBRA                       	40	TAVEIRO, AMEAL E ARZILA	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	29	TORRES DO MONDEGO	0728	COIMBRA-1.                    
06	COIMBRA                       	03	COIMBRA                       	41	TROUXEMIL E TORRE DE VILELA	3050	COIMBRA-2.                    

*/

INSERT INTO pcj.COMISSAO_PCJ values (73, null, 'COIMBRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 03, 'COIMBRA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (944, 73, 'S. ALMALAGUES', 01 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (945, 73, 'S. JO�O DO CAMPO', 20 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (946,  73, 'S. SILVESTRE', 24 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (947, 73, 'SANTO ANTONIO DOS OLIVAIS', 18 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (948, 73, 'ANTUZEDE E VIL DE MATOS', 32 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (949, 73, 'ASSAFARGE E ANTANHOL', 33 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (950, 73, 'BRASFEMES', 09 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (951, 73, 'CEIRA', 11 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (952, 73, 'CERNACHE', 12 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (953, 73, 'EIRAS E S�O PAULO DE FRADES', 25 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (954, 73, 'SANTA CLARA E CASTELO VIEGAS', 36 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (955, 73, 'SS�O MARTINHO DE �RVORE E LAMAROSA', 37 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (956, 73, 'S�O MARTINHO DO BISPO E RIBEIRA DE FRADES', 38 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (957, 73, 'S� NOVA, SANTA CRUZ, ALMEDINA E S�O BARTOLOMEU', 34 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (958, 73, 'SOUSELAS E BOT�O', 39 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (959, 73, 'TAVEIRO, AMEAL E ARZILA', 40 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (960, 73, 'TORRES DO MONDEGO', 29 ,03, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (961, 73, 'TROUXEMIL E TORRE DE VILELA', 41 ,03, 06);


/*

06	COIMBRA                       	04	CONDEIXA-A-NOVA               	01	ANOBRA	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	11	CONDEIXA-A-VELHA E CONDEIXA-A-NOVA	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	06	EGA	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	07	FURADOURO	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	12	SEBAL E BELIDE	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	13	VILA SECA E BEM DA F�	0736	CONDEIXA-A-NOVA               
06	COIMBRA                       	04	CONDEIXA-A-NOVA               	10	ZAMBUJAL	0736	CONDEIXA-A-NOVA               

*/

INSERT INTO pcj.COMISSAO_PCJ values (74, null, 'CONDEIXA-A-NOVA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 04, 'CONDEIXA-A-NOVA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (962, 74, 'ANOBRA', 01 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (963, 74, 'CONDEIXA-A-VELHA E CONDEIXA-A-NOVA', 11 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (964, 74, 'EGA', 06 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (965, 74, 'FURADOURO', 07 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (966, 74, 'SEBAL E BELIDE', 12 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (967, 74, 'VILA SECA E BEM DA F�', 13 ,04, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (968, 74, 'ZAMBUJAL', 10 ,04, 06);


/*

06	COIMBRA                       	05	FIGUEIRA DA FOZ               	18	MOINHOS DE GANDARA	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	14	S. PEDRO	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	19	ALHADAS	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	02	ALQUEID�O	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	15	BOM SUCESSO	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	20	BUARCOS	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	21	FERREIRA-A-NOVA	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	22	LAVOS	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	07	MAIORCA	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	08	MARINHA DAS ONDAS	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	23	PAI�O	0744	FIGUEIRA DA FOZ-1.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	24	QUIAIOS	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	12	TAVAREDE	3824	FIGUEIRA DA FOZ 2.            
06	COIMBRA                       	05	FIGUEIRA DA FOZ               	13	VILA VERDE	3824	FIGUEIRA DA FOZ 2.            

*/


INSERT INTO pcj.COMISSAO_PCJ values (75, null, 'FIGUEIRA DA FOZ', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 05, 'FIGUEIRA DA FOZ', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (969, 75, 'MOINHOS DE GANDARA', 18 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (970, 75, 'S. PEDRO', 14 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (971, 75, 'ALHADAS', 19 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (972, 75, 'ALQUEID�O', 02 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (973, 75, 'BOM SUCESSO', 15 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (974, 75, 'BUARCOS', 20 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (975, 75, 'FERREIRA-A-NOVA', 21 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (976, 75, 'LAVOS', 22 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (977, 75, 'MAIORCA', 07 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (978, 75, 'MARINHA DAS ONDAS', 08 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (979, 75, 'PAI�O', 23 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (980, 75, 'QUIAIOS', 24 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (981, 75, 'TAVAREDE', 12 ,05, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (982, 75, 'VILA VERDE', 13 ,05, 06);


/*

06	COIMBRA                       	06	GOIS                          	04	GOIS	0752	GOIS                          
06	COIMBRA                       	06	GOIS                          	05	VILA NOVA DO CEIRA	0752	GOIS                          
06	COIMBRA                       	06	GOIS                          	01	ALVARES	0752	GOIS                          
06	COIMBRA                       	06	GOIS                          	06	CADAFAZ E COLMEAL	0752	GOIS                          

*/

INSERT INTO pcj.COMISSAO_PCJ values (76, null, 'G�IS', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 06, 'G�IS', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (983, 76, 'G�IS', 04 ,06, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (984, 76, 'VILA NOVA DO CEIRA', 05 ,06, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (985, 76, 'ALVARES', 01 ,06, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (986, 76, 'CADAFAZ E COLMEAL', 06 ,06, 06);


/*

06	COIMBRA                       	07	LOUS�                         	06	GANDARAS	0760	LOUSA                         
06	COIMBRA                       	07	LOUS�                         	07	FOZ DE AROUCE E CASAL DE ERMIO	0760	LOUSA                         
06	COIMBRA                       	07	LOUS�                         	08	LOUS� E VILARINHO	0760	LOUSA                         
06	COIMBRA                       	07	LOUS�                         	04	SERPINS	0760	LOUSA                         

*/


INSERT INTO pcj.COMISSAO_PCJ values (77, null, 'LOUS�', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 07, 'LOUS�', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (987, 77, 'GANDARAS', 06 ,07, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (988, 77, 'FOZ DE AROUCE E CASAL DE ERMIO', 07 ,07, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (989, 77, 'LOUS� E VILARINHO', 08 ,07, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (990, 77, 'SERPINS', 04 ,07, 06);


/*

06	COIMBRA                       	08	MIRA                          	03	CARAPELHOS	0779	MIRA                          
06	COIMBRA                       	08	MIRA                          	01	MIRA	0779	MIRA                          
06	COIMBRA                       	08	MIRA                          	04	PRAIA DE MIRA	0779	MIRA                          
06	COIMBRA                       	08	MIRA                          	02	SEIXO	0779	MIRA                          

*/

INSERT INTO pcj.COMISSAO_PCJ values (78, null, 'MIRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 08, 'MIRA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (991, 78, 'CARAPELHOS', 03 ,08, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (992, 78, 'MIRA', 01 ,08, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (993, 78, 'PRAIA DE MIRA', 04 ,08, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (994, 78, 'SEIXO', 02 ,08, 06);


/*

06	COIMBRA                       	09	MIRANDA DO CORVO              	01	LAMAS	0787	MIRANDA DO CORVO              
06	COIMBRA                       	09	MIRANDA DO CORVO              	02	MIRANDA DO CORVO	0787	MIRANDA DO CORVO              
06	COIMBRA                       	09	MIRANDA DO CORVO              	06	SEMIDE E RIO VIDE	0787	MIRANDA DO CORVO              
06	COIMBRA                       	09	MIRANDA DO CORVO              	05	VILA NOVA	0787	MIRANDA DO CORVO              

*/

INSERT INTO pcj.COMISSAO_PCJ values (79, null, 'MIRANDA DO CORVO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 09, 'MIRANDA DO CORVO', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (995, 79, 'LAMAS', 01 ,09, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (996, 79, 'MIRANDA DO CORVO', 02 ,09, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (997, 79, 'SEMIDE E RIO VIDE', 06 ,09, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (998, 79, 'VILA NOVA', 05 ,09, 06);


/*

06	COIMBRA                       	10	MONTEMOR-O-VELHO              	11	TENTUGAL	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	15	ABRUNHEIRA, VERRIDE E VILA NOVA DA BARCA	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	02	ARAZEDE	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	03	CARAPINHEIRA	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	14	EREIRA	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	05	LICEIA	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	06	ME�S DO CAMPO	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	16	MONTEMOR-O-VELHO E GAT�ES	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	08	PEREIRA	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	09	SANTO VAR�O	0795	MONTEMOR-O-VELHO              
06	COIMBRA                       	10	MONTEMOR-O-VELHO              	10	SEIXO DE GAT�ES	0795	MONTEMOR-O-VELHO              

*/

INSERT INTO pcj.COMISSAO_PCJ values (80, null, 'MONTEMOR-O-VELHO', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 10, 'MONTEMOR-O-VELHO', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (999, 80, 'TENTUGAL', 11 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1000, 80, 'ABRUNHEIRA', 15 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1001, 80, 'ARAZEDE', 02 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1002, 80, 'CARAPINHEIRA', 03 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1003, 80, 'EREIRA', 14 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1004, 80, 'LICEIA', 05 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1005, 80, 'ME�S DO CAMPO', 06 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1006, 80, 'MONTEMOR-O-VELHO E GAT�ES', 16 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1007, 80, 'PEREIRA', 08 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1008, 80, 'SANTO VAR�O', 09 ,10, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1009, 80, 'SEIXO DE GAT�ES', 10 ,10, 06);


/*

06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	02	ALVOCO DAS VARZEAS	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	03	AVO	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	15	S. GI�O	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	01	ALDEIA DAS DEZ	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	04	BOBADELA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	22	ERVEDAL E VILA FRANCA DA BEIRA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	06	LAGARES	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	23	LAGOS DA BEIRA E LAJEOSA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	09	LOUROSA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	10	MERUGE	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	11	NOGUEIRA DO CRAVO	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	24	OLIVEIRA DO HOSPITAL E S�O PAIO DE GRAMA�OS	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	25	PENALVA DE ALVA E S�O SEBASTI�O DA FEIRA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	26	SANTA OVAIA E VILA POUCA DA BEIRA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	18	SEIXO DA BEIRA	0809	OLIVEIRA DO HOSPITAL          
06	COIMBRA                       	11	OLIVEIRA DO HOSPITAL          	19	TRAVANCA DE LAGOS	0809	OLIVEIRA DO HOSPITAL          

*/

INSERT INTO pcj.COMISSAO_PCJ values (81, null, 'OLIVEIRA DO HOSPITAL', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 11, 'OLIVEIRA DO HOSPITAL', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1010, 81, 'ALVOCO DAS VARZEAS', 02 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1011, 81, 'AVO', 03 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1012, 81, 'S. GI�O', 15 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1013, 81, 'ALDEIA DAS DEZ', 01 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1014, 81, 'BOBADELA', 04 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1015, 81, 'ERVEDAL E VILA FRANCA DA BEIRA', 22 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1016, 81, 'LAGARES', 06 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1017, 81, 'LAGOS DA BEIRA E LAJEOSA', 23 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1018, 81, 'LOUROSA', 09 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1019, 81, 'MERUGE', 10 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1020, 81, 'NOGUEIRA DO CRAVO', 11 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1021, 81, 'OLIVEIRA DO HOSPITAL E S�O PAIO DE GRAMA�OS', 24 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1022, 81, 'PENALVA DE ALVA E S�O SEBASTI�O DA FEIRA', 25 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1023, 81, 'SANTA OVAIA E VILA POUCA DA BEIRA', 26 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1024, 81, 'SEIXO DA BEIRA', 18 ,11, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1025, 81, 'TRAVANCA DE LAGOS', 19 ,11, 06);


/*

06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	02	DORNELAS DO ZEZERE	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	01	CABRIL	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	11	FAJ�O-VIDUAL	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	04	JANEIRO DE BAIXO	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	06	PAMPILHOSA DA SERRA	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	07	PESSEGUEIRO	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	12	PORTELA DO FOJO-MACHIO	0817	PAMPILHOSA DA SERRA           
06	COIMBRA                       	12	PAMPILHOSA DA SERRA           	09	UNHAIS-O-VELHO	0817	PAMPILHOSA DA SERRA           

*/


INSERT INTO pcj.COMISSAO_PCJ values (82, null, 'PAMPILHOSA DA SERRA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 12, 'PAMPILHOSA DA SERRA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1026, 82, 'DORNELAS DO ZEZERE', 02 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1027, 82, 'CABRIL', 01 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1028, 82, 'FAJ�O-VIDUAL', 11 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1029, 82, 'JANEIRO DE BAIXO', 04 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1030, 82, 'PAMPILHOSA DA SERRA', 06 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1031, 82, 'PESSEGUEIRO', 07 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1032, 82, 'PORTELA DO FOJO-MACHIO', 12 ,12, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1033, 82, 'UNHAIS-O-VELHO', 09 ,12, 06);


/*

06	COIMBRA                       	13	PENACOVA                      	01	CARVALHO	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	02	FIGUEIRA DE LORV�O	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	12	FRI�MES E PARADELA	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	04	LORV�O	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	13	OLIVEIRA DO MONDEGO E TRAVANCA DO MONDEGO	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	07	PENACOVA	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	14	S�O PEDRO DE ALVA E S�O PAIO DE MONDEGO	0825	PENACOVA                      
06	COIMBRA                       	13	PENACOVA                      	10	SAZES DO LORV�O	0825	PENACOVA                      

*/


INSERT INTO pcj.COMISSAO_PCJ values (83, null, 'PENACOVA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 13, 'PENACOVA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1034, 83, 'CARVALHO', 01 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1035, 83, 'FIGUEIRA DE LORV�O', 02 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1036, 83, 'FRI�MES E PARADELA', 12 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1037, 83, 'LORV�O', 04 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1038, 83, 'OLIVEIRA DO MONDEGO E TRAVANCA DO MONDEGO', 13 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1039, 83, 'PENACOVA', 07 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1040, 83, 'S�O PEDRO DE ALVA E S�O PAIO DE MONDEGO', 14 ,13, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1041, 83, 'SAZES DO LORV�O', 10 ,13, 06);


/*

06	COIMBRA                       	14	PENELA                        	01	CUMEEIRA	0833	PENELA                        
06	COIMBRA                       	14	PENELA                        	02	ESPINHAL	0833	PENELA                        
06	COIMBRA                       	14	PENELA                        	03	PODENTES	0833	PENELA                        
06	COIMBRA                       	14	PENELA                        	07	S�O MIGUEL, SANTA EUF�MIA E RABA�AL	0833	PENELA                        

*/

INSERT INTO pcj.COMISSAO_PCJ values (84, null, 'PENELA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 14, 'PENELA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1042, 84, 'CUMEEIRA', 01 ,14, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1043, 84, 'ESPINHAL', 02 ,14, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1044, 84, 'PODENTES', 03 ,14, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1045, 84, 'S�O MIGUEL, SANTA EUF�MIA E RABA�AL', 07 ,14, 06);


/*

06	COIMBRA                       	15	SOURE                         	04	FIGUEIRO DO CAMPO	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	10	TAPEUS	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	01	ALFARELOS	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	13	DEGRACIAS E POMBALINHO	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	14	GESTEIRA E BRUNH�S	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	06	GRANJA DO ULMEIRO	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	08	SAMUEL	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	09	SOURE	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	11	VILA NOVA DE AN�OS	0850	SOURE                         
06	COIMBRA                       	15	SOURE                         	12	VINHA DA RAINHA	0850	SOURE                         

*/

INSERT INTO pcj.COMISSAO_PCJ values (85, null, 'SOURE', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 15, 'SOURE', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1046, 85, 'FIGUEIRO DO CAMPO', 04 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1047, 85, 'TAPEUS', 10 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1048, 85, 'ALFARELOS', 01 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1049, 85, 'DEGRACIAS E POMBALINHO', 13 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1050, 85, 'GESTEIRA E BRUNH�S', 14 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1051, 85, 'GRANJA DO ULMEIRO', 06 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1052, 85, 'SAMUEL', 08 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1053, 85, 'SOURE', 09 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1054, 85, 'VILA NOVA DE AN�OS', 11 ,15, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1055, 85, 'VINHA DA RAINHA', 12 ,15, 06);


/*

06	COIMBRA                       	16	TABUA                         	11	POVOA DE MID�ES	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	12	S. JO�O DA BOA VISTA	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	14	TABUA	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	16	�ZERE E COVELO	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	02	CANDOSA	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	03	CARAPINHA	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	17	COVAS E VILA NOVA DE OLIVEIRINHA	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	18	ESPARIZ E SINDE	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	08	MID�ES	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	09	MOURONHO	0868	TABUA                         
06	COIMBRA                       	16	TABUA                         	19	PINHEIRO DE COJA E MEDA DE MOUROS	0868	TABUA                         

*/


INSERT INTO pcj.COMISSAO_PCJ values (86, null, 'T�BUA', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 16, 'T�BUA', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1056, 86, 'POVOA DE MID�ES', 11 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1057, 86, 'S. JO�O DA BOA VISTA', 12 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1058, 86, 'T�BUA', 14 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1059, 86, '�ZERE E COVELO', 16 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1060, 86, 'CANDOSA', 02 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1061, 86, 'CARAPINHA', 03 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1062, 86, 'COVAS E VILA NOVA DE OLIVEIRINHA', 17 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1063, 86, 'ESPARIZ E SINDE', 18 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1064, 86, 'MID�ES', 08 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1065, 86, 'MOURONHO', 09 ,16, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1066, 86, 'PINHEIRO DE COJA E MEDA DE MOUROS', 19 ,16, 06);


/*

06	COIMBRA                       	17	VILA NOVA DE POIARES          	03	POIARES (SANTO ANDRE)	0841	VILA NOVA DE POIARES          
06	COIMBRA                       	17	VILA NOVA DE POIARES          	04	S. MIGUEL DE POIARES	0841	VILA NOVA DE POIARES          
06	COIMBRA                       	17	VILA NOVA DE POIARES          	01	ARRIFANA	0841	VILA NOVA DE POIARES          
06	COIMBRA                       	17	VILA NOVA DE POIARES          	02	LAVEGADAS	0841	VILA NOVA DE POIARES          

*/

INSERT INTO pcj.COMISSAO_PCJ values (87, null, 'VILA NOVA DE POIARES', 'email', null, null, 121231234, 123123123, 
                                    'morada arteria', 'morada localidade', '0000-000', 'morada freguesia', 'morada concelho', 'morada distrito', 0, null, 0, null,
                                    06, 17, 'VILA NOVA DE POIARES', 'COIMBRA');

INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1067, 87, 'POIARES (SANTO ANDRE)', 03 ,17, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1068, 87, 'S. MIGUEL DE POIARES', 04 ,17, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1069, 87, 'ARRIFANA', 01 ,17, 06);
INSERT INTO pcj.FREGUESIA_CORRESPONDENTE VALUES (1070, 87, 'LAVEGADAS', 02 ,17, 06);


commit;
