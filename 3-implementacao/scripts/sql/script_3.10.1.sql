alter session set current_schema = PCJ;
whenever sqlerror exit sql.sqlcode rollback;
SELECT * FROM whoami;
SET timing ON

set define off; --devido aos e comercial-- Quando existe aqui tem de existir --set define on; no final
Declare
BEGIN

update PCJ.documento set motivo_invalidez = 'DOCUMENTO_INCORRETO' where motivo_invalidez = 'Documento Incorreto';
update PCJ.documento set motivo_invalidez = 'DOCUMENTO_ILEGIVEL' where motivo_invalidez like 'Documento Il%';

END;
/
commit;
set define on;