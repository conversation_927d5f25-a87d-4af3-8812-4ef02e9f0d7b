package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.InfraestruturaDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.PerfilElementoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.SalaReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.SalaReuniao;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:58-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class SalaReuniaoMapperImpl implements SalaReuniaoMapper {

    @Override
    public SalaReuniao toEntidade(InfraestruturaDTO infra) {
        if ( infra == null ) {
            return null;
        }

        SalaReuniao salaReuniao = new SalaReuniao();

        salaReuniao.setId( infra.getId() );
        salaReuniao.setNome( infra.getNome() );
        salaReuniao.setCapacidade( infra.getCapacidade() );
        salaReuniao.setObservacao( infra.getObservacao() );
        salaReuniao.setTipoSala( infra.getTipoSala() );
        salaReuniao.setStatusFuncionamento( infra.getStatusFuncionamento() );
        salaReuniao.setSalaAcustica( infra.getSalaAcustica() );
        salaReuniao.setEquipamentoAudio( infra.getEquipamentoAudio() );
        salaReuniao.setEspelhoUnidirecional( infra.getEspelhoUnidirecional() );
        salaReuniao.setRecursosAudicao( infra.getRecursosAudicao() );
        salaReuniao.setLudicoPedagogico( infra.getLudicoPedagogico() );
        salaReuniao.setUtilizacaoExterna( infra.getUtilizacaoExterna() );
        salaReuniao.setEquipamentoVideo( infra.getEquipamentoVideo() );
        salaReuniao.setSalaSegura( infra.getSalaSegura() );
        salaReuniao.setSalaAcolhedora( infra.getSalaAcolhedora() );
        salaReuniao.setComportaTodasIdades( infra.getComportaTodasIdades() );
        salaReuniao.setCapacidadeImpressao( infra.getCapacidadeImpressao() );

        return salaReuniao;
    }

    @Override
    public List<InfraestruturaDTO> toDTOs(List<SalaReuniao> salas) {
        if ( salas == null ) {
            return null;
        }

        List<InfraestruturaDTO> list = new ArrayList<InfraestruturaDTO>( salas.size() );
        for ( SalaReuniao salaReuniao : salas ) {
            list.add( salaReuniaoToInfraestruturaDTO( salaReuniao ) );
        }

        return list;
    }

    @Override
    public List<SalaReuniaoDTO> toSalaDTOs(List<SalaReuniao> salas) {
        if ( salas == null ) {
            return null;
        }

        List<SalaReuniaoDTO> list = new ArrayList<SalaReuniaoDTO>( salas.size() );
        for ( SalaReuniao salaReuniao : salas ) {
            list.add( salaReuniaoToSalaReuniaoDTO( salaReuniao ) );
        }

        return list;
    }

    protected InfraestruturaDTO salaReuniaoToInfraestruturaDTO(SalaReuniao salaReuniao) {
        if ( salaReuniao == null ) {
            return null;
        }

        InfraestruturaDTO infraestruturaDTO = new InfraestruturaDTO();

        infraestruturaDTO.setNome( salaReuniao.getNome() );
        infraestruturaDTO.setCapacidade( salaReuniao.getCapacidade() );
        if ( salaReuniao.getStatusFuncionamento() != null ) {
            infraestruturaDTO.setStatusFuncionamento( salaReuniao.getStatusFuncionamento() );
        }
        infraestruturaDTO.setObservacao( salaReuniao.getObservacao() );
        infraestruturaDTO.setTipoSala( salaReuniao.getTipoSala() );
        infraestruturaDTO.setId( salaReuniao.getId() );
        infraestruturaDTO.setSalaAcustica( salaReuniao.getSalaAcustica() );
        infraestruturaDTO.setEquipamentoAudio( salaReuniao.getEquipamentoAudio() );
        infraestruturaDTO.setEspelhoUnidirecional( salaReuniao.getEspelhoUnidirecional() );
        infraestruturaDTO.setRecursosAudicao( salaReuniao.getRecursosAudicao() );
        infraestruturaDTO.setLudicoPedagogico( salaReuniao.getLudicoPedagogico() );
        infraestruturaDTO.setUtilizacaoExterna( salaReuniao.getUtilizacaoExterna() );
        infraestruturaDTO.setEquipamentoVideo( salaReuniao.getEquipamentoVideo() );
        infraestruturaDTO.setSalaSegura( salaReuniao.getSalaSegura() );
        infraestruturaDTO.setSalaAcolhedora( salaReuniao.getSalaAcolhedora() );
        infraestruturaDTO.setComportaTodasIdades( salaReuniao.getComportaTodasIdades() );
        infraestruturaDTO.setCapacidadeImpressao( salaReuniao.getCapacidadeImpressao() );

        return infraestruturaDTO;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }

    protected PerfilElementoDTO perfilElementoToPerfilElementoDTO(PerfilElemento perfilElemento) {
        if ( perfilElemento == null ) {
            return null;
        }

        PerfilElementoDTO perfilElementoDTO = new PerfilElementoDTO();

        perfilElementoDTO.setId( perfilElemento.getId() );
        perfilElementoDTO.setInicioVigencia( perfilElemento.getInicioVigencia() );
        perfilElementoDTO.setFimVigencia( perfilElemento.getFimVigencia() );
        perfilElementoDTO.setPerfil( perfilElemento.getPerfil() );

        return perfilElementoDTO;
    }

    protected List<PerfilElementoDTO> perfilElementoListToPerfilElementoDTOList(List<PerfilElemento> list) {
        if ( list == null ) {
            return null;
        }

        List<PerfilElementoDTO> list1 = new ArrayList<PerfilElementoDTO>( list.size() );
        for ( PerfilElemento perfilElemento : list ) {
            list1.add( perfilElementoToPerfilElementoDTO( perfilElemento ) );
        }

        return list1;
    }

    protected ElementoDTO elementoToElementoDTO(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }

        ElementoDTO elementoDTO = new ElementoDTO();

        elementoDTO.setId( elemento.getId() );
        elementoDTO.setEmail( elemento.getEmail() );
        elementoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( elemento.getComissao() ) );
        elementoDTO.setTipoElemento( elemento.getTipoElemento() );
        if ( elemento.getHoraSemanal() != null ) {
            elementoDTO.setHoraSemanal( elemento.getHoraSemanal() );
        }
        if ( elemento.getHoraMensal() != null ) {
            elementoDTO.setHoraMensal( elemento.getHoraMensal() );
        }
        elementoDTO.setValenciaTecnica( elemento.getValenciaTecnica() );
        elementoDTO.setOutraValencia( elemento.getOutraValencia() );
        elementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isApoioCn() != null ) {
            elementoDTO.setApoioCn( elemento.isApoioCn() );
        }
        elementoDTO.setDataInicioVigencia( elemento.getDataInicioVigencia() );
        elementoDTO.setDataFimVigencia( elemento.getDataFimVigencia() );
        elementoDTO.setDataPrimeiraRenovacaoVigencia( elemento.getDataPrimeiraRenovacaoVigencia() );
        elementoDTO.setDataSegundaRenovacaoVigencia( elemento.getDataSegundaRenovacaoVigencia() );
        elementoDTO.setCargoMandato( elemento.getCargoMandato() );
        elementoDTO.setDataInicioMandato( elemento.getDataInicioMandato() );
        elementoDTO.setDataRenovacaoMandato( elemento.getDataRenovacaoMandato() );
        elementoDTO.setDataFimMandato( elemento.getDataFimMandato() );
        if ( elemento.isAtivo() != null ) {
            elementoDTO.setAtivo( elemento.isAtivo() );
        }
        elementoDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( elemento.getUtilizadorPCJ() ) );
        elementoDTO.setNumeroCartao( elemento.getNumeroCartao() );
        elementoDTO.setPerfisElementos( perfilElementoListToPerfilElementoDTOList( elemento.getPerfisElementos() ) );

        return elementoDTO;
    }

    protected ReuniaoPCJDTO reuniaoPCJToReuniaoPCJDTO(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoPCJDTO reuniaoPCJDTO = new ReuniaoPCJDTO();

        reuniaoPCJDTO.setId( reuniaoPCJ.getId() );
        reuniaoPCJDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoPCJDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoPCJDTO.setCodigoModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoPCJDTO.setCodigoTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoPCJDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoPCJDTO.setDataFim( reuniaoPCJ.getDataFim() );
        reuniaoPCJDTO.setHoraInicio( reuniaoPCJ.getHoraInicio() );
        reuniaoPCJDTO.setHoraFim( reuniaoPCJ.getHoraFim() );
        reuniaoPCJDTO.setElemento( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoPCJDTO.setLocalReuniao( reuniaoPCJ.getLocalReuniao() );
        reuniaoPCJDTO.setAtivo( reuniaoPCJ.isAtivo() );

        return reuniaoPCJDTO;
    }

    protected Set<ReuniaoPCJDTO> reuniaoPCJSetToReuniaoPCJDTOSet(Set<ReuniaoPCJ> set) {
        if ( set == null ) {
            return null;
        }

        Set<ReuniaoPCJDTO> set1 = new HashSet<ReuniaoPCJDTO>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( ReuniaoPCJ reuniaoPCJ : set ) {
            set1.add( reuniaoPCJToReuniaoPCJDTO( reuniaoPCJ ) );
        }

        return set1;
    }

    protected SalaReuniaoDTO salaReuniaoToSalaReuniaoDTO(SalaReuniao salaReuniao) {
        if ( salaReuniao == null ) {
            return null;
        }

        SalaReuniaoDTO salaReuniaoDTO = new SalaReuniaoDTO();

        salaReuniaoDTO.setNome( salaReuniao.getNome() );
        salaReuniaoDTO.setCapacidade( salaReuniao.getCapacidade() );
        salaReuniaoDTO.setObservacao( salaReuniao.getObservacao() );
        salaReuniaoDTO.setTipoSala( salaReuniao.getTipoSala() );
        salaReuniaoDTO.setStatusFuncionamento( salaReuniao.getStatusFuncionamento() );
        salaReuniaoDTO.setSalaAcustica( salaReuniao.getSalaAcustica() );
        salaReuniaoDTO.setEquipamentoAudio( salaReuniao.getEquipamentoAudio() );
        salaReuniaoDTO.setEspelhoUnidirecional( salaReuniao.getEspelhoUnidirecional() );
        salaReuniaoDTO.setRecursosAudicao( salaReuniao.getRecursosAudicao() );
        salaReuniaoDTO.setLudicoPedagogico( salaReuniao.getLudicoPedagogico() );
        salaReuniaoDTO.setUtilizacaoExterna( salaReuniao.getUtilizacaoExterna() );
        salaReuniaoDTO.setEquipamentoVideo( salaReuniao.getEquipamentoVideo() );
        salaReuniaoDTO.setSalaSegura( salaReuniao.getSalaSegura() );
        salaReuniaoDTO.setSalaAcolhedora( salaReuniao.getSalaAcolhedora() );
        salaReuniaoDTO.setComportaTodasIdades( salaReuniao.getComportaTodasIdades() );
        salaReuniaoDTO.setCapacidadeImpressao( salaReuniao.getCapacidadeImpressao() );
        salaReuniaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( salaReuniao.getComissao() ) );
        salaReuniaoDTO.setId( salaReuniao.getId() );
        salaReuniaoDTO.setReunioes( reuniaoPCJSetToReuniaoPCJDTOSet( salaReuniao.getReunioes() ) );

        return salaReuniaoDTO;
    }
}
