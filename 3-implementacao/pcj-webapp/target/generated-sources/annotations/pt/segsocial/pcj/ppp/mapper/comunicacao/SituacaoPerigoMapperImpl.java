package pt.segsocial.pcj.ppp.mapper.comunicacao;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.comunicacao.SituacaoPerigoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.SituacaoPerigoPpp;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class SituacaoPerigoMapperImpl implements SituacaoPerigoMapper {

    @Override
    public SituacaoPerigoDTO toDTO(SituacaoPerigoPpp situacaoPerigoPpp) {
        if ( situacaoPerigoPpp == null ) {
            return null;
        }

        SituacaoPerigoDTO situacaoPerigoDTO = new SituacaoPerigoDTO();

        situacaoPerigoDTO.setCodigoSituacaoAtribuida( situacaoPerigoPpp.getCodigoSituacaoAtribuida() );
        situacaoPerigoDTO.setCodigoSituacaoSinalizada( situacaoPerigoPpp.getCodigoSituacaoSinalizada() );
        situacaoPerigoDTO.setCodigoTipologiaPerigo( situacaoPerigoPpp.getCodigoTipologiaPerigo() );
        situacaoPerigoDTO.setDescricaoFactos( situacaoPerigoPpp.getDescricaoFactos() );
        situacaoPerigoDTO.setNuipc( situacaoPerigoPpp.getNuipc() );
        situacaoPerigoDTO.setOutraSituacaoAtribuida( situacaoPerigoPpp.getOutraSituacaoAtribuida() );
        situacaoPerigoDTO.setMorada( situacaoPerigoPpp.getMorada() );
        situacaoPerigoDTO.setIntervencaoUrgente( situacaoPerigoPpp.getIntervencaoUrgente() );
        situacaoPerigoDTO.setId( situacaoPerigoPpp.getId() );

        return situacaoPerigoDTO;
    }

    @Override
    public SituacaoPerigoPpp toEntity(SituacaoPerigoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        SituacaoPerigoPpp situacaoPerigoPpp = new SituacaoPerigoPpp();

        situacaoPerigoPpp.setId( dto.getId() );
        situacaoPerigoPpp.setCodigoTipologiaPerigo( dto.getCodigoTipologiaPerigo() );
        situacaoPerigoPpp.setCodigoSituacaoSinalizada( dto.getCodigoSituacaoSinalizada() );
        situacaoPerigoPpp.setCodigoSituacaoAtribuida( dto.getCodigoSituacaoAtribuida() );
        situacaoPerigoPpp.setOutraSituacaoAtribuida( dto.getOutraSituacaoAtribuida() );
        situacaoPerigoPpp.setNuipc( dto.getNuipc() );
        situacaoPerigoPpp.setDescricaoFactos( dto.getDescricaoFactos() );
        situacaoPerigoPpp.setIntervencaoUrgente( dto.getIntervencaoUrgente() );
        situacaoPerigoPpp.setMorada( dto.getMorada() );

        return situacaoPerigoPpp;
    }

    @Override
    public List<SituacaoPerigoDTO> toDTOList(List<SituacaoPerigoPpp> situacaoPerigoPpps) {
        if ( situacaoPerigoPpps == null ) {
            return null;
        }

        List<SituacaoPerigoDTO> list = new ArrayList<SituacaoPerigoDTO>( situacaoPerigoPpps.size() );
        for ( SituacaoPerigoPpp situacaoPerigoPpp : situacaoPerigoPpps ) {
            list.add( toDTO( situacaoPerigoPpp ) );
        }

        return list;
    }

    @Override
    public List<SituacaoPerigoPpp> toEntities(List<SituacaoPerigoDTO> situacaoPerigoDTOS) {
        if ( situacaoPerigoDTOS == null ) {
            return null;
        }

        List<SituacaoPerigoPpp> list = new ArrayList<SituacaoPerigoPpp>( situacaoPerigoDTOS.size() );
        for ( SituacaoPerigoDTO situacaoPerigoDTO : situacaoPerigoDTOS ) {
            list.add( toEntity( situacaoPerigoDTO ) );
        }

        return list;
    }
}
