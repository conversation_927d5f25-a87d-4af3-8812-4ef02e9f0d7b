package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ConvidadoFolhaIdDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.ConvidadoFolhaId;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ConvidadoFolhaIdMapperImpl implements ConvidadoFolhaIdMapper {

    @Override
    public ConvidadoFolhaIdDTO toDTO(ConvidadoFolhaId convidadoFolhaId) {
        if ( convidadoFolhaId == null ) {
            return null;
        }

        ConvidadoFolhaIdDTO convidadoFolhaIdDTO = new ConvidadoFolhaIdDTO();

        return convidadoFolhaIdDTO;
    }

    @Override
    public ConvidadoFolhaId toEntity(ConvidadoFolhaIdDTO convidadoFolhaIdDTO) {
        if ( convidadoFolhaIdDTO == null ) {
            return null;
        }

        ConvidadoFolhaId convidadoFolhaId = new ConvidadoFolhaId();

        return convidadoFolhaId;
    }
}
