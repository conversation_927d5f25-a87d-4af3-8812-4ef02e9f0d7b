package pt.segsocial.pcj.ppp.mapper.comunicacao;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoCriancaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaDTO;
import pt.segsocial.pcj.ppp.enums.RececaoExpedienteEnum;
import pt.segsocial.pcj.ppp.enums.TipoParticipanteEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.ComunicacaoCrianca;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Crianca;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ComunicacaoCriancaMapperImpl implements ComunicacaoCriancaMapper {

    @Override
    public ComunicacaoCriancaDTO toDTO(ComunicacaoCrianca comunicacaoCrianca) {
        if ( comunicacaoCrianca == null ) {
            return null;
        }

        ComunicacaoCriancaDTO comunicacaoCriancaDTO = new ComunicacaoCriancaDTO();

        comunicacaoCriancaDTO.setCodigoIdade( comunicacaoCrianca.getCodigoIdade() );
        comunicacaoCriancaDTO.setCodigoInformacaoIdade( comunicacaoCrianca.getCodigoInformacaoIdade() );
        comunicacaoCriancaDTO.setComunicacao( comunicacaoToComunicacaoDTO( comunicacaoCrianca.getComunicacao() ) );
        comunicacaoCriancaDTO.setCrianca( criancaToCriancaDTO( comunicacaoCrianca.getCrianca() ) );
        comunicacaoCriancaDTO.setDataNascimento( comunicacaoCrianca.getDataNascimento() );
        comunicacaoCriancaDTO.setEstabelecimentoEnsino( comunicacaoCrianca.getEstabelecimentoEnsino() );
        comunicacaoCriancaDTO.setId( comunicacaoCrianca.getId() );
        comunicacaoCriancaDTO.setMorada( moradaToMoradaDTO( comunicacaoCrianca.getMorada() ) );
        comunicacaoCriancaDTO.setNome( comunicacaoCrianca.getNome() );
        comunicacaoCriancaDTO.setMoradaConhecida( comunicacaoCrianca.isMoradaConhecida() );
        comunicacaoCriancaDTO.setCodigoGenero( comunicacaoCrianca.getCodigoGenero() );

        return comunicacaoCriancaDTO;
    }

    @Override
    public ComunicacaoCrianca toEntity(ComunicacaoCriancaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ComunicacaoCrianca comunicacaoCrianca = new ComunicacaoCrianca();

        comunicacaoCrianca.setId( dto.getId() );
        comunicacaoCrianca.setComunicacao( comunicacaoDTOToComunicacao( dto.getComunicacao() ) );
        comunicacaoCrianca.setMorada( moradaDTOToMorada( dto.getMorada() ) );
        comunicacaoCrianca.setCrianca( criancaDTOToCrianca( dto.getCrianca() ) );
        comunicacaoCrianca.setNome( dto.getNome() );
        comunicacaoCrianca.setDataNascimento( dto.getDataNascimento() );
        comunicacaoCrianca.setCodigoInformacaoIdade( dto.getCodigoInformacaoIdade() );
        comunicacaoCrianca.setCodigoIdade( dto.getCodigoIdade() );
        comunicacaoCrianca.setCodigoGenero( dto.getCodigoGenero() );
        comunicacaoCrianca.setEstabelecimentoEnsino( dto.getEstabelecimentoEnsino() );
        comunicacaoCrianca.setMoradaConhecida( dto.isMoradaConhecida() );

        return comunicacaoCrianca;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected ComunicacaoDTO comunicacaoToComunicacaoDTO(Comunicacao comunicacao) {
        if ( comunicacao == null ) {
            return null;
        }

        ComunicacaoDTO comunicacaoDTO = new ComunicacaoDTO();

        comunicacaoDTO.setCodigoEstado( comunicacao.getCodigoEstado() );
        comunicacaoDTO.setCodigoEstadoAnalise( comunicacao.getCodigoEstadoAnalise() );
        comunicacaoDTO.setCodigoModalidadeContato( comunicacao.getCodigoModalidadeContato() );
        comunicacaoDTO.setCodigoParticipanteParentesco( comunicacao.getCodigoParticipanteParentesco() );
        comunicacaoDTO.setCodigoParticipanteRelacao( comunicacao.getCodigoParticipanteRelacao() );
        comunicacaoDTO.setDataRececao( comunicacao.getDataRececao() );
        comunicacaoDTO.setDataRegisto( comunicacao.getDataRegisto() );
        comunicacaoDTO.setDeliberado( comunicacao.getDeliberado() );
        comunicacaoDTO.setExpedienteAssociado( comunicacao.getExpedienteAssociado() );
        comunicacaoDTO.setId( comunicacao.getId() );
        comunicacaoDTO.setOutrasObservacoes( comunicacao.getOutrasObservacoes() );
        comunicacaoDTO.setParticipanteAnonimo( comunicacao.getParticipanteAnonimo() );
        comunicacaoDTO.setParticipanteCrianca( comunicacao.getParticipanteCrianca() );
        comunicacaoDTO.setParticipanteFamiliar( comunicacao.getParticipanteFamiliar() );
        if ( comunicacao.getRececaoExpediente() != null ) {
            comunicacaoDTO.setRececaoExpediente( comunicacao.getRececaoExpediente().name() );
        }
        if ( comunicacao.getTipoParticipante() != null ) {
            comunicacaoDTO.setTipoParticipante( comunicacao.getTipoParticipante().name() );
        }
        comunicacaoDTO.setNumeroComunicacao( comunicacao.getNumeroComunicacao() );
        comunicacaoDTO.setUrgente( comunicacao.getUrgente() );
        comunicacaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( comunicacao.getComissao() ) );

        return comunicacaoDTO;
    }

    protected CriancaDTO criancaToCriancaDTO(Crianca crianca) {
        if ( crianca == null ) {
            return null;
        }

        CriancaDTO criancaDTO = new CriancaDTO();

        criancaDTO.setCodigoIdade( crianca.getCodigoIdade() );
        criancaDTO.setCodigoInformacaoIdade( crianca.getCodigoInformacaoIdade() );
        criancaDTO.setDataNascimento( crianca.getDataNascimento() );
        criancaDTO.setNome( crianca.getNome() );
        criancaDTO.setId( crianca.getId() );
        criancaDTO.setCodigoGenero( crianca.getCodigoGenero() );

        return criancaDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected Comunicacao comunicacaoDTOToComunicacao(ComunicacaoDTO comunicacaoDTO) {
        if ( comunicacaoDTO == null ) {
            return null;
        }

        Comunicacao comunicacao = new Comunicacao();

        comunicacao.setId( comunicacaoDTO.getId() );
        comunicacao.setNumeroComunicacao( comunicacaoDTO.getNumeroComunicacao() );
        comunicacao.setComissao( comissaoPCJDTOToComissaoPCJppp( comunicacaoDTO.getComissao() ) );
        comunicacao.setCodigoModalidadeContato( comunicacaoDTO.getCodigoModalidadeContato() );
        comunicacao.setExpedienteAssociado( comunicacaoDTO.getExpedienteAssociado() );
        if ( comunicacaoDTO.getTipoParticipante() != null ) {
            comunicacao.setTipoParticipante( Enum.valueOf( TipoParticipanteEnum.class, comunicacaoDTO.getTipoParticipante() ) );
        }
        comunicacao.setParticipanteAnonimo( comunicacaoDTO.getParticipanteAnonimo() );
        comunicacao.setParticipanteCrianca( comunicacaoDTO.getParticipanteCrianca() );
        comunicacao.setParticipanteFamiliar( comunicacaoDTO.getParticipanteFamiliar() );
        comunicacao.setCodigoParticipanteParentesco( comunicacaoDTO.getCodigoParticipanteParentesco() );
        comunicacao.setCodigoParticipanteRelacao( comunicacaoDTO.getCodigoParticipanteRelacao() );
        comunicacao.setOutrasObservacoes( comunicacaoDTO.getOutrasObservacoes() );
        comunicacao.setDataRececao( comunicacaoDTO.getDataRececao() );
        comunicacao.setDataRegisto( comunicacaoDTO.getDataRegisto() );
        if ( comunicacaoDTO.getRececaoExpediente() != null ) {
            comunicacao.setRececaoExpediente( Enum.valueOf( RececaoExpedienteEnum.class, comunicacaoDTO.getRececaoExpediente() ) );
        }
        comunicacao.setCodigoEstado( comunicacaoDTO.getCodigoEstado() );
        comunicacao.setDeliberado( comunicacaoDTO.getDeliberado() );
        comunicacao.setCodigoEstadoAnalise( comunicacaoDTO.getCodigoEstadoAnalise() );
        comunicacao.setUrgente( comunicacaoDTO.getUrgente() );

        return comunicacao;
    }

    protected Crianca criancaDTOToCrianca(CriancaDTO criancaDTO) {
        if ( criancaDTO == null ) {
            return null;
        }

        Crianca crianca = new Crianca();

        crianca.setId( criancaDTO.getId() );
        crianca.setNome( criancaDTO.getNome() );
        crianca.setDataNascimento( criancaDTO.getDataNascimento() );
        crianca.setCodigoInformacaoIdade( criancaDTO.getCodigoInformacaoIdade() );
        crianca.setCodigoIdade( criancaDTO.getCodigoIdade() );
        crianca.setCodigoGenero( criancaDTO.getCodigoGenero() );

        return crianca;
    }
}
