package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DetalheOutrosDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheOutros;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:49-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DetalheOutrosMapperImpl implements DetalheOutrosMapper {

    @Override
    public DetalheOutrosDTO toDTO(DetalheOutros detalheOutros) {
        if ( detalheOutros == null ) {
            return null;
        }

        DetalheOutrosDTO detalheOutrosDTO = new DetalheOutrosDTO();

        if ( detalheOutros.getId() != null ) {
            detalheOutrosDTO.setId( detalheOutros.getId() );
        }
        detalheOutrosDTO.setCodMotorista( detalheOutros.getCodMotorista() );
        detalheOutrosDTO.setFundoManeioMovimentacao( detalheOutros.getFundoManeioMovimentacao() );
        detalheOutrosDTO.setViatura( detalheOutros.getViatura() );
        detalheOutrosDTO.setSeguroComissario( detalheOutros.getSeguroComissario() );
        detalheOutrosDTO.setViaturaCaracterizada( detalheOutros.getViaturaCaracterizada() );
        detalheOutrosDTO.setFundoManeioReposicao( detalheOutros.getFundoManeioReposicao() );
        detalheOutrosDTO.setFundoManeioDisponivel( detalheOutros.getFundoManeioDisponivel() );
        detalheOutrosDTO.setSeguroSemEntidade( detalheOutros.getSeguroSemEntidade() );

        return detalheOutrosDTO;
    }

    @Override
    public DetalheOutros toEntidade(DetalheOutrosDTO detalheOutrosDTO) {
        if ( detalheOutrosDTO == null ) {
            return null;
        }

        DetalheOutros detalheOutros = new DetalheOutros();

        detalheOutros.setId( detalheOutrosDTO.getId() );
        detalheOutros.setCodMotorista( detalheOutrosDTO.getCodMotorista() );
        detalheOutros.setFundoManeioMovimentacao( detalheOutrosDTO.getFundoManeioMovimentacao() );
        detalheOutros.setViatura( detalheOutrosDTO.getViatura() );
        detalheOutros.setSeguroComissario( detalheOutrosDTO.getSeguroComissario() );
        detalheOutros.setViaturaCaracterizada( detalheOutrosDTO.getViaturaCaracterizada() );
        detalheOutros.setFundoManeioReposicao( detalheOutrosDTO.getFundoManeioReposicao() );
        detalheOutros.setFundoManeioDisponivel( detalheOutrosDTO.getFundoManeioDisponivel() );
        detalheOutros.setSeguroSemEntidade( detalheOutrosDTO.getSeguroSemEntidade() );

        return detalheOutros;
    }
}
