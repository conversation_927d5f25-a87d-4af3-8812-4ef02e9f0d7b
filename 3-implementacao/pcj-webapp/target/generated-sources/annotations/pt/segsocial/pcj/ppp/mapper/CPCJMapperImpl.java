package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.CPCJDTO;
import pt.segsocial.pcj.ppp.dto.DadosCPCJDTO;
import pt.segsocial.pcj.ppp.dto.DadosConstituicaoDTO;
import pt.segsocial.pcj.ppp.dto.DetalheInfoLogisticaDTO;
import pt.segsocial.pcj.ppp.dto.DetalheInfraestruturaDTO;
import pt.segsocial.pcj.ppp.dto.DetalheOutrosDTO;
import pt.segsocial.pcj.ppp.dto.HorarioFuncionamentoDTO;
import pt.segsocial.pcj.ppp.dto.InfraestruturaDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheInfoLogistica;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheInfraestrutura;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheOutros;
import pt.segsocial.pcj.ppp.jpa.entity.SalaReuniao;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class CPCJMapperImpl implements CPCJMapper {

    @Override
    public CPCJDTO toDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        CPCJDTO cPCJDTO = new CPCJDTO();

        cPCJDTO.setDetalheOutrosDTO( detalheOutrosToDetalheOutrosDTO( comissaoPCJppp.getDetalheOutros() ) );
        cPCJDTO.setDetalheInfoLogisticaDTO( detalheInfoLogisticaToDetalheInfoLogisticaDTO( comissaoPCJppp.getDetalheInfoLogistica() ) );
        cPCJDTO.setCodigoHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        cPCJDTO.setDetalheInfraestruturaDTO( detalheInfraestruturaToDetalheInfraestruturaDTO( comissaoPCJppp.getDetalheInfraestrutura() ) );
        cPCJDTO.setId( comissaoPCJppp.getId() );
        cPCJDTO.setSalas( salaReuniaoListToInfraestruturaDTOList( comissaoPCJppp.getSalas() ) );

        return cPCJDTO;
    }

    @Override
    public ComissaoPCJppp toEntidade(CPCJDTO cpcjdto, DadosCPCJDTO dadosCPCJDTO, DadosConstituicaoDTO dadosConstituicaoDTO, HorarioFuncionamentoDTO horarioFuncionamentoDTO) {
        if ( cpcjdto == null && dadosCPCJDTO == null && dadosConstituicaoDTO == null && horarioFuncionamentoDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        if ( cpcjdto != null ) {
            comissaoPCJppp.setMorada( moradaDTOToMorada( cpcjdto.getMoradaDTO() ) );
            comissaoPCJppp.setHorarioFuncionamento( cpcjdto.getCodigoHorarioFuncionamento() );
            comissaoPCJppp.setId( cpcjdto.getId() );
            comissaoPCJppp.setSalas( infraestruturaDTOListToSalaReuniaoList( cpcjdto.getSalas() ) );
        }
        if ( dadosCPCJDTO != null ) {
            comissaoPCJppp.setCodigo( dadosCPCJDTO.getCodigo() );
            comissaoPCJppp.setNome( dadosCPCJDTO.getNome() );
            comissaoPCJppp.setNumeroLinhaDireta( dadosCPCJDTO.getNumeroLinhaDireta() );
            comissaoPCJppp.setNumeroTelemovel( dadosCPCJDTO.getNumeroTelemovel() );
            comissaoPCJppp.setSocialInstagram( dadosCPCJDTO.getSocialInstagram() );
            comissaoPCJppp.setSocialFacebook( dadosCPCJDTO.getSocialFacebook() );
            comissaoPCJppp.setSocialLinkedin( dadosCPCJDTO.getSocialLinkedin() );
            comissaoPCJppp.setSocialOutra( dadosCPCJDTO.getSocialOutra() );
            comissaoPCJppp.setEmailInstitucional( dadosCPCJDTO.getEmailInstitucional() );
            comissaoPCJppp.setUrlSite( dadosCPCJDTO.getUrlSite() );
        }
        if ( dadosConstituicaoDTO != null ) {
            comissaoPCJppp.setNumeroPortaria( dadosConstituicaoDTO.getNumeroPortaria() );
            comissaoPCJppp.setDataPortaria( dadosConstituicaoDTO.getDataPortaria() );
            comissaoPCJppp.setNumeroDiarioPortaria( dadosConstituicaoDTO.getNumeroDiarioPortaria() );
            comissaoPCJppp.setDataInicioFuncionamento( dadosConstituicaoDTO.getDataInicioFuncionamento() );
            comissaoPCJppp.setNumeroPortariaReorganizacao( dadosConstituicaoDTO.getNumeroPortariaReorganizacao() );
            comissaoPCJppp.setDataPortariaReorganizacao( dadosConstituicaoDTO.getDataPortariaReorganizacao() );
            comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( dadosConstituicaoDTO.getNumeroDiarioPortariaReorganizacao() );
        }
        if ( horarioFuncionamentoDTO != null ) {
            comissaoPCJppp.setRegimePermanencia( horarioFuncionamentoDTO.getRegimePermanencia() );
            comissaoPCJppp.setOutraPermanecia( horarioFuncionamentoDTO.getOutraPermanecia() );
            comissaoPCJppp.setOutraHorario( horarioFuncionamentoDTO.getOutraHorario() );
            comissaoPCJppp.setHoraAberturaManha( horarioFuncionamentoDTO.getHoraAberturaManha() );
            comissaoPCJppp.setHoraFechoManha( horarioFuncionamentoDTO.getHoraFechoManha() );
            comissaoPCJppp.setHoraAberturaTarde( horarioFuncionamentoDTO.getHoraAberturaTarde() );
            comissaoPCJppp.setHoraFechoTarde( horarioFuncionamentoDTO.getHoraFechoTarde() );
        }

        return comissaoPCJppp;
    }

    @Override
    public void atualizarComissaoFromDto(CPCJDTO cpcjdto, DadosCPCJDTO dadosCPCJDTO, DadosConstituicaoDTO dadosConstituicaoDTO, HorarioFuncionamentoDTO horarioFuncionamentoDTO, ComissaoPCJppp comissaoPCJppp) {
        if ( cpcjdto == null && dadosCPCJDTO == null && dadosConstituicaoDTO == null && horarioFuncionamentoDTO == null ) {
            return;
        }

        if ( cpcjdto != null ) {
            comissaoPCJppp.setHorarioFuncionamento( cpcjdto.getCodigoHorarioFuncionamento() );
            comissaoPCJppp.setId( cpcjdto.getId() );
            if ( comissaoPCJppp.getSalas() != null ) {
                List<SalaReuniao> list = infraestruturaDTOListToSalaReuniaoList( cpcjdto.getSalas() );
                if ( list != null ) {
                    comissaoPCJppp.getSalas().clear();
                    comissaoPCJppp.getSalas().addAll( list );
                }
                else {
                    comissaoPCJppp.setSalas( null );
                }
            }
            else {
                List<SalaReuniao> list = infraestruturaDTOListToSalaReuniaoList( cpcjdto.getSalas() );
                if ( list != null ) {
                    comissaoPCJppp.setSalas( list );
                }
            }
        }
        if ( dadosCPCJDTO != null ) {
            comissaoPCJppp.setCodigo( dadosCPCJDTO.getCodigo() );
            comissaoPCJppp.setNome( dadosCPCJDTO.getNome() );
            comissaoPCJppp.setNumeroLinhaDireta( dadosCPCJDTO.getNumeroLinhaDireta() );
            comissaoPCJppp.setNumeroTelemovel( dadosCPCJDTO.getNumeroTelemovel() );
            comissaoPCJppp.setSocialInstagram( dadosCPCJDTO.getSocialInstagram() );
            comissaoPCJppp.setSocialFacebook( dadosCPCJDTO.getSocialFacebook() );
            comissaoPCJppp.setSocialLinkedin( dadosCPCJDTO.getSocialLinkedin() );
            comissaoPCJppp.setSocialOutra( dadosCPCJDTO.getSocialOutra() );
            comissaoPCJppp.setEmailInstitucional( dadosCPCJDTO.getEmailInstitucional() );
            comissaoPCJppp.setUrlSite( dadosCPCJDTO.getUrlSite() );
        }
        if ( dadosConstituicaoDTO != null ) {
            comissaoPCJppp.setNumeroPortaria( dadosConstituicaoDTO.getNumeroPortaria() );
            comissaoPCJppp.setDataPortaria( dadosConstituicaoDTO.getDataPortaria() );
            comissaoPCJppp.setNumeroDiarioPortaria( dadosConstituicaoDTO.getNumeroDiarioPortaria() );
            comissaoPCJppp.setDataInicioFuncionamento( dadosConstituicaoDTO.getDataInicioFuncionamento() );
            comissaoPCJppp.setNumeroPortariaReorganizacao( dadosConstituicaoDTO.getNumeroPortariaReorganizacao() );
            comissaoPCJppp.setDataPortariaReorganizacao( dadosConstituicaoDTO.getDataPortariaReorganizacao() );
            comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( dadosConstituicaoDTO.getNumeroDiarioPortariaReorganizacao() );
        }
        if ( horarioFuncionamentoDTO != null ) {
            comissaoPCJppp.setRegimePermanencia( horarioFuncionamentoDTO.getRegimePermanencia() );
            comissaoPCJppp.setOutraPermanecia( horarioFuncionamentoDTO.getOutraPermanecia() );
            comissaoPCJppp.setOutraHorario( horarioFuncionamentoDTO.getOutraHorario() );
            comissaoPCJppp.setHoraAberturaManha( horarioFuncionamentoDTO.getHoraAberturaManha() );
            comissaoPCJppp.setHoraFechoManha( horarioFuncionamentoDTO.getHoraFechoManha() );
            comissaoPCJppp.setHoraAberturaTarde( horarioFuncionamentoDTO.getHoraAberturaTarde() );
            comissaoPCJppp.setHoraFechoTarde( horarioFuncionamentoDTO.getHoraFechoTarde() );
        }
    }

    @Override
    public void atualizarDadosCpcjFromEntidade(ComissaoPCJppp comissao, DadosCPCJDTO dadosCPCJDTO) {
        if ( comissao == null ) {
            return;
        }

        dadosCPCJDTO.setCodigo( comissao.getCodigo() );
        dadosCPCJDTO.setNome( comissao.getNome() );
        dadosCPCJDTO.setNumeroTelemovel( comissao.getNumeroTelemovel() );
        dadosCPCJDTO.setNumeroLinhaDireta( comissao.getNumeroLinhaDireta() );
        dadosCPCJDTO.setEmailInstitucional( comissao.getEmailInstitucional() );
        dadosCPCJDTO.setUrlSite( comissao.getUrlSite() );
        dadosCPCJDTO.setSocialFacebook( comissao.getSocialFacebook() );
        dadosCPCJDTO.setSocialInstagram( comissao.getSocialInstagram() );
        dadosCPCJDTO.setSocialLinkedin( comissao.getSocialLinkedin() );
        dadosCPCJDTO.setSocialOutra( comissao.getSocialOutra() );
    }

    @Override
    public void atualizarDadosConstitucionaisCpcjFromEntidade(ComissaoPCJppp comissao, DadosConstituicaoDTO dadosConstituicaoDTO) {
        if ( comissao == null ) {
            return;
        }

        dadosConstituicaoDTO.setNumeroPortaria( comissao.getNumeroPortaria() );
        dadosConstituicaoDTO.setDataPortaria( comissao.getDataPortaria() );
        dadosConstituicaoDTO.setNumeroDiarioPortaria( comissao.getNumeroDiarioPortaria() );
        dadosConstituicaoDTO.setDataInicioFuncionamento( comissao.getDataInicioFuncionamento() );
        dadosConstituicaoDTO.setNumeroPortariaReorganizacao( comissao.getNumeroPortariaReorganizacao() );
        dadosConstituicaoDTO.setDataPortariaReorganizacao( comissao.getDataPortariaReorganizacao() );
        dadosConstituicaoDTO.setNumeroDiarioPortariaReorganizacao( comissao.getNumeroDiarioPortariaReorganizacao() );
    }

    @Override
    public void atualizarHorarioFuncionamentoCpcjFromEntidade(ComissaoPCJppp comissao, HorarioFuncionamentoDTO horarioFuncionamentoDTO) {
        if ( comissao == null ) {
            return;
        }

        horarioFuncionamentoDTO.setHorarioFuncionamento( comissao.getHorarioFuncionamento() );
        horarioFuncionamentoDTO.setOutraHorario( comissao.getOutraHorario() );
        horarioFuncionamentoDTO.setRegimePermanencia( comissao.getRegimePermanencia() );
        horarioFuncionamentoDTO.setOutraPermanecia( comissao.getOutraPermanecia() );
        horarioFuncionamentoDTO.setHoraAberturaManha( comissao.getHoraAberturaManha() );
        horarioFuncionamentoDTO.setHoraFechoManha( comissao.getHoraFechoManha() );
        horarioFuncionamentoDTO.setHoraAberturaTarde( comissao.getHoraAberturaTarde() );
        horarioFuncionamentoDTO.setHoraFechoTarde( comissao.getHoraFechoTarde() );
    }

    protected DetalheOutrosDTO detalheOutrosToDetalheOutrosDTO(DetalheOutros detalheOutros) {
        if ( detalheOutros == null ) {
            return null;
        }

        DetalheOutrosDTO detalheOutrosDTO = new DetalheOutrosDTO();

        if ( detalheOutros.getId() != null ) {
            detalheOutrosDTO.setId( detalheOutros.getId() );
        }
        detalheOutrosDTO.setCodMotorista( detalheOutros.getCodMotorista() );
        detalheOutrosDTO.setFundoManeioMovimentacao( detalheOutros.getFundoManeioMovimentacao() );
        detalheOutrosDTO.setViatura( detalheOutros.getViatura() );
        detalheOutrosDTO.setSeguroComissario( detalheOutros.getSeguroComissario() );
        detalheOutrosDTO.setViaturaCaracterizada( detalheOutros.getViaturaCaracterizada() );
        detalheOutrosDTO.setFundoManeioReposicao( detalheOutros.getFundoManeioReposicao() );
        detalheOutrosDTO.setFundoManeioDisponivel( detalheOutros.getFundoManeioDisponivel() );
        detalheOutrosDTO.setSeguroSemEntidade( detalheOutros.getSeguroSemEntidade() );

        return detalheOutrosDTO;
    }

    protected DetalheInfoLogisticaDTO detalheInfoLogisticaToDetalheInfoLogisticaDTO(DetalheInfoLogistica detalheInfoLogistica) {
        if ( detalheInfoLogistica == null ) {
            return null;
        }

        DetalheInfoLogisticaDTO detalheInfoLogisticaDTO = new DetalheInfoLogisticaDTO();

        if ( detalheInfoLogistica.getId() != null ) {
            detalheInfoLogisticaDTO.setId( detalheInfoLogistica.getId() );
        }
        detalheInfoLogisticaDTO.setCodSevicoCorreio( detalheInfoLogistica.getCodSevicoCorreio() );
        detalheInfoLogisticaDTO.setCodConsumivelEscritorio( detalheInfoLogistica.getCodConsumivelEscritorio() );
        detalheInfoLogisticaDTO.setCodMaterialEscritorio( detalheInfoLogistica.getCodMaterialEscritorio() );
        detalheInfoLogisticaDTO.setApoioTecnicoInformatico( detalheInfoLogistica.getApoioTecnicoInformatico() );
        detalheInfoLogisticaDTO.setMobiliarioAtendimento( detalheInfoLogistica.getMobiliarioAtendimento() );
        detalheInfoLogisticaDTO.setComputadoresSuficiente( detalheInfoLogistica.getComputadoresSuficiente() );
        detalheInfoLogisticaDTO.setInternetSuficiente( detalheInfoLogistica.getInternetSuficiente() );
        detalheInfoLogisticaDTO.setLimpeza( detalheInfoLogistica.getLimpeza() );
        detalheInfoLogisticaDTO.setManutencao( detalheInfoLogistica.getManutencao() );
        detalheInfoLogisticaDTO.setSoftwareCompativel( detalheInfoLogistica.getSoftwareCompativel() );
        detalheInfoLogisticaDTO.setMobiliarioPostoTrabalho( detalheInfoLogistica.getMobiliarioPostoTrabalho() );
        detalheInfoLogisticaDTO.setLivroReclamacao( detalheInfoLogistica.getLivroReclamacao() );
        detalheInfoLogisticaDTO.setDestruicaoPapel( detalheInfoLogistica.getDestruicaoPapel() );
        detalheInfoLogisticaDTO.setCodPartilhaImpressao( detalheInfoLogistica.getCodPartilhaImpressao() );
        detalheInfoLogisticaDTO.setTelefoneDireto( detalheInfoLogistica.getTelefoneDireto() );
        detalheInfoLogisticaDTO.setTelefoneReencaminhamento( detalheInfoLogistica.getTelefoneReencaminhamento() );
        detalheInfoLogisticaDTO.setPossuiMultifuncoes( detalheInfoLogistica.getPossuiMultifuncoes() );
        detalheInfoLogisticaDTO.setPossuiFotocopia( detalheInfoLogistica.getPossuiFotocopia() );
        detalheInfoLogisticaDTO.setPossuiImpressora( detalheInfoLogistica.getPossuiImpressora() );

        return detalheInfoLogisticaDTO;
    }

    protected DetalheInfraestruturaDTO detalheInfraestruturaToDetalheInfraestruturaDTO(DetalheInfraestrutura detalheInfraestrutura) {
        if ( detalheInfraestrutura == null ) {
            return null;
        }

        DetalheInfraestruturaDTO detalheInfraestruturaDTO = new DetalheInfraestruturaDTO();

        if ( detalheInfraestrutura.getId() != null ) {
            detalheInfraestruturaDTO.setId( detalheInfraestrutura.getId() );
        }
        detalheInfraestruturaDTO.setCodSalaEspera( detalheInfraestrutura.getCodSalaEspera() );
        detalheInfraestruturaDTO.setCodAreaTrabalho( detalheInfraestrutura.getCodAreaTrabalho() );
        detalheInfraestruturaDTO.setInstalacaoSanitaria( detalheInfraestrutura.getInstalacaoSanitaria() );
        detalheInfraestruturaDTO.setAcessibilidade( detalheInfraestrutura.getAcessibilidade() );
        detalheInfraestruturaDTO.setArquivo( detalheInfraestrutura.getArquivo() );
        detalheInfraestruturaDTO.setForaHorarioNecessario( detalheInfraestrutura.getForaHorarioNecessario() );
        detalheInfraestruturaDTO.setForaHorarioExistente( detalheInfraestrutura.getForaHorarioExistente() );

        return detalheInfraestruturaDTO;
    }

    protected InfraestruturaDTO salaReuniaoToInfraestruturaDTO(SalaReuniao salaReuniao) {
        if ( salaReuniao == null ) {
            return null;
        }

        InfraestruturaDTO infraestruturaDTO = new InfraestruturaDTO();

        infraestruturaDTO.setNome( salaReuniao.getNome() );
        infraestruturaDTO.setCapacidade( salaReuniao.getCapacidade() );
        if ( salaReuniao.getStatusFuncionamento() != null ) {
            infraestruturaDTO.setStatusFuncionamento( salaReuniao.getStatusFuncionamento() );
        }
        infraestruturaDTO.setObservacao( salaReuniao.getObservacao() );
        infraestruturaDTO.setTipoSala( salaReuniao.getTipoSala() );
        infraestruturaDTO.setId( salaReuniao.getId() );
        infraestruturaDTO.setSalaAcustica( salaReuniao.getSalaAcustica() );
        infraestruturaDTO.setEquipamentoAudio( salaReuniao.getEquipamentoAudio() );
        infraestruturaDTO.setEspelhoUnidirecional( salaReuniao.getEspelhoUnidirecional() );
        infraestruturaDTO.setRecursosAudicao( salaReuniao.getRecursosAudicao() );
        infraestruturaDTO.setLudicoPedagogico( salaReuniao.getLudicoPedagogico() );
        infraestruturaDTO.setUtilizacaoExterna( salaReuniao.getUtilizacaoExterna() );
        infraestruturaDTO.setEquipamentoVideo( salaReuniao.getEquipamentoVideo() );
        infraestruturaDTO.setSalaSegura( salaReuniao.getSalaSegura() );
        infraestruturaDTO.setSalaAcolhedora( salaReuniao.getSalaAcolhedora() );
        infraestruturaDTO.setComportaTodasIdades( salaReuniao.getComportaTodasIdades() );
        infraestruturaDTO.setCapacidadeImpressao( salaReuniao.getCapacidadeImpressao() );

        return infraestruturaDTO;
    }

    protected List<InfraestruturaDTO> salaReuniaoListToInfraestruturaDTOList(List<SalaReuniao> list) {
        if ( list == null ) {
            return null;
        }

        List<InfraestruturaDTO> list1 = new ArrayList<InfraestruturaDTO>( list.size() );
        for ( SalaReuniao salaReuniao : list ) {
            list1.add( salaReuniaoToInfraestruturaDTO( salaReuniao ) );
        }

        return list1;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected SalaReuniao infraestruturaDTOToSalaReuniao(InfraestruturaDTO infraestruturaDTO) {
        if ( infraestruturaDTO == null ) {
            return null;
        }

        SalaReuniao salaReuniao = new SalaReuniao();

        salaReuniao.setId( infraestruturaDTO.getId() );
        salaReuniao.setNome( infraestruturaDTO.getNome() );
        salaReuniao.setCapacidade( infraestruturaDTO.getCapacidade() );
        salaReuniao.setObservacao( infraestruturaDTO.getObservacao() );
        salaReuniao.setTipoSala( infraestruturaDTO.getTipoSala() );
        salaReuniao.setStatusFuncionamento( infraestruturaDTO.getStatusFuncionamento() );
        salaReuniao.setSalaAcustica( infraestruturaDTO.getSalaAcustica() );
        salaReuniao.setEquipamentoAudio( infraestruturaDTO.getEquipamentoAudio() );
        salaReuniao.setEspelhoUnidirecional( infraestruturaDTO.getEspelhoUnidirecional() );
        salaReuniao.setRecursosAudicao( infraestruturaDTO.getRecursosAudicao() );
        salaReuniao.setLudicoPedagogico( infraestruturaDTO.getLudicoPedagogico() );
        salaReuniao.setUtilizacaoExterna( infraestruturaDTO.getUtilizacaoExterna() );
        salaReuniao.setEquipamentoVideo( infraestruturaDTO.getEquipamentoVideo() );
        salaReuniao.setSalaSegura( infraestruturaDTO.getSalaSegura() );
        salaReuniao.setSalaAcolhedora( infraestruturaDTO.getSalaAcolhedora() );
        salaReuniao.setComportaTodasIdades( infraestruturaDTO.getComportaTodasIdades() );
        salaReuniao.setCapacidadeImpressao( infraestruturaDTO.getCapacidadeImpressao() );

        return salaReuniao;
    }

    protected List<SalaReuniao> infraestruturaDTOListToSalaReuniaoList(List<InfraestruturaDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<SalaReuniao> list1 = new ArrayList<SalaReuniao>( list.size() );
        for ( InfraestruturaDTO infraestruturaDTO : list ) {
            list1.add( infraestruturaDTOToSalaReuniao( infraestruturaDTO ) );
        }

        return list1;
    }
}
