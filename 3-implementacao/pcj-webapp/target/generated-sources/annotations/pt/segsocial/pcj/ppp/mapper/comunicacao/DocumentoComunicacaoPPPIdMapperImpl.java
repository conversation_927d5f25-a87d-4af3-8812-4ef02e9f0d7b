package pt.segsocial.pcj.ppp.mapper.comunicacao;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.comunicacao.DocumentoComunicacaoDTOId;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.DocumentoComunicacaoPPPId;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DocumentoComunicacaoPPPIdMapperImpl implements DocumentoComunicacaoPPPIdMapper {

    @Override
    public DocumentoComunicacaoPPPId toEntity(DocumentoComunicacaoDTOId documentoComunicacaoDTOId) {
        if ( documentoComunicacaoDTOId == null ) {
            return null;
        }

        DocumentoComunicacaoPPPId documentoComunicacaoPPPId = new DocumentoComunicacaoPPPId();

        documentoComunicacaoPPPId.setDocumentoId( documentoComunicacaoDTOId.getDocumentoId() );
        documentoComunicacaoPPPId.setComunicacaoId( documentoComunicacaoDTOId.getComunicacaoId() );

        return documentoComunicacaoPPPId;
    }

    @Override
    public DocumentoComunicacaoDTOId toDTO(DocumentoComunicacaoPPPId documentoComunicacaoPPP) {
        if ( documentoComunicacaoPPP == null ) {
            return null;
        }

        DocumentoComunicacaoDTOId documentoComunicacaoDTOId = new DocumentoComunicacaoDTOId();

        documentoComunicacaoDTOId.setDocumentoId( documentoComunicacaoPPP.getDocumentoId() );
        documentoComunicacaoDTOId.setComunicacaoId( documentoComunicacaoPPP.getComunicacaoId() );

        return documentoComunicacaoDTOId;
    }
}
