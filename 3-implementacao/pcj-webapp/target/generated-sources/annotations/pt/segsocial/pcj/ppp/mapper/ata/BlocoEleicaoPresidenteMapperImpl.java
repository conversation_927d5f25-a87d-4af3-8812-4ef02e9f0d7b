package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoEleicaoPresidenteDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoEleicaoPresidente;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:00-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoEleicaoPresidenteMapperImpl implements BlocoEleicaoPresidenteMapper {

    @Override
    public BlocoEleicaoPresidente toEntity(BlocoEleicaoPresidenteDTO blocoFinalMandatoDTO) {
        if ( blocoFinalMandatoDTO == null ) {
            return null;
        }

        BlocoEleicaoPresidente blocoEleicaoPresidente = new BlocoEleicaoPresidente();

        blocoEleicaoPresidente.setId( blocoFinalMandatoDTO.getId() );
        blocoEleicaoPresidente.setObservacao( blocoFinalMandatoDTO.getObservacao() );
        blocoEleicaoPresidente.setTextoIntrodutorio( blocoFinalMandatoDTO.getTextoIntrodutorio() );

        return blocoEleicaoPresidente;
    }

    @Override
    public BlocoEleicaoPresidenteDTO toDTO(BlocoEleicaoPresidente blocoFinalMandato) {
        if ( blocoFinalMandato == null ) {
            return null;
        }

        BlocoEleicaoPresidenteDTO blocoEleicaoPresidenteDTO = new BlocoEleicaoPresidenteDTO();

        blocoEleicaoPresidenteDTO.setId( blocoFinalMandato.getId() );
        blocoEleicaoPresidenteDTO.setObservacao( blocoFinalMandato.getObservacao() );
        blocoEleicaoPresidenteDTO.setTextoIntrodutorio( blocoFinalMandato.getTextoIntrodutorio() );

        return blocoEleicaoPresidenteDTO;
    }
}
