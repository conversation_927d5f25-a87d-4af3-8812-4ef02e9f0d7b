package pt.segsocial.pcj.ppp.mapper.comunicacao;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.ParticipanteDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ConvidadoPCJDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoDTO;
import pt.segsocial.pcj.ppp.enums.RececaoExpedienteEnum;
import pt.segsocial.pcj.ppp.enums.TipoParticipanteEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.ConvidadoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:02-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ComunicacaoMapperImpl implements ComunicacaoMapper {

    @Override
    public ComunicacaoDTO toDTO(Comunicacao comunicacao) {
        if ( comunicacao == null ) {
            return null;
        }

        ComunicacaoDTO comunicacaoDTO = new ComunicacaoDTO();

        comunicacaoDTO.setCodigoEstado( comunicacao.getCodigoEstado() );
        comunicacaoDTO.setCodigoEstadoAnalise( comunicacao.getCodigoEstadoAnalise() );
        comunicacaoDTO.setCodigoModalidadeContato( comunicacao.getCodigoModalidadeContato() );
        comunicacaoDTO.setCodigoParticipanteParentesco( comunicacao.getCodigoParticipanteParentesco() );
        comunicacaoDTO.setCodigoParticipanteRelacao( comunicacao.getCodigoParticipanteRelacao() );
        comunicacaoDTO.setDataRececao( comunicacao.getDataRececao() );
        comunicacaoDTO.setDataRegisto( comunicacao.getDataRegisto() );
        comunicacaoDTO.setDeliberado( comunicacao.getDeliberado() );
        comunicacaoDTO.setExpedienteAssociado( comunicacao.getExpedienteAssociado() );
        comunicacaoDTO.setId( comunicacao.getId() );
        comunicacaoDTO.setOutrasObservacoes( comunicacao.getOutrasObservacoes() );
        comunicacaoDTO.setParticipanteAnonimo( comunicacao.getParticipanteAnonimo() );
        comunicacaoDTO.setParticipanteCrianca( comunicacao.getParticipanteCrianca() );
        comunicacaoDTO.setParticipanteFamiliar( comunicacao.getParticipanteFamiliar() );
        if ( comunicacao.getRececaoExpediente() != null ) {
            comunicacaoDTO.setRececaoExpediente( comunicacao.getRececaoExpediente().name() );
        }
        if ( comunicacao.getTipoParticipante() != null ) {
            comunicacaoDTO.setTipoParticipante( comunicacao.getTipoParticipante().name() );
        }
        comunicacaoDTO.setNumeroComunicacao( comunicacao.getNumeroComunicacao() );
        comunicacaoDTO.setUrgente( comunicacao.getUrgente() );
        comunicacaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( comunicacao.getComissao() ) );

        return comunicacaoDTO;
    }

    @Override
    public Comunicacao toEntity(ComunicacaoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Comunicacao comunicacao = new Comunicacao();

        comunicacao.setId( dto.getId() );
        comunicacao.setNumeroComunicacao( dto.getNumeroComunicacao() );
        comunicacao.setComissao( comissaoPCJDTOToComissaoPCJppp( dto.getComissao() ) );
        comunicacao.setCodigoModalidadeContato( dto.getCodigoModalidadeContato() );
        comunicacao.setExpedienteAssociado( dto.getExpedienteAssociado() );
        if ( dto.getTipoParticipante() != null ) {
            comunicacao.setTipoParticipante( Enum.valueOf( TipoParticipanteEnum.class, dto.getTipoParticipante() ) );
        }
        comunicacao.setParticipanteAnonimo( dto.getParticipanteAnonimo() );
        comunicacao.setParticipanteCrianca( dto.getParticipanteCrianca() );
        comunicacao.setParticipanteFamiliar( dto.getParticipanteFamiliar() );
        comunicacao.setCodigoParticipanteParentesco( dto.getCodigoParticipanteParentesco() );
        comunicacao.setCodigoParticipanteRelacao( dto.getCodigoParticipanteRelacao() );
        comunicacao.setOutrasObservacoes( dto.getOutrasObservacoes() );
        comunicacao.setDataRececao( dto.getDataRececao() );
        comunicacao.setDataRegisto( dto.getDataRegisto() );
        if ( dto.getRececaoExpediente() != null ) {
            comunicacao.setRececaoExpediente( Enum.valueOf( RececaoExpedienteEnum.class, dto.getRececaoExpediente() ) );
        }
        comunicacao.setCodigoEstado( dto.getCodigoEstado() );
        comunicacao.setDeliberado( dto.getDeliberado() );
        comunicacao.setCodigoEstadoAnalise( dto.getCodigoEstadoAnalise() );
        comunicacao.setUrgente( dto.getUrgente() );

        return comunicacao;
    }

    @Override
    public Comunicacao toEntityFrom(ParticipanteDTO participante) {
        if ( participante == null ) {
            return null;
        }

        Comunicacao comunicacao = new Comunicacao();

        comunicacao.setId( participante.getId() );
        comunicacao.setNumeroComunicacao( participante.getNumeroComunicacao() );
        comunicacao.setConvidadoPCJ( convidadoPCJDTOToConvidadoPCJ( participante.getConvidadoPCJ() ) );
        comunicacao.setCodigoModalidadeContato( participante.getCodigoModalidadeContato() );
        comunicacao.setExpedienteAssociado( participante.getExpedienteAssociado() );
        comunicacao.setTipoParticipante( participante.getTipoParticipante() );
        comunicacao.setParticipanteAnonimo( participante.isParticipanteAnonimo() );
        comunicacao.setParticipanteCrianca( participante.getParticipanteCrianca() );
        comunicacao.setParticipanteFamiliar( participante.getParticipanteFamiliar() );
        comunicacao.setCodigoParticipanteParentesco( participante.getCodigoParticipanteParentesco() );
        comunicacao.setCodigoParticipanteRelacao( participante.getCodigoParticipanteRelacao() );
        comunicacao.setOutrasObservacoes( participante.getOutrasObservacoes() );
        comunicacao.setDataRececao( participante.getDataRececao() );
        comunicacao.setDataRegisto( participante.getDataRegisto() );
        comunicacao.setRececaoExpediente( participante.getRececaoExpediente() );
        comunicacao.setCodigoEstado( participante.getCodigoEstado() );

        return comunicacao;
    }

    @Override
    public ParticipanteDTO toParticipanteDTOFrom(Comunicacao comunicacao) {
        if ( comunicacao == null ) {
            return null;
        }

        ParticipanteDTO participanteDTO = new ParticipanteDTO();

        participanteDTO.setRececaoExpediente( comunicacao.getRececaoExpediente() );
        participanteDTO.setDataRececao( comunicacao.getDataRececao() );
        participanteDTO.setDataRegisto( comunicacao.getDataRegisto() );
        participanteDTO.setCodigoModalidadeContato( comunicacao.getCodigoModalidadeContato() );
        participanteDTO.setExpedienteAssociado( comunicacao.getExpedienteAssociado() );
        if ( comunicacao.getParticipanteAnonimo() != null ) {
            participanteDTO.setParticipanteAnonimo( comunicacao.getParticipanteAnonimo() );
        }
        participanteDTO.setTipoParticipante( comunicacao.getTipoParticipante() );
        participanteDTO.setOutrasObservacoes( comunicacao.getOutrasObservacoes() );
        participanteDTO.setParticipanteCrianca( comunicacao.getParticipanteCrianca() );
        participanteDTO.setCodigoParticipanteParentesco( comunicacao.getCodigoParticipanteParentesco() );
        participanteDTO.setCodigoParticipanteRelacao( comunicacao.getCodigoParticipanteRelacao() );
        participanteDTO.setParticipanteFamiliar( comunicacao.getParticipanteFamiliar() );
        participanteDTO.setConvidadoPCJ( convidadoPCJToConvidadoPCJDTO( comunicacao.getConvidadoPCJ() ) );
        participanteDTO.setId( comunicacao.getId() );
        participanteDTO.setCodigoEstado( comunicacao.getCodigoEstado() );
        participanteDTO.setNumeroComunicacao( comunicacao.getNumeroComunicacao() );

        return participanteDTO;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected ConvidadoPCJ convidadoPCJDTOToConvidadoPCJ(ConvidadoPCJDTO convidadoPCJDTO) {
        if ( convidadoPCJDTO == null ) {
            return null;
        }

        ConvidadoPCJ convidadoPCJ = new ConvidadoPCJ();

        convidadoPCJ.setId( convidadoPCJDTO.getId() );
        convidadoPCJ.setMorada( convidadoPCJDTO.getMorada() );
        convidadoPCJ.setEmail( convidadoPCJDTO.getEmail() );
        convidadoPCJ.setTelefone( convidadoPCJDTO.getTelefone() );
        convidadoPCJ.setCargo( convidadoPCJDTO.getCargo() );
        convidadoPCJ.setEntidade( convidadoPCJDTO.getEntidade() );
        convidadoPCJ.setNome( convidadoPCJDTO.getNome() );
        convidadoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( convidadoPCJDTO.getComissao() ) );
        convidadoPCJ.setCodigoEntidadeParticipante( convidadoPCJDTO.getCodigoEntidadeParticipante() );
        convidadoPCJ.setDetalheEntidadeParticipante( convidadoPCJDTO.getDetalheEntidadeParticipante() );

        return convidadoPCJ;
    }

    protected ConvidadoPCJDTO convidadoPCJToConvidadoPCJDTO(ConvidadoPCJ convidadoPCJ) {
        if ( convidadoPCJ == null ) {
            return null;
        }

        ConvidadoPCJDTO convidadoPCJDTO = new ConvidadoPCJDTO();

        convidadoPCJDTO.setId( convidadoPCJ.getId() );
        convidadoPCJDTO.setCargo( convidadoPCJ.getCargo() );
        convidadoPCJDTO.setEntidade( convidadoPCJ.getEntidade() );
        convidadoPCJDTO.setNome( convidadoPCJ.getNome() );
        convidadoPCJDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( convidadoPCJ.getComissao() ) );
        convidadoPCJDTO.setCodigoEntidadeParticipante( convidadoPCJ.getCodigoEntidadeParticipante() );
        convidadoPCJDTO.setDetalheEntidadeParticipante( convidadoPCJ.getDetalheEntidadeParticipante() );
        convidadoPCJDTO.setTelefone( convidadoPCJ.getTelefone() );
        convidadoPCJDTO.setEmail( convidadoPCJ.getEmail() );
        convidadoPCJDTO.setMorada( convidadoPCJ.getMorada() );

        return convidadoPCJDTO;
    }
}
