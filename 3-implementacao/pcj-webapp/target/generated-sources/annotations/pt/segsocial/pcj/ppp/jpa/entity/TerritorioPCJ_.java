package pt.segsocial.pcj.ppp.jpa.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(TerritorioPCJ.class)
public abstract class TerritorioPCJ_ extends pt.segsocial.fraw.jpa.DefaultPersistentDomainObject_ {

	public static volatile SingularAttribute<TerritorioPCJ, Integer> codigoFreguesia;
	public static volatile SingularAttribute<TerritorioPCJ, Integer> codigoDistrito;
	public static volatile SingularAttribute<TerritorioPCJ, Integer> codigoConcelho;

}

