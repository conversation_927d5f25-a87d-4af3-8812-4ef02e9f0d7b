package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentosPPPDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.BlocoGeralRestritaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentosPPP;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.BlocoGeralRestrita;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:55-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoGeralRestritaMapperImpl implements BlocoGeralRestritaMapper {

    @Override
    public BlocoGeralRestrita toEntity(BlocoGeralRestritaDTO blocoAssuntosGeraisDTO) {
        if ( blocoAssuntosGeraisDTO == null ) {
            return null;
        }

        BlocoGeralRestrita blocoGeralRestrita = new BlocoGeralRestrita();

        blocoGeralRestrita.setId( blocoAssuntosGeraisDTO.getId() );
        blocoGeralRestrita.setAssunto( blocoAssuntosGeraisDTO.getAssunto() );
        blocoGeralRestrita.setDocumentoPCJ( documentosPPPDTOToDocumentosPPP( blocoAssuntosGeraisDTO.getDocumentoPCJ() ) );
        blocoGeralRestrita.setObservacao( blocoAssuntosGeraisDTO.getObservacao() );
        blocoGeralRestrita.setTextoIntrodutorio( blocoAssuntosGeraisDTO.getTextoIntrodutorio() );

        return blocoGeralRestrita;
    }

    @Override
    public BlocoGeralRestritaDTO toDTO(BlocoGeralRestrita blocoAssuntoGeral) {
        if ( blocoAssuntoGeral == null ) {
            return null;
        }

        BlocoGeralRestritaDTO blocoGeralRestritaDTO = new BlocoGeralRestritaDTO();

        blocoGeralRestritaDTO.setId( blocoAssuntoGeral.getId() );
        blocoGeralRestritaDTO.setDocumentoPCJ( documentosPPPToDocumentosPPPDTO( blocoAssuntoGeral.getDocumentoPCJ() ) );
        blocoGeralRestritaDTO.setAssunto( blocoAssuntoGeral.getAssunto() );
        blocoGeralRestritaDTO.setObservacao( blocoAssuntoGeral.getObservacao() );
        blocoGeralRestritaDTO.setTextoIntrodutorio( blocoAssuntoGeral.getTextoIntrodutorio() );

        return blocoGeralRestritaDTO;
    }

    protected DocumentosPPP documentosPPPDTOToDocumentosPPP(DocumentosPPPDTO documentosPPPDTO) {
        if ( documentosPPPDTO == null ) {
            return null;
        }

        DocumentosPPP documentosPPP = new DocumentosPPP();

        documentosPPP.setId( documentosPPPDTO.getId() );
        documentosPPP.setDataUpload( documentosPPPDTO.getDataUpload() );
        documentosPPP.setIdentificadorFicheiro( documentosPPPDTO.getIdentificadorFicheiro() );
        documentosPPP.setNomeDocumento( documentosPPPDTO.getNomeDocumento() );
        documentosPPP.setTipoDocumento( documentosPPPDTO.getTipoDocumento() );
        documentosPPP.setUploadDocumento( documentosPPPDTO.getUploadDocumento() );

        return documentosPPP;
    }

    protected DocumentosPPPDTO documentosPPPToDocumentosPPPDTO(DocumentosPPP documentosPPP) {
        if ( documentosPPP == null ) {
            return null;
        }

        DocumentosPPPDTO documentosPPPDTO = new DocumentosPPPDTO();

        documentosPPPDTO.setTipoDocumento( documentosPPP.getTipoDocumento() );
        documentosPPPDTO.setId( documentosPPP.getId() );
        documentosPPPDTO.setDataUpload( documentosPPP.getDataUpload() );
        documentosPPPDTO.setIdentificadorFicheiro( documentosPPP.getIdentificadorFicheiro() );
        documentosPPPDTO.setUploadDocumento( documentosPPP.getUploadDocumento() );
        documentosPPPDTO.setNomeDocumento( documentosPPP.getNomeDocumento() );

        return documentosPPPDTO;
    }
}
