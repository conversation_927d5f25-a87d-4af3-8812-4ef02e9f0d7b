package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.comparar.BaseCompararVersaoDTO;
import pt.segsocial.pcj.ppp.dto.comparar.UtilizadorVersaoPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorVersaoPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class UtilizadorVersaoPCJMapperImpl implements UtilizadorVersaoPCJMapper {

    @Override
    public UtilizadorVersaoPCJDTO toDTO(UtilizadorVersaoPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorVersaoPCJDTO utilizadorVersaoPCJDTO = new UtilizadorVersaoPCJDTO();

        utilizadorVersaoPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorVersaoPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorVersaoPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorVersaoPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorVersaoPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorVersaoPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorVersaoPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorVersaoPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorVersaoPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorVersaoPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorVersaoPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );
        utilizadorVersaoPCJDTO.setDataVersao( utilizadorPCJ.getDataVersao() );
        utilizadorVersaoPCJDTO.setNissUtilizadorResponsavel( utilizadorPCJ.getNissUtilizadorResponsavel() );

        return utilizadorVersaoPCJDTO;
    }

    @Override
    public UtilizadorVersaoPCJ toEntidade(UtilizadorVersaoPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorVersaoPCJ utilizadorVersaoPCJ = new UtilizadorVersaoPCJ();

        utilizadorVersaoPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorVersaoPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorVersaoPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorVersaoPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorVersaoPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorVersaoPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorVersaoPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorVersaoPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorVersaoPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorVersaoPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorVersaoPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );
        utilizadorVersaoPCJ.setDataVersao( utilizadorPCJDTO.getDataVersao() );
        utilizadorVersaoPCJ.setNissUtilizadorResponsavel( utilizadorPCJDTO.getNissUtilizadorResponsavel() );

        return utilizadorVersaoPCJ;
    }

    @Override
    public List<UtilizadorVersaoPCJDTO> toDTOs(List<UtilizadorVersaoPCJ> utilizadorVersao) {
        if ( utilizadorVersao == null ) {
            return null;
        }

        List<UtilizadorVersaoPCJDTO> list = new ArrayList<UtilizadorVersaoPCJDTO>( utilizadorVersao.size() );
        for ( UtilizadorVersaoPCJ utilizadorVersaoPCJ : utilizadorVersao ) {
            list.add( toDTO( utilizadorVersaoPCJ ) );
        }

        return list;
    }

    @Override
    public List<UtilizadorVersaoPCJ> toEntidades(List<UtilizadorVersaoPCJDTO> utilizadorVersaoDTO) {
        if ( utilizadorVersaoDTO == null ) {
            return null;
        }

        List<UtilizadorVersaoPCJ> list = new ArrayList<UtilizadorVersaoPCJ>( utilizadorVersaoDTO.size() );
        for ( UtilizadorVersaoPCJDTO utilizadorVersaoPCJDTO : utilizadorVersaoDTO ) {
            list.add( toEntidade( utilizadorVersaoPCJDTO ) );
        }

        return list;
    }

    @Override
    public List<BaseCompararVersaoDTO> toBaseDTOs(List<UtilizadorVersaoPCJ> utilizadorVersao) {
        if ( utilizadorVersao == null ) {
            return null;
        }

        List<BaseCompararVersaoDTO> list = new ArrayList<BaseCompararVersaoDTO>( utilizadorVersao.size() );
        for ( UtilizadorVersaoPCJ utilizadorVersaoPCJ : utilizadorVersao ) {
            list.add( toDTO( utilizadorVersaoPCJ ) );
        }

        return list;
    }
}
