package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.MembroComissaoIdDTO;
import pt.segsocial.pcj.ppp.jpa.entity.MembroComissaoId;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class MembroComissaoIdMapperImpl implements MembroComissaoIdMapper {

    @Override
    public MembroComissaoIdDTO toDTO(MembroComissaoId membroComissaoId) {
        if ( membroComissaoId == null ) {
            return null;
        }

        MembroComissaoIdDTO membroComissaoIdDTO = new MembroComissaoIdDTO();

        membroComissaoIdDTO.setReuniaoId( membroComissaoId.getReuniaoId() );
        membroComissaoIdDTO.setElementoId( membroComissaoId.getElementoId() );

        return membroComissaoIdDTO;
    }

    @Override
    public MembroComissaoId toEntidade(MembroComissaoIdDTO membroComissaoIdDTO) {
        if ( membroComissaoIdDTO == null ) {
            return null;
        }

        MembroComissaoId membroComissaoId = new MembroComissaoId();

        membroComissaoId.setReuniaoId( membroComissaoIdDTO.getReuniaoId() );
        membroComissaoId.setElementoId( membroComissaoIdDTO.getElementoId() );

        return membroComissaoId;
    }
}
