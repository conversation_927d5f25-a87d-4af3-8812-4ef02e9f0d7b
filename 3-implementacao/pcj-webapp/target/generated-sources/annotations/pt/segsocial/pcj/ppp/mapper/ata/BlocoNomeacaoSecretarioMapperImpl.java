package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoNomeacaoSecretarioDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoNomeacaoSecretario;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:50-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoNomeacaoSecretarioMapperImpl implements BlocoNomeacaoSecretarioMapper {

    @Override
    public BlocoNomeacaoSecretario toEntity(BlocoNomeacaoSecretarioDTO blocoNomeacaoSecretarioDTO) {
        if ( blocoNomeacaoSecretarioDTO == null ) {
            return null;
        }

        BlocoNomeacaoSecretario blocoNomeacaoSecretario = new BlocoNomeacaoSecretario();

        blocoNomeacaoSecretario.setId( blocoNomeacaoSecretarioDTO.getId() );
        blocoNomeacaoSecretario.setObservacao( blocoNomeacaoSecretarioDTO.getObservacao() );
        blocoNomeacaoSecretario.setTextoIntrodutorio( blocoNomeacaoSecretarioDTO.getTextoIntrodutorio() );

        return blocoNomeacaoSecretario;
    }

    @Override
    public BlocoNomeacaoSecretarioDTO toDTO(BlocoNomeacaoSecretario blocoNomeacaoSecretario) {
        if ( blocoNomeacaoSecretario == null ) {
            return null;
        }

        BlocoNomeacaoSecretarioDTO blocoNomeacaoSecretarioDTO = new BlocoNomeacaoSecretarioDTO();

        blocoNomeacaoSecretarioDTO.setId( blocoNomeacaoSecretario.getId() );
        blocoNomeacaoSecretarioDTO.setObservacao( blocoNomeacaoSecretario.getObservacao() );
        blocoNomeacaoSecretarioDTO.setTextoIntrodutorio( blocoNomeacaoSecretario.getTextoIntrodutorio() );

        return blocoNomeacaoSecretarioDTO;
    }
}
