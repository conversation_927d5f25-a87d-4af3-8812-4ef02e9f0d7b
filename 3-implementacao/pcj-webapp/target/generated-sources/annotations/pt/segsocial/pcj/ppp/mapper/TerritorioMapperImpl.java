package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class TerritorioMapperImpl implements TerritorioMapper {

    @Override
    public TerritorioPCJDTO toDTO(TerritorioPCJ territorio) {
        if ( territorio == null ) {
            return null;
        }

        TerritorioPCJDTO territorioPCJDTO = new TerritorioPCJDTO();

        if ( territorio.getId() != null ) {
            territorioPCJDTO.setId( territorio.getId() );
        }
        territorioPCJDTO.setCodigoDistrito( territorio.getCodigoDistrito() );
        territorioPCJDTO.setCodigoConcelho( territorio.getCodigoConcelho() );
        territorioPCJDTO.setCodigoFreguesia( territorio.getCodigoFreguesia() );

        return territorioPCJDTO;
    }

    @Override
    public TerritorioPCJ toEntidade(TerritorioPCJDTO territorioDTO) {
        if ( territorioDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }
}
