package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.FicheiroPontoTrabalhoDTO;
import pt.segsocial.pcj.ppp.dto.FicheiroPontoTrabalhoIdDTO;
import pt.segsocial.pcj.ppp.dto.PontoTrabalhoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.FicheiroPontoTrabalho;
import pt.segsocial.pcj.ppp.jpa.entity.FicheiroPontoTrabalhoId;
import pt.segsocial.pcj.ppp.jpa.entity.PontoTrabalhoPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class PontoTrabalhoPCJMapperImpl implements PontoTrabalhoPCJMapper {

    @Override
    public PontoTrabalhoPCJ toEntidade(PontoTrabalhoDTO pontoTrabalhoDTO) {
        if ( pontoTrabalhoDTO == null ) {
            return null;
        }

        PontoTrabalhoPCJ pontoTrabalhoPCJ = new PontoTrabalhoPCJ();

        pontoTrabalhoPCJ.setId( pontoTrabalhoDTO.getId() );
        pontoTrabalhoPCJ.setCodigoPontoTrabalho( pontoTrabalhoDTO.getCodigoPontoTrabalho() );
        pontoTrabalhoPCJ.setObservacao( pontoTrabalhoDTO.getObservacao() );
        pontoTrabalhoPCJ.setFicheirosPontoTrabalho( ficheiroPontoTrabalhoDTOSetToFicheiroPontoTrabalhoSet( pontoTrabalhoDTO.getFicheirosPontoTrabalho() ) );

        return pontoTrabalhoPCJ;
    }

    @Override
    public List<PontoTrabalhoDTO> toDTOs(List<PontoTrabalhoPCJ> pontoTrabalhoPCJS) {
        if ( pontoTrabalhoPCJS == null ) {
            return null;
        }

        List<PontoTrabalhoDTO> list = new ArrayList<PontoTrabalhoDTO>( pontoTrabalhoPCJS.size() );
        for ( PontoTrabalhoPCJ pontoTrabalhoPCJ : pontoTrabalhoPCJS ) {
            list.add( pontoTrabalhoPCJToPontoTrabalhoDTO( pontoTrabalhoPCJ ) );
        }

        return list;
    }

    protected FicheiroPontoTrabalhoId ficheiroPontoTrabalhoIdDTOToFicheiroPontoTrabalhoId(FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdDTO) {
        if ( ficheiroPontoTrabalhoIdDTO == null ) {
            return null;
        }

        FicheiroPontoTrabalhoId ficheiroPontoTrabalhoId = new FicheiroPontoTrabalhoId();

        ficheiroPontoTrabalhoId.setDocumentoId( ficheiroPontoTrabalhoIdDTO.getDocumentoId() );
        ficheiroPontoTrabalhoId.setPontoTrabalhoId( ficheiroPontoTrabalhoIdDTO.getPontoTrabalhoId() );

        return ficheiroPontoTrabalhoId;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected FicheiroPontoTrabalho ficheiroPontoTrabalhoDTOToFicheiroPontoTrabalho(FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoDTO) {
        if ( ficheiroPontoTrabalhoDTO == null ) {
            return null;
        }

        FicheiroPontoTrabalho ficheiroPontoTrabalho = new FicheiroPontoTrabalho();

        ficheiroPontoTrabalho.setId( ficheiroPontoTrabalhoIdDTOToFicheiroPontoTrabalhoId( ficheiroPontoTrabalhoDTO.getId() ) );
        ficheiroPontoTrabalho.setDocumentoPCJ( documentoDTOToDocumentoPCJ( ficheiroPontoTrabalhoDTO.getDocumentoPCJ() ) );

        return ficheiroPontoTrabalho;
    }

    protected Set<FicheiroPontoTrabalho> ficheiroPontoTrabalhoDTOSetToFicheiroPontoTrabalhoSet(Set<FicheiroPontoTrabalhoDTO> set) {
        if ( set == null ) {
            return null;
        }

        Set<FicheiroPontoTrabalho> set1 = new HashSet<FicheiroPontoTrabalho>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoDTO : set ) {
            set1.add( ficheiroPontoTrabalhoDTOToFicheiroPontoTrabalho( ficheiroPontoTrabalhoDTO ) );
        }

        return set1;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    protected FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdToFicheiroPontoTrabalhoIdDTO(FicheiroPontoTrabalhoId ficheiroPontoTrabalhoId) {
        if ( ficheiroPontoTrabalhoId == null ) {
            return null;
        }

        FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdDTO = new FicheiroPontoTrabalhoIdDTO();

        ficheiroPontoTrabalhoIdDTO.setDocumentoId( ficheiroPontoTrabalhoId.getDocumentoId() );
        ficheiroPontoTrabalhoIdDTO.setPontoTrabalhoId( ficheiroPontoTrabalhoId.getPontoTrabalhoId() );

        return ficheiroPontoTrabalhoIdDTO;
    }

    protected FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoToFicheiroPontoTrabalhoDTO(FicheiroPontoTrabalho ficheiroPontoTrabalho) {
        if ( ficheiroPontoTrabalho == null ) {
            return null;
        }

        FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoDTO = new FicheiroPontoTrabalhoDTO();

        ficheiroPontoTrabalhoDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( ficheiroPontoTrabalho.getDocumentoPCJ() ) );
        ficheiroPontoTrabalhoDTO.setId( ficheiroPontoTrabalhoIdToFicheiroPontoTrabalhoIdDTO( ficheiroPontoTrabalho.getId() ) );

        return ficheiroPontoTrabalhoDTO;
    }

    protected Set<FicheiroPontoTrabalhoDTO> ficheiroPontoTrabalhoSetToFicheiroPontoTrabalhoDTOSet(Set<FicheiroPontoTrabalho> set) {
        if ( set == null ) {
            return null;
        }

        Set<FicheiroPontoTrabalhoDTO> set1 = new HashSet<FicheiroPontoTrabalhoDTO>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( FicheiroPontoTrabalho ficheiroPontoTrabalho : set ) {
            set1.add( ficheiroPontoTrabalhoToFicheiroPontoTrabalhoDTO( ficheiroPontoTrabalho ) );
        }

        return set1;
    }

    protected PontoTrabalhoDTO pontoTrabalhoPCJToPontoTrabalhoDTO(PontoTrabalhoPCJ pontoTrabalhoPCJ) {
        if ( pontoTrabalhoPCJ == null ) {
            return null;
        }

        PontoTrabalhoDTO pontoTrabalhoDTO = new PontoTrabalhoDTO();

        pontoTrabalhoDTO.setId( pontoTrabalhoPCJ.getId() );
        pontoTrabalhoDTO.setCodigoPontoTrabalho( pontoTrabalhoPCJ.getCodigoPontoTrabalho() );
        pontoTrabalhoDTO.setObservacao( pontoTrabalhoPCJ.getObservacao() );
        pontoTrabalhoDTO.setFicheirosPontoTrabalho( ficheiroPontoTrabalhoSetToFicheiroPontoTrabalhoDTOSet( pontoTrabalhoPCJ.getFicheirosPontoTrabalho() ) );

        return pontoTrabalhoDTO;
    }
}
