package pt.segsocial.pcj.ppp.mapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.id.api.exception.IDException;
import pt.segsocial.pcj.ppp.dto.elemento.ResultadoPesquisaElementoDTO;
import pt.segsocial.pcj.ppp.jpa.dao.PaginacaoResultado;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ResultadoPesquisaElementoMapperImpl implements ResultadoPesquisaElementoMapper {

    @Override
    public ResultadoPesquisaElementoDTO toDTO(Elemento elemento) throws IDException {
        if ( elemento == null ) {
            return null;
        }

        ResultadoPesquisaElementoDTO resultadoPesquisaElementoDTO = new ResultadoPesquisaElementoDTO();

        String nome = elementoComissaoNome( elemento );
        if ( nome != null ) {
            resultadoPesquisaElementoDTO.setNomeComissao( nome );
        }
        String niss = elementoUtilizadorPCJNiss( elemento );
        if ( niss != null ) {
            resultadoPesquisaElementoDTO.setNiss( niss );
        }
        resultadoPesquisaElementoDTO.setFuncao( elemento.getTipoElemento() );
        String nome1 = elementoUtilizadorPCJNome( elemento );
        if ( nome1 != null ) {
            resultadoPesquisaElementoDTO.setNome( nome1 );
        }
        resultadoPesquisaElementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isAtivo() != null ) {
            resultadoPesquisaElementoDTO.setAtivo( elemento.isAtivo() );
        }
        if ( elemento.getId() != null ) {
            resultadoPesquisaElementoDTO.setId( BigDecimal.valueOf( elemento.getId() ) );
        }
        resultadoPesquisaElementoDTO.setCargoMandato( elemento.getCargoMandato() );

        return resultadoPesquisaElementoDTO;
    }

    @Override
    public List<ResultadoPesquisaElementoDTO> toDTOs(List<Elemento> elementos) {
        if ( elementos == null ) {
            return null;
        }

        List<ResultadoPesquisaElementoDTO> list = new ArrayList<ResultadoPesquisaElementoDTO>( elementos.size() );
        for ( Elemento elemento : elementos ) {
            try {
                list.add( toDTO( elemento ) );
            }
            catch ( IDException e ) {
                throw new RuntimeException( e );
            }
        }

        return list;
    }

    @Override
    public PaginacaoResultado<ResultadoPesquisaElementoDTO> toDTOsPaginados(PaginacaoResultado<Elemento> elementos) {
        if ( elementos == null ) {
            return null;
        }

        PaginacaoResultado<ResultadoPesquisaElementoDTO> paginacaoResultado = new PaginacaoResultado<ResultadoPesquisaElementoDTO>();

        paginacaoResultado.setConteudo( toDTOs( elementos.getConteudo() ) );
        paginacaoResultado.setTotalElementos( elementos.getTotalElementos() );
        paginacaoResultado.setNumeroPagina( elementos.getNumeroPagina() );
        paginacaoResultado.setTamanhoPagina( elementos.getTamanhoPagina() );

        return paginacaoResultado;
    }

    private String elementoComissaoNome(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }
        ComissaoPCJppp comissao = elemento.getComissao();
        if ( comissao == null ) {
            return null;
        }
        String nome = comissao.getNome();
        if ( nome == null ) {
            return null;
        }
        return nome;
    }

    private String elementoUtilizadorPCJNiss(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }
        UtilizadorPCJ utilizadorPCJ = elemento.getUtilizadorPCJ();
        if ( utilizadorPCJ == null ) {
            return null;
        }
        String niss = utilizadorPCJ.getNiss();
        if ( niss == null ) {
            return null;
        }
        return niss;
    }

    private String elementoUtilizadorPCJNome(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }
        UtilizadorPCJ utilizadorPCJ = elemento.getUtilizadorPCJ();
        if ( utilizadorPCJ == null ) {
            return null;
        }
        String nome = utilizadorPCJ.getNome();
        if ( nome == null ) {
            return null;
        }
        return nome;
    }
}
