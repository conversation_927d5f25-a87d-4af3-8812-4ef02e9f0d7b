package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ResultadoGerirPedidoSuporteDTO;
import pt.segsocial.pcj.ppp.dto.ResultadoPesquisaPedidoSuporteDTO;
import pt.segsocial.pcj.ppp.dto.SuporteDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.Suporte;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:55-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class SuporteMapperImpl implements SuporteMapper {

    @Inject
    private ComissaoPPPMapper comissaoPPPMapper;
    @Inject
    private ElementoMapper elementoMapper;

    @Override
    public SuporteDTO toDTO(Suporte suporte) {
        if ( suporte == null ) {
            return null;
        }

        SuporteDTO suporteDTO = new SuporteDTO();

        suporteDTO.setElemento( elementoMapper.toDTO( suporte.getElemento() ) );
        suporteDTO.setComissao( comissaoPPPMapper.toDTO( suporte.getComissao() ) );
        suporteDTO.setDocumentos( documentoPCJListToDocumentoDTOSet( suporte.getDocumentos() ) );
        suporteDTO.setAssunto( suporte.getAssunto() );
        suporteDTO.setDescricao( suporte.getDescricao() );
        suporteDTO.setEstado( suporte.getEstado() );
        suporteDTO.setDataPedidoSuporte( suporte.getDataPedidoSuporte() );

        return suporteDTO;
    }

    @Override
    public Suporte toEntity(SuporteDTO suporte) {
        if ( suporte == null ) {
            return null;
        }

        Suporte suporte1 = new Suporte();

        suporte1.setElemento( elementoMapper.toEntidade( suporte.getElemento() ) );
        suporte1.setComissao( comissaoPPPMapper.toEntidade( suporte.getComissao() ) );
        suporte1.setDocumentos( documentoDTOSetToDocumentoPCJList( suporte.getDocumentos() ) );
        suporte1.setAssunto( suporte.getAssunto() );
        suporte1.setDescricao( suporte.getDescricao() );
        suporte1.setEstado( suporte.getEstado() );
        suporte1.setDataPedidoSuporte( suporte.getDataPedidoSuporte() );

        return suporte1;
    }

    @Override
    public List<ResultadoPesquisaPedidoSuporteDTO> toListaPesquisa(List<Suporte> pedidosSuporte) {
        if ( pedidosSuporte == null ) {
            return null;
        }

        List<ResultadoPesquisaPedidoSuporteDTO> list = new ArrayList<ResultadoPesquisaPedidoSuporteDTO>( pedidosSuporte.size() );
        for ( Suporte suporte : pedidosSuporte ) {
            list.add( toListaPesquisa( suporte ) );
        }

        return list;
    }

    @Override
    public ResultadoPesquisaPedidoSuporteDTO toListaPesquisa(Suporte pedidoSuporte) {
        if ( pedidoSuporte == null ) {
            return null;
        }

        ResultadoPesquisaPedidoSuporteDTO resultadoPesquisaPedidoSuporteDTO = new ResultadoPesquisaPedidoSuporteDTO();

        String nome = pedidoSuporteElementoUtilizadorPCJNome( pedidoSuporte );
        if ( nome != null ) {
            resultadoPesquisaPedidoSuporteDTO.setNomeElemento( nome );
        }
        String nome1 = pedidoSuporteComissaoNome( pedidoSuporte );
        if ( nome1 != null ) {
            resultadoPesquisaPedidoSuporteDTO.setNomeCpcj( nome1 );
        }
        resultadoPesquisaPedidoSuporteDTO.setDataPedido( pedidoSuporte.getDataPedidoSuporte() );
        resultadoPesquisaPedidoSuporteDTO.setAssunto( pedidoSuporte.getAssunto() );
        resultadoPesquisaPedidoSuporteDTO.setEstado( pedidoSuporte.getEstado() );
        resultadoPesquisaPedidoSuporteDTO.setId( pedidoSuporte.getId() );

        return resultadoPesquisaPedidoSuporteDTO;
    }

    @Override
    public ResultadoGerirPedidoSuporteDTO toConsultaPedidoSuporteDTO(Suporte pedidoSuporte) {
        if ( pedidoSuporte == null ) {
            return null;
        }

        ResultadoGerirPedidoSuporteDTO resultadoGerirPedidoSuporteDTO = new ResultadoGerirPedidoSuporteDTO();

        resultadoGerirPedidoSuporteDTO.setDataConclusao( pedidoSuporte.getDataConclusaoSuporte() );
        String nome = pedidoSuporteElementoUtilizadorPCJNome( pedidoSuporte );
        if ( nome != null ) {
            resultadoGerirPedidoSuporteDTO.setNomeElemento( nome );
        }
        String nome1 = pedidoSuporteComissaoNome( pedidoSuporte );
        if ( nome1 != null ) {
            resultadoGerirPedidoSuporteDTO.setNomeCpcj( nome1 );
        }
        String email = pedidoSuporteElementoEmail( pedidoSuporte );
        if ( email != null ) {
            resultadoGerirPedidoSuporteDTO.setEmailElemento( email );
        }
        resultadoGerirPedidoSuporteDTO.setDataPedido( pedidoSuporte.getDataPedidoSuporte() );
        resultadoGerirPedidoSuporteDTO.setAssunto( pedidoSuporte.getAssunto() );
        resultadoGerirPedidoSuporteDTO.setEstado( pedidoSuporte.getEstado() );
        resultadoGerirPedidoSuporteDTO.setId( pedidoSuporte.getId() );
        resultadoGerirPedidoSuporteDTO.setDescricao( pedidoSuporte.getDescricao() );
        resultadoGerirPedidoSuporteDTO.setComentarioComissaoNacional( pedidoSuporte.getComentarioComissaoNacional() );
        resultadoGerirPedidoSuporteDTO.setDocumentos( documentoPCJListToDocumentoDTOList( pedidoSuporte.getDocumentos() ) );

        return resultadoGerirPedidoSuporteDTO;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    protected Set<DocumentoDTO> documentoPCJListToDocumentoDTOSet(List<DocumentoPCJ> list) {
        if ( list == null ) {
            return null;
        }

        Set<DocumentoDTO> set = new HashSet<DocumentoDTO>( Math.max( (int) ( list.size() / .75f ) + 1, 16 ) );
        for ( DocumentoPCJ documentoPCJ : list ) {
            set.add( documentoPCJToDocumentoDTO( documentoPCJ ) );
        }

        return set;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected List<DocumentoPCJ> documentoDTOSetToDocumentoPCJList(Set<DocumentoDTO> set) {
        if ( set == null ) {
            return null;
        }

        List<DocumentoPCJ> list = new ArrayList<DocumentoPCJ>( set.size() );
        for ( DocumentoDTO documentoDTO : set ) {
            list.add( documentoDTOToDocumentoPCJ( documentoDTO ) );
        }

        return list;
    }

    private String pedidoSuporteElementoUtilizadorPCJNome(Suporte suporte) {
        if ( suporte == null ) {
            return null;
        }
        Elemento elemento = suporte.getElemento();
        if ( elemento == null ) {
            return null;
        }
        UtilizadorPCJ utilizadorPCJ = elemento.getUtilizadorPCJ();
        if ( utilizadorPCJ == null ) {
            return null;
        }
        String nome = utilizadorPCJ.getNome();
        if ( nome == null ) {
            return null;
        }
        return nome;
    }

    private String pedidoSuporteComissaoNome(Suporte suporte) {
        if ( suporte == null ) {
            return null;
        }
        ComissaoPCJppp comissao = suporte.getComissao();
        if ( comissao == null ) {
            return null;
        }
        String nome = comissao.getNome();
        if ( nome == null ) {
            return null;
        }
        return nome;
    }

    private String pedidoSuporteElementoEmail(Suporte suporte) {
        if ( suporte == null ) {
            return null;
        }
        Elemento elemento = suporte.getElemento();
        if ( elemento == null ) {
            return null;
        }
        String email = elemento.getEmail();
        if ( email == null ) {
            return null;
        }
        return email;
    }

    protected List<DocumentoDTO> documentoPCJListToDocumentoDTOList(List<DocumentoPCJ> list) {
        if ( list == null ) {
            return null;
        }

        List<DocumentoDTO> list1 = new ArrayList<DocumentoDTO>( list.size() );
        for ( DocumentoPCJ documentoPCJ : list ) {
            list1.add( documentoPCJToDocumentoDTO( documentoPCJ ) );
        }

        return list1;
    }
}
