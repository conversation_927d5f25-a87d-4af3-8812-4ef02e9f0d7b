package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAprovacaoAnualDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAprovacaoAnual;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoAprovacaoAnualMapperImpl implements BlocoAprovacaoAnualMapper {

    @Override
    public BlocoAprovacaoAnual toEntity(BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO) {
        if ( blocoAprovacaoAnualDTO == null ) {
            return null;
        }

        BlocoAprovacaoAnual blocoAprovacaoAnual = new BlocoAprovacaoAnual();

        blocoAprovacaoAnual.setId( blocoAprovacaoAnualDTO.getId() );
        blocoAprovacaoAnual.setObservacao( blocoAprovacaoAnualDTO.getObservacao() );
        blocoAprovacaoAnual.setTextoIntrodutorio( blocoAprovacaoAnualDTO.getTextoIntrodutorio() );

        return blocoAprovacaoAnual;
    }

    @Override
    public BlocoAprovacaoAnualDTO toDTO(BlocoAprovacaoAnual blocoAprovacaoAnual) {
        if ( blocoAprovacaoAnual == null ) {
            return null;
        }

        BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO = new BlocoAprovacaoAnualDTO();

        blocoAprovacaoAnualDTO.setId( blocoAprovacaoAnual.getId() );
        blocoAprovacaoAnualDTO.setObservacao( blocoAprovacaoAnual.getObservacao() );
        blocoAprovacaoAnualDTO.setTextoIntrodutorio( blocoAprovacaoAnual.getTextoIntrodutorio() );

        return blocoAprovacaoAnualDTO;
    }
}
