package pt.segsocial.pcj.ppp.mapper.ata;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoConhecimentoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoConhecimento;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoConhecimentoMapperImpl implements BlocoConhecimentoMapper {

    @Override
    public BlocoConhecimento toEntity(BlocoConhecimentoDTO blocoConhecimentoDTO) {
        if ( blocoConhecimentoDTO == null ) {
            return null;
        }

        BlocoConhecimento blocoConhecimento = new BlocoConhecimento();

        blocoConhecimento.setId( blocoConhecimentoDTO.getId() );
        blocoConhecimento.setAssunto( blocoConhecimentoDTO.getAssunto() );
        blocoConhecimento.setObservacao( blocoConhecimentoDTO.getObservacao() );
        blocoConhecimento.setTextoIntrodutorio( blocoConhecimentoDTO.getTextoIntrodutorio() );
        blocoConhecimento.setComissao( comissaoPCJDTOToComissaoPCJppp( blocoConhecimentoDTO.getComissao() ) );
        blocoConhecimento.setExecutado( blocoConhecimentoDTO.isExecutado() );
        blocoConhecimento.setDocumentoPCJ( documentoDTOToDocumentoPCJ( blocoConhecimentoDTO.getDocumentoPCJ() ) );
        blocoConhecimento.setAtaExterna( blocoConhecimentoDTO.isAtaExterna() );

        return blocoConhecimento;
    }

    @Override
    public BlocoConhecimentoDTO toDTO(BlocoConhecimento blocoConhecimento) {
        if ( blocoConhecimento == null ) {
            return null;
        }

        BlocoConhecimentoDTO blocoConhecimentoDTO = new BlocoConhecimentoDTO();

        blocoConhecimentoDTO.setId( blocoConhecimento.getId() );
        blocoConhecimentoDTO.setAssunto( blocoConhecimento.getAssunto() );
        blocoConhecimentoDTO.setObservacao( blocoConhecimento.getObservacao() );
        blocoConhecimentoDTO.setTextoIntrodutorio( blocoConhecimento.getTextoIntrodutorio() );
        blocoConhecimentoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( blocoConhecimento.getComissao() ) );
        blocoConhecimentoDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( blocoConhecimento.getDocumentoPCJ() ) );
        if ( blocoConhecimento.getExecutado() != null ) {
            blocoConhecimentoDTO.setExecutado( blocoConhecimento.getExecutado() );
        }
        if ( blocoConhecimento.isAtaExterna() != null ) {
            blocoConhecimentoDTO.setAtaExterna( blocoConhecimento.isAtaExterna() );
        }

        return blocoConhecimentoDTO;
    }

    @Override
    public void updateEntityFromDto(BlocoConhecimentoDTO dto, BlocoConhecimento entidade) {
        if ( dto == null ) {
            return;
        }

        entidade.setId( dto.getId() );
        entidade.setAssunto( dto.getAssunto() );
        entidade.setObservacao( dto.getObservacao() );
        entidade.setTextoIntrodutorio( dto.getTextoIntrodutorio() );
        if ( dto.getComissao() != null ) {
            if ( entidade.getComissao() == null ) {
                entidade.setComissao( new ComissaoPCJppp() );
            }
            comissaoPCJDTOToComissaoPCJppp1( dto.getComissao(), entidade.getComissao() );
        }
        else {
            entidade.setComissao( null );
        }
        entidade.setExecutado( dto.isExecutado() );
        if ( dto.getDocumentoPCJ() != null ) {
            if ( entidade.getDocumentoPCJ() == null ) {
                entidade.setDocumentoPCJ( new DocumentoPCJ() );
            }
            documentoDTOToDocumentoPCJ1( dto.getDocumentoPCJ(), entidade.getDocumentoPCJ() );
        }
        else {
            entidade.setDocumentoPCJ( null );
        }
        entidade.setAtaExterna( dto.isAtaExterna() );
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    protected void moradaDTOToMorada1(MoradaDTO moradaDTO, Morada mappingTarget) {
        if ( moradaDTO == null ) {
            return;
        }

        mappingTarget.setId( moradaDTO.getId() );
        mappingTarget.setDistrito( moradaDTO.getDistrito() );
        mappingTarget.setConcelho( moradaDTO.getConcelho() );
        mappingTarget.setFreguesia( moradaDTO.getFreguesia() );
        mappingTarget.setArteria( moradaDTO.getArteria() );
        mappingTarget.setCodigoPostal( moradaDTO.getCodigoPostal() );
        mappingTarget.setLocalidade( moradaDTO.getLocalidade() );
        mappingTarget.setTipoInstalacao( moradaDTO.getTipoInstalacao() );
    }

    protected void comissaoPCJDTOToComissaoPCJppp1(ComissaoPCJDTO comissaoPCJDTO, ComissaoPCJppp mappingTarget) {
        if ( comissaoPCJDTO == null ) {
            return;
        }

        mappingTarget.setId( comissaoPCJDTO.getId() );
        if ( comissaoPCJDTO.getMorada() != null ) {
            if ( mappingTarget.getMorada() == null ) {
                mappingTarget.setMorada( new Morada() );
            }
            moradaDTOToMorada1( comissaoPCJDTO.getMorada(), mappingTarget.getMorada() );
        }
        else {
            mappingTarget.setMorada( null );
        }
        mappingTarget.setCodigo( comissaoPCJDTO.getCodigo() );
        mappingTarget.setNome( comissaoPCJDTO.getNome() );
        mappingTarget.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        mappingTarget.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        mappingTarget.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        mappingTarget.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        mappingTarget.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        mappingTarget.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        mappingTarget.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        mappingTarget.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        mappingTarget.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        mappingTarget.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        mappingTarget.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        mappingTarget.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        mappingTarget.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        mappingTarget.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        mappingTarget.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        mappingTarget.setAtivo( comissaoPCJDTO.getAtivo() );
        mappingTarget.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        mappingTarget.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        mappingTarget.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        mappingTarget.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        mappingTarget.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        mappingTarget.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        mappingTarget.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        mappingTarget.setUrlSite( comissaoPCJDTO.getUrlSite() );
        if ( mappingTarget.getCompetencias() != null ) {
            List<CompetenciaTerritorial> list = competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() );
            if ( list != null ) {
                mappingTarget.getCompetencias().clear();
                mappingTarget.getCompetencias().addAll( list );
            }
            else {
                mappingTarget.setCompetencias( null );
            }
        }
        else {
            List<CompetenciaTerritorial> list = competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() );
            if ( list != null ) {
                mappingTarget.setCompetencias( list );
            }
        }
        mappingTarget.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );
    }

    protected void documentoDTOToDocumentoPCJ1(DocumentoDTO documentoDTO, DocumentoPCJ mappingTarget) {
        if ( documentoDTO == null ) {
            return;
        }

        mappingTarget.setId( documentoDTO.getId() );
        mappingTarget.setDataUpload( documentoDTO.getDataUpload() );
        mappingTarget.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        mappingTarget.setUploadDocumento( documentoDTO.getUploadDocumento() );
        mappingTarget.setNomeDocumento( documentoDTO.getNomeDocumento() );
    }
}
