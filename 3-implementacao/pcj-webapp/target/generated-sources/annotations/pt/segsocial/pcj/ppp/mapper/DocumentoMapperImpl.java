package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:50-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DocumentoMapperImpl implements DocumentoMapper {

    @Override
    public DocumentoDTO toDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    @Override
    public DocumentoPCJ toEntidade(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    @Override
    public List<DocumentoPCJ> toEntidades(List<DocumentoDTO> documentosDTO) {
        if ( documentosDTO == null ) {
            return null;
        }

        List<DocumentoPCJ> list = new ArrayList<DocumentoPCJ>( documentosDTO.size() );
        for ( DocumentoDTO documentoDTO : documentosDTO ) {
            list.add( toEntidade( documentoDTO ) );
        }

        return list;
    }
}
