package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAssuntosGeraisDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAssuntoGeral;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoAssuntoGeralMapperImpl implements BlocoAssuntoGeralMapper {

    @Override
    public BlocoAssuntoGeral toEntity(BlocoAssuntosGeraisDTO obj) {
        if ( obj == null ) {
            return null;
        }

        BlocoAssuntoGeral blocoAssuntoGeral = new BlocoAssuntoGeral();

        blocoAssuntoGeral.setId( obj.getId() );
        blocoAssuntoGeral.setObservacao( obj.getObservacao() );
        blocoAssuntoGeral.setTextoIntrodutorio( obj.getTextoIntrodutorio() );
        blocoAssuntoGeral.setAssunto( obj.getAssunto() );
        blocoAssuntoGeral.setDocumentoPCJ( documentoDTOToDocumentoPCJ( obj.getDocumentoPCJ() ) );

        return blocoAssuntoGeral;
    }

    @Override
    public BlocoAssuntosGeraisDTO toDTO(BlocoAssuntoGeral obj) {
        if ( obj == null ) {
            return null;
        }

        BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO = new BlocoAssuntosGeraisDTO();

        blocoAssuntosGeraisDTO.setId( obj.getId() );
        blocoAssuntosGeraisDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( obj.getDocumentoPCJ() ) );
        blocoAssuntosGeraisDTO.setAssunto( obj.getAssunto() );
        blocoAssuntosGeraisDTO.setObservacao( obj.getObservacao() );
        blocoAssuntosGeraisDTO.setTextoIntrodutorio( obj.getTextoIntrodutorio() );

        return blocoAssuntosGeraisDTO;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }
}
