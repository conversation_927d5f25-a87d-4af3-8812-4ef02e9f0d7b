package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ElementoFolhaIdDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.ElementoFolhaId;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ElementoFolhaIdMapperImpl implements ElementoFolhaIdMapper {

    @Override
    public ElementoFolhaIdDTO toDTO(ElementoFolhaId elementoFolhaId) {
        if ( elementoFolhaId == null ) {
            return null;
        }

        ElementoFolhaIdDTO elementoFolhaIdDTO = new ElementoFolhaIdDTO();

        return elementoFolhaIdDTO;
    }

    @Override
    public ElementoFolhaId toEntity(ElementoFolhaIdDTO elementoFolhaIdDTO) {
        if ( elementoFolhaIdDTO == null ) {
            return null;
        }

        ElementoFolhaId elementoFolhaId = new ElementoFolhaId();

        return elementoFolhaId;
    }
}
