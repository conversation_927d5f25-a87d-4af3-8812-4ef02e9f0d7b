package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.HorarioFuncionamentoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DiaSemana;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:02-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class HorarioFuncionamentoMapperImpl implements HorarioFuncionamentoMapper {

    @Override
    public DiaSemana toEntity(HorarioFuncionamentoDTO horarioFuncionamentoDTO) {
        if ( horarioFuncionamentoDTO == null ) {
            return null;
        }

        DiaSemana diaSemana = new DiaSemana();

        diaSemana.setCodigoDiaSemana( horarioFuncionamentoDTO.getDiaSemana() );
        diaSemana.setHoraAberturaManha( horarioFuncionamentoDTO.getHoraAberturaManha() );
        diaSemana.setHoraFechoManha( horarioFuncionamentoDTO.getHoraFechoManha() );
        diaSemana.setHoraAberturaTarde( horarioFuncionamentoDTO.getHoraAberturaTarde() );
        diaSemana.setHoraFechoTarde( horarioFuncionamentoDTO.getHoraFechoTarde() );

        return diaSemana;
    }

    @Override
    public HorarioFuncionamentoDTO toDTO(DiaSemana diaSemana) {
        if ( diaSemana == null ) {
            return null;
        }

        HorarioFuncionamentoDTO horarioFuncionamentoDTO = new HorarioFuncionamentoDTO();

        horarioFuncionamentoDTO.setDiaSemana( diaSemana.getCodigoDiaSemana() );
        horarioFuncionamentoDTO.setHoraAberturaManha( diaSemana.getHoraAberturaManha() );
        horarioFuncionamentoDTO.setHoraFechoManha( diaSemana.getHoraFechoManha() );
        horarioFuncionamentoDTO.setHoraAberturaTarde( diaSemana.getHoraAberturaTarde() );
        horarioFuncionamentoDTO.setHoraFechoTarde( diaSemana.getHoraFechoTarde() );

        return horarioFuncionamentoDTO;
    }

    @Override
    public List<HorarioFuncionamentoDTO> toDTOs(List<DiaSemana> horariosFuncionamento) {
        if ( horariosFuncionamento == null ) {
            return null;
        }

        List<HorarioFuncionamentoDTO> list = new ArrayList<HorarioFuncionamentoDTO>( horariosFuncionamento.size() );
        for ( DiaSemana diaSemana : horariosFuncionamento ) {
            list.add( toDTO( diaSemana ) );
        }

        return list;
    }

    @Override
    public List<DiaSemana> toEntidades(List<HorarioFuncionamentoDTO> horarioFuncionamentoDTOS) {
        if ( horarioFuncionamentoDTOS == null ) {
            return null;
        }

        List<DiaSemana> list = new ArrayList<DiaSemana>( horarioFuncionamentoDTOS.size() );
        for ( HorarioFuncionamentoDTO horarioFuncionamentoDTO : horarioFuncionamentoDTOS ) {
            list.add( toEntity( horarioFuncionamentoDTO ) );
        }

        return list;
    }
}
