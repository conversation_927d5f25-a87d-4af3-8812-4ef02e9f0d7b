package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DetalheInfoLogisticaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheInfoLogistica;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DetalheInfoLogisticaMapperImpl implements DetalheInfoLogisticaMapper {

    @Override
    public DetalheInfoLogisticaDTO toDTO(DetalheInfoLogistica detalheInfoLogistica) {
        if ( detalheInfoLogistica == null ) {
            return null;
        }

        DetalheInfoLogisticaDTO detalheInfoLogisticaDTO = new DetalheInfoLogisticaDTO();

        if ( detalheInfoLogistica.getId() != null ) {
            detalheInfoLogisticaDTO.setId( detalheInfoLogistica.getId() );
        }
        detalheInfoLogisticaDTO.setCodSevicoCorreio( detalheInfoLogistica.getCodSevicoCorreio() );
        detalheInfoLogisticaDTO.setCodConsumivelEscritorio( detalheInfoLogistica.getCodConsumivelEscritorio() );
        detalheInfoLogisticaDTO.setCodMaterialEscritorio( detalheInfoLogistica.getCodMaterialEscritorio() );
        detalheInfoLogisticaDTO.setApoioTecnicoInformatico( detalheInfoLogistica.getApoioTecnicoInformatico() );
        detalheInfoLogisticaDTO.setMobiliarioAtendimento( detalheInfoLogistica.getMobiliarioAtendimento() );
        detalheInfoLogisticaDTO.setComputadoresSuficiente( detalheInfoLogistica.getComputadoresSuficiente() );
        detalheInfoLogisticaDTO.setInternetSuficiente( detalheInfoLogistica.getInternetSuficiente() );
        detalheInfoLogisticaDTO.setLimpeza( detalheInfoLogistica.getLimpeza() );
        detalheInfoLogisticaDTO.setManutencao( detalheInfoLogistica.getManutencao() );
        detalheInfoLogisticaDTO.setSoftwareCompativel( detalheInfoLogistica.getSoftwareCompativel() );
        detalheInfoLogisticaDTO.setMobiliarioPostoTrabalho( detalheInfoLogistica.getMobiliarioPostoTrabalho() );
        detalheInfoLogisticaDTO.setLivroReclamacao( detalheInfoLogistica.getLivroReclamacao() );
        detalheInfoLogisticaDTO.setDestruicaoPapel( detalheInfoLogistica.getDestruicaoPapel() );
        detalheInfoLogisticaDTO.setCodPartilhaImpressao( detalheInfoLogistica.getCodPartilhaImpressao() );
        detalheInfoLogisticaDTO.setTelefoneDireto( detalheInfoLogistica.getTelefoneDireto() );
        detalheInfoLogisticaDTO.setTelefoneReencaminhamento( detalheInfoLogistica.getTelefoneReencaminhamento() );
        detalheInfoLogisticaDTO.setPossuiMultifuncoes( detalheInfoLogistica.getPossuiMultifuncoes() );
        detalheInfoLogisticaDTO.setPossuiFotocopia( detalheInfoLogistica.getPossuiFotocopia() );
        detalheInfoLogisticaDTO.setPossuiImpressora( detalheInfoLogistica.getPossuiImpressora() );

        return detalheInfoLogisticaDTO;
    }

    @Override
    public DetalheInfoLogistica toEntidade(DetalheInfoLogisticaDTO detalheInfoLogisticaDTO) {
        if ( detalheInfoLogisticaDTO == null ) {
            return null;
        }

        DetalheInfoLogistica detalheInfoLogistica = new DetalheInfoLogistica();

        detalheInfoLogistica.setId( detalheInfoLogisticaDTO.getId() );
        detalheInfoLogistica.setCodSevicoCorreio( detalheInfoLogisticaDTO.getCodSevicoCorreio() );
        detalheInfoLogistica.setCodConsumivelEscritorio( detalheInfoLogisticaDTO.getCodConsumivelEscritorio() );
        detalheInfoLogistica.setCodMaterialEscritorio( detalheInfoLogisticaDTO.getCodMaterialEscritorio() );
        detalheInfoLogistica.setApoioTecnicoInformatico( detalheInfoLogisticaDTO.getApoioTecnicoInformatico() );
        detalheInfoLogistica.setMobiliarioAtendimento( detalheInfoLogisticaDTO.getMobiliarioAtendimento() );
        detalheInfoLogistica.setComputadoresSuficiente( detalheInfoLogisticaDTO.getComputadoresSuficiente() );
        detalheInfoLogistica.setInternetSuficiente( detalheInfoLogisticaDTO.getInternetSuficiente() );
        detalheInfoLogistica.setLimpeza( detalheInfoLogisticaDTO.getLimpeza() );
        detalheInfoLogistica.setManutencao( detalheInfoLogisticaDTO.getManutencao() );
        detalheInfoLogistica.setSoftwareCompativel( detalheInfoLogisticaDTO.getSoftwareCompativel() );
        detalheInfoLogistica.setMobiliarioPostoTrabalho( detalheInfoLogisticaDTO.getMobiliarioPostoTrabalho() );
        detalheInfoLogistica.setLivroReclamacao( detalheInfoLogisticaDTO.getLivroReclamacao() );
        detalheInfoLogistica.setDestruicaoPapel( detalheInfoLogisticaDTO.getDestruicaoPapel() );
        detalheInfoLogistica.setCodPartilhaImpressao( detalheInfoLogisticaDTO.getCodPartilhaImpressao() );
        detalheInfoLogistica.setTelefoneDireto( detalheInfoLogisticaDTO.getTelefoneDireto() );
        detalheInfoLogistica.setTelefoneReencaminhamento( detalheInfoLogisticaDTO.getTelefoneReencaminhamento() );
        detalheInfoLogistica.setPossuiMultifuncoes( detalheInfoLogisticaDTO.getPossuiMultifuncoes() );
        detalheInfoLogistica.setPossuiFotocopia( detalheInfoLogisticaDTO.getPossuiFotocopia() );
        detalheInfoLogistica.setPossuiImpressora( detalheInfoLogisticaDTO.getPossuiImpressora() );

        return detalheInfoLogistica;
    }
}
