package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ReuniaoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.Reuniao;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ReuniaoMapperImpl implements ReuniaoMapper {

    @Override
    public ReuniaoDTO toDTO(Reuniao reuniao) {
        if ( reuniao == null ) {
            return null;
        }

        ReuniaoDTO reuniaoDTO = new ReuniaoDTO();

        return reuniaoDTO;
    }

    @Override
    public Reuniao toEntidade(ReuniaoDTO reuniaoDTO) {
        if ( reuniaoDTO == null ) {
            return null;
        }

        Reuniao reuniao = new Reuniao();

        return reuniao;
    }
}
