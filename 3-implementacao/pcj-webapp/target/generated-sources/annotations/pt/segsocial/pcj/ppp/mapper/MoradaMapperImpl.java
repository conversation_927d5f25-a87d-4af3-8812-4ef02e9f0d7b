package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.core.vo.MoradaVO;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ConcelhoDTO;
import pt.segsocial.pcj.ppp.dto.DistritoDTO;
import pt.segsocial.pcj.ppp.dto.FreguesiaDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class MoradaMapperImpl implements MoradaMapper {

    @Override
    public MoradaDTO toDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setTerritorioPCJDTO( territorioPCJToTerritorioPCJDTO( morada.getTerritorioPCJ() ) );
        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    @Override
    public Morada toEntidade(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        Integer codigoFreguesia = moradaDTOFreguesiaDTOCodigoFreguesia( moradaDTO );
        if ( codigoFreguesia != null ) {
            morada.setFreguesia( String.valueOf( codigoFreguesia ) );
        }
        Integer codigoConcelho = moradaDTOConcelhoDtoCodigoConcelho( moradaDTO );
        if ( codigoConcelho != null ) {
            morada.setConcelho( String.valueOf( codigoConcelho ) );
        }
        Integer codigo = moradaDTODistritoDtoCodigo( moradaDTO );
        if ( codigo != null ) {
            morada.setDistrito( String.valueOf( codigo ) );
        }
        morada.setId( moradaDTO.getId() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    @Override
    public Morada voEntidade(MoradaVO moradaVO) {
        if ( moradaVO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setTerritorioPCJ( moradaVOToTerritorioPCJ( moradaVO ) );
        morada.setArteria( moradaVO.getArteria() );
        morada.setCodigoPostal( moradaVO.getCodigoPostal() );

        return morada;
    }

    @Override
    public void atualizarMoradaFromDto(MoradaDTO moradaDTO, Morada morada) {
        if ( moradaDTO == null ) {
            return;
        }

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );
    }

    @Override
    public void atualizarMoradaDTOFromEntidade(Morada morada, MoradaDTO moradaDTO) {
        if ( morada == null ) {
            return;
        }

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );
    }

    protected TerritorioPCJDTO territorioPCJToTerritorioPCJDTO(TerritorioPCJ territorioPCJ) {
        if ( territorioPCJ == null ) {
            return null;
        }

        TerritorioPCJDTO territorioPCJDTO = new TerritorioPCJDTO();

        if ( territorioPCJ.getId() != null ) {
            territorioPCJDTO.setId( territorioPCJ.getId() );
        }
        territorioPCJDTO.setCodigoDistrito( territorioPCJ.getCodigoDistrito() );
        territorioPCJDTO.setCodigoConcelho( territorioPCJ.getCodigoConcelho() );
        territorioPCJDTO.setCodigoFreguesia( territorioPCJ.getCodigoFreguesia() );

        return territorioPCJDTO;
    }

    private Integer moradaDTOFreguesiaDTOCodigoFreguesia(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }
        FreguesiaDTO freguesiaDTO = moradaDTO.getFreguesiaDTO();
        if ( freguesiaDTO == null ) {
            return null;
        }
        Integer codigoFreguesia = freguesiaDTO.getCodigoFreguesia();
        if ( codigoFreguesia == null ) {
            return null;
        }
        return codigoFreguesia;
    }

    private Integer moradaDTOConcelhoDtoCodigoConcelho(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }
        ConcelhoDTO concelhoDto = moradaDTO.getConcelhoDto();
        if ( concelhoDto == null ) {
            return null;
        }
        Integer codigoConcelho = concelhoDto.getCodigoConcelho();
        if ( codigoConcelho == null ) {
            return null;
        }
        return codigoConcelho;
    }

    private Integer moradaDTODistritoDtoCodigo(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }
        DistritoDTO distritoDto = moradaDTO.getDistritoDto();
        if ( distritoDto == null ) {
            return null;
        }
        Integer codigo = distritoDto.getCodigo();
        if ( codigo == null ) {
            return null;
        }
        return codigo;
    }

    protected TerritorioPCJ moradaVOToTerritorioPCJ(MoradaVO moradaVO) {
        if ( moradaVO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setCodigoDistrito( moradaVO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( moradaVO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( moradaVO.getCodigoFreguesia() );

        return territorioPCJ;
    }
}
