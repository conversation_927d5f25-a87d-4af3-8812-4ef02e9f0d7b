package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoRenovacaoCooptacaoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoRenovacaoCooptacao;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoRenovacaoCooptacaoMapperImpl implements BlocoRenovacaoCooptacaoMapper {

    @Override
    public BlocoRenovacaoCooptacao toEntity(BlocoRenovacaoCooptacaoDTO blocoFinalMandatoDTO) {
        if ( blocoFinalMandatoDTO == null ) {
            return null;
        }

        BlocoRenovacaoCooptacao blocoRenovacaoCooptacao = new BlocoRenovacaoCooptacao();

        blocoRenovacaoCooptacao.setId( blocoFinalMandatoDTO.getId() );
        blocoRenovacaoCooptacao.setObservacao( blocoFinalMandatoDTO.getObservacao() );
        blocoRenovacaoCooptacao.setTextoIntrodutorio( blocoFinalMandatoDTO.getTextoIntrodutorio() );

        return blocoRenovacaoCooptacao;
    }

    @Override
    public BlocoRenovacaoCooptacaoDTO toDTO(BlocoRenovacaoCooptacao blocoFinalMandato) {
        if ( blocoFinalMandato == null ) {
            return null;
        }

        BlocoRenovacaoCooptacaoDTO blocoRenovacaoCooptacaoDTO = new BlocoRenovacaoCooptacaoDTO();

        blocoRenovacaoCooptacaoDTO.setId( blocoFinalMandato.getId() );
        blocoRenovacaoCooptacaoDTO.setObservacao( blocoFinalMandato.getObservacao() );
        blocoRenovacaoCooptacaoDTO.setTextoIntrodutorio( blocoFinalMandato.getTextoIntrodutorio() );

        return blocoRenovacaoCooptacaoDTO;
    }
}
