package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ReuniaoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoRestrita;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ReuniaoRestritaMapperImpl implements ReuniaoRestritaMapper {

    @Override
    public ReuniaoRestrita toEntidade(ReuniaoDTO reuniaoRestritaDTO) {
        if ( reuniaoRestritaDTO == null ) {
            return null;
        }

        ReuniaoRestrita reuniaoRestrita = new ReuniaoRestrita();

        reuniaoRestrita.setPeriodicidadeReuniaoRestrita( reuniaoRestritaDTO.getPeriodicidadeReuniaoRestrita() );
        reuniaoRestrita.setOutraPeriodicidade( reuniaoRestritaDTO.getOutroPeriodicidadeReuniaoRestrita() );
        reuniaoRestrita.setRazaoSemPeriodicidade( reuniaoRestritaDTO.getRazaoSemPeriodicidade() );

        return reuniaoRestrita;
    }

    @Override
    public void atualizarReuniaoRestritaFromDTO(ReuniaoDTO reuniaoDTO, ReuniaoRestrita reuniaoRestrita) {
        if ( reuniaoDTO == null ) {
            return;
        }

        reuniaoRestrita.setPeriodicidadeReuniaoRestrita( reuniaoDTO.getPeriodicidadeReuniaoRestrita() );
        reuniaoRestrita.setOutraPeriodicidade( reuniaoDTO.getOutroPeriodicidadeReuniaoRestrita() );
        reuniaoRestrita.setRazaoSemPeriodicidade( reuniaoDTO.getRazaoSemPeriodicidade() );
    }
}
