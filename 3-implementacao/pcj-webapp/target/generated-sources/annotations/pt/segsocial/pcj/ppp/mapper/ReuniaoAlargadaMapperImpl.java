package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ReuniaoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoAlargada;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoRestrita;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ReuniaoAlargadaMapperImpl implements ReuniaoAlargadaMapper {

    @Override
    public ReuniaoDTO toDTO(ReuniaoAlargada reuniaoAlargada) {
        if ( reuniaoAlargada == null ) {
            return null;
        }

        ReuniaoDTO reuniaoDTO = new ReuniaoDTO();

        reuniaoDTO.setPeriodicidade( reuniaoAlargada.getPeriodicidade() );
        reuniaoDTO.setMotivoPeriodicidadeMaiorDoisMeses( reuniaoAlargada.getMotivoPeriodicidadeMaiorDoisMeses() );
        reuniaoDTO.setCodigoPeriodoReuniao( reuniaoAlargada.getCodigoPeriodoReuniao() );
        reuniaoDTO.setOutroHorario( reuniaoAlargada.getOutroHorario() );

        return reuniaoDTO;
    }

    @Override
    public ReuniaoAlargada toEntidade(ReuniaoDTO reuniaoAlargadaDTO) {
        if ( reuniaoAlargadaDTO == null ) {
            return null;
        }

        ReuniaoAlargada reuniaoAlargada = new ReuniaoAlargada();

        reuniaoAlargada.setPeriodicidade( reuniaoAlargadaDTO.getPeriodicidade() );
        reuniaoAlargada.setCodigoPeriodoReuniao( reuniaoAlargadaDTO.getCodigoPeriodoReuniao() );
        reuniaoAlargada.setOutroHorario( reuniaoAlargadaDTO.getOutroHorario() );
        reuniaoAlargada.setMotivoPeriodicidadeMaiorDoisMeses( reuniaoAlargadaDTO.getMotivoPeriodicidadeMaiorDoisMeses() );

        return reuniaoAlargada;
    }

    @Override
    public ReuniaoDTO toDTO(ReuniaoAlargada reuniaoAlargada, ReuniaoRestrita reuniaoRestrita) {
        if ( reuniaoAlargada == null && reuniaoRestrita == null ) {
            return null;
        }

        ReuniaoDTO reuniaoDTO = new ReuniaoDTO();

        if ( reuniaoAlargada != null ) {
            reuniaoDTO.setPeriodicidade( reuniaoAlargada.getPeriodicidade() );
            reuniaoDTO.setMotivoPeriodicidadeMaiorDoisMeses( reuniaoAlargada.getMotivoPeriodicidadeMaiorDoisMeses() );
            reuniaoDTO.setCodigoPeriodoReuniao( reuniaoAlargada.getCodigoPeriodoReuniao() );
            reuniaoDTO.setOutroHorario( reuniaoAlargada.getOutroHorario() );
        }
        if ( reuniaoRestrita != null ) {
            reuniaoDTO.setPeriodicidadeReuniaoRestrita( reuniaoRestrita.getPeriodicidadeReuniaoRestrita() );
            reuniaoDTO.setRazaoSemPeriodicidade( reuniaoRestrita.getRazaoSemPeriodicidade() );
            reuniaoDTO.setOutraPeriodicidade( reuniaoRestrita.getOutraPeriodicidade() );
        }

        return reuniaoDTO;
    }

    @Override
    public void atualizarReuniaoAlargadaFromDTO(ReuniaoDTO reuniaoDTO, ReuniaoAlargada reuniaoAlargada) {
        if ( reuniaoDTO == null ) {
            return;
        }

        reuniaoAlargada.setPeriodicidade( reuniaoDTO.getPeriodicidade() );
        reuniaoAlargada.setCodigoPeriodoReuniao( reuniaoDTO.getCodigoPeriodoReuniao() );
        reuniaoAlargada.setOutroHorario( reuniaoDTO.getOutroHorario() );
        reuniaoAlargada.setMotivoPeriodicidadeMaiorDoisMeses( reuniaoDTO.getMotivoPeriodicidadeMaiorDoisMeses() );
    }
}
