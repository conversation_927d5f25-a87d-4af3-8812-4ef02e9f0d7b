package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.FinancimentoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.Financiamento;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class FinanciamentoMapperImpl implements FinanciamentoMapper {

    @Override
    public FinancimentoDTO toDTO(Financiamento financiamento) {
        if ( financiamento == null ) {
            return null;
        }

        FinancimentoDTO financimentoDTO = new FinancimentoDTO();

        String codigo = financiamentoComissaoCodigo( financiamento );
        if ( codigo != null ) {
            financimentoDTO.setCodigoCpcj( codigo );
        }
        String nome = financiamentoComissaoNome( financiamento );
        if ( nome != null ) {
            financimentoDTO.setNomeCpcj( nome );
        }
        financimentoDTO.setEscalao( financiamento.getEscalao() );
        financimentoDTO.setAno( financiamento.getAno() );
        if ( financiamento.getApoioLogistico() != null ) {
            financimentoDTO.setApoioLogistico( financiamento.getApoioLogistico() );
        }
        if ( financiamento.getApoioAdministrativo() != null ) {
            financimentoDTO.setApoioAdministrativo( financiamento.getApoioAdministrativo() );
        }
        if ( financiamento.getFundoManeio() != null ) {
            financimentoDTO.setFundoManeio( financiamento.getFundoManeio() );
        }
        if ( financiamento.getSeguro() != null ) {
            financimentoDTO.setSeguro( financiamento.getSeguro() );
        }
        if ( financiamento.getPagarPrimeiroSemestre() != null ) {
            financimentoDTO.setPagarPrimeiroSemestre( financiamento.getPagarPrimeiroSemestre() );
        }
        if ( financiamento.getPagarSegundoSemestre() != null ) {
            financimentoDTO.setPagarSegundoSemestre( financiamento.getPagarSegundoSemestre() );
        }

        return financimentoDTO;
    }

    @Override
    public Financiamento toEntidade(FinancimentoDTO financimentoDto) {
        if ( financimentoDto == null ) {
            return null;
        }

        Financiamento financiamento = new Financiamento();

        if ( financimentoDto.getEscalao() != null ) {
            financiamento.setEscalao( financimentoDto.getEscalao() );
        }
        financiamento.setApoioLogistico( financimentoDto.getApoioLogistico() );
        financiamento.setApoioAdministrativo( financimentoDto.getApoioAdministrativo() );
        financiamento.setFundoManeio( financimentoDto.getFundoManeio() );
        financiamento.setSeguro( financimentoDto.getSeguro() );
        financiamento.setPagarPrimeiroSemestre( financimentoDto.getPagarPrimeiroSemestre() );
        financiamento.setPagarSegundoSemestre( financimentoDto.getPagarSegundoSemestre() );

        return financiamento;
    }

    @Override
    public List<FinancimentoDTO> toDTOs(List<Financiamento> financiamentoList) {
        if ( financiamentoList == null ) {
            return null;
        }

        List<FinancimentoDTO> list = new ArrayList<FinancimentoDTO>( financiamentoList.size() );
        for ( Financiamento financiamento : financiamentoList ) {
            list.add( toDTO( financiamento ) );
        }

        return list;
    }

    @Override
    public List<Financiamento> toEntidades(List<FinancimentoDTO> financimentoDtos) {
        if ( financimentoDtos == null ) {
            return null;
        }

        List<Financiamento> list = new ArrayList<Financiamento>( financimentoDtos.size() );
        for ( FinancimentoDTO financimentoDTO : financimentoDtos ) {
            list.add( toEntidade( financimentoDTO ) );
        }

        return list;
    }

    private String financiamentoComissaoCodigo(Financiamento financiamento) {
        if ( financiamento == null ) {
            return null;
        }
        ComissaoPCJppp comissao = financiamento.getComissao();
        if ( comissao == null ) {
            return null;
        }
        String codigo = comissao.getCodigo();
        if ( codigo == null ) {
            return null;
        }
        return codigo;
    }

    private String financiamentoComissaoNome(Financiamento financiamento) {
        if ( financiamento == null ) {
            return null;
        }
        ComissaoPCJppp comissao = financiamento.getComissao();
        if ( comissao == null ) {
            return null;
        }
        String nome = comissao.getNome();
        if ( nome == null ) {
            return null;
        }
        return nome;
    }
}
