package pt.segsocial.pcj.ppp.mapper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.HorarioDisponivel;
import pt.segsocial.pcj.ppp.dto.LocalReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.PerfilElementoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoEventoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.SalaReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.LocalReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.SalaReuniao;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:59-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ReuniaoPCJMapperImpl implements ReuniaoPCJMapper {

    @Override
    public ReuniaoPCJDTO toDTO(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoPCJDTO reuniaoPCJDTO = new ReuniaoPCJDTO();

        reuniaoPCJDTO.setId( reuniaoPCJ.getId() );
        reuniaoPCJDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoPCJDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoPCJDTO.setCodigoModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoPCJDTO.setCodigoTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoPCJDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoPCJDTO.setDataFim( reuniaoPCJ.getDataFim() );
        reuniaoPCJDTO.setHoraInicio( reuniaoPCJ.getHoraInicio() );
        reuniaoPCJDTO.setHoraFim( reuniaoPCJ.getHoraFim() );
        reuniaoPCJDTO.setElemento( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoPCJDTO.setLocalReuniao( reuniaoPCJ.getLocalReuniao() );
        reuniaoPCJDTO.setAtivo( reuniaoPCJ.isAtivo() );

        return reuniaoPCJDTO;
    }

    @Override
    public List<ReuniaoPCJDTO> toDTOs(List<ReuniaoPCJ> reunioes) {
        if ( reunioes == null ) {
            return null;
        }

        List<ReuniaoPCJDTO> list = new ArrayList<ReuniaoPCJDTO>( reunioes.size() );
        for ( ReuniaoPCJ reuniaoPCJ : reunioes ) {
            list.add( toDTO( reuniaoPCJ ) );
        }

        return list;
    }

    @Override
    public ReuniaoPCJ toEntidade(ReuniaoPCJDTO reuniaoPCJDTO) {
        if ( reuniaoPCJDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setId( reuniaoPCJDTO.getId() );
        reuniaoPCJ.setCodigoModalidade( reuniaoPCJDTO.getCodigoModalidade() );
        reuniaoPCJ.setCodigoTipo( reuniaoPCJDTO.getCodigoTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoPCJDTO.getHoraInicio() );
        reuniaoPCJ.setHoraFim( reuniaoPCJDTO.getHoraFim() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoPCJDTO.getElemento() ) );
        reuniaoPCJ.setLocalReuniao( reuniaoPCJDTO.getLocalReuniao() );
        reuniaoPCJ.setAtivo( reuniaoPCJDTO.isAtivo() );
        reuniaoPCJ.setDataFim( reuniaoPCJDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoPCJDTO.getDataInicio() );
        reuniaoPCJ.setAssunto( reuniaoPCJDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoPCJDTO.getDescricao() );

        return reuniaoPCJ;
    }

    @Override
    public ReuniaoPCJ reuniaoEventoToEntidadeMoradaCpcj(ReuniaoEventoDTO reuniaoEventoDTO) {
        if ( reuniaoEventoDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setCodigoTipo( reuniaoEventoDTO.getTipo() );
        reuniaoPCJ.setDataFim( reuniaoEventoDTO.getDataInicio() );
        Date startTime = reuniaoEventoDTOHorarioDisponivelStartTime( reuniaoEventoDTO );
        if ( startTime != null ) {
            reuniaoPCJ.setHoraInicio( startTime );
        }
        Date endTime = reuniaoEventoDTOHorarioDisponivelEndTime( reuniaoEventoDTO );
        if ( endTime != null ) {
            reuniaoPCJ.setHoraFim( endTime );
        }
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoEventoDTO.getResponsavel() ) );
        reuniaoPCJ.setCodigoModalidade( reuniaoEventoDTO.getModalidade() );
        reuniaoPCJ.setSala( salaReuniaoDTOToSalaReuniao( reuniaoEventoDTO.getSalaReuniao() ) );
        reuniaoPCJ.setId( reuniaoEventoDTO.getId() );
        reuniaoPCJ.setLocalReuniao( localReuniaoDTOToLocalReuniaoPCJ( reuniaoEventoDTO.getLocalReuniao() ) );
        reuniaoPCJ.setDataInicio( reuniaoEventoDTO.getDataInicio() );
        reuniaoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( reuniaoEventoDTO.getComissao() ) );
        reuniaoPCJ.setAssunto( reuniaoEventoDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoEventoDTO.getDescricao() );
        reuniaoPCJ.setTipoConvocatoria( reuniaoEventoDTO.getTipoConvocatoria() );

        reuniaoPCJ.setAtivo( Boolean.parseBoolean( "true" ) );

        return reuniaoPCJ;
    }

    @Override
    public ReuniaoEventoDTO reuniaoToReuniaoEventoMoradaCpcj(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoEventoDTO reuniaoEventoDTO = new ReuniaoEventoDTO();

        reuniaoEventoDTO.setHorarioDisponivel( reuniaoPCJToHorarioDisponivel( reuniaoPCJ ) );
        reuniaoEventoDTO.setTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoEventoDTO.setResponsavel( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoEventoDTO.setSalaReuniao( salaReuniaoToSalaReuniaoDTO( reuniaoPCJ.getSala() ) );
        reuniaoEventoDTO.setModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoEventoDTO.setId( reuniaoPCJ.getId() );
        reuniaoEventoDTO.setLocalReuniao( localReuniaoPCJToLocalReuniaoDTO( reuniaoPCJ.getLocalReuniao() ) );
        reuniaoEventoDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoEventoDTO.setDataFim( reuniaoPCJ.getDataFim() );
        if ( reuniaoPCJ.getHoraInicio() != null ) {
            reuniaoEventoDTO.setHoraInicio( new SimpleDateFormat().format( reuniaoPCJ.getHoraInicio() ) );
        }
        if ( reuniaoPCJ.getHoraFim() != null ) {
            reuniaoEventoDTO.setHoraFim( new SimpleDateFormat().format( reuniaoPCJ.getHoraFim() ) );
        }
        reuniaoEventoDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoEventoDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoEventoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( reuniaoPCJ.getComissao() ) );
        reuniaoEventoDTO.setTipoConvocatoria( reuniaoPCJ.getTipoConvocatoria() );

        reuniaoEventoDTO.setTipoReuniaoSelecionado( pt.segsocial.pcj.ppp.enums.TipoLocalReuniao.MORADA_CPCJ );

        return reuniaoEventoDTO;
    }

    @Override
    public ReuniaoPCJ reuniaoEventoToEntidadeOutraMorada(ReuniaoEventoDTO reuniaoEventoDTO) {
        if ( reuniaoEventoDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setCodigoTipo( reuniaoEventoDTO.getTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoEventoDTO.getHoraInicioDate() );
        reuniaoPCJ.setHoraFim( reuniaoEventoDTO.getHoraFimDate() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoEventoDTO.getResponsavel() ) );
        reuniaoPCJ.setCodigoModalidade( reuniaoEventoDTO.getModalidade() );
        reuniaoPCJ.setLocalReuniao( localReuniaoDTOToLocalReuniaoPCJ( reuniaoEventoDTO.getLocalReuniao() ) );
        reuniaoPCJ.setId( reuniaoEventoDTO.getId() );
        reuniaoPCJ.setDataFim( reuniaoEventoDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoEventoDTO.getDataInicio() );
        reuniaoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( reuniaoEventoDTO.getComissao() ) );
        reuniaoPCJ.setAssunto( reuniaoEventoDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoEventoDTO.getDescricao() );
        reuniaoPCJ.setTipoConvocatoria( reuniaoEventoDTO.getTipoConvocatoria() );

        reuniaoPCJ.setAtivo( Boolean.parseBoolean( "true" ) );

        return reuniaoPCJ;
    }

    @Override
    public ReuniaoEventoDTO reuniaoToReuniaoEventoOutraMorada(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoEventoDTO reuniaoEventoDTO = new ReuniaoEventoDTO();

        reuniaoEventoDTO.setTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoEventoDTO.setHoraFimDate( reuniaoPCJ.getHoraFim() );
        reuniaoEventoDTO.setResponsavel( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoEventoDTO.setModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoEventoDTO.setHoraInicioDate( reuniaoPCJ.getHoraInicio() );
        reuniaoEventoDTO.setId( reuniaoPCJ.getId() );
        reuniaoEventoDTO.setLocalReuniao( localReuniaoPCJToLocalReuniaoDTO( reuniaoPCJ.getLocalReuniao() ) );
        reuniaoEventoDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoEventoDTO.setDataFim( reuniaoPCJ.getDataFim() );
        if ( reuniaoPCJ.getHoraInicio() != null ) {
            reuniaoEventoDTO.setHoraInicio( new SimpleDateFormat().format( reuniaoPCJ.getHoraInicio() ) );
        }
        if ( reuniaoPCJ.getHoraFim() != null ) {
            reuniaoEventoDTO.setHoraFim( new SimpleDateFormat().format( reuniaoPCJ.getHoraFim() ) );
        }
        reuniaoEventoDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoEventoDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoEventoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( reuniaoPCJ.getComissao() ) );
        reuniaoEventoDTO.setTipoConvocatoria( reuniaoPCJ.getTipoConvocatoria() );

        reuniaoEventoDTO.setTipoReuniaoSelecionado( pt.segsocial.pcj.ppp.enums.TipoLocalReuniao.OUTRA_MORADA );

        return reuniaoEventoDTO;
    }

    @Override
    public ReuniaoPCJ reuniaoEventoToEntidadeSemLocalDefinido(ReuniaoEventoDTO reuniaoEventoDTO) {
        if ( reuniaoEventoDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setCodigoTipo( reuniaoEventoDTO.getTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoEventoDTO.getHoraInicioDate() );
        reuniaoPCJ.setHoraFim( reuniaoEventoDTO.getHoraFimDate() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoEventoDTO.getResponsavel() ) );
        reuniaoPCJ.setCodigoModalidade( reuniaoEventoDTO.getModalidade() );
        reuniaoPCJ.setId( reuniaoEventoDTO.getId() );
        reuniaoPCJ.setLocalReuniao( localReuniaoDTOToLocalReuniaoPCJ( reuniaoEventoDTO.getLocalReuniao() ) );
        reuniaoPCJ.setDataFim( reuniaoEventoDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoEventoDTO.getDataInicio() );
        reuniaoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( reuniaoEventoDTO.getComissao() ) );
        reuniaoPCJ.setAssunto( reuniaoEventoDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoEventoDTO.getDescricao() );
        reuniaoPCJ.setTipoConvocatoria( reuniaoEventoDTO.getTipoConvocatoria() );

        reuniaoPCJ.setAtivo( Boolean.parseBoolean( "true" ) );

        return reuniaoPCJ;
    }

    @Override
    public ReuniaoEventoDTO reuniaoToReuniaoEventoSemLocalDefinido(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoEventoDTO reuniaoEventoDTO = new ReuniaoEventoDTO();

        reuniaoEventoDTO.setTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoEventoDTO.setHoraFimDate( reuniaoPCJ.getHoraFim() );
        reuniaoEventoDTO.setResponsavel( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoEventoDTO.setModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoEventoDTO.setHoraInicioDate( reuniaoPCJ.getHoraInicio() );
        reuniaoEventoDTO.setId( reuniaoPCJ.getId() );
        reuniaoEventoDTO.setLocalReuniao( localReuniaoPCJToLocalReuniaoDTO( reuniaoPCJ.getLocalReuniao() ) );
        reuniaoEventoDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoEventoDTO.setDataFim( reuniaoPCJ.getDataFim() );
        if ( reuniaoPCJ.getHoraInicio() != null ) {
            reuniaoEventoDTO.setHoraInicio( new SimpleDateFormat().format( reuniaoPCJ.getHoraInicio() ) );
        }
        if ( reuniaoPCJ.getHoraFim() != null ) {
            reuniaoEventoDTO.setHoraFim( new SimpleDateFormat().format( reuniaoPCJ.getHoraFim() ) );
        }
        reuniaoEventoDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoEventoDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoEventoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( reuniaoPCJ.getComissao() ) );
        reuniaoEventoDTO.setTipoConvocatoria( reuniaoPCJ.getTipoConvocatoria() );

        reuniaoEventoDTO.setTipoReuniaoSelecionado( pt.segsocial.pcj.ppp.enums.TipoLocalReuniao.SEM_LOCAL );

        return reuniaoEventoDTO;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }

    protected PerfilElementoDTO perfilElementoToPerfilElementoDTO(PerfilElemento perfilElemento) {
        if ( perfilElemento == null ) {
            return null;
        }

        PerfilElementoDTO perfilElementoDTO = new PerfilElementoDTO();

        perfilElementoDTO.setId( perfilElemento.getId() );
        perfilElementoDTO.setInicioVigencia( perfilElemento.getInicioVigencia() );
        perfilElementoDTO.setFimVigencia( perfilElemento.getFimVigencia() );
        perfilElementoDTO.setPerfil( perfilElemento.getPerfil() );

        return perfilElementoDTO;
    }

    protected List<PerfilElementoDTO> perfilElementoListToPerfilElementoDTOList(List<PerfilElemento> list) {
        if ( list == null ) {
            return null;
        }

        List<PerfilElementoDTO> list1 = new ArrayList<PerfilElementoDTO>( list.size() );
        for ( PerfilElemento perfilElemento : list ) {
            list1.add( perfilElementoToPerfilElementoDTO( perfilElemento ) );
        }

        return list1;
    }

    protected ElementoDTO elementoToElementoDTO(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }

        ElementoDTO elementoDTO = new ElementoDTO();

        elementoDTO.setId( elemento.getId() );
        elementoDTO.setEmail( elemento.getEmail() );
        elementoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( elemento.getComissao() ) );
        elementoDTO.setTipoElemento( elemento.getTipoElemento() );
        if ( elemento.getHoraSemanal() != null ) {
            elementoDTO.setHoraSemanal( elemento.getHoraSemanal() );
        }
        if ( elemento.getHoraMensal() != null ) {
            elementoDTO.setHoraMensal( elemento.getHoraMensal() );
        }
        elementoDTO.setValenciaTecnica( elemento.getValenciaTecnica() );
        elementoDTO.setOutraValencia( elemento.getOutraValencia() );
        elementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isApoioCn() != null ) {
            elementoDTO.setApoioCn( elemento.isApoioCn() );
        }
        elementoDTO.setDataInicioVigencia( elemento.getDataInicioVigencia() );
        elementoDTO.setDataFimVigencia( elemento.getDataFimVigencia() );
        elementoDTO.setDataPrimeiraRenovacaoVigencia( elemento.getDataPrimeiraRenovacaoVigencia() );
        elementoDTO.setDataSegundaRenovacaoVigencia( elemento.getDataSegundaRenovacaoVigencia() );
        elementoDTO.setCargoMandato( elemento.getCargoMandato() );
        elementoDTO.setDataInicioMandato( elemento.getDataInicioMandato() );
        elementoDTO.setDataRenovacaoMandato( elemento.getDataRenovacaoMandato() );
        elementoDTO.setDataFimMandato( elemento.getDataFimMandato() );
        if ( elemento.isAtivo() != null ) {
            elementoDTO.setAtivo( elemento.isAtivo() );
        }
        elementoDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( elemento.getUtilizadorPCJ() ) );
        elementoDTO.setNumeroCartao( elemento.getNumeroCartao() );
        elementoDTO.setPerfisElementos( perfilElementoListToPerfilElementoDTOList( elemento.getPerfisElementos() ) );

        return elementoDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected UtilizadorPCJ utilizadorPCJDTOToUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorPCJ utilizadorPCJ = new UtilizadorPCJ();

        utilizadorPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );

        return utilizadorPCJ;
    }

    protected Elemento elementoDTOToElemento(ElementoDTO elementoDTO) {
        if ( elementoDTO == null ) {
            return null;
        }

        Elemento elemento = new Elemento();

        elemento.setId( elementoDTO.getId() );
        elemento.setEmail( elementoDTO.getEmail() );
        elemento.setTipoElemento( elementoDTO.getTipoElemento() );
        elemento.setHoraSemanal( elementoDTO.getHoraSemanal() );
        elemento.setHoraMensal( elementoDTO.getHoraMensal() );
        elemento.setValenciaTecnica( elementoDTO.getValenciaTecnica() );
        elemento.setOutraValencia( elementoDTO.getOutraValencia() );
        elemento.setEntidade( elementoDTO.getEntidade() );
        elemento.setApoioCn( elementoDTO.isApoioCn() );
        elemento.setDataInicioVigencia( elementoDTO.getDataInicioVigencia() );
        elemento.setDataFimVigencia( elementoDTO.getDataFimVigencia() );
        elemento.setDataPrimeiraRenovacaoVigencia( elementoDTO.getDataPrimeiraRenovacaoVigencia() );
        elemento.setDataSegundaRenovacaoVigencia( elementoDTO.getDataSegundaRenovacaoVigencia() );
        elemento.setCargoMandato( elementoDTO.getCargoMandato() );
        elemento.setDataInicioMandato( elementoDTO.getDataInicioMandato() );
        elemento.setDataRenovacaoMandato( elementoDTO.getDataRenovacaoMandato() );
        elemento.setDataFimMandato( elementoDTO.getDataFimMandato() );
        elemento.setAtivo( elementoDTO.isAtivo() );
        elemento.setComissao( comissaoPCJDTOToComissaoPCJppp( elementoDTO.getComissao() ) );
        elemento.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( elementoDTO.getUtilizadorPCJ() ) );
        elemento.setNumeroCartao( elementoDTO.getNumeroCartao() );

        return elemento;
    }

    private Date reuniaoEventoDTOHorarioDisponivelStartTime(ReuniaoEventoDTO reuniaoEventoDTO) {
        if ( reuniaoEventoDTO == null ) {
            return null;
        }
        HorarioDisponivel horarioDisponivel = reuniaoEventoDTO.getHorarioDisponivel();
        if ( horarioDisponivel == null ) {
            return null;
        }
        Date startTime = horarioDisponivel.getStartTime();
        if ( startTime == null ) {
            return null;
        }
        return startTime;
    }

    private Date reuniaoEventoDTOHorarioDisponivelEndTime(ReuniaoEventoDTO reuniaoEventoDTO) {
        if ( reuniaoEventoDTO == null ) {
            return null;
        }
        HorarioDisponivel horarioDisponivel = reuniaoEventoDTO.getHorarioDisponivel();
        if ( horarioDisponivel == null ) {
            return null;
        }
        Date endTime = horarioDisponivel.getEndTime();
        if ( endTime == null ) {
            return null;
        }
        return endTime;
    }

    protected Set<ReuniaoPCJ> reuniaoPCJDTOSetToReuniaoPCJSet(Set<ReuniaoPCJDTO> set) {
        if ( set == null ) {
            return null;
        }

        Set<ReuniaoPCJ> set1 = new HashSet<ReuniaoPCJ>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( ReuniaoPCJDTO reuniaoPCJDTO : set ) {
            set1.add( toEntidade( reuniaoPCJDTO ) );
        }

        return set1;
    }

    protected SalaReuniao salaReuniaoDTOToSalaReuniao(SalaReuniaoDTO salaReuniaoDTO) {
        if ( salaReuniaoDTO == null ) {
            return null;
        }

        SalaReuniao salaReuniao = new SalaReuniao();

        salaReuniao.setId( salaReuniaoDTO.getId() );
        salaReuniao.setNome( salaReuniaoDTO.getNome() );
        salaReuniao.setCapacidade( salaReuniaoDTO.getCapacidade() );
        salaReuniao.setObservacao( salaReuniaoDTO.getObservacao() );
        salaReuniao.setTipoSala( salaReuniaoDTO.getTipoSala() );
        salaReuniao.setComissao( comissaoPCJDTOToComissaoPCJppp( salaReuniaoDTO.getComissao() ) );
        salaReuniao.setStatusFuncionamento( salaReuniaoDTO.getStatusFuncionamento() );
        salaReuniao.setSalaAcustica( salaReuniaoDTO.getSalaAcustica() );
        salaReuniao.setEquipamentoAudio( salaReuniaoDTO.getEquipamentoAudio() );
        salaReuniao.setEspelhoUnidirecional( salaReuniaoDTO.getEspelhoUnidirecional() );
        salaReuniao.setRecursosAudicao( salaReuniaoDTO.getRecursosAudicao() );
        salaReuniao.setLudicoPedagogico( salaReuniaoDTO.getLudicoPedagogico() );
        salaReuniao.setUtilizacaoExterna( salaReuniaoDTO.getUtilizacaoExterna() );
        salaReuniao.setEquipamentoVideo( salaReuniaoDTO.getEquipamentoVideo() );
        salaReuniao.setSalaSegura( salaReuniaoDTO.getSalaSegura() );
        salaReuniao.setSalaAcolhedora( salaReuniaoDTO.getSalaAcolhedora() );
        salaReuniao.setComportaTodasIdades( salaReuniaoDTO.getComportaTodasIdades() );
        salaReuniao.setCapacidadeImpressao( salaReuniaoDTO.getCapacidadeImpressao() );
        salaReuniao.setReunioes( reuniaoPCJDTOSetToReuniaoPCJSet( salaReuniaoDTO.getReunioes() ) );

        return salaReuniao;
    }

    protected LocalReuniaoPCJ localReuniaoDTOToLocalReuniaoPCJ(LocalReuniaoDTO localReuniaoDTO) {
        if ( localReuniaoDTO == null ) {
            return null;
        }

        LocalReuniaoPCJ localReuniaoPCJ = new LocalReuniaoPCJ();

        localReuniaoPCJ.setId( localReuniaoDTO.getId() );
        localReuniaoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( localReuniaoDTO.getComissao() ) );
        localReuniaoPCJ.setTitulo( localReuniaoDTO.getTitulo() );
        localReuniaoPCJ.setDescricao( localReuniaoDTO.getDescricao() );

        return localReuniaoPCJ;
    }

    protected HorarioDisponivel reuniaoPCJToHorarioDisponivel(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        HorarioDisponivel horarioDisponivel = new HorarioDisponivel();

        horarioDisponivel.setStartTime( reuniaoPCJ.getHoraInicio() );
        horarioDisponivel.setEndTime( reuniaoPCJ.getHoraFim() );

        return horarioDisponivel;
    }

    protected Set<ReuniaoPCJDTO> reuniaoPCJSetToReuniaoPCJDTOSet(Set<ReuniaoPCJ> set) {
        if ( set == null ) {
            return null;
        }

        Set<ReuniaoPCJDTO> set1 = new HashSet<ReuniaoPCJDTO>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( ReuniaoPCJ reuniaoPCJ : set ) {
            set1.add( toDTO( reuniaoPCJ ) );
        }

        return set1;
    }

    protected SalaReuniaoDTO salaReuniaoToSalaReuniaoDTO(SalaReuniao salaReuniao) {
        if ( salaReuniao == null ) {
            return null;
        }

        SalaReuniaoDTO salaReuniaoDTO = new SalaReuniaoDTO();

        salaReuniaoDTO.setNome( salaReuniao.getNome() );
        salaReuniaoDTO.setCapacidade( salaReuniao.getCapacidade() );
        salaReuniaoDTO.setObservacao( salaReuniao.getObservacao() );
        salaReuniaoDTO.setTipoSala( salaReuniao.getTipoSala() );
        salaReuniaoDTO.setStatusFuncionamento( salaReuniao.getStatusFuncionamento() );
        salaReuniaoDTO.setSalaAcustica( salaReuniao.getSalaAcustica() );
        salaReuniaoDTO.setEquipamentoAudio( salaReuniao.getEquipamentoAudio() );
        salaReuniaoDTO.setEspelhoUnidirecional( salaReuniao.getEspelhoUnidirecional() );
        salaReuniaoDTO.setRecursosAudicao( salaReuniao.getRecursosAudicao() );
        salaReuniaoDTO.setLudicoPedagogico( salaReuniao.getLudicoPedagogico() );
        salaReuniaoDTO.setUtilizacaoExterna( salaReuniao.getUtilizacaoExterna() );
        salaReuniaoDTO.setEquipamentoVideo( salaReuniao.getEquipamentoVideo() );
        salaReuniaoDTO.setSalaSegura( salaReuniao.getSalaSegura() );
        salaReuniaoDTO.setSalaAcolhedora( salaReuniao.getSalaAcolhedora() );
        salaReuniaoDTO.setComportaTodasIdades( salaReuniao.getComportaTodasIdades() );
        salaReuniaoDTO.setCapacidadeImpressao( salaReuniao.getCapacidadeImpressao() );
        salaReuniaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( salaReuniao.getComissao() ) );
        salaReuniaoDTO.setId( salaReuniao.getId() );
        salaReuniaoDTO.setReunioes( reuniaoPCJSetToReuniaoPCJDTOSet( salaReuniao.getReunioes() ) );

        return salaReuniaoDTO;
    }

    protected LocalReuniaoDTO localReuniaoPCJToLocalReuniaoDTO(LocalReuniaoPCJ localReuniaoPCJ) {
        if ( localReuniaoPCJ == null ) {
            return null;
        }

        LocalReuniaoDTO localReuniaoDTO = new LocalReuniaoDTO();

        localReuniaoDTO.setId( localReuniaoPCJ.getId() );
        localReuniaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( localReuniaoPCJ.getComissao() ) );
        localReuniaoDTO.setDescricao( localReuniaoPCJ.getDescricao() );
        localReuniaoDTO.setTitulo( localReuniaoPCJ.getTitulo() );

        return localReuniaoDTO;
    }
}
