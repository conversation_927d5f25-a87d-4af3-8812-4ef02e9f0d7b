package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.HistoricoElementoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.HistoricoElemento;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class HistoricoElementoMapperImpl implements HistoricoElementoMapper {

    @Override
    public HistoricoElementoDTO toDTO(HistoricoElemento historicoElemento) {
        if ( historicoElemento == null ) {
            return null;
        }

        HistoricoElementoDTO historicoElementoDTO = new HistoricoElementoDTO();

        historicoElementoDTO.setData( historicoElemento.getData() );
        historicoElementoDTO.setDescricao( historicoElemento.getDescricao() );
        historicoElementoDTO.setElemento( historicoElemento.getElemento() );

        return historicoElementoDTO;
    }

    @Override
    public HistoricoElemento toEntidade(HistoricoElementoDTO historicoElementoDTO) {
        if ( historicoElementoDTO == null ) {
            return null;
        }

        HistoricoElemento historicoElemento = new HistoricoElemento();

        historicoElemento.setElemento( historicoElementoDTO.getElemento() );
        historicoElemento.setDescricao( historicoElementoDTO.getDescricao() );
        historicoElemento.setData( historicoElementoDTO.getData() );

        return historicoElemento;
    }

    @Override
    public List<HistoricoElementoDTO> toDTOs(List<HistoricoElemento> historicosElemento) {
        if ( historicosElemento == null ) {
            return null;
        }

        List<HistoricoElementoDTO> list = new ArrayList<HistoricoElementoDTO>( historicosElemento.size() );
        for ( HistoricoElemento historicoElemento : historicosElemento ) {
            list.add( toDTO( historicoElemento ) );
        }

        return list;
    }

    @Override
    public List<HistoricoElemento> toEntidades(List<HistoricoElementoDTO> historicosElemento) {
        if ( historicosElemento == null ) {
            return null;
        }

        List<HistoricoElemento> list = new ArrayList<HistoricoElemento>( historicosElemento.size() );
        for ( HistoricoElementoDTO historicoElementoDTO : historicosElemento ) {
            list.add( toEntidade( historicoElementoDTO ) );
        }

        return list;
    }
}
