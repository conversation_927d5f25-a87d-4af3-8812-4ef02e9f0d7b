package pt.segsocial.pcj.ppp.mapper.ata;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.PerfilElementoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:55-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ReuniaoAtaMapperImpl implements ReuniaoAtaMapper {

    @Override
    public ReuniaoPCJDTO toDTO(ReuniaoPCJ reuniao) {
        if ( reuniao == null ) {
            return null;
        }

        ReuniaoPCJDTO reuniaoPCJDTO = new ReuniaoPCJDTO();

        reuniaoPCJDTO.setId( reuniao.getId() );
        reuniaoPCJDTO.setAssunto( reuniao.getAssunto() );
        reuniaoPCJDTO.setDescricao( reuniao.getDescricao() );
        reuniaoPCJDTO.setCodigoModalidade( reuniao.getCodigoModalidade() );
        reuniaoPCJDTO.setCodigoTipo( reuniao.getCodigoTipo() );
        reuniaoPCJDTO.setDataInicio( reuniao.getDataInicio() );
        reuniaoPCJDTO.setDataFim( reuniao.getDataFim() );
        reuniaoPCJDTO.setHoraInicio( reuniao.getHoraInicio() );
        reuniaoPCJDTO.setHoraFim( reuniao.getHoraFim() );
        reuniaoPCJDTO.setElemento( elementoToElementoDTO( reuniao.getElemento() ) );
        reuniaoPCJDTO.setLocalReuniao( reuniao.getLocalReuniao() );
        reuniaoPCJDTO.setAtivo( reuniao.isAtivo() );

        return reuniaoPCJDTO;
    }

    @Override
    public ReuniaoPCJ toEntidade(ReuniaoPCJDTO reuniaoDTO) {
        if ( reuniaoDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setId( reuniaoDTO.getId() );
        reuniaoPCJ.setCodigoModalidade( reuniaoDTO.getCodigoModalidade() );
        reuniaoPCJ.setCodigoTipo( reuniaoDTO.getCodigoTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoDTO.getHoraInicio() );
        reuniaoPCJ.setHoraFim( reuniaoDTO.getHoraFim() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoDTO.getElemento() ) );
        reuniaoPCJ.setLocalReuniao( reuniaoDTO.getLocalReuniao() );
        reuniaoPCJ.setAtivo( reuniaoDTO.isAtivo() );
        reuniaoPCJ.setDataFim( reuniaoDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoDTO.getDataInicio() );
        reuniaoPCJ.setAssunto( reuniaoDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoDTO.getDescricao() );

        return reuniaoPCJ;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }

    protected PerfilElementoDTO perfilElementoToPerfilElementoDTO(PerfilElemento perfilElemento) {
        if ( perfilElemento == null ) {
            return null;
        }

        PerfilElementoDTO perfilElementoDTO = new PerfilElementoDTO();

        perfilElementoDTO.setId( perfilElemento.getId() );
        perfilElementoDTO.setInicioVigencia( perfilElemento.getInicioVigencia() );
        perfilElementoDTO.setFimVigencia( perfilElemento.getFimVigencia() );
        perfilElementoDTO.setPerfil( perfilElemento.getPerfil() );

        return perfilElementoDTO;
    }

    protected List<PerfilElementoDTO> perfilElementoListToPerfilElementoDTOList(List<PerfilElemento> list) {
        if ( list == null ) {
            return null;
        }

        List<PerfilElementoDTO> list1 = new ArrayList<PerfilElementoDTO>( list.size() );
        for ( PerfilElemento perfilElemento : list ) {
            list1.add( perfilElementoToPerfilElementoDTO( perfilElemento ) );
        }

        return list1;
    }

    protected ElementoDTO elementoToElementoDTO(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }

        ElementoDTO elementoDTO = new ElementoDTO();

        elementoDTO.setId( elemento.getId() );
        elementoDTO.setEmail( elemento.getEmail() );
        elementoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( elemento.getComissao() ) );
        elementoDTO.setTipoElemento( elemento.getTipoElemento() );
        if ( elemento.getHoraSemanal() != null ) {
            elementoDTO.setHoraSemanal( elemento.getHoraSemanal() );
        }
        if ( elemento.getHoraMensal() != null ) {
            elementoDTO.setHoraMensal( elemento.getHoraMensal() );
        }
        elementoDTO.setValenciaTecnica( elemento.getValenciaTecnica() );
        elementoDTO.setOutraValencia( elemento.getOutraValencia() );
        elementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isApoioCn() != null ) {
            elementoDTO.setApoioCn( elemento.isApoioCn() );
        }
        elementoDTO.setDataInicioVigencia( elemento.getDataInicioVigencia() );
        elementoDTO.setDataFimVigencia( elemento.getDataFimVigencia() );
        elementoDTO.setDataPrimeiraRenovacaoVigencia( elemento.getDataPrimeiraRenovacaoVigencia() );
        elementoDTO.setDataSegundaRenovacaoVigencia( elemento.getDataSegundaRenovacaoVigencia() );
        elementoDTO.setCargoMandato( elemento.getCargoMandato() );
        elementoDTO.setDataInicioMandato( elemento.getDataInicioMandato() );
        elementoDTO.setDataRenovacaoMandato( elemento.getDataRenovacaoMandato() );
        elementoDTO.setDataFimMandato( elemento.getDataFimMandato() );
        if ( elemento.isAtivo() != null ) {
            elementoDTO.setAtivo( elemento.isAtivo() );
        }
        elementoDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( elemento.getUtilizadorPCJ() ) );
        elementoDTO.setNumeroCartao( elemento.getNumeroCartao() );
        elementoDTO.setPerfisElementos( perfilElementoListToPerfilElementoDTOList( elemento.getPerfisElementos() ) );

        return elementoDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected UtilizadorPCJ utilizadorPCJDTOToUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorPCJ utilizadorPCJ = new UtilizadorPCJ();

        utilizadorPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );

        return utilizadorPCJ;
    }

    protected Elemento elementoDTOToElemento(ElementoDTO elementoDTO) {
        if ( elementoDTO == null ) {
            return null;
        }

        Elemento elemento = new Elemento();

        elemento.setId( elementoDTO.getId() );
        elemento.setEmail( elementoDTO.getEmail() );
        elemento.setTipoElemento( elementoDTO.getTipoElemento() );
        elemento.setHoraSemanal( elementoDTO.getHoraSemanal() );
        elemento.setHoraMensal( elementoDTO.getHoraMensal() );
        elemento.setValenciaTecnica( elementoDTO.getValenciaTecnica() );
        elemento.setOutraValencia( elementoDTO.getOutraValencia() );
        elemento.setEntidade( elementoDTO.getEntidade() );
        elemento.setApoioCn( elementoDTO.isApoioCn() );
        elemento.setDataInicioVigencia( elementoDTO.getDataInicioVigencia() );
        elemento.setDataFimVigencia( elementoDTO.getDataFimVigencia() );
        elemento.setDataPrimeiraRenovacaoVigencia( elementoDTO.getDataPrimeiraRenovacaoVigencia() );
        elemento.setDataSegundaRenovacaoVigencia( elementoDTO.getDataSegundaRenovacaoVigencia() );
        elemento.setCargoMandato( elementoDTO.getCargoMandato() );
        elemento.setDataInicioMandato( elementoDTO.getDataInicioMandato() );
        elemento.setDataRenovacaoMandato( elementoDTO.getDataRenovacaoMandato() );
        elemento.setDataFimMandato( elementoDTO.getDataFimMandato() );
        elemento.setAtivo( elementoDTO.isAtivo() );
        elemento.setComissao( comissaoPCJDTOToComissaoPCJppp( elementoDTO.getComissao() ) );
        elemento.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( elementoDTO.getUtilizadorPCJ() ) );
        elemento.setNumeroCartao( elementoDTO.getNumeroCartao() );

        return elemento;
    }
}
