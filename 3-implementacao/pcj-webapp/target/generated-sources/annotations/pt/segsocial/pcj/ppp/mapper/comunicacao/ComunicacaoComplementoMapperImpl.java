package pt.segsocial.pcj.ppp.mapper.comunicacao;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoComplementoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoDTO;
import pt.segsocial.pcj.ppp.enums.RececaoExpedienteEnum;
import pt.segsocial.pcj.ppp.enums.TipoParticipanteEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.ComunicacaoComplementoPPP;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class ComunicacaoComplementoMapperImpl implements ComunicacaoComplementoMapper {

    @Override
    public ComunicacaoComplementoDTO toDTO(ComunicacaoComplementoPPP comunicacaoComplementoPPP) {
        if ( comunicacaoComplementoPPP == null ) {
            return null;
        }

        ComunicacaoComplementoDTO comunicacaoComplementoDTO = new ComunicacaoComplementoDTO();

        if ( comunicacaoComplementoPPP.getHora() != null ) {
            comunicacaoComplementoDTO.setHoraFormatada( new SimpleDateFormat( "HH:mm" ).format( comunicacaoComplementoPPP.getHora() ) );
        }
        comunicacaoComplementoDTO.setComunicacao( comunicacaoToComunicacaoDTO( comunicacaoComplementoPPP.getComunicacao() ) );
        comunicacaoComplementoDTO.setCodigoTipoContacto( comunicacaoComplementoPPP.getCodigoTipoContacto() );
        comunicacaoComplementoDTO.setData( comunicacaoComplementoPPP.getData() );
        comunicacaoComplementoDTO.setHora( comunicacaoComplementoPPP.getHora() );
        comunicacaoComplementoDTO.setPessoaContactada( comunicacaoComplementoPPP.getPessoaContactada() );
        comunicacaoComplementoDTO.setCodigoEntidadeParticipante( comunicacaoComplementoPPP.getCodigoEntidadeParticipante() );
        comunicacaoComplementoDTO.setDetalheEntidade( comunicacaoComplementoPPP.getDetalheEntidade() );
        comunicacaoComplementoDTO.setDescricao( comunicacaoComplementoPPP.getDescricao() );
        comunicacaoComplementoDTO.setParticipanteFamiliar( comunicacaoComplementoPPP.getParticipanteFamiliar() );
        comunicacaoComplementoDTO.setCodigoParticipanteParentesco( comunicacaoComplementoPPP.getCodigoParticipanteParentesco() );
        comunicacaoComplementoDTO.setId( comunicacaoComplementoPPP.getId() );

        return comunicacaoComplementoDTO;
    }

    @Override
    public ComunicacaoComplementoPPP toEntity(ComunicacaoComplementoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ComunicacaoComplementoPPP comunicacaoComplementoPPP = new ComunicacaoComplementoPPP();

        try {
            if ( dto.getHoraFormatada() != null ) {
                comunicacaoComplementoPPP.setHora( new SimpleDateFormat( "HH:mm" ).parse( dto.getHoraFormatada() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        comunicacaoComplementoPPP.setId( dto.getId() );
        comunicacaoComplementoPPP.setComunicacao( comunicacaoDTOToComunicacao( dto.getComunicacao() ) );
        comunicacaoComplementoPPP.setCodigoTipoContacto( dto.getCodigoTipoContacto() );
        comunicacaoComplementoPPP.setData( dto.getData() );
        comunicacaoComplementoPPP.setPessoaContactada( dto.getPessoaContactada() );
        comunicacaoComplementoPPP.setCodigoEntidadeParticipante( dto.getCodigoEntidadeParticipante() );
        comunicacaoComplementoPPP.setDetalheEntidade( dto.getDetalheEntidade() );
        comunicacaoComplementoPPP.setDescricao( dto.getDescricao() );
        comunicacaoComplementoPPP.setParticipanteFamiliar( dto.getParticipanteFamiliar() );
        comunicacaoComplementoPPP.setCodigoParticipanteParentesco( dto.getCodigoParticipanteParentesco() );

        return comunicacaoComplementoPPP;
    }

    @Override
    public List<ComunicacaoComplementoDTO> toDTOList(List<ComunicacaoComplementoPPP> comunicacaoComplementoPPPS) {
        if ( comunicacaoComplementoPPPS == null ) {
            return null;
        }

        List<ComunicacaoComplementoDTO> list = new ArrayList<ComunicacaoComplementoDTO>( comunicacaoComplementoPPPS.size() );
        for ( ComunicacaoComplementoPPP comunicacaoComplementoPPP : comunicacaoComplementoPPPS ) {
            list.add( toDTO( comunicacaoComplementoPPP ) );
        }

        return list;
    }

    @Override
    public List<ComunicacaoComplementoPPP> toEntities(List<ComunicacaoComplementoDTO> comunicacaoComplementoDTOS) {
        if ( comunicacaoComplementoDTOS == null ) {
            return null;
        }

        List<ComunicacaoComplementoPPP> list = new ArrayList<ComunicacaoComplementoPPP>( comunicacaoComplementoDTOS.size() );
        for ( ComunicacaoComplementoDTO comunicacaoComplementoDTO : comunicacaoComplementoDTOS ) {
            list.add( toEntity( comunicacaoComplementoDTO ) );
        }

        return list;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected ComunicacaoDTO comunicacaoToComunicacaoDTO(Comunicacao comunicacao) {
        if ( comunicacao == null ) {
            return null;
        }

        ComunicacaoDTO comunicacaoDTO = new ComunicacaoDTO();

        comunicacaoDTO.setCodigoEstado( comunicacao.getCodigoEstado() );
        comunicacaoDTO.setCodigoEstadoAnalise( comunicacao.getCodigoEstadoAnalise() );
        comunicacaoDTO.setCodigoModalidadeContato( comunicacao.getCodigoModalidadeContato() );
        comunicacaoDTO.setCodigoParticipanteParentesco( comunicacao.getCodigoParticipanteParentesco() );
        comunicacaoDTO.setCodigoParticipanteRelacao( comunicacao.getCodigoParticipanteRelacao() );
        comunicacaoDTO.setDataRececao( comunicacao.getDataRececao() );
        comunicacaoDTO.setDataRegisto( comunicacao.getDataRegisto() );
        comunicacaoDTO.setDeliberado( comunicacao.getDeliberado() );
        comunicacaoDTO.setExpedienteAssociado( comunicacao.getExpedienteAssociado() );
        comunicacaoDTO.setId( comunicacao.getId() );
        comunicacaoDTO.setOutrasObservacoes( comunicacao.getOutrasObservacoes() );
        comunicacaoDTO.setParticipanteAnonimo( comunicacao.getParticipanteAnonimo() );
        comunicacaoDTO.setParticipanteCrianca( comunicacao.getParticipanteCrianca() );
        comunicacaoDTO.setParticipanteFamiliar( comunicacao.getParticipanteFamiliar() );
        if ( comunicacao.getRececaoExpediente() != null ) {
            comunicacaoDTO.setRececaoExpediente( comunicacao.getRececaoExpediente().name() );
        }
        if ( comunicacao.getTipoParticipante() != null ) {
            comunicacaoDTO.setTipoParticipante( comunicacao.getTipoParticipante().name() );
        }
        comunicacaoDTO.setNumeroComunicacao( comunicacao.getNumeroComunicacao() );
        comunicacaoDTO.setUrgente( comunicacao.getUrgente() );
        comunicacaoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( comunicacao.getComissao() ) );

        return comunicacaoDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected Comunicacao comunicacaoDTOToComunicacao(ComunicacaoDTO comunicacaoDTO) {
        if ( comunicacaoDTO == null ) {
            return null;
        }

        Comunicacao comunicacao = new Comunicacao();

        comunicacao.setId( comunicacaoDTO.getId() );
        comunicacao.setNumeroComunicacao( comunicacaoDTO.getNumeroComunicacao() );
        comunicacao.setComissao( comissaoPCJDTOToComissaoPCJppp( comunicacaoDTO.getComissao() ) );
        comunicacao.setCodigoModalidadeContato( comunicacaoDTO.getCodigoModalidadeContato() );
        comunicacao.setExpedienteAssociado( comunicacaoDTO.getExpedienteAssociado() );
        if ( comunicacaoDTO.getTipoParticipante() != null ) {
            comunicacao.setTipoParticipante( Enum.valueOf( TipoParticipanteEnum.class, comunicacaoDTO.getTipoParticipante() ) );
        }
        comunicacao.setParticipanteAnonimo( comunicacaoDTO.getParticipanteAnonimo() );
        comunicacao.setParticipanteCrianca( comunicacaoDTO.getParticipanteCrianca() );
        comunicacao.setParticipanteFamiliar( comunicacaoDTO.getParticipanteFamiliar() );
        comunicacao.setCodigoParticipanteParentesco( comunicacaoDTO.getCodigoParticipanteParentesco() );
        comunicacao.setCodigoParticipanteRelacao( comunicacaoDTO.getCodigoParticipanteRelacao() );
        comunicacao.setOutrasObservacoes( comunicacaoDTO.getOutrasObservacoes() );
        comunicacao.setDataRececao( comunicacaoDTO.getDataRececao() );
        comunicacao.setDataRegisto( comunicacaoDTO.getDataRegisto() );
        if ( comunicacaoDTO.getRececaoExpediente() != null ) {
            comunicacao.setRececaoExpediente( Enum.valueOf( RececaoExpedienteEnum.class, comunicacaoDTO.getRececaoExpediente() ) );
        }
        comunicacao.setCodigoEstado( comunicacaoDTO.getCodigoEstado() );
        comunicacao.setDeliberado( comunicacaoDTO.getDeliberado() );
        comunicacao.setCodigoEstadoAnalise( comunicacaoDTO.getCodigoEstadoAnalise() );
        comunicacao.setUrgente( comunicacaoDTO.getUrgente() );

        return comunicacao;
    }
}
