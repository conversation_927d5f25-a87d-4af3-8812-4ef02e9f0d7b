package pt.segsocial.pcj.ppp.jpa.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Agrupamento.class)
public abstract class Agrupamento_ extends pt.segsocial.fraw.jpa.DefaultPersistentDomainObject_ {

	public static volatile ListAttribute<Agrupamento, ComissaoPCJppp> comissoes;
	public static volatile SingularAttribute<Agrupamento, Date> dataInsercao;
	public static volatile SingularAttribute<Agrupamento, String> nome;

}

