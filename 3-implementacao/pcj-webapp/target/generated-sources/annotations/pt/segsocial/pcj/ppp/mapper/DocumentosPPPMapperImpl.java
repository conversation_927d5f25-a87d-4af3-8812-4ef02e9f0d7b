package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentosPPPDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentosPPP;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:00-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DocumentosPPPMapperImpl implements DocumentosPPPMapper {

    @Override
    public DocumentosPPPDTO toDTO(DocumentosPPP documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentosPPPDTO documentosPPPDTO = new DocumentosPPPDTO();

        documentosPPPDTO.setTipoDocumento( documentoPCJ.getTipoDocumento() );
        documentosPPPDTO.setId( documentoPCJ.getId() );
        documentosPPPDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentosPPPDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentosPPPDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentosPPPDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentosPPPDTO;
    }

    @Override
    public DocumentosPPP toEntidade(DocumentosPPPDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentosPPP documentosPPP = new DocumentosPPP();

        documentosPPP.setId( documentoDTO.getId() );
        documentosPPP.setDataUpload( documentoDTO.getDataUpload() );
        documentosPPP.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentosPPP.setNomeDocumento( documentoDTO.getNomeDocumento() );
        documentosPPP.setTipoDocumento( documentoDTO.getTipoDocumento() );
        documentosPPP.setUploadDocumento( documentoDTO.getUploadDocumento() );

        return documentosPPP;
    }

    @Override
    public List<DocumentosPPP> toEntidades(List<DocumentosPPPDTO> documentosDTO) {
        if ( documentosDTO == null ) {
            return null;
        }

        List<DocumentosPPP> list = new ArrayList<DocumentosPPP>( documentosDTO.size() );
        for ( DocumentosPPPDTO documentosPPPDTO : documentosDTO ) {
            list.add( toEntidade( documentosPPPDTO ) );
        }

        return list;
    }

    @Override
    public List<DocumentosPPPDTO> toDTOs(List<DocumentosPPP> documentosPPPS) {
        if ( documentosPPPS == null ) {
            return null;
        }

        List<DocumentosPPPDTO> list = new ArrayList<DocumentosPPPDTO>( documentosPPPS.size() );
        for ( DocumentosPPP documentosPPP : documentosPPPS ) {
            list.add( toDTO( documentosPPP ) );
        }

        return list;
    }
}
