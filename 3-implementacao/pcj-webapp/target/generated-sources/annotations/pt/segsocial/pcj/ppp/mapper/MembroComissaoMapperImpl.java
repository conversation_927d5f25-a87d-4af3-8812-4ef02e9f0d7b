package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.MembroComissaoDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.PerfilElementoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.MembroComissao;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:00-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class MembroComissaoMapperImpl implements MembroComissaoMapper {

    @Inject
    private MembroComissaoIdMapper membroComissaoIdMapper;

    @Override
    public MembroComissaoDTO toDTO(MembroComissao membroComissao) {
        if ( membroComissao == null ) {
            return null;
        }

        MembroComissaoDTO membroComissaoDTO = new MembroComissaoDTO();

        membroComissaoDTO.setId( membroComissaoIdMapper.toDTO( membroComissao.getId() ) );
        membroComissaoDTO.setReuniaoPCJ( reuniaoPCJToReuniaoPCJDTO( membroComissao.getReuniaoPCJ() ) );
        membroComissaoDTO.setElemento( elementoToElementoDTO( membroComissao.getElemento() ) );

        return membroComissaoDTO;
    }

    @Override
    public MembroComissao toEntidade(MembroComissaoDTO membroComissaoDTO) {
        if ( membroComissaoDTO == null ) {
            return null;
        }

        MembroComissao membroComissao = new MembroComissao();

        membroComissao.setId( membroComissaoIdMapper.toEntidade( membroComissaoDTO.getId() ) );
        membroComissao.setReuniaoPCJ( reuniaoPCJDTOToReuniaoPCJ( membroComissaoDTO.getReuniaoPCJ() ) );
        membroComissao.setElemento( elementoDTOToElemento( membroComissaoDTO.getElemento() ) );

        return membroComissao;
    }

    @Override
    public List<MembroComissaoDTO> toDTOs(List<MembroComissao> membroList) {
        if ( membroList == null ) {
            return null;
        }

        List<MembroComissaoDTO> list = new ArrayList<MembroComissaoDTO>( membroList.size() );
        for ( MembroComissao membroComissao : membroList ) {
            list.add( toDTO( membroComissao ) );
        }

        return list;
    }

    @Override
    public List<MembroComissao> toEntidades(List<MembroComissaoDTO> membroDTOList) {
        if ( membroDTOList == null ) {
            return null;
        }

        List<MembroComissao> list = new ArrayList<MembroComissao>( membroDTOList.size() );
        for ( MembroComissaoDTO membroComissaoDTO : membroDTOList ) {
            list.add( toEntidade( membroComissaoDTO ) );
        }

        return list;
    }

    @Override
    public MembroComissao toUpdate(MembroComissao membroDTO) {
        if ( membroDTO == null ) {
            return null;
        }

        MembroComissao membroComissao = new MembroComissao();

        membroComissao.setFieldHandler( membroDTO.getFieldHandler() );
        membroComissao.setId( membroDTO.getId() );
        membroComissao.setReuniaoPCJ( membroDTO.getReuniaoPCJ() );
        membroComissao.setElemento( membroDTO.getElemento() );

        return membroComissao;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }

    protected PerfilElementoDTO perfilElementoToPerfilElementoDTO(PerfilElemento perfilElemento) {
        if ( perfilElemento == null ) {
            return null;
        }

        PerfilElementoDTO perfilElementoDTO = new PerfilElementoDTO();

        perfilElementoDTO.setId( perfilElemento.getId() );
        perfilElementoDTO.setInicioVigencia( perfilElemento.getInicioVigencia() );
        perfilElementoDTO.setFimVigencia( perfilElemento.getFimVigencia() );
        perfilElementoDTO.setPerfil( perfilElemento.getPerfil() );

        return perfilElementoDTO;
    }

    protected List<PerfilElementoDTO> perfilElementoListToPerfilElementoDTOList(List<PerfilElemento> list) {
        if ( list == null ) {
            return null;
        }

        List<PerfilElementoDTO> list1 = new ArrayList<PerfilElementoDTO>( list.size() );
        for ( PerfilElemento perfilElemento : list ) {
            list1.add( perfilElementoToPerfilElementoDTO( perfilElemento ) );
        }

        return list1;
    }

    protected ElementoDTO elementoToElementoDTO(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }

        ElementoDTO elementoDTO = new ElementoDTO();

        elementoDTO.setId( elemento.getId() );
        elementoDTO.setEmail( elemento.getEmail() );
        elementoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( elemento.getComissao() ) );
        elementoDTO.setTipoElemento( elemento.getTipoElemento() );
        if ( elemento.getHoraSemanal() != null ) {
            elementoDTO.setHoraSemanal( elemento.getHoraSemanal() );
        }
        if ( elemento.getHoraMensal() != null ) {
            elementoDTO.setHoraMensal( elemento.getHoraMensal() );
        }
        elementoDTO.setValenciaTecnica( elemento.getValenciaTecnica() );
        elementoDTO.setOutraValencia( elemento.getOutraValencia() );
        elementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isApoioCn() != null ) {
            elementoDTO.setApoioCn( elemento.isApoioCn() );
        }
        elementoDTO.setDataInicioVigencia( elemento.getDataInicioVigencia() );
        elementoDTO.setDataFimVigencia( elemento.getDataFimVigencia() );
        elementoDTO.setDataPrimeiraRenovacaoVigencia( elemento.getDataPrimeiraRenovacaoVigencia() );
        elementoDTO.setDataSegundaRenovacaoVigencia( elemento.getDataSegundaRenovacaoVigencia() );
        elementoDTO.setCargoMandato( elemento.getCargoMandato() );
        elementoDTO.setDataInicioMandato( elemento.getDataInicioMandato() );
        elementoDTO.setDataRenovacaoMandato( elemento.getDataRenovacaoMandato() );
        elementoDTO.setDataFimMandato( elemento.getDataFimMandato() );
        if ( elemento.isAtivo() != null ) {
            elementoDTO.setAtivo( elemento.isAtivo() );
        }
        elementoDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( elemento.getUtilizadorPCJ() ) );
        elementoDTO.setNumeroCartao( elemento.getNumeroCartao() );
        elementoDTO.setPerfisElementos( perfilElementoListToPerfilElementoDTOList( elemento.getPerfisElementos() ) );

        return elementoDTO;
    }

    protected ReuniaoPCJDTO reuniaoPCJToReuniaoPCJDTO(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoPCJDTO reuniaoPCJDTO = new ReuniaoPCJDTO();

        reuniaoPCJDTO.setId( reuniaoPCJ.getId() );
        reuniaoPCJDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoPCJDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoPCJDTO.setCodigoModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoPCJDTO.setCodigoTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoPCJDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoPCJDTO.setDataFim( reuniaoPCJ.getDataFim() );
        reuniaoPCJDTO.setHoraInicio( reuniaoPCJ.getHoraInicio() );
        reuniaoPCJDTO.setHoraFim( reuniaoPCJ.getHoraFim() );
        reuniaoPCJDTO.setElemento( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoPCJDTO.setLocalReuniao( reuniaoPCJ.getLocalReuniao() );
        reuniaoPCJDTO.setAtivo( reuniaoPCJ.isAtivo() );

        return reuniaoPCJDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected UtilizadorPCJ utilizadorPCJDTOToUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorPCJ utilizadorPCJ = new UtilizadorPCJ();

        utilizadorPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );

        return utilizadorPCJ;
    }

    protected Elemento elementoDTOToElemento(ElementoDTO elementoDTO) {
        if ( elementoDTO == null ) {
            return null;
        }

        Elemento elemento = new Elemento();

        elemento.setId( elementoDTO.getId() );
        elemento.setEmail( elementoDTO.getEmail() );
        elemento.setTipoElemento( elementoDTO.getTipoElemento() );
        elemento.setHoraSemanal( elementoDTO.getHoraSemanal() );
        elemento.setHoraMensal( elementoDTO.getHoraMensal() );
        elemento.setValenciaTecnica( elementoDTO.getValenciaTecnica() );
        elemento.setOutraValencia( elementoDTO.getOutraValencia() );
        elemento.setEntidade( elementoDTO.getEntidade() );
        elemento.setApoioCn( elementoDTO.isApoioCn() );
        elemento.setDataInicioVigencia( elementoDTO.getDataInicioVigencia() );
        elemento.setDataFimVigencia( elementoDTO.getDataFimVigencia() );
        elemento.setDataPrimeiraRenovacaoVigencia( elementoDTO.getDataPrimeiraRenovacaoVigencia() );
        elemento.setDataSegundaRenovacaoVigencia( elementoDTO.getDataSegundaRenovacaoVigencia() );
        elemento.setCargoMandato( elementoDTO.getCargoMandato() );
        elemento.setDataInicioMandato( elementoDTO.getDataInicioMandato() );
        elemento.setDataRenovacaoMandato( elementoDTO.getDataRenovacaoMandato() );
        elemento.setDataFimMandato( elementoDTO.getDataFimMandato() );
        elemento.setAtivo( elementoDTO.isAtivo() );
        elemento.setComissao( comissaoPCJDTOToComissaoPCJppp( elementoDTO.getComissao() ) );
        elemento.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( elementoDTO.getUtilizadorPCJ() ) );
        elemento.setNumeroCartao( elementoDTO.getNumeroCartao() );

        return elemento;
    }

    protected ReuniaoPCJ reuniaoPCJDTOToReuniaoPCJ(ReuniaoPCJDTO reuniaoPCJDTO) {
        if ( reuniaoPCJDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setId( reuniaoPCJDTO.getId() );
        reuniaoPCJ.setCodigoModalidade( reuniaoPCJDTO.getCodigoModalidade() );
        reuniaoPCJ.setCodigoTipo( reuniaoPCJDTO.getCodigoTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoPCJDTO.getHoraInicio() );
        reuniaoPCJ.setHoraFim( reuniaoPCJDTO.getHoraFim() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoPCJDTO.getElemento() ) );
        reuniaoPCJ.setLocalReuniao( reuniaoPCJDTO.getLocalReuniao() );
        reuniaoPCJ.setAtivo( reuniaoPCJDTO.isAtivo() );
        reuniaoPCJ.setDataFim( reuniaoPCJDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoPCJDTO.getDataInicio() );
        reuniaoPCJ.setAssunto( reuniaoPCJDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoPCJDTO.getDescricao() );

        return reuniaoPCJ;
    }
}
