package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAssuntosGeraisDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAssuntoGeral;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoAssuntosGeraisMapperImpl implements BlocoAssuntosGeraisMapper {

    @Override
    public BlocoAssuntoGeral toEntity(BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO) {
        if ( blocoAssuntosGeraisDTO == null ) {
            return null;
        }

        BlocoAssuntoGeral blocoAssuntoGeral = new BlocoAssuntoGeral();

        blocoAssuntoGeral.setId( blocoAssuntosGeraisDTO.getId() );
        blocoAssuntoGeral.setObservacao( blocoAssuntosGeraisDTO.getObservacao() );
        blocoAssuntoGeral.setTextoIntrodutorio( blocoAssuntosGeraisDTO.getTextoIntrodutorio() );
        blocoAssuntoGeral.setAssunto( blocoAssuntosGeraisDTO.getAssunto() );
        blocoAssuntoGeral.setDocumentoPCJ( documentoDTOToDocumentoPCJ( blocoAssuntosGeraisDTO.getDocumentoPCJ() ) );

        return blocoAssuntoGeral;
    }

    @Override
    public BlocoAssuntosGeraisDTO toDTO(BlocoAssuntoGeral blocoAssuntoGeral) {
        if ( blocoAssuntoGeral == null ) {
            return null;
        }

        BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO = new BlocoAssuntosGeraisDTO();

        blocoAssuntosGeraisDTO.setId( blocoAssuntoGeral.getId() );
        blocoAssuntosGeraisDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( blocoAssuntoGeral.getDocumentoPCJ() ) );
        blocoAssuntosGeraisDTO.setAssunto( blocoAssuntoGeral.getAssunto() );
        blocoAssuntosGeraisDTO.setObservacao( blocoAssuntoGeral.getObservacao() );
        blocoAssuntosGeraisDTO.setTextoIntrodutorio( blocoAssuntoGeral.getTextoIntrodutorio() );

        return blocoAssuntosGeraisDTO;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }
}
