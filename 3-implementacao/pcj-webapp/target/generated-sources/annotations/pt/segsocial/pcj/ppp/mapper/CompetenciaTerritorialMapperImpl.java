package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class CompetenciaTerritorialMapperImpl implements CompetenciaTerritorialMapper {

    @Override
    public CompetenciaTerritorialDTO toDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    @Override
    public CompetenciaTerritorial toEntidade(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    @Override
    public List<CompetenciaTerritorialDTO> toDTOs(List<CompetenciaTerritorial> competenciasTerritorial) {
        if ( competenciasTerritorial == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list = new ArrayList<CompetenciaTerritorialDTO>( competenciasTerritorial.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : competenciasTerritorial ) {
            list.add( toDTO( competenciaTerritorial ) );
        }

        return list;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }
}
