package pt.segsocial.pcj.ppp.mapper.comunicacao;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaInicialDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ElementoComunicacaoInicialDTO;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Crianca;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.ElementoPpp;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class CriancaMapperImpl implements CriancaMapper {

    @Override
    public CriancaDTO toDTO(Crianca crianca) {
        if ( crianca == null ) {
            return null;
        }

        CriancaDTO criancaDTO = new CriancaDTO();

        criancaDTO.setCodigoIdade( crianca.getCodigoIdade() );
        criancaDTO.setCodigoInformacaoIdade( crianca.getCodigoInformacaoIdade() );
        criancaDTO.setDataNascimento( crianca.getDataNascimento() );
        criancaDTO.setNome( crianca.getNome() );
        criancaDTO.setId( crianca.getId() );
        criancaDTO.setCodigoGenero( crianca.getCodigoGenero() );

        return criancaDTO;
    }

    @Override
    public Crianca toEntity(CriancaDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Crianca crianca = new Crianca();

        crianca.setId( dto.getId() );
        crianca.setNome( dto.getNome() );
        crianca.setDataNascimento( dto.getDataNascimento() );
        crianca.setCodigoInformacaoIdade( dto.getCodigoInformacaoIdade() );
        crianca.setCodigoIdade( dto.getCodigoIdade() );
        crianca.setCodigoGenero( dto.getCodigoGenero() );

        return crianca;
    }

    @Override
    public List<CriancaDTO> toDTOList(List<Crianca> criancas) {
        if ( criancas == null ) {
            return null;
        }

        List<CriancaDTO> list = new ArrayList<CriancaDTO>( criancas.size() );
        for ( Crianca crianca : criancas ) {
            list.add( toDTO( crianca ) );
        }

        return list;
    }

    @Override
    public List<CriancaInicialDTO> toInicialDTOList(List<Crianca> criancas) {
        if ( criancas == null ) {
            return null;
        }

        List<CriancaInicialDTO> list = new ArrayList<CriancaInicialDTO>( criancas.size() );
        for ( Crianca crianca : criancas ) {
            list.add( criancaToCriancaInicialDTO( crianca ) );
        }

        return list;
    }

    protected ElementoComunicacaoInicialDTO elementoPppToElementoComunicacaoInicialDTO(ElementoPpp elementoPpp) {
        if ( elementoPpp == null ) {
            return null;
        }

        ElementoComunicacaoInicialDTO elementoComunicacaoInicialDTO = new ElementoComunicacaoInicialDTO();

        elementoComunicacaoInicialDTO.setCodigoParentesco( elementoPpp.getCodigoParentesco() );
        elementoComunicacaoInicialDTO.setNome( elementoPpp.getNome() );

        return elementoComunicacaoInicialDTO;
    }

    protected Set<ElementoComunicacaoInicialDTO> elementoPppSetToElementoComunicacaoInicialDTOSet(Set<ElementoPpp> set) {
        if ( set == null ) {
            return null;
        }

        Set<ElementoComunicacaoInicialDTO> set1 = new HashSet<ElementoComunicacaoInicialDTO>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( ElementoPpp elementoPpp : set ) {
            set1.add( elementoPppToElementoComunicacaoInicialDTO( elementoPpp ) );
        }

        return set1;
    }

    protected CriancaInicialDTO criancaToCriancaInicialDTO(Crianca crianca) {
        if ( crianca == null ) {
            return null;
        }

        CriancaInicialDTO criancaInicialDTO = new CriancaInicialDTO();

        criancaInicialDTO.setCodigoIdade( crianca.getCodigoIdade() );
        criancaInicialDTO.setCodigoInformacaoIdade( crianca.getCodigoInformacaoIdade() );
        criancaInicialDTO.setDataNascimento( crianca.getDataNascimento() );
        criancaInicialDTO.setNome( crianca.getNome() );
        criancaInicialDTO.setId( crianca.getId() );
        criancaInicialDTO.setCodigoGenero( crianca.getCodigoGenero() );
        criancaInicialDTO.setElementos( elementoPppSetToElementoComunicacaoInicialDTOSet( crianca.getElementos() ) );

        return criancaInicialDTO;
    }
}
