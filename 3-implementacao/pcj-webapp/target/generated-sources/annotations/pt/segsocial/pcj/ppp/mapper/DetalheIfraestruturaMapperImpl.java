package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DetalheInfraestruturaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheInfraestrutura;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class DetalheIfraestruturaMapperImpl implements DetalheIfraestruturaMapper {

    @Override
    public DetalheInfraestruturaDTO toDTO(DetalheInfraestrutura detalheInfraestrutura) {
        if ( detalheInfraestrutura == null ) {
            return null;
        }

        DetalheInfraestruturaDTO detalheInfraestruturaDTO = new DetalheInfraestruturaDTO();

        if ( detalheInfraestrutura.getId() != null ) {
            detalheInfraestruturaDTO.setId( detalheInfraestrutura.getId() );
        }
        detalheInfraestruturaDTO.setCodSalaEspera( detalheInfraestrutura.getCodSalaEspera() );
        detalheInfraestruturaDTO.setCodAreaTrabalho( detalheInfraestrutura.getCodAreaTrabalho() );
        detalheInfraestruturaDTO.setInstalacaoSanitaria( detalheInfraestrutura.getInstalacaoSanitaria() );
        detalheInfraestruturaDTO.setAcessibilidade( detalheInfraestrutura.getAcessibilidade() );
        detalheInfraestruturaDTO.setArquivo( detalheInfraestrutura.getArquivo() );
        detalheInfraestruturaDTO.setForaHorarioNecessario( detalheInfraestrutura.getForaHorarioNecessario() );
        detalheInfraestruturaDTO.setForaHorarioExistente( detalheInfraestrutura.getForaHorarioExistente() );

        return detalheInfraestruturaDTO;
    }

    @Override
    public DetalheInfraestrutura toEntidade(DetalheInfraestruturaDTO detalheInfraestruturaDTO) {
        if ( detalheInfraestruturaDTO == null ) {
            return null;
        }

        DetalheInfraestrutura detalheInfraestrutura = new DetalheInfraestrutura();

        detalheInfraestrutura.setId( detalheInfraestruturaDTO.getId() );
        detalheInfraestrutura.setCodSalaEspera( detalheInfraestruturaDTO.getCodSalaEspera() );
        detalheInfraestrutura.setCodAreaTrabalho( detalheInfraestruturaDTO.getCodAreaTrabalho() );
        detalheInfraestrutura.setInstalacaoSanitaria( detalheInfraestruturaDTO.getInstalacaoSanitaria() );
        detalheInfraestrutura.setAcessibilidade( detalheInfraestruturaDTO.getAcessibilidade() );
        detalheInfraestrutura.setArquivo( detalheInfraestruturaDTO.getArquivo() );
        detalheInfraestrutura.setForaHorarioNecessario( detalheInfraestruturaDTO.getForaHorarioNecessario() );
        detalheInfraestrutura.setForaHorarioExistente( detalheInfraestruturaDTO.getForaHorarioExistente() );

        return detalheInfraestrutura;
    }
}
