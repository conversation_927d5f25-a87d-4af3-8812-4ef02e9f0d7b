package pt.segsocial.pcj.ppp.mapper;

import java.util.HashSet;
import java.util.Set;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.FicheiroPontoTrabalhoDTO;
import pt.segsocial.pcj.ppp.dto.FicheiroPontoTrabalhoIdDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.FicheiroPontoTrabalho;
import pt.segsocial.pcj.ppp.jpa.entity.FicheiroPontoTrabalhoId;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:55-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class FicheiroPontoTrabalhoPCJMapperImpl implements FicheiroPontoTrabalhoPCJMapper {

    @Override
    public FicheiroPontoTrabalho toEntidade(FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoDTO) {
        if ( ficheiroPontoTrabalhoDTO == null ) {
            return null;
        }

        FicheiroPontoTrabalho ficheiroPontoTrabalho = new FicheiroPontoTrabalho();

        ficheiroPontoTrabalho.setId( ficheiroPontoTrabalhoIdDTOToFicheiroPontoTrabalhoId( ficheiroPontoTrabalhoDTO.getId() ) );
        ficheiroPontoTrabalho.setDocumentoPCJ( documentoDTOToDocumentoPCJ( ficheiroPontoTrabalhoDTO.getDocumentoPCJ() ) );

        return ficheiroPontoTrabalho;
    }

    @Override
    public Set<FicheiroPontoTrabalhoDTO> toDTOs(Set<FicheiroPontoTrabalho> ficheiroPontoTrabalhos) {
        if ( ficheiroPontoTrabalhos == null ) {
            return null;
        }

        Set<FicheiroPontoTrabalhoDTO> set = new HashSet<FicheiroPontoTrabalhoDTO>( Math.max( (int) ( ficheiroPontoTrabalhos.size() / .75f ) + 1, 16 ) );
        for ( FicheiroPontoTrabalho ficheiroPontoTrabalho : ficheiroPontoTrabalhos ) {
            set.add( ficheiroPontoTrabalhoToFicheiroPontoTrabalhoDTO( ficheiroPontoTrabalho ) );
        }

        return set;
    }

    protected FicheiroPontoTrabalhoId ficheiroPontoTrabalhoIdDTOToFicheiroPontoTrabalhoId(FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdDTO) {
        if ( ficheiroPontoTrabalhoIdDTO == null ) {
            return null;
        }

        FicheiroPontoTrabalhoId ficheiroPontoTrabalhoId = new FicheiroPontoTrabalhoId();

        ficheiroPontoTrabalhoId.setDocumentoId( ficheiroPontoTrabalhoIdDTO.getDocumentoId() );
        ficheiroPontoTrabalhoId.setPontoTrabalhoId( ficheiroPontoTrabalhoIdDTO.getPontoTrabalhoId() );

        return ficheiroPontoTrabalhoId;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    protected FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdToFicheiroPontoTrabalhoIdDTO(FicheiroPontoTrabalhoId ficheiroPontoTrabalhoId) {
        if ( ficheiroPontoTrabalhoId == null ) {
            return null;
        }

        FicheiroPontoTrabalhoIdDTO ficheiroPontoTrabalhoIdDTO = new FicheiroPontoTrabalhoIdDTO();

        ficheiroPontoTrabalhoIdDTO.setDocumentoId( ficheiroPontoTrabalhoId.getDocumentoId() );
        ficheiroPontoTrabalhoIdDTO.setPontoTrabalhoId( ficheiroPontoTrabalhoId.getPontoTrabalhoId() );

        return ficheiroPontoTrabalhoIdDTO;
    }

    protected FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoToFicheiroPontoTrabalhoDTO(FicheiroPontoTrabalho ficheiroPontoTrabalho) {
        if ( ficheiroPontoTrabalho == null ) {
            return null;
        }

        FicheiroPontoTrabalhoDTO ficheiroPontoTrabalhoDTO = new FicheiroPontoTrabalhoDTO();

        ficheiroPontoTrabalhoDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( ficheiroPontoTrabalho.getDocumentoPCJ() ) );
        ficheiroPontoTrabalhoDTO.setId( ficheiroPontoTrabalhoIdToFicheiroPontoTrabalhoIdDTO( ficheiroPontoTrabalho.getId() ) );

        return ficheiroPontoTrabalhoDTO;
    }
}
