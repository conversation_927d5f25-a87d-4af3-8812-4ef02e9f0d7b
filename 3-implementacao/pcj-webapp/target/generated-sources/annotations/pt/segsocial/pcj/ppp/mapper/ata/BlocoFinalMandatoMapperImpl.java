package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoFinalMandatoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoFinalMandato;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:01-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoFinalMandatoMapperImpl implements BlocoFinalMandatoMapper {

    @Override
    public BlocoFinalMandato toEntity(BlocoFinalMandatoDTO blocoFinalMandatoDTO) {
        if ( blocoFinalMandatoDTO == null ) {
            return null;
        }

        BlocoFinalMandato blocoFinalMandato = new BlocoFinalMandato();

        blocoFinalMandato.setId( blocoFinalMandatoDTO.getId() );
        blocoFinalMandato.setObservacao( blocoFinalMandatoDTO.getObservacao() );
        blocoFinalMandato.setTextoIntrodutorio( blocoFinalMandatoDTO.getTextoIntrodutorio() );

        return blocoFinalMandato;
    }

    @Override
    public BlocoFinalMandatoDTO toDTO(BlocoFinalMandato blocoFinalMandato) {
        if ( blocoFinalMandato == null ) {
            return null;
        }

        BlocoFinalMandatoDTO blocoFinalMandatoDTO = new BlocoFinalMandatoDTO();

        blocoFinalMandatoDTO.setId( blocoFinalMandato.getId() );
        blocoFinalMandatoDTO.setObservacao( blocoFinalMandato.getObservacao() );
        blocoFinalMandatoDTO.setTextoIntrodutorio( blocoFinalMandato.getTextoIntrodutorio() );

        return blocoFinalMandatoDTO;
    }
}
