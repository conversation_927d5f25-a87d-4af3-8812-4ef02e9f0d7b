package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoCooptacaoElementoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoCooptacaoElemento;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:50-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoCooptacaoElementoMapperImpl implements BlocoCooptacaoElementoMapper {

    @Override
    public BlocoCooptacaoElemento toEntity(BlocoCooptacaoElementoDTO blocoCooptacaoElementoDTO) {
        if ( blocoCooptacaoElementoDTO == null ) {
            return null;
        }

        BlocoCooptacaoElemento blocoCooptacaoElemento = new BlocoCooptacaoElemento();

        blocoCooptacaoElemento.setId( blocoCooptacaoElementoDTO.getId() );
        blocoCooptacaoElemento.setObservacao( blocoCooptacaoElementoDTO.getObservacao() );
        blocoCooptacaoElemento.setTextoIntrodutorio( blocoCooptacaoElementoDTO.getTextoIntrodutorio() );

        return blocoCooptacaoElemento;
    }

    @Override
    public BlocoCooptacaoElementoDTO toDTO(BlocoCooptacaoElemento blocoCooptacaoElemento) {
        if ( blocoCooptacaoElemento == null ) {
            return null;
        }

        BlocoCooptacaoElementoDTO blocoCooptacaoElementoDTO = new BlocoCooptacaoElementoDTO();

        blocoCooptacaoElementoDTO.setId( blocoCooptacaoElemento.getId() );
        blocoCooptacaoElementoDTO.setObservacao( blocoCooptacaoElemento.getObservacao() );
        blocoCooptacaoElementoDTO.setTextoIntrodutorio( blocoCooptacaoElemento.getTextoIntrodutorio() );

        return blocoCooptacaoElementoDTO;
    }
}
