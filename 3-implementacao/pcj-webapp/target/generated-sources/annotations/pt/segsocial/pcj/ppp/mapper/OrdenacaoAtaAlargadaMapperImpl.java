package pt.segsocial.pcj.ppp.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CompetenciaTerritorialDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.PerfilElementoDTO;
import pt.segsocial.pcj.ppp.dto.ReuniaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.TerritorioPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.AtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAprovacaoAnualDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAprovacaoPlanoAnualDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAssuntosGeraisDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoComposicaoRestritaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoConhecimentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoCooptacaoElementoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoEleicaoPresidenteDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoFinalMandatoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoNomeacaoSecretarioDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoParecerDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoRenovacaoCooptacaoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ConvidadoFolhaPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ConvidadoPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ElementoFolhaPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.OrdenacaoAtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.ConvidadoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.AtaAlargada;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAprovacaoAnual;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAprovacaoPlanoAnual;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAssuntoGeral;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoComposicaoRestrita;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoConhecimento;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoCooptacaoElemento;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoEleicaoPresidente;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoFinalMandato;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoNomeacaoSecretario;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoParecer;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoRenovacaoCooptacao;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.ConvidadoFolhaPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.ElementoFolhaPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.OrdenacaoAtaAlargadaPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:00-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class OrdenacaoAtaAlargadaMapperImpl implements OrdenacaoAtaAlargadaMapper {

    @Override
    public OrdenacaoAtaAlargadaPCJ toEntity(OrdenacaoAtaAlargadaDTO ordenacaoAtaAlargadaDTO) {
        if ( ordenacaoAtaAlargadaDTO == null ) {
            return null;
        }

        OrdenacaoAtaAlargadaPCJ ordenacaoAtaAlargadaPCJ = new OrdenacaoAtaAlargadaPCJ();

        ordenacaoAtaAlargadaPCJ.setId( ordenacaoAtaAlargadaDTO.getId() );
        ordenacaoAtaAlargadaPCJ.setAtaAlargada( ataAlargadaDTOToAtaAlargada( ordenacaoAtaAlargadaDTO.getAtaAlargada() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoAprovacaoAnual( blocoAprovacaoAnualDTOToBlocoAprovacaoAnual( ordenacaoAtaAlargadaDTO.getBlocoAprovacaoAnual() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoAprovacaoPlanoAnual( blocoAprovacaoPlanoAnualDTOToBlocoAprovacaoPlanoAnual( ordenacaoAtaAlargadaDTO.getBlocoAprovacaoPlanoAnual() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoAssuntoGeral( blocoAssuntosGeraisDTOToBlocoAssuntoGeral( ordenacaoAtaAlargadaDTO.getBlocoAssuntoGeral() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoConhecimento( blocoConhecimentoDTOToBlocoConhecimento( ordenacaoAtaAlargadaDTO.getBlocoConhecimento() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoCooptacaoElemento( blocoCooptacaoElementoDTOToBlocoCooptacaoElemento( ordenacaoAtaAlargadaDTO.getBlocoCooptacaoElemento() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoEleicaoPresidente( blocoEleicaoPresidenteDTOToBlocoEleicaoPresidente( ordenacaoAtaAlargadaDTO.getBlocoEleicaoPresidente() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoFinalMandato( blocoFinalMandatoDTOToBlocoFinalMandato( ordenacaoAtaAlargadaDTO.getBlocoFinalMandato() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoNomeacaoSecretario( blocoNomeacaoSecretarioDTOToBlocoNomeacaoSecretario( ordenacaoAtaAlargadaDTO.getBlocoNomeacaoSecretario() ) );
        ordenacaoAtaAlargadaPCJ.setBlocoRenovacaoCooptacao( blocoRenovacaoCooptacaoDTOToBlocoRenovacaoCooptacao( ordenacaoAtaAlargadaDTO.getBlocoRenovacaoCooptacao() ) );
        ordenacaoAtaAlargadaPCJ.setParecer( blocoParecerDTOToBlocoParecer( ordenacaoAtaAlargadaDTO.getParecer() ) );
        ordenacaoAtaAlargadaPCJ.setNomeBloco( ordenacaoAtaAlargadaDTO.getNomeBloco() );
        ordenacaoAtaAlargadaPCJ.setBlocoComposicaoRestrita( blocoComposicaoRestritaDTOToBlocoComposicaoRestrita( ordenacaoAtaAlargadaDTO.getBlocoComposicaoRestrita() ) );

        return ordenacaoAtaAlargadaPCJ;
    }

    @Override
    public OrdenacaoAtaAlargadaDTO toDTO(OrdenacaoAtaAlargadaPCJ ordenacaoAtaAlargada) {
        if ( ordenacaoAtaAlargada == null ) {
            return null;
        }

        OrdenacaoAtaAlargadaDTO ordenacaoAtaAlargadaDTO = new OrdenacaoAtaAlargadaDTO();

        ordenacaoAtaAlargadaDTO.setId( ordenacaoAtaAlargada.getId() );
        ordenacaoAtaAlargadaDTO.setAtaAlargada( ataAlargadaToAtaAlargadaDTO( ordenacaoAtaAlargada.getAtaAlargada() ) );
        ordenacaoAtaAlargadaDTO.setBlocoAssuntoGeral( blocoAssuntoGeralToBlocoAssuntosGeraisDTO( ordenacaoAtaAlargada.getBlocoAssuntoGeral() ) );
        ordenacaoAtaAlargadaDTO.setParecer( blocoParecerToBlocoParecerDTO( ordenacaoAtaAlargada.getParecer() ) );
        ordenacaoAtaAlargadaDTO.setNomeBloco( ordenacaoAtaAlargada.getNomeBloco() );
        ordenacaoAtaAlargadaDTO.setBlocoAprovacaoAnual( blocoAprovacaoAnualToBlocoAprovacaoAnualDTO( ordenacaoAtaAlargada.getBlocoAprovacaoAnual() ) );
        ordenacaoAtaAlargadaDTO.setBlocoAprovacaoPlanoAnual( blocoAprovacaoPlanoAnualToBlocoAprovacaoPlanoAnualDTO( ordenacaoAtaAlargada.getBlocoAprovacaoPlanoAnual() ) );
        ordenacaoAtaAlargadaDTO.setBlocoComposicaoRestrita( blocoComposicaoRestritaToBlocoComposicaoRestritaDTO( ordenacaoAtaAlargada.getBlocoComposicaoRestrita() ) );
        ordenacaoAtaAlargadaDTO.setBlocoConhecimento( blocoConhecimentoToBlocoConhecimentoDTO( ordenacaoAtaAlargada.getBlocoConhecimento() ) );
        ordenacaoAtaAlargadaDTO.setBlocoCooptacaoElemento( blocoCooptacaoElementoToBlocoCooptacaoElementoDTO( ordenacaoAtaAlargada.getBlocoCooptacaoElemento() ) );
        ordenacaoAtaAlargadaDTO.setBlocoEleicaoPresidente( blocoEleicaoPresidenteToBlocoEleicaoPresidenteDTO( ordenacaoAtaAlargada.getBlocoEleicaoPresidente() ) );
        ordenacaoAtaAlargadaDTO.setBlocoFinalMandato( blocoFinalMandatoToBlocoFinalMandatoDTO( ordenacaoAtaAlargada.getBlocoFinalMandato() ) );
        ordenacaoAtaAlargadaDTO.setBlocoNomeacaoSecretario( blocoNomeacaoSecretarioToBlocoNomeacaoSecretarioDTO( ordenacaoAtaAlargada.getBlocoNomeacaoSecretario() ) );
        ordenacaoAtaAlargadaDTO.setBlocoRenovacaoCooptacao( blocoRenovacaoCooptacaoToBlocoRenovacaoCooptacaoDTO( ordenacaoAtaAlargada.getBlocoRenovacaoCooptacao() ) );

        return ordenacaoAtaAlargadaDTO;
    }

    protected Morada moradaDTOToMorada(MoradaDTO moradaDTO) {
        if ( moradaDTO == null ) {
            return null;
        }

        Morada morada = new Morada();

        morada.setId( moradaDTO.getId() );
        morada.setDistrito( moradaDTO.getDistrito() );
        morada.setConcelho( moradaDTO.getConcelho() );
        morada.setFreguesia( moradaDTO.getFreguesia() );
        morada.setArteria( moradaDTO.getArteria() );
        morada.setCodigoPostal( moradaDTO.getCodigoPostal() );
        morada.setLocalidade( moradaDTO.getLocalidade() );
        morada.setTipoInstalacao( moradaDTO.getTipoInstalacao() );

        return morada;
    }

    protected TerritorioPCJ territorioPCJDTOToTerritorioPCJ(TerritorioPCJDTO territorioPCJDTO) {
        if ( territorioPCJDTO == null ) {
            return null;
        }

        TerritorioPCJ territorioPCJ = new TerritorioPCJ();

        territorioPCJ.setId( territorioPCJDTO.getId() );
        territorioPCJ.setCodigoDistrito( territorioPCJDTO.getCodigoDistrito() );
        territorioPCJ.setCodigoConcelho( territorioPCJDTO.getCodigoConcelho() );
        territorioPCJ.setCodigoFreguesia( territorioPCJDTO.getCodigoFreguesia() );

        return territorioPCJ;
    }

    protected CompetenciaTerritorial competenciaTerritorialDTOToCompetenciaTerritorial(CompetenciaTerritorialDTO competenciaTerritorialDTO) {
        if ( competenciaTerritorialDTO == null ) {
            return null;
        }

        CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorial();

        competenciaTerritorial.setId( competenciaTerritorialDTO.getId() );
        competenciaTerritorial.setTerritorioPCJ( territorioPCJDTOToTerritorioPCJ( competenciaTerritorialDTO.getTerritorioPCJ() ) );

        return competenciaTerritorial;
    }

    protected List<CompetenciaTerritorial> competenciaTerritorialDTOListToCompetenciaTerritorialList(List<CompetenciaTerritorialDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorial> list1 = new ArrayList<CompetenciaTerritorial>( list.size() );
        for ( CompetenciaTerritorialDTO competenciaTerritorialDTO : list ) {
            list1.add( competenciaTerritorialDTOToCompetenciaTerritorial( competenciaTerritorialDTO ) );
        }

        return list1;
    }

    protected ComissaoPCJppp comissaoPCJDTOToComissaoPCJppp(ComissaoPCJDTO comissaoPCJDTO) {
        if ( comissaoPCJDTO == null ) {
            return null;
        }

        ComissaoPCJppp comissaoPCJppp = new ComissaoPCJppp();

        comissaoPCJppp.setId( comissaoPCJDTO.getId() );
        comissaoPCJppp.setMorada( moradaDTOToMorada( comissaoPCJDTO.getMorada() ) );
        comissaoPCJppp.setCodigo( comissaoPCJDTO.getCodigo() );
        comissaoPCJppp.setNome( comissaoPCJDTO.getNome() );
        comissaoPCJppp.setNumeroPortaria( comissaoPCJDTO.getNumeroPortaria() );
        comissaoPCJppp.setDataPortaria( comissaoPCJDTO.getDataPortaria() );
        comissaoPCJppp.setNumeroDiarioPortaria( comissaoPCJDTO.getNumeroDiarioPortaria() );
        comissaoPCJppp.setDataInicioFuncionamento( comissaoPCJDTO.getDataInicioFuncionamento() );
        comissaoPCJppp.setNumeroPortariaReorganizacao( comissaoPCJDTO.getNumeroPortariaReorganizacao() );
        comissaoPCJppp.setDataPortariaReorganizacao( comissaoPCJDTO.getDataPortariaReorganizacao() );
        comissaoPCJppp.setNumeroDiarioPortariaReorganizacao( comissaoPCJDTO.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJppp.setRegimePermanencia( comissaoPCJDTO.getRegimePermanencia() );
        comissaoPCJppp.setOutraPermanecia( comissaoPCJDTO.getOutraPermanecia() );
        comissaoPCJppp.setHorarioFuncionamento( comissaoPCJDTO.getHorarioFuncionamento() );
        comissaoPCJppp.setOutraHorario( comissaoPCJDTO.getOutraHorario() );
        comissaoPCJppp.setHoraAberturaManha( comissaoPCJDTO.getHoraAberturaManha() );
        comissaoPCJppp.setHoraFechoManha( comissaoPCJDTO.getHoraFechoManha() );
        comissaoPCJppp.setHoraAberturaTarde( comissaoPCJDTO.getHoraAberturaTarde() );
        comissaoPCJppp.setHoraFechoTarde( comissaoPCJDTO.getHoraFechoTarde() );
        comissaoPCJppp.setAtivo( comissaoPCJDTO.getAtivo() );
        comissaoPCJppp.setNumeroLinhaDireta( comissaoPCJDTO.getNumeroLinhaDireta() );
        comissaoPCJppp.setNumeroTelemovel( comissaoPCJDTO.getNumeroTelemovel() );
        comissaoPCJppp.setSocialInstagram( comissaoPCJDTO.getSocialInstagram() );
        comissaoPCJppp.setSocialFacebook( comissaoPCJDTO.getSocialFacebook() );
        comissaoPCJppp.setSocialLinkedin( comissaoPCJDTO.getSocialLinkedin() );
        comissaoPCJppp.setSocialOutra( comissaoPCJDTO.getSocialOutra() );
        comissaoPCJppp.setEmailInstitucional( comissaoPCJDTO.getEmailInstitucional() );
        comissaoPCJppp.setUrlSite( comissaoPCJDTO.getUrlSite() );
        comissaoPCJppp.setCompetencias( competenciaTerritorialDTOListToCompetenciaTerritorialList( comissaoPCJDTO.getCompetencias() ) );
        comissaoPCJppp.setDesignacaoSecretarioManual( comissaoPCJDTO.getDesignacaoSecretarioManual() );

        return comissaoPCJppp;
    }

    protected UtilizadorPCJ utilizadorPCJDTOToUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorPCJ utilizadorPCJ = new UtilizadorPCJ();

        utilizadorPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );

        return utilizadorPCJ;
    }

    protected Elemento elementoDTOToElemento(ElementoDTO elementoDTO) {
        if ( elementoDTO == null ) {
            return null;
        }

        Elemento elemento = new Elemento();

        elemento.setId( elementoDTO.getId() );
        elemento.setEmail( elementoDTO.getEmail() );
        elemento.setTipoElemento( elementoDTO.getTipoElemento() );
        elemento.setHoraSemanal( elementoDTO.getHoraSemanal() );
        elemento.setHoraMensal( elementoDTO.getHoraMensal() );
        elemento.setValenciaTecnica( elementoDTO.getValenciaTecnica() );
        elemento.setOutraValencia( elementoDTO.getOutraValencia() );
        elemento.setEntidade( elementoDTO.getEntidade() );
        elemento.setApoioCn( elementoDTO.isApoioCn() );
        elemento.setDataInicioVigencia( elementoDTO.getDataInicioVigencia() );
        elemento.setDataFimVigencia( elementoDTO.getDataFimVigencia() );
        elemento.setDataPrimeiraRenovacaoVigencia( elementoDTO.getDataPrimeiraRenovacaoVigencia() );
        elemento.setDataSegundaRenovacaoVigencia( elementoDTO.getDataSegundaRenovacaoVigencia() );
        elemento.setCargoMandato( elementoDTO.getCargoMandato() );
        elemento.setDataInicioMandato( elementoDTO.getDataInicioMandato() );
        elemento.setDataRenovacaoMandato( elementoDTO.getDataRenovacaoMandato() );
        elemento.setDataFimMandato( elementoDTO.getDataFimMandato() );
        elemento.setAtivo( elementoDTO.isAtivo() );
        elemento.setComissao( comissaoPCJDTOToComissaoPCJppp( elementoDTO.getComissao() ) );
        elemento.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( elementoDTO.getUtilizadorPCJ() ) );
        elemento.setNumeroCartao( elementoDTO.getNumeroCartao() );

        return elemento;
    }

    protected ReuniaoPCJ reuniaoPCJDTOToReuniaoPCJ(ReuniaoPCJDTO reuniaoPCJDTO) {
        if ( reuniaoPCJDTO == null ) {
            return null;
        }

        ReuniaoPCJ reuniaoPCJ = new ReuniaoPCJ();

        reuniaoPCJ.setId( reuniaoPCJDTO.getId() );
        reuniaoPCJ.setCodigoModalidade( reuniaoPCJDTO.getCodigoModalidade() );
        reuniaoPCJ.setCodigoTipo( reuniaoPCJDTO.getCodigoTipo() );
        reuniaoPCJ.setHoraInicio( reuniaoPCJDTO.getHoraInicio() );
        reuniaoPCJ.setHoraFim( reuniaoPCJDTO.getHoraFim() );
        reuniaoPCJ.setElemento( elementoDTOToElemento( reuniaoPCJDTO.getElemento() ) );
        reuniaoPCJ.setLocalReuniao( reuniaoPCJDTO.getLocalReuniao() );
        reuniaoPCJ.setAtivo( reuniaoPCJDTO.isAtivo() );
        reuniaoPCJ.setDataFim( reuniaoPCJDTO.getDataFim() );
        reuniaoPCJ.setDataInicio( reuniaoPCJDTO.getDataInicio() );
        reuniaoPCJ.setAssunto( reuniaoPCJDTO.getAssunto() );
        reuniaoPCJ.setDescricao( reuniaoPCJDTO.getDescricao() );

        return reuniaoPCJ;
    }

    protected ConvidadoPCJ convidadoPCJDTOToConvidadoPCJ(ConvidadoPCJDTO convidadoPCJDTO) {
        if ( convidadoPCJDTO == null ) {
            return null;
        }

        ConvidadoPCJ convidadoPCJ = new ConvidadoPCJ();

        convidadoPCJ.setId( convidadoPCJDTO.getId() );
        convidadoPCJ.setMorada( convidadoPCJDTO.getMorada() );
        convidadoPCJ.setEmail( convidadoPCJDTO.getEmail() );
        convidadoPCJ.setTelefone( convidadoPCJDTO.getTelefone() );
        convidadoPCJ.setCargo( convidadoPCJDTO.getCargo() );
        convidadoPCJ.setEntidade( convidadoPCJDTO.getEntidade() );
        convidadoPCJ.setNome( convidadoPCJDTO.getNome() );
        convidadoPCJ.setComissao( comissaoPCJDTOToComissaoPCJppp( convidadoPCJDTO.getComissao() ) );
        convidadoPCJ.setCodigoEntidadeParticipante( convidadoPCJDTO.getCodigoEntidadeParticipante() );
        convidadoPCJ.setDetalheEntidadeParticipante( convidadoPCJDTO.getDetalheEntidadeParticipante() );

        return convidadoPCJ;
    }

    protected ConvidadoFolhaPCJ convidadoFolhaPCJDTOToConvidadoFolhaPCJ(ConvidadoFolhaPCJDTO convidadoFolhaPCJDTO) {
        if ( convidadoFolhaPCJDTO == null ) {
            return null;
        }

        ConvidadoFolhaPCJ convidadoFolhaPCJ = new ConvidadoFolhaPCJ();

        convidadoFolhaPCJ.setAtaAlargada( ataAlargadaDTOToAtaAlargada( convidadoFolhaPCJDTO.getAtaAlargada() ) );
        convidadoFolhaPCJ.setConvidadoPCJ( convidadoPCJDTOToConvidadoPCJ( convidadoFolhaPCJDTO.getConvidadoPCJ() ) );
        convidadoFolhaPCJ.setAssinatura( convidadoFolhaPCJDTO.isAssinatura() );

        return convidadoFolhaPCJ;
    }

    protected List<ConvidadoFolhaPCJ> convidadoFolhaPCJDTOListToConvidadoFolhaPCJList(List<ConvidadoFolhaPCJDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<ConvidadoFolhaPCJ> list1 = new ArrayList<ConvidadoFolhaPCJ>( list.size() );
        for ( ConvidadoFolhaPCJDTO convidadoFolhaPCJDTO : list ) {
            list1.add( convidadoFolhaPCJDTOToConvidadoFolhaPCJ( convidadoFolhaPCJDTO ) );
        }

        return list1;
    }

    protected ElementoFolhaPCJ elementoFolhaPCJDTOToElementoFolhaPCJ(ElementoFolhaPCJDTO elementoFolhaPCJDTO) {
        if ( elementoFolhaPCJDTO == null ) {
            return null;
        }

        ElementoFolhaPCJ elementoFolhaPCJ = new ElementoFolhaPCJ();

        elementoFolhaPCJ.setAtaAlargada( ataAlargadaDTOToAtaAlargada( elementoFolhaPCJDTO.getAtaAlargada() ) );
        elementoFolhaPCJ.setElemento( elementoDTOToElemento( elementoFolhaPCJDTO.getElemento() ) );
        elementoFolhaPCJ.setAssinatura( elementoFolhaPCJDTO.isAssinatura() );

        return elementoFolhaPCJ;
    }

    protected List<ElementoFolhaPCJ> elementoFolhaPCJDTOListToElementoFolhaPCJList(List<ElementoFolhaPCJDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<ElementoFolhaPCJ> list1 = new ArrayList<ElementoFolhaPCJ>( list.size() );
        for ( ElementoFolhaPCJDTO elementoFolhaPCJDTO : list ) {
            list1.add( elementoFolhaPCJDTOToElementoFolhaPCJ( elementoFolhaPCJDTO ) );
        }

        return list1;
    }

    protected AtaAlargada ataAlargadaDTOToAtaAlargada(AtaAlargadaDTO ataAlargadaDTO) {
        if ( ataAlargadaDTO == null ) {
            return null;
        }

        AtaAlargada ataAlargada = new AtaAlargada();

        ataAlargada.setId( ataAlargadaDTO.getId() );
        ataAlargada.setNumeroAtaAlargada( ataAlargadaDTO.getNumeroAtaAlargada() );
        ataAlargada.setDataRealizacao( ataAlargadaDTO.getDataRealizacao() );
        ataAlargada.setTextoIntrodutorio( ataAlargadaDTO.getTextoIntrodutorio() );
        ataAlargada.setTextoConclusivo( ataAlargadaDTO.getTextoConclusivo() );
        ataAlargada.setEstadoAta( ataAlargadaDTO.getEstadoAta() );
        ataAlargada.setComissao( comissaoPCJDTOToComissaoPCJppp( ataAlargadaDTO.getComissao() ) );
        ataAlargada.setReuniaoPCJ( reuniaoPCJDTOToReuniaoPCJ( ataAlargadaDTO.getReuniaoPCJ() ) );
        ataAlargada.setConvidadosFolhaPresenca( convidadoFolhaPCJDTOListToConvidadoFolhaPCJList( ataAlargadaDTO.getConvidadosFolhaPresenca() ) );
        ataAlargada.setElementosFolhaPresenca( elementoFolhaPCJDTOListToElementoFolhaPCJList( ataAlargadaDTO.getElementosFolhaPresenca() ) );

        return ataAlargada;
    }

    protected BlocoAprovacaoAnual blocoAprovacaoAnualDTOToBlocoAprovacaoAnual(BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO) {
        if ( blocoAprovacaoAnualDTO == null ) {
            return null;
        }

        BlocoAprovacaoAnual blocoAprovacaoAnual = new BlocoAprovacaoAnual();

        blocoAprovacaoAnual.setId( blocoAprovacaoAnualDTO.getId() );
        blocoAprovacaoAnual.setObservacao( blocoAprovacaoAnualDTO.getObservacao() );
        blocoAprovacaoAnual.setTextoIntrodutorio( blocoAprovacaoAnualDTO.getTextoIntrodutorio() );

        return blocoAprovacaoAnual;
    }

    protected BlocoAprovacaoPlanoAnual blocoAprovacaoPlanoAnualDTOToBlocoAprovacaoPlanoAnual(BlocoAprovacaoPlanoAnualDTO blocoAprovacaoPlanoAnualDTO) {
        if ( blocoAprovacaoPlanoAnualDTO == null ) {
            return null;
        }

        BlocoAprovacaoPlanoAnual blocoAprovacaoPlanoAnual = new BlocoAprovacaoPlanoAnual();

        blocoAprovacaoPlanoAnual.setId( blocoAprovacaoPlanoAnualDTO.getId() );
        blocoAprovacaoPlanoAnual.setObservacao( blocoAprovacaoPlanoAnualDTO.getObservacao() );
        blocoAprovacaoPlanoAnual.setTextoIntrodutorio( blocoAprovacaoPlanoAnualDTO.getTextoIntrodutorio() );
        blocoAprovacaoPlanoAnual.setAtaExterna( blocoAprovacaoPlanoAnualDTO.isAtaExterna() );

        return blocoAprovacaoPlanoAnual;
    }

    protected DocumentoPCJ documentoDTOToDocumentoPCJ(DocumentoDTO documentoDTO) {
        if ( documentoDTO == null ) {
            return null;
        }

        DocumentoPCJ documentoPCJ = new DocumentoPCJ();

        documentoPCJ.setId( documentoDTO.getId() );
        documentoPCJ.setDataUpload( documentoDTO.getDataUpload() );
        documentoPCJ.setIdentificadorFicheiro( documentoDTO.getIdentificadorFicheiro() );
        documentoPCJ.setUploadDocumento( documentoDTO.getUploadDocumento() );
        documentoPCJ.setNomeDocumento( documentoDTO.getNomeDocumento() );

        return documentoPCJ;
    }

    protected BlocoAssuntoGeral blocoAssuntosGeraisDTOToBlocoAssuntoGeral(BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO) {
        if ( blocoAssuntosGeraisDTO == null ) {
            return null;
        }

        BlocoAssuntoGeral blocoAssuntoGeral = new BlocoAssuntoGeral();

        blocoAssuntoGeral.setId( blocoAssuntosGeraisDTO.getId() );
        blocoAssuntoGeral.setObservacao( blocoAssuntosGeraisDTO.getObservacao() );
        blocoAssuntoGeral.setTextoIntrodutorio( blocoAssuntosGeraisDTO.getTextoIntrodutorio() );
        blocoAssuntoGeral.setAssunto( blocoAssuntosGeraisDTO.getAssunto() );
        blocoAssuntoGeral.setDocumentoPCJ( documentoDTOToDocumentoPCJ( blocoAssuntosGeraisDTO.getDocumentoPCJ() ) );

        return blocoAssuntoGeral;
    }

    protected BlocoConhecimento blocoConhecimentoDTOToBlocoConhecimento(BlocoConhecimentoDTO blocoConhecimentoDTO) {
        if ( blocoConhecimentoDTO == null ) {
            return null;
        }

        BlocoConhecimento blocoConhecimento = new BlocoConhecimento();

        blocoConhecimento.setId( blocoConhecimentoDTO.getId() );
        blocoConhecimento.setAssunto( blocoConhecimentoDTO.getAssunto() );
        blocoConhecimento.setObservacao( blocoConhecimentoDTO.getObservacao() );
        blocoConhecimento.setTextoIntrodutorio( blocoConhecimentoDTO.getTextoIntrodutorio() );
        blocoConhecimento.setComissao( comissaoPCJDTOToComissaoPCJppp( blocoConhecimentoDTO.getComissao() ) );
        blocoConhecimento.setExecutado( blocoConhecimentoDTO.isExecutado() );
        blocoConhecimento.setDocumentoPCJ( documentoDTOToDocumentoPCJ( blocoConhecimentoDTO.getDocumentoPCJ() ) );
        blocoConhecimento.setAtaExterna( blocoConhecimentoDTO.isAtaExterna() );

        return blocoConhecimento;
    }

    protected BlocoCooptacaoElemento blocoCooptacaoElementoDTOToBlocoCooptacaoElemento(BlocoCooptacaoElementoDTO blocoCooptacaoElementoDTO) {
        if ( blocoCooptacaoElementoDTO == null ) {
            return null;
        }

        BlocoCooptacaoElemento blocoCooptacaoElemento = new BlocoCooptacaoElemento();

        blocoCooptacaoElemento.setId( blocoCooptacaoElementoDTO.getId() );
        blocoCooptacaoElemento.setObservacao( blocoCooptacaoElementoDTO.getObservacao() );
        blocoCooptacaoElemento.setTextoIntrodutorio( blocoCooptacaoElementoDTO.getTextoIntrodutorio() );

        return blocoCooptacaoElemento;
    }

    protected BlocoEleicaoPresidente blocoEleicaoPresidenteDTOToBlocoEleicaoPresidente(BlocoEleicaoPresidenteDTO blocoEleicaoPresidenteDTO) {
        if ( blocoEleicaoPresidenteDTO == null ) {
            return null;
        }

        BlocoEleicaoPresidente blocoEleicaoPresidente = new BlocoEleicaoPresidente();

        blocoEleicaoPresidente.setId( blocoEleicaoPresidenteDTO.getId() );
        blocoEleicaoPresidente.setObservacao( blocoEleicaoPresidenteDTO.getObservacao() );
        blocoEleicaoPresidente.setTextoIntrodutorio( blocoEleicaoPresidenteDTO.getTextoIntrodutorio() );

        return blocoEleicaoPresidente;
    }

    protected BlocoFinalMandato blocoFinalMandatoDTOToBlocoFinalMandato(BlocoFinalMandatoDTO blocoFinalMandatoDTO) {
        if ( blocoFinalMandatoDTO == null ) {
            return null;
        }

        BlocoFinalMandato blocoFinalMandato = new BlocoFinalMandato();

        blocoFinalMandato.setId( blocoFinalMandatoDTO.getId() );
        blocoFinalMandato.setObservacao( blocoFinalMandatoDTO.getObservacao() );
        blocoFinalMandato.setTextoIntrodutorio( blocoFinalMandatoDTO.getTextoIntrodutorio() );

        return blocoFinalMandato;
    }

    protected BlocoNomeacaoSecretario blocoNomeacaoSecretarioDTOToBlocoNomeacaoSecretario(BlocoNomeacaoSecretarioDTO blocoNomeacaoSecretarioDTO) {
        if ( blocoNomeacaoSecretarioDTO == null ) {
            return null;
        }

        BlocoNomeacaoSecretario blocoNomeacaoSecretario = new BlocoNomeacaoSecretario();

        blocoNomeacaoSecretario.setId( blocoNomeacaoSecretarioDTO.getId() );
        blocoNomeacaoSecretario.setObservacao( blocoNomeacaoSecretarioDTO.getObservacao() );
        blocoNomeacaoSecretario.setTextoIntrodutorio( blocoNomeacaoSecretarioDTO.getTextoIntrodutorio() );

        return blocoNomeacaoSecretario;
    }

    protected BlocoRenovacaoCooptacao blocoRenovacaoCooptacaoDTOToBlocoRenovacaoCooptacao(BlocoRenovacaoCooptacaoDTO blocoRenovacaoCooptacaoDTO) {
        if ( blocoRenovacaoCooptacaoDTO == null ) {
            return null;
        }

        BlocoRenovacaoCooptacao blocoRenovacaoCooptacao = new BlocoRenovacaoCooptacao();

        blocoRenovacaoCooptacao.setId( blocoRenovacaoCooptacaoDTO.getId() );
        blocoRenovacaoCooptacao.setObservacao( blocoRenovacaoCooptacaoDTO.getObservacao() );
        blocoRenovacaoCooptacao.setTextoIntrodutorio( blocoRenovacaoCooptacaoDTO.getTextoIntrodutorio() );

        return blocoRenovacaoCooptacao;
    }

    protected BlocoParecer blocoParecerDTOToBlocoParecer(BlocoParecerDTO blocoParecerDTO) {
        if ( blocoParecerDTO == null ) {
            return null;
        }

        BlocoParecer blocoParecer = new BlocoParecer();

        blocoParecer.setId( blocoParecerDTO.getId() );
        blocoParecer.setAssunto( blocoParecerDTO.getAssunto() );
        blocoParecer.setObservacao( blocoParecerDTO.getObservacao() );
        blocoParecer.setTextoIntrodutorio( blocoParecerDTO.getTextoIntrodutorio() );
        blocoParecer.setComissao( comissaoPCJDTOToComissaoPCJppp( blocoParecerDTO.getComissao() ) );
        blocoParecer.setAtaExterna( blocoParecerDTO.isAtaExterna() );

        return blocoParecer;
    }

    protected BlocoComposicaoRestrita blocoComposicaoRestritaDTOToBlocoComposicaoRestrita(BlocoComposicaoRestritaDTO blocoComposicaoRestritaDTO) {
        if ( blocoComposicaoRestritaDTO == null ) {
            return null;
        }

        BlocoComposicaoRestrita blocoComposicaoRestrita = new BlocoComposicaoRestrita();

        blocoComposicaoRestrita.setId( blocoComposicaoRestritaDTO.getId() );
        blocoComposicaoRestrita.setObservacao( blocoComposicaoRestritaDTO.getObservacao() );
        blocoComposicaoRestrita.setTextoIntrodutorio( blocoComposicaoRestritaDTO.getTextoIntrodutorio() );

        return blocoComposicaoRestrita;
    }

    protected MoradaDTO moradaToMoradaDTO(Morada morada) {
        if ( morada == null ) {
            return null;
        }

        MoradaDTO moradaDTO = new MoradaDTO();

        moradaDTO.setId( morada.getId() );
        moradaDTO.setArteria( morada.getArteria() );
        moradaDTO.setCodigoPostal( morada.getCodigoPostal() );
        moradaDTO.setLocalidade( morada.getLocalidade() );
        moradaDTO.setTipoInstalacao( morada.getTipoInstalacao() );
        moradaDTO.setFreguesia( morada.getFreguesia() );
        moradaDTO.setConcelho( morada.getConcelho() );
        moradaDTO.setDistrito( morada.getDistrito() );

        return moradaDTO;
    }

    protected CompetenciaTerritorialDTO competenciaTerritorialToCompetenciaTerritorialDTO(CompetenciaTerritorial competenciaTerritorial) {
        if ( competenciaTerritorial == null ) {
            return null;
        }

        CompetenciaTerritorialDTO competenciaTerritorialDTO = new CompetenciaTerritorialDTO();

        competenciaTerritorialDTO.setId( competenciaTerritorial.getId() );

        return competenciaTerritorialDTO;
    }

    protected List<CompetenciaTerritorialDTO> competenciaTerritorialListToCompetenciaTerritorialDTOList(List<CompetenciaTerritorial> list) {
        if ( list == null ) {
            return null;
        }

        List<CompetenciaTerritorialDTO> list1 = new ArrayList<CompetenciaTerritorialDTO>( list.size() );
        for ( CompetenciaTerritorial competenciaTerritorial : list ) {
            list1.add( competenciaTerritorialToCompetenciaTerritorialDTO( competenciaTerritorial ) );
        }

        return list1;
    }

    protected ComissaoPCJDTO comissaoPCJpppToComissaoPCJDTO(ComissaoPCJppp comissaoPCJppp) {
        if ( comissaoPCJppp == null ) {
            return null;
        }

        ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();

        comissaoPCJDTO.setId( comissaoPCJppp.getId() );
        comissaoPCJDTO.setMorada( moradaToMoradaDTO( comissaoPCJppp.getMorada() ) );
        comissaoPCJDTO.setCodigo( comissaoPCJppp.getCodigo() );
        comissaoPCJDTO.setNome( comissaoPCJppp.getNome() );
        comissaoPCJDTO.setNumeroPortaria( comissaoPCJppp.getNumeroPortaria() );
        comissaoPCJDTO.setDataPortaria( comissaoPCJppp.getDataPortaria() );
        comissaoPCJDTO.setNumeroDiarioPortaria( comissaoPCJppp.getNumeroDiarioPortaria() );
        comissaoPCJDTO.setDataInicioFuncionamento( comissaoPCJppp.getDataInicioFuncionamento() );
        comissaoPCJDTO.setNumeroPortariaReorganizacao( comissaoPCJppp.getNumeroPortariaReorganizacao() );
        comissaoPCJDTO.setDataPortariaReorganizacao( comissaoPCJppp.getDataPortariaReorganizacao() );
        comissaoPCJDTO.setNumeroDiarioPortariaReorganizacao( comissaoPCJppp.getNumeroDiarioPortariaReorganizacao() );
        comissaoPCJDTO.setRegimePermanencia( comissaoPCJppp.getRegimePermanencia() );
        comissaoPCJDTO.setOutraPermanecia( comissaoPCJppp.getOutraPermanecia() );
        comissaoPCJDTO.setHorarioFuncionamento( comissaoPCJppp.getHorarioFuncionamento() );
        comissaoPCJDTO.setOutraHorario( comissaoPCJppp.getOutraHorario() );
        comissaoPCJDTO.setHoraAberturaManha( comissaoPCJppp.getHoraAberturaManha() );
        comissaoPCJDTO.setHoraFechoManha( comissaoPCJppp.getHoraFechoManha() );
        comissaoPCJDTO.setHoraAberturaTarde( comissaoPCJppp.getHoraAberturaTarde() );
        comissaoPCJDTO.setHoraFechoTarde( comissaoPCJppp.getHoraFechoTarde() );
        comissaoPCJDTO.setAtivo( comissaoPCJppp.getAtivo() );
        comissaoPCJDTO.setNumeroLinhaDireta( comissaoPCJppp.getNumeroLinhaDireta() );
        comissaoPCJDTO.setNumeroTelemovel( comissaoPCJppp.getNumeroTelemovel() );
        comissaoPCJDTO.setSocialInstagram( comissaoPCJppp.getSocialInstagram() );
        comissaoPCJDTO.setSocialFacebook( comissaoPCJppp.getSocialFacebook() );
        comissaoPCJDTO.setSocialLinkedin( comissaoPCJppp.getSocialLinkedin() );
        comissaoPCJDTO.setSocialOutra( comissaoPCJppp.getSocialOutra() );
        comissaoPCJDTO.setEmailInstitucional( comissaoPCJppp.getEmailInstitucional() );
        comissaoPCJDTO.setUrlSite( comissaoPCJppp.getUrlSite() );
        comissaoPCJDTO.setCompetencias( competenciaTerritorialListToCompetenciaTerritorialDTOList( comissaoPCJppp.getCompetencias() ) );
        comissaoPCJDTO.setDesignacaoSecretarioManual( comissaoPCJppp.isDesignacaoSecretarioManual() );

        return comissaoPCJDTO;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }

    protected PerfilElementoDTO perfilElementoToPerfilElementoDTO(PerfilElemento perfilElemento) {
        if ( perfilElemento == null ) {
            return null;
        }

        PerfilElementoDTO perfilElementoDTO = new PerfilElementoDTO();

        perfilElementoDTO.setId( perfilElemento.getId() );
        perfilElementoDTO.setInicioVigencia( perfilElemento.getInicioVigencia() );
        perfilElementoDTO.setFimVigencia( perfilElemento.getFimVigencia() );
        perfilElementoDTO.setPerfil( perfilElemento.getPerfil() );

        return perfilElementoDTO;
    }

    protected List<PerfilElementoDTO> perfilElementoListToPerfilElementoDTOList(List<PerfilElemento> list) {
        if ( list == null ) {
            return null;
        }

        List<PerfilElementoDTO> list1 = new ArrayList<PerfilElementoDTO>( list.size() );
        for ( PerfilElemento perfilElemento : list ) {
            list1.add( perfilElementoToPerfilElementoDTO( perfilElemento ) );
        }

        return list1;
    }

    protected ElementoDTO elementoToElementoDTO(Elemento elemento) {
        if ( elemento == null ) {
            return null;
        }

        ElementoDTO elementoDTO = new ElementoDTO();

        elementoDTO.setId( elemento.getId() );
        elementoDTO.setEmail( elemento.getEmail() );
        elementoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( elemento.getComissao() ) );
        elementoDTO.setTipoElemento( elemento.getTipoElemento() );
        if ( elemento.getHoraSemanal() != null ) {
            elementoDTO.setHoraSemanal( elemento.getHoraSemanal() );
        }
        if ( elemento.getHoraMensal() != null ) {
            elementoDTO.setHoraMensal( elemento.getHoraMensal() );
        }
        elementoDTO.setValenciaTecnica( elemento.getValenciaTecnica() );
        elementoDTO.setOutraValencia( elemento.getOutraValencia() );
        elementoDTO.setEntidade( elemento.getEntidade() );
        if ( elemento.isApoioCn() != null ) {
            elementoDTO.setApoioCn( elemento.isApoioCn() );
        }
        elementoDTO.setDataInicioVigencia( elemento.getDataInicioVigencia() );
        elementoDTO.setDataFimVigencia( elemento.getDataFimVigencia() );
        elementoDTO.setDataPrimeiraRenovacaoVigencia( elemento.getDataPrimeiraRenovacaoVigencia() );
        elementoDTO.setDataSegundaRenovacaoVigencia( elemento.getDataSegundaRenovacaoVigencia() );
        elementoDTO.setCargoMandato( elemento.getCargoMandato() );
        elementoDTO.setDataInicioMandato( elemento.getDataInicioMandato() );
        elementoDTO.setDataRenovacaoMandato( elemento.getDataRenovacaoMandato() );
        elementoDTO.setDataFimMandato( elemento.getDataFimMandato() );
        if ( elemento.isAtivo() != null ) {
            elementoDTO.setAtivo( elemento.isAtivo() );
        }
        elementoDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( elemento.getUtilizadorPCJ() ) );
        elementoDTO.setNumeroCartao( elemento.getNumeroCartao() );
        elementoDTO.setPerfisElementos( perfilElementoListToPerfilElementoDTOList( elemento.getPerfisElementos() ) );

        return elementoDTO;
    }

    protected ReuniaoPCJDTO reuniaoPCJToReuniaoPCJDTO(ReuniaoPCJ reuniaoPCJ) {
        if ( reuniaoPCJ == null ) {
            return null;
        }

        ReuniaoPCJDTO reuniaoPCJDTO = new ReuniaoPCJDTO();

        reuniaoPCJDTO.setId( reuniaoPCJ.getId() );
        reuniaoPCJDTO.setAssunto( reuniaoPCJ.getAssunto() );
        reuniaoPCJDTO.setDescricao( reuniaoPCJ.getDescricao() );
        reuniaoPCJDTO.setCodigoModalidade( reuniaoPCJ.getCodigoModalidade() );
        reuniaoPCJDTO.setCodigoTipo( reuniaoPCJ.getCodigoTipo() );
        reuniaoPCJDTO.setDataInicio( reuniaoPCJ.getDataInicio() );
        reuniaoPCJDTO.setDataFim( reuniaoPCJ.getDataFim() );
        reuniaoPCJDTO.setHoraInicio( reuniaoPCJ.getHoraInicio() );
        reuniaoPCJDTO.setHoraFim( reuniaoPCJ.getHoraFim() );
        reuniaoPCJDTO.setElemento( elementoToElementoDTO( reuniaoPCJ.getElemento() ) );
        reuniaoPCJDTO.setLocalReuniao( reuniaoPCJ.getLocalReuniao() );
        reuniaoPCJDTO.setAtivo( reuniaoPCJ.isAtivo() );

        return reuniaoPCJDTO;
    }

    protected ConvidadoPCJDTO convidadoPCJToConvidadoPCJDTO(ConvidadoPCJ convidadoPCJ) {
        if ( convidadoPCJ == null ) {
            return null;
        }

        ConvidadoPCJDTO convidadoPCJDTO = new ConvidadoPCJDTO();

        convidadoPCJDTO.setId( convidadoPCJ.getId() );
        convidadoPCJDTO.setCargo( convidadoPCJ.getCargo() );
        convidadoPCJDTO.setEntidade( convidadoPCJ.getEntidade() );
        convidadoPCJDTO.setNome( convidadoPCJ.getNome() );
        convidadoPCJDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( convidadoPCJ.getComissao() ) );
        convidadoPCJDTO.setCodigoEntidadeParticipante( convidadoPCJ.getCodigoEntidadeParticipante() );
        convidadoPCJDTO.setDetalheEntidadeParticipante( convidadoPCJ.getDetalheEntidadeParticipante() );
        convidadoPCJDTO.setTelefone( convidadoPCJ.getTelefone() );
        convidadoPCJDTO.setEmail( convidadoPCJ.getEmail() );
        convidadoPCJDTO.setMorada( convidadoPCJ.getMorada() );

        return convidadoPCJDTO;
    }

    protected ConvidadoFolhaPCJDTO convidadoFolhaPCJToConvidadoFolhaPCJDTO(ConvidadoFolhaPCJ convidadoFolhaPCJ) {
        if ( convidadoFolhaPCJ == null ) {
            return null;
        }

        ConvidadoFolhaPCJDTO convidadoFolhaPCJDTO = new ConvidadoFolhaPCJDTO();

        if ( convidadoFolhaPCJ.getAssinatura() != null ) {
            convidadoFolhaPCJDTO.setAssinatura( convidadoFolhaPCJ.getAssinatura() );
        }
        convidadoFolhaPCJDTO.setConvidadoPCJ( convidadoPCJToConvidadoPCJDTO( convidadoFolhaPCJ.getConvidadoPCJ() ) );
        convidadoFolhaPCJDTO.setAtaAlargada( ataAlargadaToAtaAlargadaDTO( convidadoFolhaPCJ.getAtaAlargada() ) );

        return convidadoFolhaPCJDTO;
    }

    protected List<ConvidadoFolhaPCJDTO> convidadoFolhaPCJListToConvidadoFolhaPCJDTOList(List<ConvidadoFolhaPCJ> list) {
        if ( list == null ) {
            return null;
        }

        List<ConvidadoFolhaPCJDTO> list1 = new ArrayList<ConvidadoFolhaPCJDTO>( list.size() );
        for ( ConvidadoFolhaPCJ convidadoFolhaPCJ : list ) {
            list1.add( convidadoFolhaPCJToConvidadoFolhaPCJDTO( convidadoFolhaPCJ ) );
        }

        return list1;
    }

    protected ElementoFolhaPCJDTO elementoFolhaPCJToElementoFolhaPCJDTO(ElementoFolhaPCJ elementoFolhaPCJ) {
        if ( elementoFolhaPCJ == null ) {
            return null;
        }

        ElementoFolhaPCJDTO elementoFolhaPCJDTO = new ElementoFolhaPCJDTO();

        if ( elementoFolhaPCJ.getAssinatura() != null ) {
            elementoFolhaPCJDTO.setAssinatura( elementoFolhaPCJ.getAssinatura() );
        }
        elementoFolhaPCJDTO.setAtaAlargada( ataAlargadaToAtaAlargadaDTO( elementoFolhaPCJ.getAtaAlargada() ) );
        elementoFolhaPCJDTO.setElemento( elementoToElementoDTO( elementoFolhaPCJ.getElemento() ) );

        return elementoFolhaPCJDTO;
    }

    protected List<ElementoFolhaPCJDTO> elementoFolhaPCJListToElementoFolhaPCJDTOList(List<ElementoFolhaPCJ> list) {
        if ( list == null ) {
            return null;
        }

        List<ElementoFolhaPCJDTO> list1 = new ArrayList<ElementoFolhaPCJDTO>( list.size() );
        for ( ElementoFolhaPCJ elementoFolhaPCJ : list ) {
            list1.add( elementoFolhaPCJToElementoFolhaPCJDTO( elementoFolhaPCJ ) );
        }

        return list1;
    }

    protected AtaAlargadaDTO ataAlargadaToAtaAlargadaDTO(AtaAlargada ataAlargada) {
        if ( ataAlargada == null ) {
            return null;
        }

        AtaAlargadaDTO ataAlargadaDTO = new AtaAlargadaDTO();

        ataAlargadaDTO.setId( ataAlargada.getId() );
        ataAlargadaDTO.setNumeroAtaAlargada( ataAlargada.getNumeroAtaAlargada() );
        ataAlargadaDTO.setDataRealizacao( ataAlargada.getDataRealizacao() );
        ataAlargadaDTO.setTextoIntrodutorio( ataAlargada.getTextoIntrodutorio() );
        ataAlargadaDTO.setTextoConclusivo( ataAlargada.getTextoConclusivo() );
        ataAlargadaDTO.setEstadoAta( ataAlargada.getEstadoAta() );
        ataAlargadaDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( ataAlargada.getComissao() ) );
        ataAlargadaDTO.setReuniaoPCJ( reuniaoPCJToReuniaoPCJDTO( ataAlargada.getReuniaoPCJ() ) );
        ataAlargadaDTO.setConvidadosFolhaPresenca( convidadoFolhaPCJListToConvidadoFolhaPCJDTOList( ataAlargada.getConvidadosFolhaPresenca() ) );
        ataAlargadaDTO.setElementosFolhaPresenca( elementoFolhaPCJListToElementoFolhaPCJDTOList( ataAlargada.getElementosFolhaPresenca() ) );

        return ataAlargadaDTO;
    }

    protected DocumentoDTO documentoPCJToDocumentoDTO(DocumentoPCJ documentoPCJ) {
        if ( documentoPCJ == null ) {
            return null;
        }

        DocumentoDTO documentoDTO = new DocumentoDTO();

        documentoDTO.setId( documentoPCJ.getId() );
        documentoDTO.setDataUpload( documentoPCJ.getDataUpload() );
        documentoDTO.setIdentificadorFicheiro( documentoPCJ.getIdentificadorFicheiro() );
        documentoDTO.setUploadDocumento( documentoPCJ.getUploadDocumento() );
        documentoDTO.setNomeDocumento( documentoPCJ.getNomeDocumento() );

        return documentoDTO;
    }

    protected BlocoAssuntosGeraisDTO blocoAssuntoGeralToBlocoAssuntosGeraisDTO(BlocoAssuntoGeral blocoAssuntoGeral) {
        if ( blocoAssuntoGeral == null ) {
            return null;
        }

        BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO = new BlocoAssuntosGeraisDTO();

        blocoAssuntosGeraisDTO.setId( blocoAssuntoGeral.getId() );
        blocoAssuntosGeraisDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( blocoAssuntoGeral.getDocumentoPCJ() ) );
        blocoAssuntosGeraisDTO.setAssunto( blocoAssuntoGeral.getAssunto() );
        blocoAssuntosGeraisDTO.setObservacao( blocoAssuntoGeral.getObservacao() );
        blocoAssuntosGeraisDTO.setTextoIntrodutorio( blocoAssuntoGeral.getTextoIntrodutorio() );

        return blocoAssuntosGeraisDTO;
    }

    protected BlocoParecerDTO blocoParecerToBlocoParecerDTO(BlocoParecer blocoParecer) {
        if ( blocoParecer == null ) {
            return null;
        }

        BlocoParecerDTO blocoParecerDTO = new BlocoParecerDTO();

        blocoParecerDTO.setId( blocoParecer.getId() );
        blocoParecerDTO.setAssunto( blocoParecer.getAssunto() );
        blocoParecerDTO.setObservacao( blocoParecer.getObservacao() );
        blocoParecerDTO.setTextoIntrodutorio( blocoParecer.getTextoIntrodutorio() );
        blocoParecerDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( blocoParecer.getComissao() ) );
        blocoParecerDTO.setAtaExterna( blocoParecer.isAtaExterna() );

        return blocoParecerDTO;
    }

    protected BlocoAprovacaoAnualDTO blocoAprovacaoAnualToBlocoAprovacaoAnualDTO(BlocoAprovacaoAnual blocoAprovacaoAnual) {
        if ( blocoAprovacaoAnual == null ) {
            return null;
        }

        BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO = new BlocoAprovacaoAnualDTO();

        blocoAprovacaoAnualDTO.setId( blocoAprovacaoAnual.getId() );
        blocoAprovacaoAnualDTO.setObservacao( blocoAprovacaoAnual.getObservacao() );
        blocoAprovacaoAnualDTO.setTextoIntrodutorio( blocoAprovacaoAnual.getTextoIntrodutorio() );

        return blocoAprovacaoAnualDTO;
    }

    protected BlocoAprovacaoPlanoAnualDTO blocoAprovacaoPlanoAnualToBlocoAprovacaoPlanoAnualDTO(BlocoAprovacaoPlanoAnual blocoAprovacaoPlanoAnual) {
        if ( blocoAprovacaoPlanoAnual == null ) {
            return null;
        }

        BlocoAprovacaoPlanoAnualDTO blocoAprovacaoPlanoAnualDTO = new BlocoAprovacaoPlanoAnualDTO();

        blocoAprovacaoPlanoAnualDTO.setId( blocoAprovacaoPlanoAnual.getId() );
        blocoAprovacaoPlanoAnualDTO.setObservacao( blocoAprovacaoPlanoAnual.getObservacao() );
        blocoAprovacaoPlanoAnualDTO.setTextoIntrodutorio( blocoAprovacaoPlanoAnual.getTextoIntrodutorio() );

        return blocoAprovacaoPlanoAnualDTO;
    }

    protected BlocoComposicaoRestritaDTO blocoComposicaoRestritaToBlocoComposicaoRestritaDTO(BlocoComposicaoRestrita blocoComposicaoRestrita) {
        if ( blocoComposicaoRestrita == null ) {
            return null;
        }

        BlocoComposicaoRestritaDTO blocoComposicaoRestritaDTO = new BlocoComposicaoRestritaDTO();

        blocoComposicaoRestritaDTO.setId( blocoComposicaoRestrita.getId() );
        blocoComposicaoRestritaDTO.setObservacao( blocoComposicaoRestrita.getObservacao() );
        blocoComposicaoRestritaDTO.setTextoIntrodutorio( blocoComposicaoRestrita.getTextoIntrodutorio() );

        return blocoComposicaoRestritaDTO;
    }

    protected BlocoConhecimentoDTO blocoConhecimentoToBlocoConhecimentoDTO(BlocoConhecimento blocoConhecimento) {
        if ( blocoConhecimento == null ) {
            return null;
        }

        BlocoConhecimentoDTO blocoConhecimentoDTO = new BlocoConhecimentoDTO();

        blocoConhecimentoDTO.setId( blocoConhecimento.getId() );
        blocoConhecimentoDTO.setAssunto( blocoConhecimento.getAssunto() );
        blocoConhecimentoDTO.setObservacao( blocoConhecimento.getObservacao() );
        blocoConhecimentoDTO.setTextoIntrodutorio( blocoConhecimento.getTextoIntrodutorio() );
        blocoConhecimentoDTO.setComissao( comissaoPCJpppToComissaoPCJDTO( blocoConhecimento.getComissao() ) );
        blocoConhecimentoDTO.setDocumentoPCJ( documentoPCJToDocumentoDTO( blocoConhecimento.getDocumentoPCJ() ) );
        if ( blocoConhecimento.getExecutado() != null ) {
            blocoConhecimentoDTO.setExecutado( blocoConhecimento.getExecutado() );
        }
        if ( blocoConhecimento.isAtaExterna() != null ) {
            blocoConhecimentoDTO.setAtaExterna( blocoConhecimento.isAtaExterna() );
        }

        return blocoConhecimentoDTO;
    }

    protected BlocoCooptacaoElementoDTO blocoCooptacaoElementoToBlocoCooptacaoElementoDTO(BlocoCooptacaoElemento blocoCooptacaoElemento) {
        if ( blocoCooptacaoElemento == null ) {
            return null;
        }

        BlocoCooptacaoElementoDTO blocoCooptacaoElementoDTO = new BlocoCooptacaoElementoDTO();

        blocoCooptacaoElementoDTO.setId( blocoCooptacaoElemento.getId() );
        blocoCooptacaoElementoDTO.setObservacao( blocoCooptacaoElemento.getObservacao() );
        blocoCooptacaoElementoDTO.setTextoIntrodutorio( blocoCooptacaoElemento.getTextoIntrodutorio() );

        return blocoCooptacaoElementoDTO;
    }

    protected BlocoEleicaoPresidenteDTO blocoEleicaoPresidenteToBlocoEleicaoPresidenteDTO(BlocoEleicaoPresidente blocoEleicaoPresidente) {
        if ( blocoEleicaoPresidente == null ) {
            return null;
        }

        BlocoEleicaoPresidenteDTO blocoEleicaoPresidenteDTO = new BlocoEleicaoPresidenteDTO();

        blocoEleicaoPresidenteDTO.setId( blocoEleicaoPresidente.getId() );
        blocoEleicaoPresidenteDTO.setObservacao( blocoEleicaoPresidente.getObservacao() );
        blocoEleicaoPresidenteDTO.setTextoIntrodutorio( blocoEleicaoPresidente.getTextoIntrodutorio() );

        return blocoEleicaoPresidenteDTO;
    }

    protected BlocoFinalMandatoDTO blocoFinalMandatoToBlocoFinalMandatoDTO(BlocoFinalMandato blocoFinalMandato) {
        if ( blocoFinalMandato == null ) {
            return null;
        }

        BlocoFinalMandatoDTO blocoFinalMandatoDTO = new BlocoFinalMandatoDTO();

        blocoFinalMandatoDTO.setId( blocoFinalMandato.getId() );
        blocoFinalMandatoDTO.setObservacao( blocoFinalMandato.getObservacao() );
        blocoFinalMandatoDTO.setTextoIntrodutorio( blocoFinalMandato.getTextoIntrodutorio() );

        return blocoFinalMandatoDTO;
    }

    protected BlocoNomeacaoSecretarioDTO blocoNomeacaoSecretarioToBlocoNomeacaoSecretarioDTO(BlocoNomeacaoSecretario blocoNomeacaoSecretario) {
        if ( blocoNomeacaoSecretario == null ) {
            return null;
        }

        BlocoNomeacaoSecretarioDTO blocoNomeacaoSecretarioDTO = new BlocoNomeacaoSecretarioDTO();

        blocoNomeacaoSecretarioDTO.setId( blocoNomeacaoSecretario.getId() );
        blocoNomeacaoSecretarioDTO.setObservacao( blocoNomeacaoSecretario.getObservacao() );
        blocoNomeacaoSecretarioDTO.setTextoIntrodutorio( blocoNomeacaoSecretario.getTextoIntrodutorio() );

        return blocoNomeacaoSecretarioDTO;
    }

    protected BlocoRenovacaoCooptacaoDTO blocoRenovacaoCooptacaoToBlocoRenovacaoCooptacaoDTO(BlocoRenovacaoCooptacao blocoRenovacaoCooptacao) {
        if ( blocoRenovacaoCooptacao == null ) {
            return null;
        }

        BlocoRenovacaoCooptacaoDTO blocoRenovacaoCooptacaoDTO = new BlocoRenovacaoCooptacaoDTO();

        blocoRenovacaoCooptacaoDTO.setId( blocoRenovacaoCooptacao.getId() );
        blocoRenovacaoCooptacaoDTO.setObservacao( blocoRenovacaoCooptacao.getObservacao() );
        blocoRenovacaoCooptacaoDTO.setTextoIntrodutorio( blocoRenovacaoCooptacao.getTextoIntrodutorio() );

        return blocoRenovacaoCooptacaoDTO;
    }
}
