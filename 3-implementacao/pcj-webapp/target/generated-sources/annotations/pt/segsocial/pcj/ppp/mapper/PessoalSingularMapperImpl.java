package pt.segsocial.pcj.ppp.mapper;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.id.api.dto.PessoaSingularDTO;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:11:02-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class PessoalSingularMapperImpl implements PessoalSingularMapper {

    @Override
    public pt.segsocial.pcj.ppp.dto.PessoaSingularDTO toDTO(PessoaSingularDTO api) {
        if ( api == null ) {
            return null;
        }

        pt.segsocial.pcj.ppp.dto.PessoaSingularDTO pessoaSingularDTO = new pt.segsocial.pcj.ppp.dto.PessoaSingularDTO();

        pessoaSingularDTO.setTelemovel( api.getTelemovel() );
        pessoaSingularDTO.setNumeroDocumento( api.getNumeroIdentificacaoCivil() );
        pessoaSingularDTO.setEmailPessoal( api.getEMail() );
        pessoaSingularDTO.setNomeCompleto( api.getNomeCompleto() );
        if ( api.getNumeroIdentificacaoFiscal() != null ) {
            pessoaSingularDTO.setNumeroIdentificacaoFiscal( String.valueOf( api.getNumeroIdentificacaoFiscal() ) );
        }

        return pessoaSingularDTO;
    }
}
