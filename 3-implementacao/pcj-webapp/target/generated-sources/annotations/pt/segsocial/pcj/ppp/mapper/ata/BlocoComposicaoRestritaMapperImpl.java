package pt.segsocial.pcj.ppp.mapper.ata;

import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoComposicaoRestritaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoComposicaoRestrita;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:51-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class BlocoComposicaoRestritaMapperImpl implements BlocoComposicaoRestritaMapper {

    @Override
    public BlocoComposicaoRestrita toEntity(BlocoComposicaoRestritaDTO blocoComposicaoRestritaDTO) {
        if ( blocoComposicaoRestritaDTO == null ) {
            return null;
        }

        BlocoComposicaoRestrita blocoComposicaoRestrita = new BlocoComposicaoRestrita();

        blocoComposicaoRestrita.setId( blocoComposicaoRestritaDTO.getId() );
        blocoComposicaoRestrita.setObservacao( blocoComposicaoRestritaDTO.getObservacao() );
        blocoComposicaoRestrita.setTextoIntrodutorio( blocoComposicaoRestritaDTO.getTextoIntrodutorio() );

        return blocoComposicaoRestrita;
    }

    @Override
    public BlocoComposicaoRestritaDTO toDTO(BlocoComposicaoRestrita blocoComposicaoRestrita) {
        if ( blocoComposicaoRestrita == null ) {
            return null;
        }

        BlocoComposicaoRestritaDTO blocoComposicaoRestritaDTO = new BlocoComposicaoRestritaDTO();

        blocoComposicaoRestritaDTO.setId( blocoComposicaoRestrita.getId() );
        blocoComposicaoRestritaDTO.setObservacao( blocoComposicaoRestrita.getObservacao() );
        blocoComposicaoRestritaDTO.setTextoIntrodutorio( blocoComposicaoRestrita.getTextoIntrodutorio() );

        return blocoComposicaoRestritaDTO;
    }
}
