package pt.segsocial.pcj.core.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Generated;
import javax.enterprise.context.ApplicationScoped;
import pt.segsocial.pcj.ppp.dto.log.FiltroPesquisaLogDTO;
import pt.segsocial.pcj.ppp.dto.log.RegistoLogDTO;
import pt.segsocial.pcj.ppp.dto.log.ResultadoPesquisaLogDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.RegistoLog;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorPCJ;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T13:10:56-0300",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.7.0_181 (Oracle Corporation)"
)
@ApplicationScoped
public class RegistoLogMapperImpl implements RegistoLogMapper {

    @Override
    public RegistoLog toEntidadeFromDTO(RegistoLogDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RegistoLog registoLog = new RegistoLog();

        registoLog.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( dto.getUtilizadorPCJDTO() ) );
        registoLog.setId( dto.getId() );
        registoLog.setDataHora( dto.getDataHora() );
        registoLog.setNumeroCartao( dto.getNumeroCartao() );
        registoLog.setDescricao( dto.getDescricao() );

        return registoLog;
    }

    @Override
    public RegistoLog toEntidadeFromFiltro(FiltroPesquisaLogDTO dto) {
        if ( dto == null ) {
            return null;
        }

        RegistoLog registoLog = new RegistoLog();

        registoLog.setDescricao( dto.getDescricao() );
        registoLog.setUtilizadorPCJ( utilizadorPCJDTOToUtilizadorPCJ( dto.getUtilizadorPCJ() ) );
        registoLog.setDataInicio( dto.getDataInicio() );
        registoLog.setDataFim( dto.getDataFim() );
        registoLog.setHoraInicio( dto.getHoraInicio() );
        registoLog.setHoraFim( dto.getHoraFim() );

        return registoLog;
    }

    @Override
    public ResultadoPesquisaLogDTO toResultadoPesquisaFromEntidade(RegistoLog registoLog) {
        if ( registoLog == null ) {
            return null;
        }

        ResultadoPesquisaLogDTO resultadoPesquisaLogDTO = new ResultadoPesquisaLogDTO();

        resultadoPesquisaLogDTO.setId( registoLog.getId() );
        resultadoPesquisaLogDTO.setDataHora( registoLog.getDataHora() );
        resultadoPesquisaLogDTO.setDescricao( registoLog.getDescricao() );
        resultadoPesquisaLogDTO.setUtilizadorPCJ( utilizadorPCJToUtilizadorPCJDTO( registoLog.getUtilizadorPCJ() ) );

        return resultadoPesquisaLogDTO;
    }

    @Override
    public List<ResultadoPesquisaLogDTO> toResultadosPesquisaFromEntidades(List<RegistoLog> registros) {
        if ( registros == null ) {
            return null;
        }

        List<ResultadoPesquisaLogDTO> list = new ArrayList<ResultadoPesquisaLogDTO>( registros.size() );
        for ( RegistoLog registoLog : registros ) {
            list.add( toResultadoPesquisaFromEntidade( registoLog ) );
        }

        return list;
    }

    protected UtilizadorPCJ utilizadorPCJDTOToUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJDTO) {
        if ( utilizadorPCJDTO == null ) {
            return null;
        }

        UtilizadorPCJ utilizadorPCJ = new UtilizadorPCJ();

        utilizadorPCJ.setId( utilizadorPCJDTO.getId() );
        utilizadorPCJ.setNiss( utilizadorPCJDTO.getNiss() );
        utilizadorPCJ.setNomeProfissional( utilizadorPCJDTO.getNomeProfissional() );
        byte[] foto = utilizadorPCJDTO.getFoto();
        if ( foto != null ) {
            utilizadorPCJ.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJ.setNome( utilizadorPCJDTO.getNome() );
        utilizadorPCJ.setNif( utilizadorPCJDTO.getNif() );
        utilizadorPCJ.setEmailPessoal( utilizadorPCJDTO.getEmailPessoal() );
        utilizadorPCJ.setPais( utilizadorPCJDTO.getPais() );
        utilizadorPCJ.setTelemovel( utilizadorPCJDTO.getTelemovel() );
        utilizadorPCJ.setNumeroDocumento( utilizadorPCJDTO.getNumeroDocumento() );
        utilizadorPCJ.setCodigoDocumento( utilizadorPCJDTO.getCodigoDocumento() );

        return utilizadorPCJ;
    }

    protected UtilizadorPCJDTO utilizadorPCJToUtilizadorPCJDTO(UtilizadorPCJ utilizadorPCJ) {
        if ( utilizadorPCJ == null ) {
            return null;
        }

        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();

        utilizadorPCJDTO.setId( utilizadorPCJ.getId() );
        utilizadorPCJDTO.setNiss( utilizadorPCJ.getNiss() );
        utilizadorPCJDTO.setNomeProfissional( utilizadorPCJ.getNomeProfissional() );
        byte[] foto = utilizadorPCJ.getFoto();
        if ( foto != null ) {
            utilizadorPCJDTO.setFoto( Arrays.copyOf( foto, foto.length ) );
        }
        utilizadorPCJDTO.setNome( utilizadorPCJ.getNome() );
        utilizadorPCJDTO.setNif( utilizadorPCJ.getNif() );
        utilizadorPCJDTO.setEmailPessoal( utilizadorPCJ.getEmailPessoal() );
        utilizadorPCJDTO.setPais( utilizadorPCJ.getPais() );
        utilizadorPCJDTO.setTelemovel( utilizadorPCJ.getTelemovel() );
        utilizadorPCJDTO.setNumeroDocumento( utilizadorPCJ.getNumeroDocumento() );
        utilizadorPCJDTO.setCodigoDocumento( utilizadorPCJ.getCodigoDocumento() );

        return utilizadorPCJDTO;
    }
}
