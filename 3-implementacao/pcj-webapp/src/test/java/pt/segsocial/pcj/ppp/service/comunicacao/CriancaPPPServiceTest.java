package pt.segsocial.pcj.ppp.service.comunicacao;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import pt.segsocial.pcj.ppp.dto.MoradaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaInicialDTO;
import pt.segsocial.pcj.ppp.jpa.dao.comunicacao.CriancaDAO;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Crianca;
import pt.segsocial.pcj.ppp.mapper.comunicacao.CriancaMapper;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CriancaPPPServiceTest {

    @InjectMocks
    private CriancaPPPService criancaPPPService;

    @Mock
    private CriancaDAO criancaDAO;

    @Mock
    private CriancaMapper criancaMapper;

    @Mock
    private CriancaMoradaService criancaMoradaService;

    private String nomeCrianca;
    private String query;
    private Long idCrianca;
    private Crianca crianca;
    private CriancaDTO criancaDTO;
    private CriancaInicialDTO criancaInicialDTO;
    private List<Crianca> criancasList;
    private List<CriancaDTO> criancasDTOList;
    private List<CriancaInicialDTO> criancasIniciaisDTOList;
    private List<MoradaDTO> moradasList;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        nomeCrianca = "João Silva";
        query = "João";
        idCrianca = 1L;

        // Criando entidade Crianca
        crianca = new Crianca();
        crianca.setId(idCrianca);
        crianca.setNome(nomeCrianca);

        // Criando DTO Crianca
        criancaDTO = new CriancaDTO();
        criancaDTO.setId(idCrianca);
        criancaDTO.setNome(nomeCrianca);

        // Criando DTO CriancaInicial
        criancaInicialDTO = new CriancaInicialDTO();
        criancaInicialDTO.setId(idCrianca);
        criancaInicialDTO.setNome(nomeCrianca);

        // Criando listas
        criancasList = new ArrayList<>();
        criancasList.add(crianca);

        criancasDTOList = new ArrayList<>();
        criancasDTOList.add(criancaDTO);

        criancasIniciaisDTOList = new ArrayList<>();
        criancasIniciaisDTOList.add(criancaInicialDTO);

        moradasList = new ArrayList<>();
        MoradaDTO moradaDTO = new MoradaDTO();
        moradaDTO.setId(1L);
        moradaDTO.setArteria("Rua de Teste");
        moradasList.add(moradaDTO);
    }

    @Test
    public void testListarCrianca_ComResultados_DeveRetornarListaDTO() {
        // Given
        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(criancasList);
        when(criancaMapper.toDTOList(criancasList)).thenReturn(criancasDTOList);

        // When
        List<CriancaDTO> resultado = criancaPPPService.listarCrianca(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(1, resultado.size());
        assertEquals(criancaDTO, resultado.get(0));
        assertEquals(nomeCrianca, resultado.get(0).getNome());
        assertEquals(idCrianca, resultado.get(0).getId());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper).toDTOList(criancasList);
    }

    @Test
    public void testListarCrianca_SemResultados_DeveRetornarListaVazia() {
        // Given
        List<Crianca> listaVazia = new ArrayList<>();
        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(listaVazia);

        // When
        List<CriancaDTO> resultado = criancaPPPService.listarCrianca(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());
        assertEquals(0, resultado.size());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper, never()).toDTOList(anyList());
    }

    @Test
    public void testListarCrianca_DAORetornaNulo_DeveRetornarListaVazia() {
        // Given
        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(null);

        // When
        List<CriancaDTO> resultado = criancaPPPService.listarCrianca(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());
        assertEquals(0, resultado.size());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper, never()).toDTOList(anyList());
    }

    @Test
    public void testListarCriancasComunicacaoInicial_ComResultados_DeveRetornarListaComMoradas() {
        // Given
        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(criancasList);
        when(criancaMapper.toInicialDTOList(criancasList)).thenReturn(criancasIniciaisDTOList);
        when(criancaMoradaService.listarMoradaPrincipalPorIdCrianca(idCrianca)).thenReturn(moradasList);

        // When
        List<CriancaInicialDTO> resultado = criancaPPPService.listarCriancasComunicacaoInicial(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(1, resultado.size());
        assertEquals(criancaInicialDTO, resultado.get(0));
        assertEquals(moradasList, resultado.get(0).getMoradas());
        assertEquals(1, resultado.get(0).getMoradas().size());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper).toInicialDTOList(criancasList);
        verify(criancaMoradaService).listarMoradaPrincipalPorIdCrianca(idCrianca);
    }

    @Test
    public void testListarCriancasComunicacaoInicial_SemResultados_DeveRetornarListaVazia() {
        // Given
        List<Crianca> listaVazia = new ArrayList<>();
        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(listaVazia);

        // When
        List<CriancaInicialDTO> resultado = criancaPPPService.listarCriancasComunicacaoInicial(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());
        assertEquals(0, resultado.size());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper, never()).toInicialDTOList(anyList());
        verify(criancaMoradaService, never()).listarMoradaPrincipalPorIdCrianca(anyLong());
    }

    @Test
    public void testListarCriancasComunicacaoInicial_MultiplarCriancas_DeveCarregarMoradasParaTodas() {
        // Given
        Crianca segundaCrianca = new Crianca();
        segundaCrianca.setId(2L);
        segundaCrianca.setNome("Maria Silva");

        CriancaInicialDTO segundaCriancaDTO = new CriancaInicialDTO();
        segundaCriancaDTO.setId(2L);
        segundaCriancaDTO.setNome("Maria Silva");

        List<Crianca> multiplasChiancas = new ArrayList<>();
        multiplasChiancas.add(crianca);
        multiplasChiancas.add(segundaCrianca);

        List<CriancaInicialDTO> multiplosDTO = new ArrayList<>();
        multiplosDTO.add(criancaInicialDTO);
        multiplosDTO.add(segundaCriancaDTO);

        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(multiplasChiancas);
        when(criancaMapper.toInicialDTOList(multiplasChiancas)).thenReturn(multiplosDTO);
        when(criancaMoradaService.listarMoradaPrincipalPorIdCrianca(1L)).thenReturn(moradasList);
        when(criancaMoradaService.listarMoradaPrincipalPorIdCrianca(2L)).thenReturn(new ArrayList<MoradaDTO>());

        // When
        List<CriancaInicialDTO> resultado = criancaPPPService.listarCriancasComunicacaoInicial(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(2, resultado.size());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper).toInicialDTOList(multiplasChiancas);
        verify(criancaMoradaService).listarMoradaPrincipalPorIdCrianca(1L);
        verify(criancaMoradaService).listarMoradaPrincipalPorIdCrianca(2L);
    }

    @Test
    public void testBuscaCriancaPorId_ComResultado_DeveRetornarDTO() {
        // Given
        when(criancaDAO.buscarCriancaPorId(idCrianca)).thenReturn(crianca);
        when(criancaMapper.toDTO(crianca)).thenReturn(criancaDTO);

        // When
        CriancaDTO resultado = criancaPPPService.buscaCriancaPorId(idCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(criancaDTO, resultado);
        assertEquals(idCrianca, resultado.getId());
        assertEquals(nomeCrianca, resultado.getNome());

        verify(criancaDAO).buscarCriancaPorId(idCrianca);
        verify(criancaMapper).toDTO(crianca);
    }

    @Test
    public void testBuscaCriancaPorId_SemResultado_DeveRetornarNulo() {
        // Given
        when(criancaDAO.buscarCriancaPorId(idCrianca)).thenReturn(null);
        when(criancaMapper.toDTO(null)).thenReturn(null);

        // When
        CriancaDTO resultado = criancaPPPService.buscaCriancaPorId(idCrianca);

        // Then
        assertNull(resultado);

        verify(criancaDAO).buscarCriancaPorId(idCrianca);
        verify(criancaMapper).toDTO(null);
    }

    @Test
    public void testGetCriancaPorId_ComResultado_DeveRetornarEntidade() {
        // Given
        when(criancaDAO.buscarCriancaPorId(idCrianca)).thenReturn(crianca);

        // When
        Crianca resultado = criancaPPPService.getCriancaPorId(idCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(crianca, resultado);
        assertEquals(idCrianca, resultado.getId());
        assertEquals(nomeCrianca, resultado.getNome());

        verify(criancaDAO).buscarCriancaPorId(idCrianca);
    }

    @Test
    public void testGetCriancaPorId_SemResultado_DeveRetornarNulo() {
        // Given
        when(criancaDAO.buscarCriancaPorId(idCrianca)).thenReturn(null);

        // When
        Crianca resultado = criancaPPPService.getCriancaPorId(idCrianca);

        // Then
        assertNull(resultado);

        verify(criancaDAO).buscarCriancaPorId(idCrianca);
    }

    @Test
    public void testBuscaCriancasPorNome_ComResultados_DeveRetornarListaDTO() {
        // Given
        when(criancaDAO.buscaCriancasPorNome(query)).thenReturn(criancasList);
        when(criancaMapper.toDTOList(criancasList)).thenReturn(criancasDTOList);

        // When
        List<CriancaDTO> resultado = criancaPPPService.buscaCriancasPorNome(query);

        // Then
        assertNotNull(resultado);
        assertEquals(1, resultado.size());
        assertEquals(criancaDTO, resultado.get(0));

        verify(criancaDAO).buscaCriancasPorNome(query);
        verify(criancaMapper).toDTOList(criancasList);
    }

    @Test
    public void testBuscaCriancasPorNome_SemResultados_DeveRetornarListaVazia() {
        // Given
        List<Crianca> listaVazia = new ArrayList<>();
        List<CriancaDTO> listaDTOVazia = new ArrayList<>();

        when(criancaDAO.buscaCriancasPorNome(query)).thenReturn(listaVazia);
        when(criancaMapper.toDTOList(listaVazia)).thenReturn(listaDTOVazia);

        // When
        List<CriancaDTO> resultado = criancaPPPService.buscaCriancasPorNome(query);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());
        assertEquals(0, resultado.size());

        verify(criancaDAO).buscaCriancasPorNome(query);
        verify(criancaMapper).toDTOList(listaVazia);
    }

    @Test
    public void testBuscaCriancasPorNome_DAORetornaNulo_DeveChamarMapper() {
        // Given
        when(criancaDAO.buscaCriancasPorNome(query)).thenReturn(null);
        when(criancaMapper.toDTOList(null)).thenReturn(new ArrayList<CriancaDTO>());

        // When
        List<CriancaDTO> resultado = criancaPPPService.buscaCriancasPorNome(query);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());

        verify(criancaDAO).buscaCriancasPorNome(query);
        verify(criancaMapper).toDTOList(null);
    }

    @Test
    public void testListarCrianca_ComNomeNulo_DeveFuncionar() {
        // Given
        when(criancaDAO.listarCrianca(null)).thenReturn(new ArrayList<Crianca>());

        // When
        List<CriancaDTO> resultado = criancaPPPService.listarCrianca(null);

        // Then
        assertNotNull(resultado);
        assertTrue(resultado.isEmpty());

        verify(criancaDAO).listarCrianca(null);
    }

    @Test
    public void testBuscaCriancaPorId_ComIdNulo_DeveFuncionar() {
        // Given
        when(criancaDAO.buscarCriancaPorId(null)).thenReturn(null);
        when(criancaMapper.toDTO(null)).thenReturn(null);

        // When
        CriancaDTO resultado = criancaPPPService.buscaCriancaPorId(null);

        // Then
        assertNull(resultado);

        verify(criancaDAO).buscarCriancaPorId(null);
        verify(criancaMapper).toDTO(null);
    }

    @Test
    public void testListarCriancasComunicacaoInicial_CriancaSemMoradas_DeveFuncionar() {
        // Given
        List<MoradaDTO> listaVaziaMoradas = new ArrayList<>();

        when(criancaDAO.listarCrianca(nomeCrianca)).thenReturn(criancasList);
        when(criancaMapper.toInicialDTOList(criancasList)).thenReturn(criancasIniciaisDTOList);
        when(criancaMoradaService.listarMoradaPrincipalPorIdCrianca(idCrianca)).thenReturn(listaVaziaMoradas);

        // When
        List<CriancaInicialDTO> resultado = criancaPPPService.listarCriancasComunicacaoInicial(nomeCrianca);

        // Then
        assertNotNull(resultado);
        assertEquals(1, resultado.size());
        assertNotNull(resultado.get(0).getMoradas());
        assertTrue(resultado.get(0).getMoradas().isEmpty());

        verify(criancaDAO).listarCrianca(nomeCrianca);
        verify(criancaMapper).toInicialDTOList(criancasList);
        verify(criancaMoradaService).listarMoradaPrincipalPorIdCrianca(idCrianca);
    }
}