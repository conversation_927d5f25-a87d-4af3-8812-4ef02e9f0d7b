package pt.segsocial.pcj.ppp.service.comunicacao;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.ppp.dto.comunicacao.SituacaoPerigoDTO;
import pt.segsocial.pcj.ppp.jpa.dao.comunicacao.SituacaoPerigoDAO;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.SituacaoPerigoPpp;
import pt.segsocial.pcj.ppp.mapper.comunicacao.SituacaoPerigoMapper;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SituacaoPerigoServiceTest {

    @InjectMocks
    private SituacaoPerigoService situacaoPerigoService;

    @Mock
    private SituacaoPerigoDAO situacaoPerigoDAO;

    @Mock
    private SituacaoPerigoMapper situacaoPerigoMapper;

    private Long idComunicacao;
    private Comunicacao comunicacao;
    private SituacaoPerigoDTO situacaoPerigoDTO;
    private SituacaoPerigoPpp situacaoPerigoPpp;
    private List<SituacaoPerigoDTO> situacaoPerigoDTOs;
    private List<SituacaoPerigoPpp> situacaoPerigoPpps;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        idComunicacao = 1L;

        // Criar Comunicacao
        comunicacao = new Comunicacao();
        comunicacao.setId(idComunicacao);

        // Criar SituacaoPerigoDTO
        situacaoPerigoDTO = new SituacaoPerigoDTO();
        situacaoPerigoDTO.setId(1L);
        situacaoPerigoDTO.setNuipc("123.456.789-00");
        situacaoPerigoDTO.setDescricaoFactos("Situação de teste");

        // Criar SituacaoPerigoPpp
        situacaoPerigoPpp = new SituacaoPerigoPpp();
        situacaoPerigoPpp.setId(1L);
        situacaoPerigoPpp.setNuipc("12345678900");
        situacaoPerigoPpp.setDescricaoFactos("Situação de teste");
        situacaoPerigoPpp.setComunicacao(comunicacao);

        // Criar listas
        situacaoPerigoDTOs = new ArrayList<>();
        situacaoPerigoDTOs.add(situacaoPerigoDTO);

        situacaoPerigoPpps = new ArrayList<>();
        situacaoPerigoPpps.add(situacaoPerigoPpp);
    }

    @Test
    public void testSalvarSituacoes_Sucesso_DeveLimparNuipcESalvar() throws DomainException {
        // Given
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);
        doNothing().when(situacaoPerigoDAO).create(situacaoPerigoPpp);

        // When
        situacaoPerigoService.salvarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        ArgumentCaptor<SituacaoPerigoDTO> dtoCaptor = ArgumentCaptor.forClass(SituacaoPerigoDTO.class);
        verify(situacaoPerigoMapper).toEntity(dtoCaptor.capture());
        assertEquals("12345678900", dtoCaptor.getValue().getNuipc()); // Verificar se foi limpo

        ArgumentCaptor<SituacaoPerigoPpp> entityCaptor = ArgumentCaptor.forClass(SituacaoPerigoPpp.class);
        verify(situacaoPerigoDAO).create(entityCaptor.capture());
        assertEquals(comunicacao, entityCaptor.getValue().getComunicacao());
    }

    @Test
    public void testSalvarSituacoes_MultiplasEntidades_DeveSalvarTodas() throws DomainException {
        // Given
        SituacaoPerigoDTO segundoDTO = new SituacaoPerigoDTO();
        segundoDTO.setId(2L);
        segundoDTO.setNuipc("987-654-321");

        SituacaoPerigoPpp segundaEntity = new SituacaoPerigoPpp();
        segundaEntity.setId(2L);
        segundaEntity.setNuipc("987654321");

        situacaoPerigoDTOs.add(segundoDTO);

        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);
        when(situacaoPerigoMapper.toEntity(segundoDTO)).thenReturn(segundaEntity);
        doNothing().when(situacaoPerigoDAO).create(any(SituacaoPerigoPpp.class));

        // When
        situacaoPerigoService.salvarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        verify(situacaoPerigoMapper, times(2)).toEntity(any(SituacaoPerigoDTO.class));
        verify(situacaoPerigoDAO, times(2)).create(any(SituacaoPerigoPpp.class));
        assertEquals("12345678900", situacaoPerigoDTOs.get(0).getNuipc());
        assertEquals("987654321", situacaoPerigoDTOs.get(1).getNuipc());
    }

    @Test
    public void testSalvarSituacoes_NuipcNulo_DeveManterNulo() throws DomainException {
        // Given
        situacaoPerigoDTO.setNuipc(null);
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);
        doNothing().when(situacaoPerigoDAO).create(situacaoPerigoPpp);

        // When
        situacaoPerigoService.salvarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        assertNull(situacaoPerigoDTO.getNuipc());
        verify(situacaoPerigoMapper).toEntity(situacaoPerigoDTO);
        verify(situacaoPerigoDAO).create(situacaoPerigoPpp);
    }

    @Test
    public void testSalvarSituacoes_ListaVazia_NaoDeveFazerNada() throws DomainException {
        // Given
        List<SituacaoPerigoDTO> listaVazia = new ArrayList<>();

        // When
        situacaoPerigoService.salvarSituacoes(listaVazia, comunicacao);

        // Then
        verify(situacaoPerigoMapper, never()).toEntity(any(SituacaoPerigoDTO.class));
        verify(situacaoPerigoDAO, never()).create(any(SituacaoPerigoPpp.class));
    }

    @Test
    public void testSalvarSituacoes_ErroDAO_DeveLancarExcecao() throws DomainException {
        // Given
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);
        doThrow(new DomainException("Erro ao salvar")).when(situacaoPerigoDAO).create(situacaoPerigoPpp);

        // When & Then
        try {
            situacaoPerigoService.salvarSituacoes(situacaoPerigoDTOs, comunicacao);
            fail("Deveria ter lançado DomainException");
        } catch (DomainException e) {
            assertEquals("Erro ao salvar", e.getMessage());
        }

        verify(situacaoPerigoDAO).create(situacaoPerigoPpp);
    }

    @Test
    public void testAtualizarSituacoes_SituacoesNovas_DeveAtualizarTodas() throws DomainException {
        // Given
        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(new ArrayList<SituacaoPerigoPpp>());
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);

        // When
        situacaoPerigoService.atualizarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        verify(situacaoPerigoDAO).findByIdComunicacao(idComunicacao);
        verify(situacaoPerigoMapper).toEntity(situacaoPerigoDTO);
        verify(situacaoPerigoDAO).update(situacaoPerigoPpp);
        verify(situacaoPerigoDAO, never()).remove(any(SituacaoPerigoPpp.class));
        assertEquals("12345678900", situacaoPerigoDTO.getNuipc());
    }

    @Test
    public void testAtualizarSituacoes_SituacoesExistentesParaRemover_DeveRemover() throws DomainException {
        // Given
        SituacaoPerigoPpp existenteParaRemover = new SituacaoPerigoPpp();
        existenteParaRemover.setId(999L);

        List<SituacaoPerigoPpp> existentes = new ArrayList<>();
        existentes.add(existenteParaRemover);

        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(existentes);
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);

        // When
        situacaoPerigoService.atualizarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        verify(situacaoPerigoDAO).update(situacaoPerigoPpp);
        verify(situacaoPerigoDAO).remove(existenteParaRemover);
    }

    @Test
    public void testAtualizarSituacoes_SituacaoExistenteAtualizada_NaoDeveRemover() throws DomainException {
        // Given
        SituacaoPerigoPpp existente = new SituacaoPerigoPpp();
        existente.setId(1L); // Mesmo ID da situação que será atualizada

        List<SituacaoPerigoPpp> existentes = new ArrayList<>();
        existentes.add(existente);

        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(existentes);
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);

        // When
        situacaoPerigoService.atualizarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        verify(situacaoPerigoDAO).update(situacaoPerigoPpp);
        verify(situacaoPerigoDAO, never()).remove(existente);
    }

    @Test
    public void testAtualizarSituacoes_IdNuloNaComparacao_DeveIgnorar() throws DomainException {
        // Given
        SituacaoPerigoPpp existenteComIdNulo = new SituacaoPerigoPpp();
        existenteComIdNulo.setId(null);

        SituacaoPerigoPpp recebidaComIdNulo = new SituacaoPerigoPpp();
        recebidaComIdNulo.setId(null);

        List<SituacaoPerigoPpp> existentes = new ArrayList<>();
        existentes.add(existenteComIdNulo);

        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(existentes);
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(recebidaComIdNulo);

        // When
        situacaoPerigoService.atualizarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        verify(situacaoPerigoDAO).remove(existenteComIdNulo); // Deve remover pois IDs nulos não são considerados iguais
    }

    @Test
    public void testRemoverSituacoes_ComSituacoes_DeveRemoverTodas() throws DomainException {
        // Given
        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(situacaoPerigoPpps);
        doNothing().when(situacaoPerigoDAO).remove(situacaoPerigoPpp);

        // When
        situacaoPerigoService.removerSituacoes(idComunicacao);

        // Then
        verify(situacaoPerigoDAO).findByIdComunicacao(idComunicacao);
        verify(situacaoPerigoDAO).remove(situacaoPerigoPpp);
    }

    @Test
    public void testRemoverSituacoes_SemSituacoes_NaoDeveRemoverNada() throws DomainException {
        // Given
        List<SituacaoPerigoPpp> listaVazia = new ArrayList<>();
        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(listaVazia);

        // When
        situacaoPerigoService.removerSituacoes(idComunicacao);

        // Then
        verify(situacaoPerigoDAO).findByIdComunicacao(idComunicacao);
        verify(situacaoPerigoDAO, never()).remove(any(SituacaoPerigoPpp.class));
    }

    @Test
    public void testRemoverSituacoes_MultiplasSituacoes_DeveRemoverTodas() throws DomainException {
        // Given
        SituacaoPerigoPpp segunda = new SituacaoPerigoPpp();
        segunda.setId(2L);

        SituacaoPerigoPpp terceira = new SituacaoPerigoPpp();
        terceira.setId(3L);

        List<SituacaoPerigoPpp> multiplas = new ArrayList<>();
        multiplas.add(situacaoPerigoPpp);
        multiplas.add(segunda);
        multiplas.add(terceira);

        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(multiplas);
        doNothing().when(situacaoPerigoDAO).remove(any(SituacaoPerigoPpp.class));

        // When
        situacaoPerigoService.removerSituacoes(idComunicacao);

        // Then
        verify(situacaoPerigoDAO, times(3)).remove(any(SituacaoPerigoPpp.class));
        verify(situacaoPerigoDAO).remove(situacaoPerigoPpp);
        verify(situacaoPerigoDAO).remove(segunda);
        verify(situacaoPerigoDAO).remove(terceira);
    }

    @Test
    public void testRemoverSituacoes_ErroDAO_DeveLancarExcecao() throws DomainException {
        // Given
        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(situacaoPerigoPpps);
        doThrow(new DomainException("Erro ao remover")).when(situacaoPerigoDAO).remove(situacaoPerigoPpp);

        // When & Then
        try {
            situacaoPerigoService.removerSituacoes(idComunicacao);
            fail("Deveria ter lançado DomainException");
        } catch (DomainException e) {
            assertEquals("Erro ao remover", e.getMessage());
        }

        verify(situacaoPerigoDAO).remove(situacaoPerigoPpp);
    }

    @Test
    public void testLimparSomenteNumeros_ValoresComCaracteresEspeciais() throws Exception {
        // Usando reflexão para testar método privado
        java.lang.reflect.Method metodo = SituacaoPerigoService.class.getDeclaredMethod("limparSomenteNumeros", String.class);
        metodo.setAccessible(true);

        // Test cases
        assertEquals("12345678900", metodo.invoke(situacaoPerigoService, "123.456.789-00"));
        assertEquals("123456789", metodo.invoke(situacaoPerigoService, "123-456-789"));
        assertEquals("123456789", metodo.invoke(situacaoPerigoService, "123.456.789"));
        assertEquals("123456789", metodo.invoke(situacaoPerigoService, "123 456 789"));
        assertEquals("123456789", metodo.invoke(situacaoPerigoService, "abc123def456ghi789"));
        assertEquals("", metodo.invoke(situacaoPerigoService, "abcdefg"));
        assertEquals("", metodo.invoke(situacaoPerigoService, ""));
        assertNull(metodo.invoke(situacaoPerigoService, (String) null));
    }

    @Test
    public void testAtualizarSituacoes_ListaVazia_ApenasRemoveExistentes() throws DomainException {
        // Given
        List<SituacaoPerigoDTO> listaVazia = new ArrayList<>();
        when(situacaoPerigoDAO.findByIdComunicacao(idComunicacao)).thenReturn(situacaoPerigoPpps);
        doNothing().when(situacaoPerigoDAO).remove(situacaoPerigoPpp);

        // When
        situacaoPerigoService.atualizarSituacoes(listaVazia, comunicacao);

        // Then
        verify(situacaoPerigoDAO).findByIdComunicacao(idComunicacao);
        verify(situacaoPerigoDAO).remove(situacaoPerigoPpp);
        verify(situacaoPerigoMapper, never()).toEntity(any(SituacaoPerigoDTO.class));
        verify(situacaoPerigoDAO, never()).update(any(SituacaoPerigoPpp.class));
    }

    @Test
    public void testSalvarSituacoes_NuipcComEspacos_DeveLimpar() throws DomainException {
        // Given
        situacaoPerigoDTO.setNuipc("  123 456 789  ");
        when(situacaoPerigoMapper.toEntity(situacaoPerigoDTO)).thenReturn(situacaoPerigoPpp);
        doNothing().when(situacaoPerigoDAO).create(situacaoPerigoPpp);

        // When
        situacaoPerigoService.salvarSituacoes(situacaoPerigoDTOs, comunicacao);

        // Then
        assertEquals("123456789", situacaoPerigoDTO.getNuipc());
        verify(situacaoPerigoMapper).toEntity(situacaoPerigoDTO);
        verify(situacaoPerigoDAO).create(situacaoPerigoPpp);
    }
}
