<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="05 seconds" debug="true">
    <appender name="RESOURCES" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${user.home}/logs/resources.${com.sun.aas.instanceName}.log</File>
        <Encoding>ISO-8859-15</Encoding>
        <encoder>
            <pattern>%d [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <maxIndex>${iies.logBackupIndex}</maxIndex>
            <FileNamePattern>${user.home}/logs/Resources.${com.sun.aas.instanceName}.log.%i.gz</FileNamePattern>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>40MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="FRAB.ENGINE" class="pt.segsocial.iies.common.logging.SiftingAppender">
        <discriminator class="pt.segsocial.iies.common.logging.MultiPatternMDCBasedDiscriminator">
            <key>loggingPathPrefix</key>
            <identifierKeys>batch.name|app.name</identifierKeys>
            <patterns>
                BATCH/%mdc{batch.subsystem:-UNKNOWN}/%mdc{batch.name:-UNKNOWN}.%mdc{batch.execution.id:-0}|%mdc{app.name:-UNKNOWN}
            </patterns>
            <defaultValue>FRAB.ENGINE</defaultValue>
        </discriminator>
        <sift>
            <appender class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}/logs/${loggingPathPrefix}.${com.sun.aas.instanceName}.log
                </file>
                <encoder>
                    <pattern>
                        [#|%date|%level|%logger{10}|%thread|app.name:%mdc{app.name}|app.version:%mdc{app.version}|remote.client.host:%mdc{remote.client.host}|remote.client.ip:%mdc{remote.client.ip}|remote.os.user:%mdc{remote.os.user}|operation:%mdc{operation}|remote.user:%mdc{remote.user}|remote.timestamp:%mdc{remote.timestamp}|server.ip:%mdc{server.ip}|request.id:%mdc{request.id}|class.name:%mdc{class.name}|method.name:%mdc{method.name}|client.tz:%mdc{client.tz}|batch.name:%mdc{batch.name}|batch.subsystem:%mdc{batch.subsystem}|batch.group.id:%mdc{batch.group.id}|batch.execution.id:%mdc{batch.execution.id}|batch.unit.id:%mdc{batch.unit.id}|trace.id:%mdc{trace.id}|%message%xException|#]%n
                    </pattern>
                </encoder>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <maxHistory>5</maxHistory>
                    <fileNamePattern>${user.home}/logs/${loggingPathPrefix}.${com.sun.aas.instanceName}.%d.%i.log
                    </fileNamePattern>
                    <timeBasedFileNamingAndTriggeringPolicy
                            class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <maxFileSize>10MB</maxFileSize>
                    </timeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
            </appender>
        </sift>
    </appender>

    <appender name="pcj" class="pt.segsocial.iies.common.logging.SiftingAppender">
        <discriminator class="pt.segsocial.iies.common.logging.MultiPatternMDCBasedDiscriminator">
            <key>loggingPathPrefix</key>
            <identifierKeys>batch.name|app.name</identifierKeys>
            <patterns>
                BATCH/%mdc{batch.subsystem:-UNKNOWN}/%mdc{batch.name:-UNKNOWN}.%mdc{batch.execution.id:-0}|%mdc{app.name:-UNKNOWN}
            </patterns>
            <defaultValue>PCJ</defaultValue>
        </discriminator>
        <sift>
            <appender class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}/logs/${loggingPathPrefix}.${com.sun.aas.instanceName}.log
                </file>
                <encoder>
                    <pattern>
                        [#|%date|%level|%logger{10}|%thread|app.name:%mdc{app.name}|app.version:%mdc{app.version}|remote.client.host:%mdc{remote.client.host}|remote.client.ip:%mdc{remote.client.ip}|remote.os.user:%mdc{remote.os.user}|operation:%mdc{operation}|remote.user:%mdc{remote.user}|remote.timestamp:%mdc{remote.timestamp}|server.ip:%mdc{server.ip}|request.id:%mdc{request.id}|class.name:%mdc{class.name}|method.name:%mdc{method.name}|client.tz:%mdc{client.tz}|batch.name:%mdc{batch.name}|batch.subsystem:%mdc{batch.subsystem}|batch.group.id:%mdc{batch.group.id}|batch.execution.id:%mdc{batch.execution.id}|batch.unit.id:%mdc{batch.unit.id}|fraw.session.uuid:%mdc{fraw_sessionUUID}|fraw.query.string:%mdc{fraw_queryString}|trace.id:%mdc{trace.id}|%message%xException|#]%n
                    </pattern>
                </encoder>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <maxHistory>5</maxHistory>
                    <fileNamePattern>${user.home}/logs/${loggingPathPrefix}.${com.sun.aas.instanceName}.%d.%i.log
                    </fileNamePattern>
                    <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <maxFileSize>10MB</maxFileSize>
                    </timeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
            </appender>
        </sift>
    </appender>

    <appender name="gelf" class="biz.paluch.logging.gelf.logback.GelfLogbackAppender">
        <host>udp:_XXXX_logging.seg-social.pt</host>
        <port>_XXXX_</port>
        <version>1.1</version>
        <facility>PCJ</facility>
        <extractStackTrace>true</extractStackTrace>
        <filterStackTrace>true</filterStackTrace>
        <mdcProfiling>true</mdcProfiling>
        <timestampPattern>yyyy-MM-dd HH:mm:ss,SSS</timestampPattern>
        <maximumMessageSize>8192</maximumMessageSize>
        <mdcFields>app.name, app.version, correlation, remote.client.host, remote.client.ip, remote.os.user, operation,
            remote.user, remote.timestamp, server.ip, request.id, class.name, method.name, client.tz, batch.name,
            batch.subsystem, batch.group.id, batch.execution.id, batch.unit.id, fraw_sessionID, fraw_sessionUUID,
            fraw_queryString, fraw_clientExceptionUUID, trace.id
        </mdcFields>
    </appender>


    <logger name="FRAB.ENGINE" additivity="false" level="error">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="FRAB.AGENT" additivity="false" level="error">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.iies.frab" additivity="false" level="error">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.apache.shiro" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.fraw" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.gus" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.dcid" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.ocpsoft.rewrite" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.hibernate.SQL" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.hibernate" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.hibernate.type" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="net.sf.ehcache" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.jgroups" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="javax.enterprise.resource.webcontainer.jsf.config" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>

    <logger name="org.apache.naming" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>

    <logger name="org.apache.myfaces" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.atmosphere" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="com.sun.jersey" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.primefaces" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.omnifaces" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.glassfish" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="javax.enterprise" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.jboss.weld" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="javax.enterprise.system.container.web.com.sun.enterprise.web" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="javax.enterprise.webservices.org.glassfish.webservices" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="javax.enterprise.system.container.ejb.com.sun.ejb.containers.builder" level="error"
            additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.glassfish.ha.store.adapter.file.FileBackingStore" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.atmosphere.cpr.MetaBroadcaster" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.arf" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="org.apache.shiro.authc.pam.ModularRealmAuthenticator" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.id" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>
    <logger name="pt.segsocial.gvr" level="error" additivity="false">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </logger>

    <logger name="BD" additivity="false" level="error">
        <appender-ref ref="RESOURCES"/>
        <appender-ref ref="gelf"/>
    </logger>

    <root level="error">
        <appender-ref ref="pcj"/>
        <appender-ref ref="gelf"/>
    </root>
</configuration>

