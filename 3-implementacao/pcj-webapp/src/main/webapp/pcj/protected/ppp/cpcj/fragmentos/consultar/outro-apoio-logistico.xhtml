<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <style>
    .corpo .ui-chkbox .ui-chkbox-box.ui-state-focus, .corpo .ui-chkbox .ui-chkbox-box.ui-state-active {
        background: #c5c5c5;
        border: 1px solid #c5c5c5;
    }
    .corpo .ui-chkbox .ui-chkbox-box .ui-chkbox-icon.ui-icon.ui-icon-blank {
        background: #c5c5c5;
        border: 1px solid #c5c5c5;
        border-radius: 0.5rem;
        width: 100%;
        height: 100%;
    }

    .corpo .select-radio-2 .ui-radiobutton-box.ui-state-active {
        border: 2px solid transparent;
        background: #c5c5c5;
    }

    .ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
        background: none;
        border-color: #c5c5c5;
        color: #ccc;
    }
    </style>

    <div class="accordion basic">
        <p:accordionPanel value="dados"
                          varStatus="myIndex" id="pcjResumoOutroApoio"
                          styleClass="accordion-container" activeIndex="-1">
            <p:tab titleStyleClass="accordion-label">
                <f:facet name="title" styleClass="accordion-body">
                    <h:panelGroup layout="block" styleClass="accordion-body">
                        <div class="accordion-body-description">
                            <div class="accordion-body-description-left">
                                <span class="accordion-body-description-left-title">Outros</span>
                            </div>
                        </div>
                    </h:panelGroup>
                </f:facet>
                <div class="row-fluid mb-5">
                    <h5 class="bold">Outro Apoio Logístico</h5>
                </div>
        <div class="row-fluid mb-5">
            <div class="span6">
                <p:outputLabel value="Geral" />
                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutros1"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.livroReclamacao}"
                                         itemLabel="Livro de reclamações"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">
                </p:selectBooleanCheckbox>
                <p:selectBooleanCheckbox id="cpcjConsultahkBoxOutros2"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.destruicaoPapel}"
                                         itemLabel="Destruição de papel"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">

                </p:selectBooleanCheckbox>

                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutros3"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.mobiliarioPostoTrabalho}"
                                         itemLabel="Mobiliário de posto de trabalho"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">

                </p:selectBooleanCheckbox>

                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutros4"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.mobiliarioAtendimento}"
                                         itemLabel="Mobiliário para atendimento"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">

                </p:selectBooleanCheckbox>
            </div>
            <div class="span6">
                <p:outputLabel value="Serviços disponibilizados pelo município" />
                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutros5"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.manutencao}"
                                         itemLabel="Manutenção"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">

                </p:selectBooleanCheckbox>
                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutros6"
                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.limpeza}"
                                         itemLabel="Limpeza"
                                         disabled="true" styleClass="ui-chkbox-box.ui-state-active-disable">

                </p:selectBooleanCheckbox>

            </div>
        </div>
                <div class="row-fluid mb-5">

                    <div class="span6">
                        <p:outputLabel value="Material de escritório" />
                        <p:selectOneRadio id="cpcjConsultarRB5"
                                          value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.codMaterialEscritorio}"
                                          styleClass="select-radio-2 sm" layout="grid" columns="1"
                                          disabled="true" >
                            <f:selectItems value="#{pcjConsultarCPCJBean.materiaisEscritorio}"
                                           var="item"
                                           itemLabel="#{item.designacao}"
                                           itemValue="#{item.codigoDetalheDominio}"/>

                        </p:selectOneRadio>
                    </div>
                    <div class="span6">
                        <p:outputLabel value="Consumíveis de escritório" />
                        <p:selectOneRadio id="cpcjConsultarRB6"
                                          value="#{pcjConsultarCPCJBean.comissaoDTO.detalheInfoLogisticaDTO.codConsumivelEscritorio}"
                                          styleClass="select-radio-2 sm" layout="grid" columns="1"
                                          disabled="true"  >
                            <f:selectItems value="#{pcjConsultarCPCJBean.materiaisEscritorio}"
                                           var="item"
                                           itemLabel="#{item.designacao}"
                                           itemValue="#{item.codigoDetalheDominio}"/>
                        </p:selectOneRadio>

                    </div>
                </div>

                <div class="row-fluid mb-3">
                    <div class="span6">
                        <p:outputLabel id="seguroComissarios" value="Seguro de comissários" styleClass="dica mr-6">
                            <p:tooltip for="seguroComissarios" value="#{pcjComponentMessage['tooltip.seguro.comissionario']}" />
                        </p:outputLabel>
                        <div class="row-fluid mb-5">
                            <div class="span12">
                                <p:selectBooleanCheckbox id="cpcjConsultaChkBoxOutroApoioSeg"
                                                         value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.seguroComissario}"
                                                         itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('SEGURO_COMISSARIO')}"
                                                         disabled="true" >
                                </p:selectBooleanCheckbox>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row-fluid mb-5">
                    <div class="row-fluid mb-5">
                        <h5 class="bold">Deslocações</h5>
                    </div>
                <div class="row-fluid mb-5">
                    <div class="span12">
                        <p:outputLabel value="Viatura" />
                        <p:selectBooleanCheckbox id="cpcjEditarChkBoxOutroApoio1"
                                                 value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.viatura}"
                                                 itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('VIATURA')}"
                                                 disabled="true" >
                        </p:selectBooleanCheckbox>
                        <p:selectBooleanCheckbox id="cpcjEditarChkBoxOutroApoio2"
                                                 value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.viaturaCaracterizada}"
                                                 itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('VIATURA_CARACTERIZADA')}"
                                                 disabled="true" >
                        </p:selectBooleanCheckbox>

                        <p:selectBooleanCheckbox id="cpcjEditarChkBoxOutroApoio3"
                                                 value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.seguroSemEntidade}"
                                                 itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('SEGURO_SEM_ENTIDADE')}"
                                                 disabled="true" >
                        </p:selectBooleanCheckbox>

                    </div>
                </div>
                </div>
                <div class="row-fluid mb-5">
                    <div class="span12">
                        <p:outputLabel value="Motorista"/>

                        <p:selectOneRadio id="cpcjConsultarRB7"
                                          value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.codMotorista}"
                                          styleClass="select-radio-2 sm" layout="grid" columns="1"
                                          disabled="true" >
                            <f:selectItems value="#{pcjConsultarCPCJBean.motoristas}"
                                           var="item"
                                           itemLabel="#{item.designacao}"
                                           itemValue="#{item.codigoDetalheDominio}"/>
                        </p:selectOneRadio>

                    </div>
                </div>
                <div class="row-fluid mb-5">
                    <div class="row-fluid mb-5">
                        <h5 class="bold">Fundo de maneio</h5>
                    </div>
                    <div class="row-fluid mb-5">
                        <div class="span12">
                            <p:outputLabel value="Fundo de maneio"/>
                            <p:selectBooleanCheckbox id="cpcjConsultarChkBoxOutroApoio4"
                                                     value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.fundoManeioDisponivel}"
                                                     itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('FUNDO_MANEIO_DISPONIVEL')}"
                                                     disabled="true">
                            </p:selectBooleanCheckbox>

                            <p:selectBooleanCheckbox id="cpcjConsultarChkBoxOutroApoio5"
                                                     value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.fundoManeioReposicao}"
                                                     itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('FUNDO_MANEIO_REPOSICAO')}"
                                                     disabled="true">
                            </p:selectBooleanCheckbox>

                            <p:selectBooleanCheckbox id="cpcjConsultarChkBoxOutroApoio6"
                                                     value="#{pcjConsultarCPCJBean.comissaoDTO.detalheOutrosDTO.fundoManeioMovimentacao}"
                                                     itemLabel="#{pcjEditarCpcjBean.getTextChkBoxEnum('FUNDO_MANEIO_MOVIMENTACAO')}"
                                                     disabled="true">
                            </p:selectBooleanCheckbox>
                        </div>

                    </div>
                </div>

            </p:tab>
        </p:accordionPanel>
    </div>
</ui:fragment>