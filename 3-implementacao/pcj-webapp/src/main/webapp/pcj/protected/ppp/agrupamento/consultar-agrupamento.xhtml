<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="bean" value="#{pcjConsultarAgrupamentoBean}"/>

    <ui:param name="pageTitle" value="#{bean.tituloTxt}"/>

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessagesPesquisa" class="info" layout="block">
            <ul>
                <h:outputText escape="false" value="#{pcjComponentMessage['agrupamento.infoMensagem']}"/>
            </ul>
        </h:panelGroup>
    </ui:define>

    <ui:define name="content">
        <h:form id="pcjPesquisarCpcj">
            <h:panelGroup id="pcjPainelPesquisaGrupo">
                <div class="row-fluid mb-5">
                    <div class="titulo-seccao">
                        <h2 >Pesquisa</h2>
                    </div>
                </div>

                <p:outputPanel>
                    <div class="row-fluid mb-5">
                        <div class="span4">
                            <p:outputLabel id="labelPesquisaCodigoCpcj" for="inputPesquisaCodigoCpcj"
                                           value="Código da CPCJ"/>
                            <p:inputText id="inputPesquisaCodigoCpcj"
                                         value="#{bean.filtroPesquisaDto.codigoCpcj}" maxlength="100"/>
                        </div>

                        <div class="span8">

                            <p:outputLabel id="labelPesquisaNomeCpcj" for="inputPesquisaNomeCpcj"
                                           value="Nome"/>
                            <p:inputText id="inputPesquisaNomeCpcj"
                                         value="#{bean.filtroPesquisaDto.nomeCpcj}" maxlength="100"/>
                        </div>
                    </div>
                </p:outputPanel>

                <p:outputPanel id="pcjPainelPesquisaAvancadaCpcj">
                    <div class="row-fluid acoes">
                        <div class="span12">
                            <div class="row-fluid">
                                <a id="pesquisaAvancadaLink"
                                   href="#" class="advanced-search"
                                   onclick="jQuery('#pcjDivPesquisaAvancada').slideToggle(); jQuery('#pesquisaAvancadaLink').hasClass('aberto') ? jQuery('#pesquisaAvancadaLink').removeClass('aberto') : jQuery('#pesquisaAvancadaLink').addClass('aberto'); return false;">
                                    avançada
                                </a>
                            </div>
                            <div id="pcjDivPesquisaAvancada" class="ui-helper-hidden mt-5">
                                <h:panelGroup id="pcjPanelSelecaoComissao" layout="block">
                                    <div class="row-fluid mb-5">
                                        <div class="span4">
                                            <p:outputLabel for="pcjDistritos" value="Distrito" />
                                            <p:selectOneMenu id="pcjDistritos" styleClass="max-field-size mb-5"
                                                             value="#{pcjConsultarAgrupamentoBean.filtroPesquisaDto.distritoDto}"
                                                             effect="fade" var="t" filter="true"
                                                             converter="omnifaces.SelectItemsConverter"
                                                             filterMatchMode="contains">

                                                <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                                                <f:selectItems value="#{pcjConsultarAgrupamentoBean.distritos}" var="distrito"
                                                               itemValue="#{distrito}" itemLabel="#{distrito.nome}"/>

                                                <p:ajax event="change" update="pcjConcelhos" process="@this"
                                                        listener="#{pcjConsultarAgrupamentoBean.distritoSelecionadoListener()}"
                                                        onstart="PF('frawPageBlocker').show()"
                                                        onerror="PF('frawPageBlocker').hide()"
                                                        onsuccess="PF('frawPageBlocker').hide()"/>
                                                <p:column>
                                                    <h:outputText value="#{t.nome}"/>
                                                </p:column>
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="span4">
                                            <p:outputLabel for="pcjConcelhos" value="Concelho" />
                                            <p:selectOneMenu id="pcjConcelhos" styleClass="max-field-size mb-5"
                                                             value="#{pcjConsultarAgrupamentoBean.filtroPesquisaDto.concelhoDto}"
                                                             effect="fade" var="c" filter="true"
                                                             converter="omnifaces.SelectItemsConverter"
                                                             filterMatchMode="contains">

                                                <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                                                <f:selectItems value="#{pcjConsultarAgrupamentoBean.concelhos}" var="concelho"
                                                               itemValue="#{concelho}"
                                                               itemLabel="#{concelho.nome}"/>
                                                <p:column>
                                                    <h:outputText value="#{c.nome}"/>
                                                </p:column>
                                            </p:selectOneMenu>
                                        </div>

                                    </div>
                                </h:panelGroup>

                            </div>
                        </div>
                    </div>
                </p:outputPanel>
            </h:panelGroup>

            <br/>
            <div class="row-fluid acoes mb-5">
                <div class="span12">
                    <p:commandLink id="btnLimparPesquisaGrupo"
                                   styleClass="mr-4"
                                   value="Limpar"
                                   action="#{bean.btnLimparPesquisa()}"
                                   resetValues="true"
                                   update="@form"
                                   process="@this"
                                   onstart="PF('frawPageBlocker').show()"
                                   onerror="PF('frawPageBlocker').hide()"
                                   onsuccess="PF('frawPageBlocker').hide()"/>

                    <p:commandButton id="btnPesquisarProcesso" value="Pesquisar"
                                     action="#{bean.btnPesquisar()}"
                                     update="panelResultadoPainel" process="@this pcjPainelPesquisaGrupo"
                                     onstart="PF('frawPageBlocker').show()"
                                     onerror="PF('frawPageBlocker').hide()"
                                     onsuccess="PF('frawPageBlocker').hide()"/>
                </div>
            </div>

            <div class="row-fluid mb-5">
                <div class="titulo-seccao">
                    <h2 class="texto-titulo-3">Lista de CPCJ</h2>
                </div>
            </div>

            <h:panelGroup class="row-fluid mb-5">
                <div class="span6">
                    <p:commandButton id="btnRegistarCpcj" value="Adicionar CPCJ"
                                     update="@form"
                                     process="@this"
                                     onstart="PF('frawPageBlocker').show()"
                                     onerror="PF('frawPageBlocker').hide()"
                                     onsuccess="PF('frawPageBlocker').hide()"
                                     action="#{pcjSubsystem.registarCpcj()}"
                    />
                </div>
            </h:panelGroup>

            <!-- DATA TABLE -->
            <div class="row-fluid mb-5 mt-5">
                <h:panelGroup id="panelResultadoPainel">
                    <div class="row-fluid mb-5">
                        <div class="table-list select-check-box-2 sm">
                            <p:dataTable id="pcjDataTableCPCJs" var="cpcj"
                                         value="#{bean.resultPesquisaAgrupamentoDTO}"
                                         rows="10"
                                         draggableColumns="false" paginator="true"
                                         emptyMessage="Não foram encontradas CPCJs."
                                         paginatorPosition="bottom" rowsPerPageTemplate="10,25,50"
                                         paginatorTemplate="{RowsPerPageDropdown} {PreviousPageLink} {CurrentPageReport} {NextPageLink}"
                                         currentPageReportTemplate="Página {currentPage}"
                                         rowIndexVar="rowIndexVar" rowStyleClass="resultado">

                                <f:facet name="caption">Resultado da pesquisa de CPCJ do agrupamento</f:facet>

                                <p:column style="width:2rem">
                                    <p:rowToggler/>
                                </p:column>

                                <p:column id="nomeColumn" headerText="Nome" styleClass="texto">
                                    <h:outputText value="#{cpcj.nome}">
                                    </h:outputText>
                                </p:column>

                                <p:column id="pcjProcessoAtivosColumn" headerText="Processos Ativos" styleClass="texto">
                                    <h:outputText id="pcjProcessoAtivosOutputText"
                                                  value="#{cpcj.numeroProcessoAtivos}"/>
                                </p:column>

                                <p:column id="pcjCodigoColumn" headerText="Código da CPCJ" styleClass="texto">
                                    <h:outputText value="#{cpcj.codigoCpcj}"/>
                                </p:column>

                                <p:column id="menuAcoes" styleClass="acoes text-right" headerText="Ações">
                                    <p:commandButton id="acoes" icon="fa fa-ellipsis-v" type="button"/>
                                    <p:overlayPanel id="acoesList" for="acoes" hideEffect="fade"
                                                    my="right top" at="right bottom" >
                                        <p:outputPanel id="carDetail">
                                            <ul>
                                                <li>
                                                    <p:commandLink id="pcjAgrupamentoConsutarCpcjBtn" value="Consultar CPCJ"
                                                                   immediate="true" process="@this"
                                                                   action="#{pcjSubsystem.consultarCPCJ}"
                                                                   onstart="PF('frawPageBlocker').show()"
                                                                   oncomplete="PF('frawPageBlocker').hide()"
                                                                   actionListener="#{pcjConsultarCPCJBean.definirIdComissao(bean.getPesquisarAgrupamentoDTO(), cpcj.id, true)}">
                                                    </p:commandLink>
                                                </li>
                                                <li>
                                                    <p:commandLink id="pcjPesquisarAgrupamentoRemoverBtn"
                                                                   value="Remover CPCJ" process="@this"
                                                                   action="#{bean.btnEliminarCpcjAgrupamento(cpcj)}"
                                                                   update="@form pcjDataTableCPCJs"
                                                                   onstart="PF('frawPageBlocker').show()"
                                                                   oncomplete="PF('frawPageBlocker').hide()"
                                                                   onsuccess="PF('frawPageBlocker').hide()"/>

                                                </li>
                                            </ul>
                                        </p:outputPanel>
                                    </p:overlayPanel>
                                </p:column>
                                <p:rowExpansion>
                                    <h:panelGroup id="pcjPanelComboMotivo">
                                        <div class="row-fluid mb-5">
                                            <div class="span3">
                                                <p:outputLabel for="nomeDistritoText" value="Distrito" />
                                                <p>
                                                    <h:outputText id="nomeDistritoText" value="#{cpcj.nomeDistrito}"/>
                                                </p>
                                            </div>
                                            <div class="span3">
                                                <p:outputLabel for="concelhoOutputText" value="Concelho" />
                                                <p>
                                                    <h:outputText id="concelhoOutputText" value="#{cpcj.nomeConcelho}"/>
                                                </p>
                                            </div>
                                        </div>
                                    </h:panelGroup>
                                </p:rowExpansion>
                            </p:dataTable>
                        </div>
                    </div>
                </h:panelGroup>
            </div>


        </h:form>
        <div class="row-fluid mt-7 mb-5">
            <p:commandButton id="pcjPesquisarAgrupamentoVoltarBtn"
                             styleClass="float-left ui-priority-secondary"
                             value="Voltar"
                             action="#{pcjSubsystem.pesquisarAgrupamento}"
                             immediate="true"
                             onstart="PF('frawPageBlocker').show()"
                             oncomplete="PF('frawPageBlocker').hide();" />
        </div>
    </ui:define>
</ui:composition>