<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">

    <div class="row-fluid mb-5">
        <div class="titulo-seccao">
            <h2 class="texto-titulo-3">Elementos convocados</h2>
        </div>
    </div>
    <h:panelGroup id="tabelaMembrosComissao">
        <div class="table-list select-check-box-2 sm">
            <p:dataTable id="dtoTabelaMembrosComissao" var="e"
                         value="#{dto.convocados}"
                         rowStyleClass="resultado"
                         widgetVar="uxdsShowcaseDataTableCheckboxWidgetVar" rows="10"
                         rowKey="#{e.utilizadorPCJ.nome}" draggableColumns="false" paginator="true"
                         emptyMessage="Nenhum membro encontrado." rowsPerPageTemplate="10,20,30"
                         paginatorPosition="bottom"
                         paginatorTemplate="{RowsPerPageDropdown} {PreviousPageLink} {CurrentPageReport} {NextPageLink}"
                         currentPageReportTemplate="Página {currentPage}" rowIndexVar="rowIndexVar"
                         rendered="#{bean.isModalidadeGeral()}">

                <f:facet name="caption">Elementos da comissão convocados para o evento</f:facet>

                <p:column headerText="Nome" styleClass="texto">
                    <abbr title="#{bean.getValor(e.utilizadorPCJ.nome)}">
                        <h:outputText value="#{bean.getValor(e.utilizadorPCJ.nome)}">
                            <i class="tooltip fa fa-fw fa-info-circle"/>
                            <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                        </h:outputText>
                    </abbr>
                </p:column>
                <p:column headerText="Função" styleClass="texto">
                    <abbr title="#{bean.getValorDominio('VALENCIAT', e.valenciaTecnica)}">
                        <h:outputText value="#{bean.getValorDominio('VALENCIAT', e.valenciaTecnica)}">
                            <i class="tooltip fa fa-fw fa-info-circle"/>
                            <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                        </h:outputText>
                    </abbr>
                </p:column>
                <p:column headerText="Cargo" styleClass="texto">
                    <abbr title="#{bean.getValorDominio('CARGO', e.cargoMandato)}">
                        <h:outputText value="#{bean.getValorDominio('CARGO', e.cargoMandato)}">
                            <i class="tooltip fa fa-fw fa-info-circle"/>
                            <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                        </h:outputText>
                    </abbr>
                </p:column>
                <p:column headerText="Entidade representada" styleClass="texto">
                    <abbr title="#{bean.getValorDominio('ENTIDADE', e.entidade)}">
                        <h:outputText value="#{bean.getValorDominio('ENTIDADE', e.entidade)}">
                            <i class="tooltip fa fa-fw fa-info-circle"/>
                            <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                        </h:outputText>
                    </abbr>
                </p:column>
            </p:dataTable>
        </div>
    </h:panelGroup>
</ui:fragment>