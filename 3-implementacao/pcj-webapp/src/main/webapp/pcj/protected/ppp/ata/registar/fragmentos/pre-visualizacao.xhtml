<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:c="http://java.sun.com/jsp/jstl/core"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">

    <ui:param name="id" value="IdVisualizacao"/>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Cabeçalho</h2>
    </div>

    <div class="row-fluid mb-5">
        <div class="span3">
            <p:outputLabel for="pcjAtaVisualizacaoTitulo" value="Tipo da ata"/>
            <h:outputText id="pcjAtaVisualizacaoTitulo" value="#{bean.tituloAta}"/>
        </div>

        <div class="span2">
            <p:outputLabel for="pcjAtaVisualizacaoAtaNumero" value="Ata número"/>
            <h:outputText id="pcjAtaVisualizacaoAtaNumero" value="#{ata.numeroAtaAlargada}"/>
        </div>

        <div class="span2">
            <p:outputLabel for="pcjAtaVisualizacaoDataRealizacao" value="Data"/>
            <h:outputText id="pcjAtaVisualizacaoDataRealizacao"
                          value="#{bean.retornarDataFormatada(ata.dataRealizacao)}"/>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Informação prévia à ordem de trabalhos</h2>
    </div>

    <div class="row-fluid mb-5">
        <div class="span12">
            <h:outputText id="pcjAtaVisualizacaoIntroducao" value="#{ata.textoIntrodutorio}"/>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Lista de presença</h2>

        <div class="row-fluid mb-5">
            <div class="table-list select-check-box-2 sm">
                <p:dataTable id="pcjAtaVisualizacaoTableCheckBox"
                             var="p" rowStyleClass="resultado"
                             value="#{ata.listaConclusaoDTO}"
                             widgetVar="pcjAtaVisualizacaoDataTableCheckboxWidgetVar"
                             draggableColumns="false"
                             paginator="false" emptyMessage="Nenhum elemento encontrado."
                             rowIndexVar="rowIndexVar">

                    <p:column headerText="Nome" styleClass="texto">
                        <abbr title="#{p.nome}">
                            <h:outputText value="#{p.nome}">
                                <i class="tooltip fa fa-fw fa-info-circle"/>
                                <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                            </h:outputText>
                        </abbr>
                    </p:column>

                    <p:column headerText="Cargo" styleClass="texto">
                        <abbr title="#{p.cargo}">
                            <h:outputText value="#{p.cargo}">
                                <i class="tooltip fa fa-fw fa-info-circle"/>
                                <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                            </h:outputText>
                        </abbr>
                    </p:column>

                    <p:column headerText="Entidade representada" styleClass="texto">
                        <abbr title="#{p.entidade}">
                            <h:outputText value="#{bean.getValorDominio('ENTIDADEPP', p.codigoEntidadeParticipante)}">
                                <i class="tooltip fa fa-fw fa-info-circle"/>
                                <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                            </h:outputText>
                        </abbr>
                    </p:column>
                </p:dataTable>
            </div>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Ordem de trabalhos</h2>
    </div>

    <div class="row-fluid mb-5">
        <c:if test="#{bean.getAtaAlargadaDTO().getTrabalhosGuardadosDTO().size() > 0}">
            <c:forEach items="#{bean.getAtaAlargadaDTO().getTrabalhosGuardadosDTO()}" var="item">
                <ui:include
                        src="/pcj/protected/ppp/ata/registar/fragmentos/block-visualization/#{item.ordenacaoAtaAlargada.nomeBloco.getNome()}.xhtml">
                    <ui:param name="idBlock" value="#{item.ordenacaoAtaAlargada.id}"/>
                </ui:include>
            </c:forEach>
        </c:if>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Encerramento</h2>
    </div>

    <div class="row-fluid mb-5">
        <div class="span12">
            <h:outputText id="pcjAtaVisualizacaoEncerramento" value="#{ata.textoConclusivo}"/>
        </div>
    </div>

</ui:fragment>