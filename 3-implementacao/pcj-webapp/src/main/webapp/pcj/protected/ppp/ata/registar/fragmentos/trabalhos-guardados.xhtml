<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:c="http://java.sun.com/jsp/jstl/core"
>

    <p:outputPanel id="pcjTrabalhosGuardadosPanel">
        <p:outputPanel>
            <h3>Trabalhos guardados</h3>
        </p:outputPanel>

        <c:if test="#{bean.getAtaAlargadaDTO().getTrabalhosGuardadosDTO().size() > 0}">
            <c:forEach items="#{bean.getAtaAlargadaDTO().getTrabalhosGuardadosDTO()}" var="item">
                <ui:include
                        src="/pcj/protected/ppp/ata/registar/fragmentos/work-done/#{item.ordenacaoAtaAlargada.nomeBloco.getNome()}.xhtml">
                    <ui:param name="idBlock" value="#{item.ordenacaoAtaAlargada.id}"/>
                    <ui:param name="accordionWidgetId" value="#{item.accordionWidgetId}"/>
                </ui:include>
            </c:forEach>
        </c:if>
    </p:outputPanel>
</ui:fragment>