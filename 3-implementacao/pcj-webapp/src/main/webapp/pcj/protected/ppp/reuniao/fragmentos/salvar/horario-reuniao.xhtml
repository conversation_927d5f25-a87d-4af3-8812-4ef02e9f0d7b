<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:ii="http://java.sun.com/jsf/composite/iies">

    <p:outputPanel rendered="#{bean.isMoradaCpcj()}">
        <div class="row-fluid mb-5">
            <div class="span2">
                <p:outputLabel for="reuniaoDataInicioMorada" value="Data*" />
                <ii:calendar id="reuniaoDataInicioMorada" value="#{dto.dataInicio}"
                             required="#{bean.isMoradaCpcj()}"
                             minDate="#{bean.calculaMinimaData()}"
                             maxDate="+50y"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             converterMessage="#{pcjComponentMessage['data.invalida']}"
                             yearRange="c-122:c+20">
                    <p:ajax event="change" process="@this" listener="#{bean.atualizarHorarioDisponivel()}" update="#{form}:horarioDisponivel #{form}:reuniaoplanoTrabalho #{form}:reuniaoTipoConvocatoria #{form}:moradaCpcjReuniaoDataInicioMoradaMensagem"/>
                </ii:calendar>
                <p:message for="reuniaoDataInicioMorada:calendar" />
                <ui:include src="mensagem-data-superior.xhtml">
                    <ui:param name="bean" value="#{bean}"/>
                    <ui:param name="panel" value="moradaCpcj"/>
                    <ui:param name="fluxoComunicacao" value="#{dto.fluxoComunicacao}"/>
                </ui:include>
            </div>

            <div class="span3">
                <p:outputLabel for="tempoDuracao" value="Tempo duração estimado" />

                <p:selectOneMenu id="tempoDuracao" value="#{dto.duracaoSelecionada}"
                                 styleClass="max-field-size" required="#{bean.isMoradaCpcj()}"
                                 effect="fade"
                                 var="ee"
                                 filter="true"
                                 filterMatchMode="contains"
                                 requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                    <f:selectItems value="#{bean.duracoes}" />
                    <p:ajax listener="#{bean.atualizarHorarioDisponivel()}" update="horarioDisponivel"/>
                </p:selectOneMenu>
                <p:message for="tempoDuracao" />
            </div>

            <div class="span3">
                <p:outputLabel for="horarioDisponivel" value="Horário disponível" />
                <p:selectOneMenu id="horarioDisponivel" value="#{dto.horarioDisponivel}" styleClass="max-field-size"
                                 required="#{bean.isMoradaCpcj()}"
                                 requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                    <f:selectItems value="#{bean.horariosDisponiveis}" var="horario" itemValue="#{horario}" itemLabel="#{horario.dataFormatada}"/>
                    <f:attribute name="horarioDisponivelList" value="#{bean.horariosDisponiveis}" />
                </p:selectOneMenu>

                <p:message for="horarioDisponivel" />
            </div>
        </div>
    </p:outputPanel>

    <p:outputPanel rendered="#{bean.isOutraMoradaOuSemLocal()}">
        <div class="row-fluid mb-5">
            <div class="span2">
                <p:outputLabel for="reuniaoDataInicioOutraMorada" value="Data de início*" />
                <ii:calendar id="reuniaoDataInicioOutraMorada"
                             value="#{dto.dataInicio}"
                             minDate="#{bean.calculaMinimaData()}"
                             maxDate="#{bean.calculaMaximaDataInicio()}"
                             required="#{bean.isOutraMoradaOuSemLocal()}"
                             yearRange="c-122:c+20"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             converterMessage="#{pcjComponentMessage['data.invalida']}">
                    <p:ajax event="change" process="@this"
                            update="#{form}:outraMoradaSemLocalReuniaoDataInicioMoradaMensagem #{form}:reuniaoplanoTrabalho #{form}:reuniaoTipoConvocatoria #{form}:reuniaoDataFim #{form}:panelMensagemErroHorarioFim"
                            listener="#{bean.atualizaInformacoesBaseadoDataInicio()}"/>
                </ii:calendar>
                <p:message for="reuniaoDataInicioOutraMorada:calendar" />
                <ui:include src="mensagem-data-superior.xhtml">
                    <ui:param name="bean" value="#{bean}"/>
                    <ui:param name="panel" value="outraMoradaSemLocal"/>
                    <ui:param name="fluxoComunicacao" value="#{dto.fluxoComunicacao}"/>
                </ui:include>
            </div>

            <div class="span2">
                <p:outputLabel for="reuniaoDataFim" value="Data de fim*" />
                <ii:calendar id="reuniaoDataFim"
                             value="#{dto.dataFim}"
                             minDate="#{bean.calculaMinimaDataFim()}"
                             maxDate="+50y"
                             required="#{bean.isOutraMoradaOuSemLocal()}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             converterMessage="#{pcjComponentMessage['data.invalida']}">
                    <p:ajax event="change" process="@this"
                            update="@this #{form}:reuniaoDataFim #{form}:reuniaoDataInicioOutraMorada #{form}:panelMensagemErroHorarioFim"
                            listener="#{bean.validarDataInicioMaiorDataFim()}"/>
                </ii:calendar>
                <p:message for="reuniaoDataFim:calendar" />
            </div>

            <div class="span2">
                <p:outputLabel for="reuniaoHoraInicio" value="Horário de inicio" />
                <p:inputMask id="reuniaoHoraInicio" mask="99:99"
                             placeholder="HH:mm"
                             value="#{dto.horaInicio}"
                             required="#{bean.isOutraMoradaOuSemLocal()}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             validatorMessage="#{pcjComponentMessage['hora.invalida']}">
                    <f:validateRegex pattern="^([01]?\d|2[0-3])(?::([0-5]?\d))?$" />
                    <p:ajax event="change" process="@this" update="panelMensagemErroHorarioFim"/>
                </p:inputMask>
                <p:message for="reuniaoHoraInicio" />
            </div>

            <div class="span2">
                <p:outputLabel for="reuniaoHoraFim" value="Horário de fim" />
                <p:inputMask id="reuniaoHoraFim"
                             mask="99:99"
                             placeholder="HH:mm"
                             value="#{dto.horaFim}"
                             required="#{bean.isOutraMoradaOuSemLocal()}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             validatorMessage="#{pcjComponentMessage['hora.invalida']}">
                    <f:validateRegex pattern="^([01]?\d|2[0-3])(?::([0-5]?\d))?$" />
                    <p:ajax event="change" update="panelMensagemErroHorarioFim" process="@this"/>
                </p:inputMask>
                <p:outputPanel id="panelMensagemErroHorarioFim">
                    <p:outputPanel id="internalPanelMensagemErroHorarioFim" rendered="#{bean.validarHorasInicioFim()}">
                        <div class="aviso row-fluid"
                             style="background-color: transparent;align-content: space-around;">
                            <div>
                                <i class="fa fa-exclamation-triangle"/>
                                <span class="message">A hora de fim deve ser posterior à hora de início.</span>
                            </div>
                        </div>
                    </p:outputPanel>
                </p:outputPanel>
                <p:message for="reuniaoHoraFim" />
            </div>
        </div>
    </p:outputPanel>
</ui:fragment>