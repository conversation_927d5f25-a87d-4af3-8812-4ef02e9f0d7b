<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui"
             xmlns="http://www.w3.org/1999/xhtml">
    <!--    https://devwiki.iies.seg-social.pt/xwiki/bin/viewrev/PCJ/RS318-->

    <ui:param name="dto" value="#{bean.parecerBean.blocoParecerDTO}"/>

    <div class="row-fluid">
        <div class="span12">
            <div>
                <h3 class="mb-xs mt-5">Dar parecer</h3>
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <p:outputLabel for="pcjParecerTextoIntroTextArea" value="Texto introdutório"/>
            <p:inputTextarea id="pcjParecerTextoIntroTextArea"
                             value="#{dto.textoIntrodutorio}"
                             maxlength="4000"
                             rows="3"
                             styleClass="max-field-size"
                             counter="pcjParecerTextoIntroTextAreaCounter"
                             required="true"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             counterTemplate="{0} caracteres restante.">
                <p:ajax event="change"/>
            </p:inputTextarea>
            <h:outputText id="pcjParecerTextoIntroTextAreaCounter" styleClass="TextAreaNumber"/>
            <h:outputText id="pcjParecerdisplayTextoIntro"/>
            <p:message id="pcjAtaParTextoIntroMsg" for="pcjParecerTextoIntroTextArea"/>
        </div>
    </div>
    <div class="row-fluid">
        <div class="row-fluid mt-s mb-5">
            <div class="span12">
                <p:outputLabel id="pcjParecerOutLabel" for="pcjParecerInputText" styleClass="labelmargin"
                               value="Assunto" />
                <p:inputText id="pcjParecerInputText" value="#{dto.assunto}"
                             required="true"
                             maxlength="150"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <p:ajax event="change"/>
                </p:inputText>
                <p:watermark for="pcjParecerInputText" value="Assunto"/>
                <p:message id="pcjParecerTextoAssuntoMsg" for="pcjParecerInputText"/>
            </div>
        </div>
    </div>

    <div class="row-fluid">
        <div class="span10 mb-5">
            <p:outputLabel id="pcjParecerAnexDocOutLabel" for="pcjParecerAnexDocPanel" styleClass="labelmargin"
                           value="Anexa documento"/>
            <p:outputPanel id="pcjParecerAnexDocPanel">
                <p:selectOneRadio id="pcjParecerDocRB" value="#{bean.viewFileUpload}"
                                  styleClass="select-radio-2 sm horizontal">
                    <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                    <f:selectItem itemLabel="Nao" itemValue="#{false}" checked="true"/>
                    <p:ajax event="change"
                            update="@this fileUploadViewPanel_#{id} pcjBlocoTrabalhoPanel"
                            onstart="PF('frawPageBlocker').show()"
                            oncomplete="PF('frawPageBlocker').hide();"
                            onerror="PF('frawPageBlocker').hide();"
                            process="@this"/>
                </p:selectOneRadio>
            </p:outputPanel>

        </div>
    </div>
    <p:outputPanel id="fileUploadViewPanel_#{id}" rendered="#{bean.viewFileUpload}">
        <div class="row-fluid mt-3 mb-5">
            <ui:include src="/pcj/protected/ppp/ata/registar/work-blocks/file-upload.xhtml"/>
        </div>
    </p:outputPanel>

    <div class="row-fluid">
        <div class="span12 mb-5">
            <p:outputLabel for="pcjParecerObsFinaisInputTextArea" value="Observações finais"/>
            <p:inputTextarea
                    id="pcjParecerObsFinaisInputTextArea"
                    value="#{dto.observacao}"
                    rows="3"
                    counter="pcjParecerObsFinaisInputTextAreaCounter"
                    maxlength="4000"
                    counterTemplate="{0} caracteres restantes"
                    autoResize="true"
                    styleClass="max-field-size"
                    required="true"
                    requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                <p:ajax event="change"/>
            </p:inputTextarea>
            <h:outputText id="pcjParecerObsFinaisInputTextAreaCounter" styleClass="TextAreaNumber"/>
            <h:outputText id="pcjDisplayParecerObsFinais"/>
            <p:message id="pcjParecerObsFinaisMsg" for="pcjParecerObsFinaisInputTextArea"/>
        </div>

    </div>

</ui:fragment>