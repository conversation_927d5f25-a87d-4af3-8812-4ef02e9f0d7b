<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:p="http://primefaces.org/ui"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:ii="http://java.sun.com/jsf/composite/iies"
        xmlns="http://www.w3.org/1999/xhtml"
        template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="pageTitle" value="Atas"/>
    <ui:param name="bean" value="#{pcjPesquisarAtaBean}"/>
    <ui:param name="pesquisa" value="#{pcjPesquisarAtaBean.filtroPesquisa}"/>

    <ui:define name="info_messages">
        <h:panelGroup id="ataInfoMessagesPesquisa" class="info" layout="block">
            <ul>
                <h:outputText escape="false" value="Pesquisa e consulta de atas e aditamentos."/>
            </ul>
        </h:panelGroup>
    </ui:define>

    <ui:define name="content">
        <div class="formulario row-fluid">
            <h:form id="reuniaoPesquisar" styleClass="mb-6">
                <h:panelGroup id="ataPainelPesquisa">

                    <div class="row-fluid mb-5">
                        <div class="span2">
                            <p:outputLabel for="pcjPesquisaAtaDataInicio" value="Data inicial"/>
                            <ii:calendar id="pcjPesquisaAtaDataInicio"
                                         minDate="-50Y"
                                         maxDate="+50y"
                                         yearRange="c-50:c+50"
                                         value="#{pesquisa.dataInicio}"
                                         converterMessage="#{pcjComponentMessage['data.invalida']}">
                                <p:ajax event="change" process="@this"/>
                            </ii:calendar>
                            <p:message id="pcjPesquisaAtaDataInicioMessage" for="pcjPesquisaAtaDataInicio:calendar"/>
                        </div>

                        <div class="span1" style="width: 0.0%;margin: 1%;">
                            <p:outputLabel value=""/>
                            <span>a</span>
                        </div>

                        <div class="span2" style="margin-left: 1%;">
                            <p:outputLabel for="pcjjPesquisaAtaDataFim" value="Data final"/>
                            <ii:calendar id="pcjjPesquisaAtaDataFim"
                                         minDate="-50Y"
                                         maxDate="+50y"
                                         yearRange="c-50:c+50"
                                         value="#{pesquisa.dataFim}"
                                         converterMessage="#{pcjComponentMessage['data.invalida']}">
                                <p:ajax event="change" process="@this"/>
                            </ii:calendar>
                            <p:message id="pcjjPesquisaAtaDataFimMessage" for="pcjjPesquisaAtaDataFim:calendar"/>
                        </div>
                    </div>

                    <h:panelGroup id="pcjAtaCpcjPnl" rendered="#{bean.comissaoNacional}">
                        <div class="row-fluid mb-5">
                            <div class="span4">
                                <p:outputLabel for="pcjPesquisaAtaCpcj" value="Nome da CPCJ"/>
                                <p:selectOneMenu id="pcjPesquisaAtaCpcj" styleClass="max-field-size mb-5"
                                                 value="#{pesquisa.comissao}"
                                                 effect="fade" var="b" filter="true"
                                                 converter="omnifaces.SelectItemsConverter"
                                                 filterMatchMode="contains">
                                    <f:selectItem itemLabel="Selecione" itemValue="" noSelectionOption="true"/>
                                    <f:selectItems value="#{bean.listaCpcj}"
                                                   var="cpcj" itemLabel="#{cpcj.nome}"
                                                   itemValue="#{cpcj}"/>
                                    <p:column>
                                        <h:outputText value="#{b.nome}"/>
                                    </p:column>
                                </p:selectOneMenu>
                            </div>
                        </div>
                    </h:panelGroup>

                    <div class="row-fluid mb-5">
                        <div class="span4">
                            <p:outputLabel for="pcjPesquisaAtaTipo" value="Tipo de ata"/>
                            <p:selectOneMenu id="pcjPesquisaAtaTipo" styleClass="max-field-size mb-5"
                                             value="#{pesquisa.tipoAta}"
                                             effect="fade" var="b" filter="true"
                                             converter="omnifaces.SelectItemsConverter"
                                             filterMatchMode="contains">

                                <f:selectItem itemLabel="Selecione" itemValue="" noSelectionOption="true"/>
                                <f:selectItems value="#{bean.listaTiposAta}"
                                               var="tipo" itemLabel="#{tipo.descricao}"
                                               itemValue="#{tipo}"/>
                                <p:column>
                                    <h:outputText value="#{b.descricao}"/>
                                </p:column>
                            </p:selectOneMenu>
                        </div>

                        <div class="span4">
                            <p:outputLabel for="pcjPesquisaAtaTipoReuniao" value="Tipo de reunião"/>
                            <p:selectOneMenu id="pcjPesquisaAtaTipoReuniao" styleClass="max-field-size mb-5"
                                             value="#{pesquisa.tipoReuniao}"
                                             effect="fade" var="b" filter="true"
                                             converter="omnifaces.SelectItemsConverter"
                                             filterMatchMode="contains">

                                <f:selectItem itemLabel="Selecione" itemValue="" noSelectionOption="true"/>
                                <f:selectItems value="#{bean.listaTiposReuniao}"
                                               var="tipo" itemLabel="#{tipo.descricao}"
                                               itemValue="#{tipo.codigoConvocatoria}"/>
                                <p:column>
                                    <h:outputText value="#{b.descricao}"/>
                                </p:column>
                            </p:selectOneMenu>
                        </div>

                        <div class="span4">
                            <p:outputLabel for="pcjPesquisarEstadoAta" value="Estado"/>
                            <p:selectOneMenu id="pcjPesquisarEstadoAta" styleClass="max-field-size mb-5"
                                             value="#{pesquisa.estadoAta}"
                                             effect="fade" var="e" filter="true"
                                             converter="omnifaces.SelectItemsConverter"
                                             filterMatchMode="contains">

                                <f:selectItem itemLabel="Selecione" itemValue="" noSelectionOption="true"/>
                                <f:selectItems value="#{bean.listaEstados}"
                                               var="est" itemLabel="#{est.descricao}"
                                               itemValue="#{est}"/>
                                <p:column>
                                    <h:outputText value="#{e.descricao}"/>
                                </p:column>
                            </p:selectOneMenu>
                        </div>
                    </div>

                    <div class="row-fluid acoes mb-5">
                        <div class="span12">
                            <p:commandLink id="pcjLimparPesquisaAtaBtn"
                                           styleClass="mr-4"
                                           value="Limpar"
                                           action="#{bean.limparPesquisa()}"
                                           resetValues="true"
                                           update="@form pcjResultadoPnl pcjSemResultadoPnl pcjMensagemSemResultadoPnl pcjTabelaPnl"
                                           process="@this"
                                           onstart="PF('frawPageBlocker').show()"
                                           onerror="PF('frawPageBlocker').hide()"
                                           onsuccess="PF('frawPageBlocker').hide()"/>

                            <p:commandButton id="pcjPesquisarAtaBtn"
                                             value="Pesquisar"
                                             action="#{bean.pesquisar()}"
                                             update="@this pcjResultadoPnl pcjSemResultadoPnl pcjMensagemSemResultadoPnl pcjTabelaPnl "
                                             process="@form"
                                             onstart="PF('frawPageBlocker').show()"
                                             onerror="PF('frawPageBlocker').hide()"
                                             onsuccess="PF('frawPageBlocker').hide()"/>
                        </div>
                    </div>

                    <div class="row-fluid mb-5">
                        <div class="titulo-seccao">
                            <h2 class="texto-titulo-3">Lista de atas</h2>
                        </div>
                    </div>

                    <p:outputPanel id="pcjMensagemSemResultadoPnl">
                        <h:panelGroup id="pcjSemResultadoPnl" rendered="#{bean.dataModel.noResults}">
                            <div class="row-fluid acoes mb-5">
                                <h:panelGroup layout="block" styleClass="row-fluid text-center">
                                    <p>#{bean.mensagemSemResultado}</p>
                                </h:panelGroup>
                            </div>
                        </h:panelGroup>
                    </p:outputPanel>

                    <p:outputPanel id="pcjTabelaPnl">
                        <h:panelGroup id="pcjResultadoPnl" rendered="#{!bean.dataModel.noResults}">
                            <div class="table-list">
                                <p:dataTable id="pcjAtasDataTableActions" var="item" lazy="true"
                                             widgetVar="pcjAtasDataTableActionsWidgetVar"
                                             value="#{bean.dataModel}"
                                             rows="#{bean.pageSize}"
                                             draggableColumns="false" paginator="true"
                                             emptyMessage="#{bean.mensagemSemResultado}"
                                             rowsPerPageTemplate="#{bean.rowsPerPageTemplate}"
                                             paginatorPosition="#{bean.paginatorPosition}"
                                             paginatorTemplate="{PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}"
                                             currentPageReportTemplate="Página {currentPage}"
                                             rowIndexVar="rowIndexVar" rowStyleClass="resultado">

                                    <p:ajax event="page" onstart="PF('frawPageBlocker').show()"
                                            oncomplete="PF('frawPageBlocker').hide();"/>

                                    <p:column id="datatableAtaNumero" headerText="Ata" styleClass="texto">
                                        <h:outputText id="outputAtaNumero"
                                                      value="#{item.numeroAta}"/>
                                    </p:column>
                                    <p:column id="datatableAtaTipo" headerText="Tipo de rata" styleClass="texto">
                                        <h:outputText id="outputAtaTipo"
                                                      value="#{item.tipoAta.descricao}"/>
                                    </p:column>

                                    <p:column id="datatableAtaData"
                                              headerText="Data" sortBy="#{item.data}"
                                              styleClass="data">
                                        <h:outputText id="outputAtaData"
                                                      value="#{item.data}">
                                            <f:convertDateTime pattern="yyyy-MM-dd"/>
                                        </h:outputText>
                                    </p:column>

                                    <h:panelGroup id="pcjAtaNomeCpcjPnl" rendered="#{bean.comissaoNacional}">
                                        <p:column id="datatableAtaNomeCpcj" headerText="Nome da CPCJ"
                                                  styleClass="texto">
                                            <h:outputText id="outputAtaomeCpcj"
                                                          value="#{item.nomeCpcj}"/>
                                        </p:column>
                                    </h:panelGroup>

                                    <p:column id="datatableAtaTipoReuniao" headerText="Tipo de reunião"
                                              styleClass="texto">
                                        <h:outputText id="outputAtaTipoReuniao"
                                                      value="#{bean.getValorDominio('TPCONVOCAT', item.tipoReuniao)}"/>
                                    </p:column>

                                    <p:column id="datatableAtaEstado" headerText="Estado" styleClass="texto">
                                        <h:outputText id="outputAtaEstado"
                                                      value="#{item.estado.descricao}"/>
                                    </p:column>

                                    <p:column id="datatableAtaActionsColumn"
                                              headerText="Ações"
                                              styleClass="acoes">

                                        <h:panelGroup id="pcjAtaConsultarPnl" rendered="#{item.estado ne 'MINUTA'}">
                                            <p:commandLink id="consultarAtaBotao" value="Consultar ata"
                                                           immediate="true" process="@this"
                                                           action="#{pcjSubsystem.consultarAta()}"
                                                           onstart="PF('frawPageBlocker').show()"
                                                           oncomplete="PF('frawPageBlocker').hide()"
                                                           actionListener="#{pcjConsultarAtaBean.setIdReuniao(item.idReuniao)}"/>
                                        </h:panelGroup>

                                        <h:panelGroup id="pcjAtaRetomarPnl" rendered="#{item.estado eq 'MINUTA'}">
                                            <p:commandLink id="retomarAtaBotao" value="Retomar ata"
                                                           immediate="true"
                                                           action="#{pcjSubsystem.registarAta()}"
                                                           actionListener="#{pcjRegistarAtaBean.setIdReuniao(item.idReuniao)}"
                                                           process="@form" update="@form"
                                                           onstart="PF('frawPageBlocker').show(); "
                                                           oncomplete="PF('frawPageBlocker').hide();"
                                                           onsuccess="PF('frawPageBlocker').hide()"/>
                                        </h:panelGroup>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </h:panelGroup>
                    </p:outputPanel>

                </h:panelGroup>
            </h:form>
        </div>
    </ui:define>
</ui:composition>