<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                template="/pcj/templates/pcj-base.xhtml"
                xmlns:f="http://java.sun.com/jsf/core">

    <ui:param name="pageTitle" value="Gestão de elementos"/>

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessagesPesquisa" class="info" layout="block">
                <ul>
                    <h:outputText escape="false" value="Pesquisa num campo do elemento para verificação de elementos já existentes."/>
                </ul>
        </h:panelGroup>
    </ui:define>

    <ui:define name="content">
        <p:outputPanel id="pcjPesquisaElementoPnl">
            <h:form id="pcjPesquisaElementoFrm">
                <div class="row-fluid mb-5">
                    <div class="span12">
                        <p:outputLabel id="pcjNomeLbl" 
                                       for="pcjNomeTxt"
                                       value="Nome"/>
                        <p:inputText id="pcjNomeTxt"
                                     value="#{pcjElementoBean.filtroPesquisaDadosPessoaisDTO.nome}" 
                                     maxlength="100"/>
                    </div>
                </div>
                <div class="row-fluid mb-5">
                    <div class="span4">
                        <p:outputLabel id="pcjNumeroDocumentoLbl" 
                                       for="pcjNumeroDocumentoTxt"
                                       value="N° de documento"/>
                        <p:inputText id="pcjNumeroDocumentoTxt"
                                     value="#{pcjElementoBean.filtroPesquisaDadosPessoaisDTO.numeroDocumento}" 
                                     maxlength="15"/>
                    </div>

                    <div class="span4">
                        <p:outputLabel id="pcjNifLbl" 
                                       for="pcjNifTxt"
                                       value="NIF"/>
                        <p:inputText id="pcjNifTxt"
                                     value="#{pcjElementoBean.filtroPesquisaDadosPessoaisDTO.nif}" 
                                     maxlength="12"/>
                    </div>

                    <div class="span4">
                        <p:outputLabel id="pcjNissLbl" 
                                       for="pcjNissTxt"
                                       value="NISS"/>
                        <p:inputText id="pcjNissTxt"
                                     value="#{pcjElementoBean.filtroPesquisaDadosPessoaisDTO.niss}" 
                                     maxlength="12"/>
                    </div>
                </div>

                <div class="row-fluid acoes mb-5">
                    <div class="span12">
                        <p:commandLink id="pcjLimparPesquisaDadosPessoaisLnk"
                                       styleClass="mr-4"
                                       value="Limpar"
                                       action="#{pcjElementoBean.limparPesquisaDadosPessoais()}"
                                       resetValues="true"
                                       update="@form"
                                       process="@this"
                                       onstart="PF('frawPageBlocker').show()"
                                       onerror="PF('frawPageBlocker').hide()"
                                       onsuccess="PF('frawPageBlocker').hide()"/>

                        <p:commandButton id="pcjPesquisarDadosPessoaisLnk"
                                         value="Pesquisar"
                                         action="#{pcjElementoBean.pesquisarPessoaComElemento()}"
                                         update="@this pcjResultadoPnl pcjSemResultadoPnl pcjBotoesPnl"
                                         process="@form"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         onsuccess="PF('frawPageBlocker').hide()"
                        />
                    </div>
                </div>

                <div class="row-fluid mb-5">
                    <div class="titulo-seccao">
                        <h2 class="texto-titulo-3">Lista de elementos</h2>
                    </div>
                </div>

                
                <h:panelGroup id="pcjSemResultadoPnl">
                    <div class="row-fluid acoes mb-5">
                        <h:panelGroup layout="block" styleClass="row-fluid text-center" rendered="#{not pcjElementoBean.existeElementosDadosPessoais()}">
                            <p>#{pcjElementoBean.mensagemSemResultado}</p>
                        </h:panelGroup>
                    </div>
                </h:panelGroup>

                <p:outputPanel rendered="#{pcjElementoBean.hasPerfilOperacao()}">
                    <div class="row-fluid mb-5">
                        <div class="span12">
                            <p:outputPanel id="pcjBotoesPnl">
                                <p:outputPanel style="text-align: center" rendered="#{not pcjElementoBean.existeElementosDadosPessoais()}">
                                    <p:commandButton id="pcjRegistarElementoCenterBtn"
                                        styleClass="ui-priority-primary"
                                        value="Registar Elemento"
                                        action="#{pcjSubsystem.registarElemento}"
                                        process="@form"
                                        update="@form"
                                        onstart="PF('frawPageBlocker').show()"
                                        oncomplete="PF('frawPageBlocker').hide();" />
                                </p:outputPanel>

                                <p:outputPanel style="text-align: left" rendered="#{pcjElementoBean.existeElementosDadosPessoais()}">
                                    <p:commandButton id="pcjRegistarElementoLeftBtn"
                                        styleClass="ui-priority-primary"
                                        value="Registar Elemento"
                                        action="#{pcjSubsystem.registarElemento}"
                                        process="@form"
                                        update="@form"
                                        onstart="PF('frawPageBlocker').show()"
                                        oncomplete="PF('frawPageBlocker').hide();" />
                                </p:outputPanel>
                            </p:outputPanel>
                        </div>
                    </div>
                </p:outputPanel>

                <!-- DATA TABLE -->

                <div class="row-fluid mb-5">
                    <h:panelGroup id="pcjResultadoPnl">
                        <h:panelGroup rendered="#{pcjElementoBean.existeElementosDadosPessoais()}"
                                    styleClass="mb-5">
                            <div class="table-list">
                                <p:dataTable id="pcjListaElementosDadosPessoaisDtb" var="item"
                                            paginator="true" 
                                            draggableColumns="false"
                                            value="#{pcjElementoBean.listaElementosDadosPessoaisDTO}" 
                                            rows="10"
                                            rowStyleClass="resultado"
                                            paginatorPosition="bottom"
                                            paginatorTemplate="{RowsPerPageDropdown} {PreviousPageLink} {CurrentPageReport} {NextPageLink}"
                                            currentPageReportTemplate="Página {currentPage}"
                                            rowIndexVar="rowIndexVar"
                                            rowsPerPageTemplate="10,25,50">

                                    <f:facet name="caption">Resultado da pesquisa por número de documento</f:facet>

                                    <p:column headerText="Nome" styleClass="texto" style="text-align: left" >
                                        <h:outputText value="#{item.nome}" />
                                    </p:column>
                                    <p:column headerText="Tipo de documento" styleClass="texto" style="text-align: left" >
                                        <h:outputText value="#{item.nomeTipoDocumento}" />
                                    </p:column>
                                    <p:column headerText="N° de documento" styleClass="texto" style="text-align: left">
                                        <h:outputText value="#{item.numeroDocumento}" />
                                    </p:column>
                                    <p:column headerText="NIF" styleClass="texto" style="text-align: left">
                                        <h:outputText value="#{item.nif}" />
                                    </p:column>
                                    <p:column headerText="NISS" styleClass="texto" style="text-align: left">
                                        <h:outputText value="#{item.niss}" />
                                    </p:column>

                                    <p:column id="menuAcoes" styleClass="acoes" headerText="Ações">
                                        <p:commandLink id="pcjUtilizarLnk"
                                                        value="Utilizar elemento"
                                                        process="@this"
                                                        immediate="true"
                                                        actionListener="#{pcjElementoBean.utilizar(item)}"
                                                        action="#{pcjSubsystem.registarElemento}"
                                                        ajax="false">
                                        </p:commandLink>
                                    </p:column>
                                </p:dataTable>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>
                </div>

                <div class="row-fluid mb-5">
                    <p:commandButton id="pcjVoltarRegistarBtn"
                        styleClass="ui-priority-secondary"
                        style="float: left;"
                        value="Voltar"
                        action="#{pcjSubsystem.pesquisarElementos()}"
                        actionListener="#{pcjElementoBean.voltar()}"
                        process="@this"
                        onstart="PF('frawPageBlocker').show()"
                        oncomplete="PF('frawPageBlocker').hide();"
                        onsuccess="window.scrollTo(0, 0);"  />
                </div>                    
            </h:form>
        </p:outputPanel>
    </ui:define>
</ui:composition>