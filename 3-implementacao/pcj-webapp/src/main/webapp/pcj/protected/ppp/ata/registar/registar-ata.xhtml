<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns="http://www.w3.org/1999/xhtml"
                template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="bean" value="#{pcjRegistarAtaBean}"/>
    <ui:param name="ata" value="#{pcjRegistarAtaBean.ataAlargadaDTO}"/>

    <ui:param name="pageTitle" value="Registar Ata"/>

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessageRegistar" class="info" layout="block">
            <ul>
                <p:outputPanel id="pcjInfoMessageRegistarAta">
                    <h:outputText escape="false" value="#{bean.getInfoMessage()}"/>
                </p:outputPanel>
            </ul>
        </h:panelGroup>
        <div id="messageErrorValidateForm" style="color: red; display: none;">
            <h:panelGroup id="pcjInfoMessagesErroRegAta" class="erro" layout="block">
                <ul>
                    <h:outputText escape="false" value="Os campos assinalados são de preenchimento obrigatório."/>
                </ul>
            </h:panelGroup>
        </div>
    </ui:define>
    <ui:define name="content">
        <h:form id="pcjRegistarAtaForm">

            <article class="tab">
                <h:panelGroup id="tabsAta" rendered="#{bean.isDisplayList() and not bean.isDisplayListConclusao()}">
                    <p:tabView id="pcjAtaTabView" widgetVar="tabViewWv" activeIndex="#{bean.currentTab}"
                               dynamic="true" scrollable="false"
                               cache="false"
                               onTabChange="if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]);}">
                        <p:ajax event="tabChange"
                                update=":pcjInfoMessageRegistarAta pcjRegistarAtaForm:pcjRegistarAtaBtns pcjRegistarAtaForm:pcjInfoMessagesAvisoRegAtaPanel "
                                process="@form"
                                onstart="PF('frawPageBlocker').show()"
                                onerror="PF('frawPageBlocker').hide()"
                                oncomplete="PF('frawPageBlocker').hide()"
                                listener="#{bean.saveDraft()}"/>
                        <p:tab title="Presença">
                            <div class="row-fluid">
                                <p:outputPanel
                                        rendered="#{bean.exibirNovaListaPresenca() or bean.exibirEdicaoListaPresenca() }"
                                        id="pcjPanelPresencaNewList">
                                    <ui:include src="fragmentos/folha-presenca-tab.xhtml"/>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{bean.exibirListaPresenca()}" id="pcjPanelPresencaEditList">
                                    <ui:include src="fragmentos/folha-presenca.xhtml"/>
                                </p:outputPanel>
                            </div>
                        </p:tab>
                        <p:tab title="Introdução">
                            <ui:include src="fragmentos/introducao.xhtml"/>
                        </p:tab>
                        <p:tab title="Ordem de trabalhos">
                            <ui:include src="fragmentos/bloco-trabalho.xhtml"/>
                        </p:tab>
                        <p:tab title="Conclusão">
                            <div class="row-fluid">
                                <p:outputPanel rendered="#{not bean.isEdicaoAssinatura()}"
                                               id="pcjPanelAssinaturaNewList">
                                    <ui:include src="fragmentos/conclusao.xhtml"/>
                                </p:outputPanel>
                                <p:outputPanel rendered="#{bean.isEdicaoAssinatura()}" id="pcjPanelAssinaturaEditList">
                                    <ui:include src="fragmentos/conclusao-assinatura-alterar.xhtml"/>
                                </p:outputPanel>
                            </div>

                        </p:tab>
                        <p:tab title="Pré-visualização">
                            <ui:include src="fragmentos/pre-visualizacao.xhtml"/>
                        </p:tab>

                    </p:tabView>
                </h:panelGroup>
                <h:panelGroup id="tabsEdicao" rendered="#{not bean.isDisplayList()}">
                    <p:tabView id="pcjAtaTabView2" widgetVar="tabViewWv" activeIndex="#{bean.currentTab}"
                               dynamic="true" scrollable="false"
                               onTabChange="if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]); }">
                        <p:tab title="Presença">
                            <div class="row-fluid">
                                <p:outputPanel
                                        rendered="#{bean.exibirNovaListaPresenca() or bean.exibirEdicaoListaPresenca() }"
                                        id="pcjPanelPresencaDisplayList">
                                    <ui:include src="fragmentos/folha-presenca-tab.xhtml"/>
                                </p:outputPanel>
                            </div>
                        </p:tab>
                    </p:tabView>
                </h:panelGroup>
            </article>
            <article class="tab">
                <h:panelGroup id="tabEdicaoAssinatura" rendered="#{bean.isDisplayListConclusao()}">
                    <p:tabView id="pcjAtaTabView3" widgetVar="tabViewConclusao" activeIndex="#{bean.currentTab}"
                               dynamic="true" scrollable="false"
                               onTabChange="if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]);}">
                        <p:tab title="Conclusão">
                            <div class="row-fluid">
                                <p:outputPanel id="pcjPanelAssinaturaDisplayList"
                                               rendered="#{bean.isEdicaoAssinatura()}">
                                    <ui:include
                                            src="/pcj/protected/ppp/ata/registar/fragmentos/conclusao-assinatura-alterar.xhtml"/>
                                </p:outputPanel>
                            </div>
                        </p:tab>
                    </p:tabView>
                </h:panelGroup>

            </article>
            <ui:include src="/pcj/protected/ppp/ata/registar/fragmentos/dialog-ponto-trabalho.xhtml"/>
            <ui:include src="/pcj/protected/ppp/ata/registar/fragmentos/dialog-remove-work.xhtml"/>
            <ui:include src="/pcj/protected/ppp/ata/registar/fragmentos/dialog-iniciar-ata.xhtml"/>

            <div class="row-fluid mt-xl">
                <div class="span12">
                    <p:outputPanel id="pcjRegistarAtaBtns">
                        <p:commandButton id="pcjRegistarAtaCancelarBtn"
                                         styleClass="ui-priority-secondary"
                                         rendered="#{not bean.exibirBtnCacelarAlteracaoListaPresenca() and not bean.exibirBotoesGuardarAssinatura()}"
                                         value="Cancelar"
                                         process="@this"
                                         action="#{bean.cancelarAta()}"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide()"/>

                        <p:commandButton id="pcjRegistarAtaIniciarBtn"
                                         rendered="#{bean.exibirBtnInicirAta()}"
                                         value="Iniciar Ata"
                                         onstart="PF('frawPageBlocker').show()"
                                         oncomplete="PF('frawPageBlocker').hide()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         styleClass="float-right" onclick="PF('dlgIniciarAta').show();"/>

                        <p:commandButton id="pcjRegistarAtaImpPresencaBtn"
                                         rendered="#{bean.exibirBtnImprimirPresenca()}"
                                         value="Imprimir folha de presença"
                                         styleClass="float-right" icon="fa fa-print"
                                         action="#{bean.imprimirListaPresentes()}"
                                         process="@form"
                                         update="@form"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide();"/>

                        <p:commandButton id="pcjGuardarAlterarFolhaBtn" value="Guardar alteração"
                                         rendered="#{bean.exibirBtnCacelarAlteracaoListaPresenca()}"
                                         styleClass="float-right" icon="fa fa-save"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         onsuccess="PF('frawPageBlocker').hide()"
                                         process="@form"
                                         update="pcjRegistarAtaForm pcjRegistarAtaForm:pcjAtaTabView "
                                         actionListener="#{pcjRegistarAtaBean.guardarFolhaPresenca()}"/>

                        <p:commandButton id="pcjCancelarAlterarFolhaBtn" value="Cancelar alteração"
                                         rendered="#{bean.exibirBtnCacelarAlteracaoListaPresenca()}"
                                         styleClass="ui-priority-secondary float-right mr-xs"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         onsuccess="PF('frawPageBlocker').hide()"
                                         process="@this"
                                         update="pcjRegistarAtaForm pcjRegistarAtaForm:pcjAtaTabView "
                                         actionListener="#{pcjRegistarAtaBean.cancelarListaPresencaBtn()}"/>

                        <p:commandButton id="pcjRegistarAtaConcluirBtn"
                                         rendered="#{bean.exibirBtnConcluir()}"
                                         value="Concluir"
                                         styleClass="ui-priority-primary float-right" icon="fa fa-save"
                                         action="#{bean.concluirBtn()}"
                                         actionListener="#{pcjConsultarAtaBean.setIdReuniao(bean.getIdReuniao())}"
                                         process="@this"
                                         update="@form"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide();window.scrollTo(0,0)"/>

                        <p:commandButton id="pcjRegistarAtaGuardarMinutaBtn"
                                         rendered="#{bean.exibirBtnGuardaMinuta()}"
                                         value="Guardar minuta"
                                         styleClass="ui-priority-secondary float-right" icon="fa fa-save"
                                         action="#{bean.saveDraft()}"
                                         validateClient="true"
                                         process="@form"
                                         update="@form"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide();"/>

                        <p:commandButton id="pcjGuardarAssBtn" value="Guardar Assinatura"
                                         rendered="#{bean.exibirBotoesGuardarAssinatura()}"
                                         styleClass="float-right"
                                         action="#{pcjRegistarAtaBean.guardarAssinaturaConclusaoBtn()}"
                                         process="@form"
                                         update="pcjRegistarAtaForm pcjRegistarAtaForm:pcjAtaTabView:pcjTableAssinaturaPanel
                                                 pcjRegistarAtaForm:pcjAtaTabView:pcjTableTextoConclusaoPanel
                                                 "
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide();"/>

                        <p:commandButton id="pcjCancelarAlterarAssinaturaBtn" value="Cancelar alteração"
                                         rendered="#{bean.exibirBotoesGuardarAssinatura()}"
                                         styleClass="ui-priority-secondary float-right mr-xs"
                                         process="@this"
                                         update="pcjRegistarAtaForm pcjRegistarAtaForm:pcjAtaTabView:pcjTableAssinaturaPanel
                                                 pcjRegistarAtaForm:pcjAtaTabView:pcjTableTextoConclusaoPanel
                                                 "
                                         action="#{pcjRegistarAtaBean.cancelarAlteracaoAssinaturaBtn()}"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         onsuccess="PF('frawPageBlocker').hide()"/>


                    </p:outputPanel>
                </div>
            </div>
        </h:form>
    </ui:define>
</ui:composition>