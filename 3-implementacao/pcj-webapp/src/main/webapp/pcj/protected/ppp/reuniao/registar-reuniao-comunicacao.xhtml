<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="bean" value="#{pcjRegistarReuniaoComunicacaoBean}" />
    <ui:param name="registarReuniao" value="#{pcjRegistarReuniaoComunicacaoBean.reuniaoEventoDTO}" />

    <ui:param name="pageTitle" value="Registar evento" />

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessagesRegistar" class="info" layout="block">
            <ul>
                <h:outputText escape="false"
                              value="Registre os dados, defina o local e as informações necessárias para o evento." />
            </ul>
        </h:panelGroup>
        <div id="messageErrorValidateForm" style="color: red; display: none;">
            <h:panelGroup id="pcjInfoMessagesErroRegReuniao" class="erro" layout="block">
                <ul>
                    <h:outputText escape="false" value="Os campos assinalados são de preenchimento obrigatório." />
                </ul>
            </h:panelGroup>
        </div>
    </ui:define>
    <ui:define name="content">
        <h:form id="reuniaoRegistarForm">

            <div class="row-fluid mb-3">
                <p:outputLabel value="* #{pcjComponentMessage['obrigatorio']}" />
            </div>

            <div class="row-fluid mb-5">
                <h:panelGroup id="reuniaoPainelRegistar">
                    <div class="row-fluid mb-5">
                        <div class="titulo-seccao">
                            <h2 class="texto-titulo-3">Dados do evento</h2>
                        </div>
                    </div>

                    <ui:include src="fragmentos/salvar/principal-reuniao.xhtml">
                        <ui:param name="bean" value="#{bean}"/>
                        <ui:param name="dto" value="#{registarReuniao}"/>
                        <ui:param name="form" value="reuniaoRegistarForm"/>
                    </ui:include>

                    <h:panelGroup id="pcjPanelTipoLocalReuniaoSelecionado">
                        <ui:include src="fragmentos/salvar/local-reuniao.xhtml">
                            <ui:param name="bean" value="#{bean}"/>
                            <ui:param name="dto" value="#{registarReuniao}"/>
                            <ui:param name="form" value="reuniaoRegistarForm"/>
                        </ui:include>
                    </h:panelGroup>

                    <h:panelGroup id="pcjPanelHorarioReuniaoSelecionado" layout="block">
                        <ui:include src="fragmentos/salvar/horario-reuniao.xhtml">
                            <ui:param name="bean" value="#{bean}"/>
                            <ui:param name="dto" value="#{registarReuniao}"/>
                            <ui:param name="form" value="reuniaoRegistarForm"/>
                        </ui:include>
                    </h:panelGroup>

                    <div class="row-fluid mb-5">
                        <div class="titulo-seccao">
                            <h2 class="texto-titulo-3">Detalhes do evento</h2>
                        </div>
                    </div>

                    <h:panelGroup id="reuniaoAssuntoDescricaoPanel">
                        <h:panelGroup rendered="#{bean.diferenteReuniaoComissaoAlargada()}">
                            <ui:include src="fragmentos/salvar/assunto-descricao.xhtml">
                                <ui:param name="bean" value="#{bean}"/>
                                <ui:param name="dto" value="#{registarReuniao}"/>
                                <ui:param name="form" value="reuniaoRegistarForm"/>
                            </ui:include>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup id="reuniaoTipoConvocatoria">
                        <h:panelGroup rendered="#{bean.reuniaoComissaoAlargadaOuRestrita()}">
                            <ui:include src="fragmentos/salvar/tipo-convocatoria.xhtml">
                                <ui:param name="bean" value="#{bean}"/>
                                <ui:param name="dto" value="#{registarReuniao}"/>
                                <ui:param name="form" value="reuniaoRegistarForm"/>
                            </ui:include>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup id="reuniaoplanoTrabalho">
                        <ui:include src="fragmentos/salvar/ponto-trabalho-comunicacao.xhtml">
                            <ui:param name="bean" value="#{bean}"/>
                            <ui:param name="dto" value="#{registarReuniao}"/>
                            <ui:param name="form" value="reuniaoRegistarForm"/>
                        </ui:include>
                    </h:panelGroup>

                    <h:panelGroup id="panelMembrosComissao">
                        <ui:include src="fragmentos/salvar/membros-comissao-geral.xhtml">
                            <ui:param name="bean" value="#{bean}"/>
                            <ui:param name="dto" value="#{registarReuniao}"/>
                            <ui:param name="form" value="reuniaoRegistarForm"/>
                        </ui:include>
                    </h:panelGroup>
                </h:panelGroup>
            </div>

            <div class="row-fluid mb-6">
                <p:commandButton id="pcjCancelarRegistarBtn" styleClass="ui-priority-secondary" style="float: left;"
                                 value="Voltar" action="#{pcjSubsystem.pesquisarComunicacoes()}"
                                 process="@this" onstart="PF('frawPageBlocker').show()"
                                 oncomplete="PF('frawPageBlocker').hide();" onsuccess="window.scrollTo(0, 0);" />

                <p:commandButton id="pcjRegistarReuniaoBtn" styleClass="ui-priority-primary" style="float: right;"
                                 value="Registar evento" actionListener="#{bean.registarReuniaoComunicacao()}" process="@form"
                                 update="@form" onstart="PF('frawPageBlocker').show()"
                                 oncomplete="handleSubmission(xhr, status, args);PF('frawPageBlocker').hide();window.scrollTo(0,0)" />
            </div>

        </h:form>
    </ui:define>
</ui:composition>