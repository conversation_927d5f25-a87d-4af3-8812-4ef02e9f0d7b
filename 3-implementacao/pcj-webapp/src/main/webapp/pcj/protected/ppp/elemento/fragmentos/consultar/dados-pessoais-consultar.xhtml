<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core">

    <h:panelGroup id="pcj#{id}DadosPessoaisConsultarPnl" layout="block">
        <div class="row-fluid mb-5">
            <h6 class="bold">Dados pessoais</h6>
        </div>

        <div layout="block">
            <div class="row-fluid mb-5">
                <div class="span4">
                    <p:outputLabel id="pcj#{id}TipoDocumentoConsultarLbl"
                                for="pcj#{id}TipoDocumentoConsultarTxt"
                                value="Tipo de documento" />
                    <h:outputText id="pcj#{id}TipoDocumentoConsultarTxt"
                                  value="#{bean.getValorDominio('TPDOCID', elemento.utilizadorPCJ.codigoDocumento)}"
                    />            
                </div>

                <div class="span4">
                    <p:outputLabel id="pcj#{id}NumeroDocumentoConsultarLbl"
                                   for="pcj#{id}NumeroDocumentoConsultarTxt"
                                   value="Nº de documento"/>
                    <h:outputText id="pcj#{id}NumeroDocumentoConsultarTxt"
                                  value="#{bean.getValor(elemento.utilizadorPCJ.numeroDocumento)}" />
                </div>
                <div class="span4">
                    <p:outputLabel id="pcj#{id}NacionalidadeLbl"
                                   for="pcj#{id}NacionalidadeConsultarTxt"
                                   value="Nacionalidade"/>
                    <h:outputText id="pcj#{id}NacionalidadeConsultarTxt"
                                  value="#{bean.getValor(elemento.utilizadorPCJ.pais)}" />
                </div>
            </div>

            <div class="row-fluid mb-5">
                <div class="span4">
                    <p:outputLabel id="pcj#{id}NifConsultarLbl"
                                   for="pcj#{id}NifConsultarTxt"
                                   value="NIF"/>
                    <h:outputText id="pcj#{id}NifConsultarTxt"
                                  value="#{bean.getValor(elemento.utilizadorPCJ.nif)}" />
                </div>
                <div class="span4">
                    <p:outputLabel id="pcj#{id}NissConsultarLbl"
                                   for="pcj#{id}NissConsultarTxt"
                                   value="NISS"/>
                    <h:outputText id="pcj#{id}NissConsultarTxt"
                                  value="#{elemento.utilizadorPCJ.niss}"/>
                </div>

                <div class="span4">
                    <p:outputLabel id="pcj#{id}TelemovelConsultarLbl"
                                    for="pcj#{id}TelemovelConsultarTxt"
                                    value="Telemóvel" />
                    <h:outputText id="pcj#{id}TelemovelConsultarTxt"
                                 value="#{bean.getValor(elemento.utilizadorPCJ.telemovel)}" />
                </div>
            </div>
        </div>

        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcj#{id}NomeConsultarLbl"
                               for="pcj#{id}NomeConsultarTxt"
                               value="Nome"/>
                <h:outputText id="pcj#{id}NomeConsultarTxt"
                              value="#{bean.getValor(elemento.utilizadorPCJ.nome)}" />
            </div>
        </div>

        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcj#{id}NomeProfissionalConsultarLbl"
                               for="pcj#{id}NomeProfissionalConsultarTxt"
                               value="Nome profissional"/>
                <h:outputText id="pcj#{id}NomeProfissionalConsultarTxt"
                             value="#{bean.getValor(elemento.utilizadorPCJ.nomeProfissional)}" />
            </div>
        </div>

        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcj#{id}EmailPessoalConsultarLbl"
                               for="pcj#{id}EmailPessoalConsultarTxt"
                               value="E-mail pessoal"/>
                <h:outputText id="pcj#{id}EmailPessoalConsultarTxt"
                             value="#{bean.getValor(elemento.utilizadorPCJ.emailPessoal)}" />
            </div>
        </div>        

        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcj#{id}FotografiaConsultarLbl"
                               for="pcj#{id}FotografiaDtb:pcj#{id}FotografiaConsultarFGimg"
                               value="Fotografia"/>

                <div class="table-list">
                    <p:dataTable id="pcj#{id}FotografiaDtb" var="item"
                                value="#{elemento.utilizadorPCJ.foto}" 
                                showGridlines="true"
                                rendered="#{bean.existeFotografiaCarregada(elemento.utilizadorPCJ)}" >

                        <f:facet name="caption">Fotografia de perfil do elemento</f:facet>

                        <p:column style="text-align: left; width: 13%;">
                            <div class="row-fluid mb-2">                               
                        <p:graphicImage id="pcj#{id}FotografiaConsultarFGimg"
                                        value="#{bean.carregarImagem(elemento.utilizadorPCJ.foto)}"
                                        stream="false"
                                        width="120" height="120" />
                            </div>
                        </p:column> 

                        <p:column style="text-align: left; width: 77%;">
                            <h:outputText value="fotografia" />
                        </p:column>
                    </p:dataTable>
                </div>

                <h:outputText id="pcj#{id}FotografiaConsultarTxt"
                              value="-"
                              rendered="#{!bean.existeFotografiaCarregada(elemento.utilizadorPCJ)}" />
            </div>
        </div>
   </h:panelGroup>
</ui:fragment>