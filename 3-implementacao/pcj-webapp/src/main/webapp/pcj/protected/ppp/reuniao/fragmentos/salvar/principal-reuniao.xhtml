<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:ii="http://java.sun.com/jsf/composite/iies">

    <ui:fragment rendered="#{dto.id == null}">
        <ui:include src="registar-modalidade-tipo.xhtml">
            <ui:param name="dto" value="#{dto}"/>
            <ui:param name="bean" value="#{bean}"/>
            <ui:param name="form" value="reuniaoRegistarForm"/>
        </ui:include>
    </ui:fragment>

    <ui:fragment rendered="#{dto.id != null}">
        <ui:include src="alterar-modalidade-tipo.xhtml">
            <ui:param name="dto" value="#{dto}"/>
            <ui:param name="bean" value="#{bean}"/>
            <ui:param name="form" value="reuniaoAlterarForm"/>
        </ui:include>
    </ui:fragment>

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="reuniaoResposnavelEvento" value="Responsável pelo evento" />
            <p:selectOneMenu id="reuniaoResposnavelEvento" styleClass="max-field-size"
                             value="#{dto.responsavel}" required="true"
                             disabled="#{dto.fluxoComunicacao and dto.id eq null}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             converter="omnifaces.SelectItemsConverter">
                <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                <f:selectItems value="#{bean.responsaveis}" var="e" itemLabel="#{e.utilizadorPCJ.nome}"
                               itemValue="#{e}" />
            </p:selectOneMenu>
            <p:message for="reuniaoResposnavelEvento" />
        </div>
    </div>

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="reuniaoLocalRadio" value="Local" />
            <h:selectOneRadio id="reuniaoLocalRadio" value="#{bean.tipoReuniaoSelecionado}"
                              styleClass="select-radio-2 sm" required="true"
                              requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                <f:selectItems value="#{bean.tiposLocalReuniao}" var="tipoLocalReuniao"
                               itemValue="#{tipoLocalReuniao}" itemLabel="#{tipoLocalReuniao.descricao}" />
                <p:ajax event="change" update="@this pcjPanelTipoLocalReuniaoSelecionado pcjPanelHorarioReuniaoSelecionado" process="@this"
                        listener="#{bean.carregaPainelTipoLocalReuniao()}"
                        onstart="PF('frawPageBlocker').show();" oncomplete="PF('frawPageBlocker').hide();" />
            </h:selectOneRadio>
            <p:message for="reuniaoLocalRadio" />
        </div>
    </div>


</ui:fragment>