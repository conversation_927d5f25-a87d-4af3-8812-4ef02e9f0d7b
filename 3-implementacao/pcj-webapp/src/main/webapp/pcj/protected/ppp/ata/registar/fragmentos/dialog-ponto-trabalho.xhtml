<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core">

    <p:dialog header="Adicionar trabalho" widgetVar="addTrab" modal="true" resizable="false" draggable="false"
              style="width:528px;">
        <p:outputPanel id="pcjModalSelectTipoTrabalho">
            <div class="row-fluid">
                <div class="dialog-content">
                    <p:outputPanel id="pcjInfoMessagesAvisoRegAtaPanel">
                        <p:outputPanel id="pcjInfoMessagesAvisoRegAta" rendered="#{bean.mostarMensagemPendencia()}">
                            <div class="area-mensagens">
                                <div class="area-info">
                                    <div class="aviso" role="status" aria-live="polite">
                                        <p>#{bean.getAlertMessage()}</p>
                                    </div>
                                </div>
                            </div>
                        </p:outputPanel>
                    </p:outputPanel>
                    <div class="row-fluid mb-3">
                        <p> Adicione pontos de trabalho para ficarem registados em ata.</p>
                    </div>
                    <div class="row-fluid mb-5">
                        <p:outputPanel id="pcjSelectTipoTrabalho">
                            <p:outputLabel value="Trabalhos"/>
                            <p:selectOneRadio id="pcjAddPontoTrabRadio1" value="#{bean.tipoTrabalho}"
                                              layout="grid" columns="1" styleClass="select-radio-2 sm">
                                <f:selectItem itemLabel="Pendentes" itemValue="#{true}"/>
                                <f:selectItem itemLabel="Novos" itemValue="#{false}"/>
                                <p:ajax event="change"
                                        update="@this pontosPendentesPanel pontosNovosPanel"
                                        onstart="PF('frawPageBlocker').show()"
                                        onerror="PF('frawPageBlocker').hide()"
                                        onsuccess="PF('frawPageBlocker').hide()"
                                        process="@this"/>
                            </p:selectOneRadio>
                        </p:outputPanel>
                    </div>
                    <div class="row-fluid mb-5">
                        <p:outputLabel value="Ponto de trabalho"/>
                        <p:outputPanel id="pontosPendentesPanel">
                            <p:outputPanel id="pontosPendentes" rendered="#{bean.tipoTrabalho}">
                                <p:selectOneMenu id="pcjPontoTrabPendenteSelector"
                                                 value="#{bean.blocoPendente}" style="width:561px;"
                                                 converter="omnifaces.SelectItemsConverter">
                                    <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue=""
                                                  noSelectionOption="true"/>
                                    <f:selectItems value="#{bean.trabalhosPendentes}"
                                                   var="item" itemLabel="#{item.assunto}" itemValue="#{item}"
                                                   required="true"/>
                                    <p:ajax event="change"/>
                                </p:selectOneMenu>
                            </p:outputPanel>
                        </p:outputPanel>
                        <p:outputPanel id="pontosNovosPanel">
                            <p:outputPanel id="pontosNovos" rendered="#{!bean.tipoTrabalho}">
                                <p:selectOneMenu id="pcjPontoTrabNovoSelector"
                                                 value="#{bean.nomeBloco}" style="width:561px;"
                                                 converter="blocosTrabalhoAtaConverter">
                                    <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue=""
                                                  noSelectionOption="true"/>
                                    <f:selectItems value="#{bean.tiposTrabalhos}"
                                                   var="item" itemLabel="#{item.descricao}" itemValue="#{item}"
                                                   required="true"/>
                                    <p:ajax event="change"/>
                                </p:selectOneMenu>
                            </p:outputPanel>
                        </p:outputPanel>

                    </div>
                </div>
            </div>

        </p:outputPanel>
        <f:facet name="footer">
            <p:commandButton id="cancelStateBtn" value="Cancelar"
                             styleClass="ui-priority-secondary"
                             process="@this"
                             onstart="PF('frawPageBlocker').show()"
                             onerror="PF('frawPageBlocker').hide()"
                             onsuccess="PF('frawPageBlocker').hide()"
                             oncomplete="PF('addTrab').hide(); window.scrollTo(0, 0); if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]); }"/>

            <p:commandButton id="confirmStateBtn" value="Adicionar trabalho"
                             styleClass="ui-confirmdialog-yes"
                             actionListener="#{bean.adicionarTrabalhoBtn()}"
                             process="@this"
                             update="@form pontosPendentesPanel pontosNovosPanel pcjRegistarAtaForm:pcjAtaTabView:pcjBlocoTrabalhoPanel pcjRegistarAtaForm:pcjAtaTabView:pcjPanelWork"
                             onstart="PF('frawPageBlocker').show()"
                             onerror="PF('frawPageBlocker').hide()"
                             onsuccess="PF('frawPageBlocker').hide()"
                             oncomplete="PF('addTrab').hide(); window.scrollTo(0, 0); if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]); }"/>
        </f:facet>
    </p:dialog>
</ui:fragment>