<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core">
    <div class="row-fluid mt-5 mb-5">
        <div class="span6">
            <p:outputLabel for="tipoReuniaoAlargadaRadio" value="Tipo de reunião" />
            <h:selectOneRadio id="tipoReuniaoAlargadaRadio" value="#{dto.tipoConvocatoria}"
                              styleClass="select-radio-2 sm" required="#{bean.reuniaoComissaoAlargadaOuRestrita()}"
                              disabled="#{dto.fluxoComunicacao or bean.dataAgendaCincoDezDiasPosterior()}"
                              requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                <f:selectItems value="#{bean.tipoConvocatorias()}" var="tipoConvocatoria"
                               itemValue="#{tipoConvocatoria.codigoDetalheDominio}" itemLabel="#{tipoConvocatoria.designacao}" />

                <p:ajax event="change" process="@this" update="reuniaoplanoTrabalho"
                        listener="#{bean.carregarDadosPontoTrabalhoComunicacao()}"
                        onstart="PF('frawPageBlocker').show()"
                        onerror="PF('frawPageBlocker').hide()"
                        onsuccess="PF('frawPageBlocker').hide()" />

            </h:selectOneRadio>
            <p:message for="tipoReuniaoAlargadaRadio" />
        </div>
    </div>
</ui:fragment>