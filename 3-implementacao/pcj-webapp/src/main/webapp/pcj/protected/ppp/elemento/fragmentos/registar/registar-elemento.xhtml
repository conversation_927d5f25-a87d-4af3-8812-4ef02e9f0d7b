<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui">

    <p:outputPanel id="#{id}NissContsultaPnl">
        <ui:include src="pesquisa-elemento-documento.xhtml"/>
    </p:outputPanel>

    <h:panelGroup id="#{id}RegistarElementoPnl" layout="block">
        <p:outputPanel rendered="#{!bean.pesquisaSucesso}" id="#{id}panelMensagemPesquisa">
            <div class="panel-mensagem">
                Efetue uma pesquisa para avançar com o registo
            </div>
        </p:outputPanel>
        <p:outputPanel id="#{id}DadosEcraRegistoElementoPnl">
            <p:outputPanel id="#{id}DadosElementoPnl"
                           rendered="#{bean.mostrarEcraRegistarElementoComComponente()}">
                <p:outputPanel id="#{id}DadosPessoaisElementoPnl">
                    <h:panelGroup id="#{id}DadosPessoaisReadOnlyIncludePnl"
                                  layout="block">
                        <ui:include src="../../../utilizador/fragmentos/registar/dados-pessoais-principal.xhtml"/>
                    </h:panelGroup>
                </p:outputPanel>

                <p:outputPanel id="#{id}DadosFuncaoIncludePnl"
                               layout="block">
                    <ui:include src="dados-funcoes.xhtml"/>
                </p:outputPanel>
            </p:outputPanel>
        </p:outputPanel>
    </h:panelGroup>
</ui:fragment>
