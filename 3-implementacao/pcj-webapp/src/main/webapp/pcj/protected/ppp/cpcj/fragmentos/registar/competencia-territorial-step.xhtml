<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core">

    <p:outputPanel>
        <div class="row-fluid mb-5"><h2>Competência territorial</h2></div>
        <div class="row-fluid mb-5">
            <div class="row-fluid mb-3">
                <div class="span4">
                    <p:outputLabel id="pcjDistritoLabelStepDois" for="pcjNomeDistrito" value="Distrito"
                                   styleClass="dica"/>
                    <p:tooltip for="pcjDistritoLabelStepDois">Corresponde ao distrito da morada da CPCJ</p:tooltip>
                    <h:outputText id="pcjNomeDistrito" value="#{bean.distritoDtoCompetencia.nome}"/>
                </div>
            </div>

        </div>

        <div class="row-fluid mb-5">
            <div class="span4">
                <p:outputPanel>
                    <p:outputLabel for="pcjConcelhosStepDois" value="Concelho"/>
                    <p:selectOneMenu id="pcjConcelhosStepDois" styleClass="max-field-size"
                                     value="#{bean.concelhoDtoCompetencia}"
                                     required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                                     effect="fade" var="c" filter="true"
                                     converter="omnifaces.SelectItemsConverter"
                                     filterMatchMode="contains">

                        <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue="" noSelectionOption="true"/>
                        <f:selectItems value="#{bean.concelhos}" var="concelho"
                                       itemValue="#{concelho}"
                                       itemLabel="#{concelho.nome}"/>
                        <p:ajax event="change"
                                update="@this pcjFreguesiasStepDois pcjFreguesiaCompetencia pcjBtnFreguesiaCompetencia panelResultadoPainelFreguesia"
                                process="@this"
                                listener="#{bean.concelhoCompetentiasSelecionadoListener()}"
                                onstart="PF('frawPageBlocker').show()"
                                onerror="PF('frawPageBlocker').hide()"
                                onsuccess="PF('frawPageBlocker').hide()"/>
                        <p:column>
                            <h:outputText value="#{c.nome}"/>
                        </p:column>
                    </p:selectOneMenu>
                    <p:message for="pcjConcelhosStepDois"/>
                </p:outputPanel>
            </div>

            <div class="span5">
                <p:outputPanel id="pcjFreguesiaCompetencia">
                    <p:outputPanel>
                        <p:outputLabel id="pcjLabelDropdownMulti" value="Freguesia*"/>
                        <h:panelGroup layout="block" id="multiSelectDropdownWrapper" class="multiSelectDropdownWrapper">
                            <p:selectCheckboxMenu id="pcjFreguesiasStepDois"
                                                  widgetVar="pcjFreguesiasStepDois"
                                                  value="#{bean.multiSelectOptions}"
                                                  label="Selecione pelo menos uma opção"
                                                  filter="true"
                                                  autoClose="false"
                                                  filterMatchMode="contains"
                                                  panelStyleClass="multi-select-container"
                                                  disabled="#{bean.concelhoDtoCompetencia == null}">

                                <f:selectItems id="items"
                                               value="#{bean.listaFreguesias.entrySet()}"
                                               var="freguesia"
                                               itemValue="#{freguesia.key}"
                                               itemLabel="#{freguesia.value}"/>

                                <p:ajax event="change"
                                        process="@this"
                                        update="pcjBtnFreguesiaCompetencia"
                                        onstart="PF('frawPageBlocker').show()"
                                        onerror="PF('frawPageBlocker').hide()"
                                        onsuccess="PF('frawPageBlocker').hide()"/>
                            </p:selectCheckboxMenu>


                            <p:message for="pcjFreguesiasStepDois"/>
                        </h:panelGroup>
                    </p:outputPanel>
                </p:outputPanel>

            </div>

            <div class="span3">
                <p:outputPanel id="pcjBtnFreguesiaCompetencia">
                    <h:panelGroup id="buttonPanelGroup" layout="block"
                                  style="#{(not empty bean.multiSelectOptions) ? '' : 'display:none;'}">
                        <p:outputLabel value=""/>
                        <p:commandButton id="pcjAssociarFreguesiaBtn"
                                         styleClass="ui-priority-primary float-left"
                                         rendered="#{!pcjRegistarCPCJBean.agregacao}"
                                         value="Associar Freguesia"
                                         actionListener="#{bean.associarFreguesia()}"
                                         process="@form pcjFreguesiasStepDois"
                                         update="@form"
                                         onstart="PF('frawPageBlocker').show()"
                                         oncomplete="PF('frawPageBlocker').hide();"/>
                    </h:panelGroup>
                </p:outputPanel>
            </div>

        </div>

        <div class="row-fluid mb-5 mt-5">
            <div class="titulo-seccao">
                <h2 class="texto-titulo-3">Freguesias associadas</h2>
            </div>
        </div>
        <div class="row-fluid mb-5">
            <p:outputPanel id="panelResultadoPainelFreguesia">
                <h:panelGroup layout="block" styleClass="row-fluid text-center"
                              rendered="#{bean.dto.freguesiaComptenciaTerritorial.size() eq 0}">
                    <p>Não existem freguesias a apresentar</p>
                </h:panelGroup>
                <h:panelGroup rendered="#{bean.dto.freguesiaComptenciaTerritorial.size() > 0}">
                    <div class="row-fluid mb-5">
                        <div class="table-list select-check-box-2 sm">
                            <p:dataTable id="pcjDataTableCPCJs" var="freguesiAssociada"
                                         value="#{bean.dto.freguesiaComptenciaTerritorial}"
                                         draggableColumns="false"
                                         rows="30" paginator="true" lazy="true" paginatorAlwaysVisible="false"
                                         paginatorTemplate="{PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}"
                                         paginatorPosition="bottom" rowsPerPageTemplate="50"
                                         currentPageReportTemplate="Página {currentPage}"
                                         rowStyleClass="resultado"
                                         rowIndexVar="rowId"
                                         emptyMessage="Não foram encontradas CPCJs"
                                         reflow="true">

                                <f:facet name="caption">Lista de freguesias associadas à competência territorial da comissão</f:facet>

                                <p:column id="nomeColumnFreguesia" headerText="Nome" styleClass="texto">
                                    <h:outputText value="#{freguesiAssociada.nome}"/>
                                </p:column>

                                <p:column id="pcjConcelhoAssociadoColumn" headerText="Concelho" styleClass="texto">
                                    <h:outputText id="ConcelhoAssociadoOutputText"
                                                  value="#{freguesiAssociada.concelhoDto.nome}"/>
                                </p:column>

                                <p:column id="pcjCpcjAssociadaColumn" headerText="CPCJ associada" styleClass="texto">
                                    <abbr title="#{freguesiAssociada.nomeCpcjAssociada}">
                                        <h:outputText id="CpcjAssociadaOutputText"
                                                      value="#{pcjHifenConverter.getAsString(freguesiAssociada.nomeCpcjAssociada)}"
                                                      rendered="#{freguesiAssociada.nomeCpcjAssociada != null}">
                                            <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                                        </h:outputText>
                                    </abbr>
                                    <h:outputText id="CpcjAssociadaOutputHifenText"
                                                  value="#{pcjHifenConverter.getAsString(freguesiAssociada.nomeCpcjAssociada)}"
                                                  rendered="#{freguesiAssociada.nomeCpcjAssociada == null}"/>
                                </p:column>

                                <p:column id="menuAcoesFreguesia" styleClass="acoes" headerText="Ações">
                                    <p:commandLink id="pcjBtnRemoverFreguesia"
                                                   value="Remover"
                                                   process="@this"
                                                   update="pcjRegistarPPPForm:panelResultadoPainelFreguesia pcjRegistarPPPForm:pcjFreguesiaCompetencia"
                                                   actionListener="#{bean.remover(freguesiAssociada)}"
                                                   onstart="PF('frawPageBlocker').show()"
                                                   oncomplete="PF('frawPageBlocker').hide()">
                                    </p:commandLink>
                                </p:column>

                            </p:dataTable>
                        </div>
                    </div>
                </h:panelGroup>
            </p:outputPanel>
        </div>
    </p:outputPanel>


</ui:fragment>