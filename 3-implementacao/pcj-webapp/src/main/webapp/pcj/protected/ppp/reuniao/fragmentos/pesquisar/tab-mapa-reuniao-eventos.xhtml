<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <h:form id="mapaReuniaoPesquisar" styleClass="mb-6">
        <h:outputScript target="body">
            function scheduleExtender() {
                this.cfg.viewRender = function(view, element) {
                    console.log('Disparando evento');
                    var dataInicio = view.start.format('YYYY-MM-DD');
                    var dataFim = view.end.format('YYYY-MM-DD');
                    var viewName = view.name;
                    console.log('Evento de mapa alterado:', viewName, dataInicio, dataFim);
                    onScheduleViewChange([
                        {name: 'dataInicio', value: dataInicio},
                        {name: 'dataFim', value: dataFim},
                        {name: 'viewName', value: viewName}
                    ]);
                };
            }
        </h:outputScript>

        <p:remoteCommand name="onScheduleViewChange" actionListener="#{bean.onViewChange}">
            <f:param name="startDate" />
            <f:param name="endDate" />
            <f:param name="viewName" />
        </p:remoteCommand>

        <p:outputPanel>
            <div class="row-fluid acoes mb-5">
                <div class="span12">
                    <p:outputPanel id="pcjBotoesPnl">
                        <p:outputPanel styleClass="left-aligned-btn">
                            <p:commandButton id="pcjRegistarEventoMapaBtn" value="Registar evento"
                                             styleClass="common-btn"
                                             update="@form"
                                             process="@form"
                                             onstart="PF('frawPageBlocker').show()"
                                             onerror="PF('frawPageBlocker').hide()"
                                             onsuccess="PF('frawPageBlocker').hide()"
                                             action="#{pcjSubsystem.registarReuniao()}"
                            />
                        </p:outputPanel>
                    </p:outputPanel>
                </div>
            </div>
        </p:outputPanel>
        <h:panelGroup id="reuniaoMapaPesquisa">
            <p:schedule
                    id="schedule"
                    defaultView="month"
                    widgetVar="scheduleEvent"
                    locale="pt"
                    timeZone="Europe/Lisbon"
                    aspectRatio="0.99"
                    draggable="false"
                    showHeader="true"
                    showWeekNumbers="false"
                    centerHeaderTemplate="prev, title, next"
                    leftHeaderTemplate="today"
                    rightHeaderTemplate="none"
                    allDaySlot="true"
                    resizable="false"
                    value="#{bean.gerenciadorEventos}"
                    axisFormat="HH:mm"
                    extender="scheduleExtender">

                <!-- TODO: Criar evento <p:ajax event="dateSelect" listener="#{bean.onDateSelect}" update="eventDetails" oncomplete="PF('createEventDialog').show();" />-->
                <!--                <p:ajax event="viewChange" listener="#{bean.onViewChange}" update="@this" />-->
                <p:ajax event="eventSelect" listener="#{bean.onEventSelect}" update="infoDialog infoEventDetails" oncomplete="PF('infoDialog').show();" />
            </p:schedule>


            <div class="all_modals">
                <p:dialog widgetVar="infoDialog" id="infoDialog" header="#{bean.reuniaoSelecionada.assunto}" closable="true">
                    <p:outputPanel id="infoEventDetails" >
                        <div class="eventDetails">

                            <div class="date-range schedule-modal--division">
                                <div class="schedule-dates">
                                    <p:outputLabel value="Período"/>
                                    <h:outputLabel value="#{bean.reuniaoSelecionada.dataInicio}" >
                                        <f:convertDateTime pattern="dd-MM-yyyy" />
                                    </h:outputLabel>

                                    <h:outputLabel value=" a " />

                                    <h:outputLabel value="#{bean.reuniaoSelecionada.dataFim}" >
                                        <f:convertDateTime pattern="dd-MM-yyyy" />
                                    </h:outputLabel>

                                </div>
                            </div>
                            <div class="row-fluid mb-5">
                                <div class="span4">
                                    <p:outputLabel value="Modalidade"/>
                                    <span> #{bean.getValorDominio('MODREUNI', bean.reuniaoSelecionada.modalidade)} </span>
                                </div>

                                <div class="span4">
                                    <p:outputLabel value="Tipo"/>
                                    <span> #{bean.getValorDominio('TIPREUNI', bean.reuniaoSelecionada.tipo)} </span>
                                </div>
                            </div>
                            <div class="row-fluid mb-5">
                                <div class="span4">
                                    <p:outputLabel value="Local"/>
                                    <span> #{bean.reuniaoSelecionada.localReuniao} </span>
                                </div>
                            </div>
                        </div>
                    </p:outputPanel>
                    <div class="schedule-button-group">
                        <p:commandButton id="showEventInfo" value="Voltar" styleClass="ui-priority-secondary" oncomplete="PF('infoDialog').hide();"/>
                        <p:commandButton id="showDetails"
                                         styleClass="ui-priority-primary float-right"
                                         value="Consultar"
                                         onstart="PF('frawPageBlocker').show()"
                                         oncomplete="PF('frawPageBlocker').hide();"
                                         action="#{pcjSubsystem.consultarReuniao()}"
                                         actionListener="#{pcjConsultarReuniaoBean.setIdReuniao(bean.reuniaoSelecionada.id)}"/>

                    </div>
                </p:dialog>
            </div>

        </h:panelGroup>
        <p:commandButton style="visibility: hidden;" aria-disabled="true" />
    </h:form>
</ui:fragment>