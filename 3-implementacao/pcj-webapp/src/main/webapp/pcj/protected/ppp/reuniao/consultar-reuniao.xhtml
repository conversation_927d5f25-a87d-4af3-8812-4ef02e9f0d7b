<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns="http://www.w3.org/1999/xhtml"
                template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="pageTitle" value="Consultar evento"/>
    <h2 class="bold">Consultar evento</h2>

    <ui:param name="dto" value="#{pcjConsultarReuniaoBean.reuniao}"/>
    <ui:param name="bean" value="#{pcjConsultarReuniaoBean}"/>
    <ui:param name="id" value="Reuniao"/>

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessagesPesquisa" class="info" layout="block">
            <ul>
                <h:outputText escape="false"
                              value="Consulte os dados, local e as informações do evento."/>
            </ul>
        </h:panelGroup>
    </ui:define>

    <ui:define name="content">
        <h:form id="pcjConsultardtoForm">
            <div class="row-fluid mb-5">
                <h:panelGroup id="pcjConsultar#{id}Pnl" layout="block">
                    <div class="row-fluid mb-5">
                        <div class="span9 titulo-seccao">
                            <h2>Dados do evento</h2>
                        </div>
                        <h:panelGroup id="pcjConsultar#{id}ImprimirEventoPnl"
                                      rendered="#{bean.isImprimirEventoVisivel()}">
                            <div class="span3" style="text-align: right;">
                                <p:commandLink id="pcjConsultar#{id}ImprimirEventoBtn" value="Imprimir evento"
                                               styleClass="ui-priority-terciary" iconPos="right"
                                               action="#{bean.imprimirEvento()}"
                                               onstart="PF('frawPageBlocker').show()"
                                               oncomplete="PF('frawPageBlocker').hide();"/>
                                <i class="fa fa-download" style="color: #225AAB; margin: 0 5px;"></i>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup id="pcjConsultar#{id}ImprimirConvocatoriaPnl"
                                      rendered="#{bean.isConvocatoriaVisivel()}">
                            <div class="span3" style="text-align: right;">
                                <p:commandLink id="pcjConsultar#{id}ImprimirConvocatoriaBtn"
                                               value="Imprimir convocatória" styleClass="ui-priority-terciary"
                                               iconPos="right"
                                               action="#{bean.imprimirConvocatoriaAlargada()}"
                                               onstart="PF('frawPageBlocker').show()"
                                               oncomplete="PF('frawPageBlocker').hide();"/>
                                <i class="fa fa-download" style="color: #225AAB; margin: 0 5px;"></i>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup id="pcjConsultar#{id}ImprimirComprovativoPnl"
                                      rendered="#{bean.isComprovativoVisivel()}">
                            <div class="span3" style="text-align: right;">
                                <p:commandLink id="pcjConsultar#{id}ImprimirComprovativoBtn"
                                               value="Imprimir comprovativo" styleClass="ui-priority-terciary"
                                               iconPos="right"
                                               actionListener="#{bean.carregarParticipantes() }"
                                               update="comprovativoModal"
                                               oncomplete="PF('comprovativoModal').show();"/>
                                <i class="fa fa-download" style="color: #225AAB; margin: 0 5px;"></i>
                                <ui:include src="fragmentos/consultar/modal-comprovativo.xhtml"/>
                            </div>
                        </h:panelGroup>
                    </div>

                    <div class="row-fluid mb-5">
                        <div class="span6">
                            <p:outputLabel for="pcj#{id}ModalidadeConsultarTxt" value="Modalidade"/>
                            <h:outputText id="pcj#{id}ModalidadeConsultarTxt" value="#{dto.modalidade}"/>
                        </div>

                        <div class="span6">
                            <p:outputLabel for="pcj#{id}TipoConsultarTxt" value="Tipo"/>
                            <h:outputText id="pcj#{id}TipoConsultarTxt" value="#{dto.tipo}"/>
                        </div>
                    </div>

                    <div class="row-fluid mb-5">
                        <div class="span12">
                            <p:outputLabel for="pcj#{id}ResponsavelConsultarTxt" value="Responsável pelo evento"/>
                            <h:outputText id="pcj#{id}ResponsavelConsultarTxt" value="#{dto.responsavel}"/>
                        </div>
                    </div>

                    <h:panelGroup id="pcjDadosMoradaCpcjIncludePnl" layout="block"
                                  rendered="#{bean.isMoradaCpcj()}">
                        <ui:include src="fragmentos/consultar/dados-morada-cpcj.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcjDadosOutraMoradaIncludePnl" layout="block"
                                  rendered="#{bean.isOutraMorada() or bean.isSemLocalDefinido()}">
                        <ui:include src="fragmentos/consultar/dados-outra-morada.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelDadosDetalhesEvento"
                                  rendered="#{not bean.isAlargadaReuniaoAlargada()}">
                        <ui:include src="fragmentos/consultar/dados-detalhes-evento.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelDadosDetalhesEventoAlargada"
                                  rendered="#{bean.isAlargadaReuniaoAlargada()}">
                        <div class="row-fluid mb-5">
                            <div class="titulo-seccao">
                                <h2>Detalhes do evento</h2>
                            </div>
                        </div>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelTipoReuniao"
                                  rendered="#{bean.isAlargadaReuniaoAlargada() or bean.reuniaoRestrita}">
                        <ui:include src="fragmentos/consultar/tipo-convocatoria.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelDadosMembrosComissao" rendered="#{bean.isModalidadeGeral()}">
                        <ui:include src="fragmentos/consultar/dados-membros-comissao.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelDadosPontosTrabalho" rendered="#{bean.isAlargadaReuniaoAlargada()}">
                        <ui:include src="fragmentos/consultar/dados-pontos-trabalho.xhtml"/>
                    </h:panelGroup>

                    <h:panelGroup id="pcj#{id}PanelDadosPontosTrabalhoRestrita" rendered="#{bean.reuniaoRestritaTipoRestrita}">
                        <ui:include src="fragmentos/salvar/ponto-trabalho-comunicacao.xhtml">
                            <ui:param name="bean" value="#{bean}"/>
                            <ui:param name="dto" value="#{dto}"/>
                            <ui:param name="form" value="pcjConsultardtoForm"/>
                        </ui:include>
                    </h:panelGroup>
                </h:panelGroup>
            </div>
            <div class="row-fluid  mb-6">
                <p:commandButton id="pcjVoltarConsultaReuniaoBtn" styleClass="ui-priority-secondary float-left"
                                 value="Voltar" action="#{pcjSubsystem.pesquisarReunioes()}" process="@this"
                                 onstart="PF('frawPageBlocker').show()" oncomplete="PF('frawPageBlocker').hide();"
                                 onsuccess="window.scrollTo(0, 0);"/>

                <p:commandButton rendered="#{dto.reuniaoConvocatoriaNaoEnviada() and dto.reuniaoNaoUltrapassada() and pcjControleAtaBean.naoExisteAtaIniciadaParaReuniao(dto.id)}"
                                 id="pcjEditardReuniaoBtn" styleClass="ui-priority-primary float-right"
                                 value="Alterar evento"
                                 action="#{pcjSubsystem.editarEventoComunicacao(dto.fluxoComunicacao)}"
                                 process="@form" update="@form"
                                 onstart="PF('frawPageBlocker').show()" oncomplete="PF('frawPageBlocker').hide();">
                    <f:setPropertyActionListener
                            target="#{dto.fluxoComunicacao ? pcjAlterarReuniaoComunicacaoBean.idEvento : pcjAlterarReuniaoBean.idEvento}"
                            value="#{dto.id}" />
                </p:commandButton>

                <p:commandButton rendered="#{not pcjControleAtaBean.estadoAtaAssinadaOuConcluida and pcjControleAtaBean.reuniaoAlargadaTipoModalidade}"
                                 id="#{pcjControleAtaBean.idBotaoRetomarRegistarAta}" styleClass="ui-priority-primary float-right"
                                 value="#{pcjControleAtaBean.nomeBotao}"
                                 action="#{pcjSubsystem.registarAta()}"
                                 actionListener="#{pcjRegistarAtaBean.setIdReuniao(pcjControleAtaBean.idReuniao)}"
                                 process="@form" update="@form"
                                 onstart="PF('frawPageBlocker').show()" oncomplete="PF('frawPageBlocker').hide();"/>

                <!--  Provisório para construção da ata, verificar as regras para exibir o botão -->
                <p:commandButton rendered="#{not pcjControleAtaBean.estadoAtaAssinadaOuConcluida and pcjControleAtaBean.reuniaoRestritaTipoModalidade}"
                                 id="pcjBtnRegistarAtaRestrita" styleClass="ui-priority-primary float-right"
                                 value="#{pcjControleAtaBean.nomeBotao}"
                                 action="#{pcjSubsystem.registarAtaRestrita()}"
                                 actionListener="#{pcjRegistarAtaRestritaBean.setIdReuniao(pcjControleAtaBean.idReuniao)}"
                                 process="@form" update="@form"
                                 onstart="PF('frawPageBlocker').show()" oncomplete="PF('frawPageBlocker').hide();"/>
                <!--  FIM -->
            </div>
        </h:form>
    </ui:define>
</ui:composition>