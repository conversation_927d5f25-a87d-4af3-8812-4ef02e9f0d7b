<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:c="http://java.sun.com/jsp/jstl/core"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">

    <div class="row-fluid mb-5">
        <h3>#{pcjMessages['pp.comunicacao.crianca.form.label.dados-crianca']}</h3>
    </div>

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="pcjAnaliseComunicacaoNome" value="#{pcjMessages['label.nome']}"/>
            <h:outputText id="pcjAnaliseComunicacaoNome" value="#{crianca.nome}"/>
        </div>

        <div class="span6">
            <p:outputLabel for="pcjAnaliseComunicacaoDtNasc" value="#{pcjMessages['pp.comunicacao.crianca.table.label.dataNascimento']}"/>
            <h:outputText id="pcjAnaliseComunicacaoDtNasc" value="#{utilBean.getValorCustom(crianca.dataNascimento, 'Desconhecida')}"/>
        </div>

    </div>
    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="pcjAnaliseComunicacaoInfoIdade" value="#{pcjMessages['pp.comunicacao.crianca.form.label.informacao-idade']}"/>
            <h:outputText id="pcjAnaliseComunicacaoInfoIdade" value="#{utilBean.getValorDominio('INFOIDADE', crianca.codigoInformacaoIdade)}"/>
        </div>

        <div class="span6 mb-3">
            <p:outputLabel for="pcjAnaliseComunicacaoIdade" value="#{pcjMessages['pp.comunicacao.crianca.form.label.idade']}"/>
            <h:outputText id="pcjAnaliseComunicacaoIdade" value="#{utilBean.getValorDominio('IDADECRIAN', crianca.codigoIdade)}"/>
        </div>

    </div>
    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="pcjAnaliseComunicacaoSexo" value="#{pcjMessages['pp.comunicacao.crianca.form.label.genero']}"/>
            <h:outputText id="pcjAnaliseComunicacaoSexo" value="#{utilBean.getValorDominio('GENERO', crianca.codigoGenero)}"/>
        </div>
    </div>
</ui:fragment>