<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
>
    <!-- Documento de Comunicação -->
    <p:outputPanel id="panelFileUploadMultiple">
        <div id="fileUploadMultiple" class="row-fluid mb-5">
            <div class="span12 ui-fileupload-border">
                <div class="fileUpload-card-name">
                    <span class="description">#{pcjMessages['pp.comunicacao.crianca.table.label.documento-comunicacao']}</span>
                </div>
                <hr/>

                <p:fileUpload id="documentoComunicacaoFileUploadMultiple"
                              immediate="true"
                              widgetVar="fileUploadVarM"
                              fileUploadListener="#{participanteStep.handleFilesMultipleUpload}"
                              invalidSizeMessage="#{pcjFileUploadBean.invalidSizeMessageMultiple}"
                              invalidFileMessage="#{pcjFileUploadBean.invalidFileMessageMultiple}"
                              fileLimitMessage="#{pcjFileUploadBean.fileLimitMessageMultiple}"
                              sizeLimit="#{pcjFileUploadBean.fileSize}"
                              mode="#{pcjFileUploadBean.mode}"
                              dragDropSupport="#{pcjFileUploadBean.dragDropSupport}"
                              auto="#{pcjFileUploadBean.auto}"
                              multiple="true"
                              fileLimit="#{pcjFileUploadBean.fileLimitMultiple}"
                              label="#{pcjMessages['pp.label.selecionar-documento']}"
                              update="panelFileUploadMultiple"
                              allowTypes="/(\.|\/)(jpe?g|png|pdf)$/"
                              accept=".jpg, .jpge, .pdf, .png"
                />

                <h:panelGroup rendered="#{participanteStep.participante.documentosComunicacao.size() lt 3}">
                    <div class="maxSizeText">#{pcjMessages['pp.label.permitir-documentos']}</div>
                </h:panelGroup>

                <p:fragment id="multipleFileTable">
                    <ui:repeat value="#{participanteStep.participante.documentosComunicacaoAsList}" var="item" varStatus="myVarStatus">
                        <div class="fileUpload-card">
                            <hr class="mt-xs"/>
                            <div class="fileUpload-card--info">
                                <div class="fileUpload-card--name-area">
                                    <p:fragment>
                                        <i class="fa fa-#{participanteStep.fileUploadBean.iconHandler(item.uploadedFile.getContentType())}"/>
                                    </p:fragment>

                                    <div class="fileUpload-card--name">
                                        <p:fragment > #{item.uploadedFile.getFileName()}</p:fragment>
                                        <p class="fileUpload-card--sub-name">#{participanteStep.fileUploadBean.convertbytesToMB(item.uploadedFile.getSize())}
                                        </p>
                                    </div>
                                </div>

                                <div class="fileUpload-card--link-group">
                                    <div class="fileUpload-card--link-left">
                                        <p:commandButton id="showFile"
                                                         value="#{pcjMessages['label.consultar']}"
                                                         process="@this"
                                                         immediate="true"
                                                         onstart="PF('frawPageBlocker').show()"
                                                         oncomplete="PF('frawPageBlocker').hide();"
                                                         ajax="false"
                                                         styleClass="ui-priority-terciary"
                                                         action="#{participanteStep.fileUploadBean.verDocumentoMultiple(item.uploadedFile)}"
                                                         onclick="this.form.target = '_blank'"/>

                                    </div>
                                    <div class="fileUpload-card--link-right">
                                        <p:commandButton id="removeSingleFile"
                                                         value="#{pcjMessages['label.remover']}"
                                                         immediate="true"
                                                         styleClass="ui-priority-terciary"
                                                         action="#{participanteStep.deleteFileSingle(item)}"
                                                         update="@parent:@parent:@parent"/>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </ui:repeat>
                </p:fragment>
            </div>
        </div>
    </p:outputPanel>
</ui:fragment>