<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:composition xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns="http://www.w3.org/1999/xhtml"
                template="/pcj/templates/pcj-base.xhtml">

    <ui:param name="bean" value="#{pcjConcluirComunicacaoBean}"/>
    <ui:param name="concluirComunicacao" value="#{bean.comunicacaoConcluirDTO}"/>
    <ui:param name="comunicacao" value="#{concluirComunicacao.comunicacao}"/>
    <ui:param name="registarReuniao" value="#{bean.reuniaoEventoDTO}" />

    <ui:param name="pageTitle" value="#{bean.getMessageTitle()}"/>

    <ui:define name="info_messages">
        <h:panelGroup id="pcjInfoMessagesConcluir" class="info" layout="block">
            <ul>
                <h:outputText escape="false" value="#{pcjMessages['pp.comunicacao.texto.info.comunicacao-concluir']}"/>
            </ul>
        </h:panelGroup>
    </ui:define>

    <ui:define name="content">
        <h:form id="pcjConcluirComunicacaoForm">
            <div class="row-fluid">
                <p:outputLabel value="* Campo obrigatório"/>
            </div>
            <div class="row-fluid mb-5">
                <h3 class="mb-3">Detalhe da conclusão</h3>
            </div>

            <div class="row-fluid mb-m">
                <div class="span6">
                    <p:outputLabel for="pcjIdAcaoSerRealizada" value="#{pcjMessages['pp.comunicacao.label.comunicacao-acao-realizar']}" />
                    <p:selectOneMenu id="pcjIdAcaoSerRealizada"
                                     value="#{concluirComunicacao.codigoEstadoAnalise}"
                                     required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                                     process="@this">
                        <f:selectItem noSelectionOption="true" itemLabel="Selecione uma opção"/>
                        <f:selectItems value="#{bean.acoesComunicacao}"
                                       var="item" itemLabel="#{item.designacao}"
                                       itemValue="#{item.codigoDetalheDominio}"/>
                        <p:ajax event="change"
                                listener="#{bean.setarAcaoComunicacao()}"
                                update="@this"
                                process="@this"
                                onstart="PF('frawPageBlocker').show()"
                                onerror="PF('frawPageBlocker').hide()"
                                onsuccess="PF('frawPageBlocker').hide()"/>
                    </p:selectOneMenu>
                    <p:message id="pcjIdAcaoSerRealizadaMessage" display="text" for="pcjIdAcaoSerRealizada" />
                </div>

            </div>

<!--            <p:outputPanel id="pcjConcluirComunicacaoArquivamentoPanel">-->
<!--                <p:outputPanel id="pcjConcluirComunicacaoArquivamento"-->
<!--                               rendered="#{bean.exibirArquivamento}">-->
<!--                    <ui:include src="/pcj/protected/pp/comunicacao/concluir/fragmentos/motivo-arquivamento.xhtml"/>-->
<!--                </p:outputPanel>-->
<!--            </p:outputPanel>-->

<!--            <p:outputPanel id="pcjConcluirComunicacaoComissaoDestinoPanel">-->
<!--                <p:outputPanel id="pcjConcluirComunicacaoTransferirParaOutra" rendered="#{bean.exibirTranferirParaOutra}">-->
<!--                    <ui:include src="/pcj/protected/pp/comunicacao/concluir/fragmentos/comissao-destino.xhtml"/>-->
<!--                </p:outputPanel>-->
<!--            </p:outputPanel>-->

<!--            <p:outputPanel id="pcjConcluirComunicacaoAnexoComunicacaoPanel">-->
<!--                <p:outputPanel id="pcjConcluirComunicacaoAnexoProcessoOuComunicacao" rendered="#{bean.exibirAnexarProcessoOuComunicacao}">-->
<!--                    <ui:include src="/pcj/protected/pp/comunicacao/concluir/fragmentos/anexar-processo.xhtml"/>-->
<!--                </p:outputPanel>-->
<!--            </p:outputPanel>-->


            <p:outputPanel id="pcjConcluirComunicacaoMotivoRemessaPanel">
                <p:outputPanel id="pcjConcluirComunicacaoMotivoRemessa" rendered="#{bean.exibirRemeterECMIJ}">
                    <p:outputLabel for="pcjConcluirComunicacaoMotivoRemessaText" value="#{pcjMessages['pp.comunicacao.label.comunicacao-motivo-remessa-ecmij']}"/>
                    <h:outputText id="pcjConcluirComunicacaoMotivoRemessaText" value="#{pcjMessages['pp.comunicacao.text.comunicacao-motivo-remessa-ecmij']}"/>
                </p:outputPanel>
            </p:outputPanel>

            <p:outputPanel id="pcjConcluirComunicacaoMotivoDevolucaoPanel">
                <p:outputPanel id="pcjConcluirComunicacaoMotivoDevolucao" rendered="#{bean.exibirDevolverECMIJ}">
                    <p:outputLabel for="pcjConcluirComunicacaoMotivoDevolucaoText" value="#{pcjMessages['pp.comunicacao.label.comunicacao-motivo-devolucao-ecmij']}"/>
                    <h:outputText id="pcjConcluirComunicacaoMotivoDevolucaoText" value="#{pcjMessages['pp.comunicacao.text.comunicacao-motivo-devolucao-ecmij']}"/>
                </p:outputPanel>
            </p:outputPanel>

            <div class="row-fluid">
                <div class="span12">
                    <p:outputLabel for="pcjConcluirInputTextArea" value="#{pcjMessages['pp.comunicacao.button.comunicacao-justificacao']}"/>
                    <p:inputTextarea
                            id="pcjConcluirInputTextArea"
                            value="#{concluirComunicacao.justificacao}"
                            rows="3"
                            counter="pcjConcluirInputTextAreaCounter"
                            maxlength="500"
                            counterTemplate="{0} caracteres restantes"
                            autoResize="false" required="true"
                            requiredMessage="#{pcjComponentMessage['obrigatorio']}"/>
                    <h:outputText id="pcjConcluirInputTextAreaCounter" styleClass="TextAreaNumber" />
                    <p:message id="pcjConcluirInputTextAreaMsg" for="pcjConcluirInputTextArea"/>
                </div>
            </div>

            <p:outputPanel id="pcjConcluirComunicacaoInstaurarProcessoCheckBoxPanel">
                <p:outputPanel id="pcjConcluirComunicacaoCkBox" rendered="#{bean.exibirInstaurarProcesso}">
                    <ui:include src="/pcj/protected/pp/comunicacao/concluir/fragmentos/check-intervencao-urgente.xhtml"/>
                </p:outputPanel>
            </p:outputPanel>

            <ui:include src="/pcj/protected/pp/comunicacao/concluir/fragmentos/dialog-concluir-analise.xhtml"/>

            <div class="row-fluid mt-xl">
                <div class="span12">
                    <p:commandButton id="pcjConsultarComunicacaoVoltarBtn"
                                     styleClass="ui-priority-secondary"
                                     value="Voltar"
                                     immediate="true"
                                     action="#{pcjSubsystem.pesquisarComunicacoes()}"
                                     process="@form"
                                     onstart="PF('frawPageBlocker').show()"
                                     onerror="PF('frawPageBlocker').hide()"
                                     oncomplete="PF('frawPageBlocker').hide()"/>

                    <p:commandButton id="pcjConcluirComunicacaoBtn"
                                     value="#{pcjMessages['pp.comunicacao.button.concluir-comunicacao']}"
                                     onclick="PF('dlgConcluirAnalise').show();"
                                     styleClass="float-right" />

                    </div>
            </div>
        </h:form>
    </ui:define>
</ui:composition>