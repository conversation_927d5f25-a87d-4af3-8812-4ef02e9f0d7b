<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:p="http://primefaces.org/ui"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:o="http://omnifaces.org/ui">

    <o:importConstants type="pt.segsocial.pcj.ppp.enums.BlocosTrabalhoAtaRestritaEnum" var="BlocosTrabalhoAtaRestritaEnum" />

    <p:outputPanel rendered="#{bean.nomeBloco eq BlocosTrabalhoAtaRestritaEnum.ASSUNTOS_GERAIS}">
        <div class="row-fluid">
            <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/assuntos-gerais.xhtml">
                <ui:param name="id" value="assuntos-gerais"/>
            </ui:include>
        </div>
    </p:outputPanel>

    <p:outputPanel rendered="#{bean.nomeBloco eq BlocosTrabalhoAtaRestritaEnum.COMUNICACAO}">
        <div class="row-fluid">
            <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/comunicacao.xhtml">
                <ui:param name="id" value="comunicacao"/>
                <ui:param name="bean" value="#{blocoComunicacaoRestritaBean}"/>
            </ui:include>
        </div>
    </p:outputPanel>

<!--    <p:outputPanel rendered="#{bean.blocoTrabalho eq 'parecer'}">-->
<!--        <div class="row-fluid">-->
<!--            <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/parecer.xhtml">-->
<!--                <ui:param name="id" value="parecer"/>-->
<!--            </ui:include>-->
<!--        </div>-->
<!--    </p:outputPanel>-->
<!--    <p:outputPanel rendered="#{bean.blocoTrabalho eq 'conhecimento'}">-->
<!--        <div class="row-fluid">-->
<!--            <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/conhecimento.xhtml">-->
<!--                <ui:param name="id" value="conhecimento"/>-->
<!--            </ui:include>-->
<!--        </div>-->
<!--    </p:outputPanel>-->

</ui:fragment>