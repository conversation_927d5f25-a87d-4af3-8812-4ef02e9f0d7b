<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui">

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="reuniaoModalidade" value="Modalidade" />
            <h:outputText id="modalidadeAlterarLabel" value="#{bean.getValorDominio('MODREUNI', dto.modalidade)}"/>
        </div>

        <div class="span6">
            <p:outputLabel for="reuniaoTipo" value="Tipo" />
            <h:outputText id="tipoAlterarLabel" value="#{bean.getValorDominio('TIPREUNI', dto.tipo)}"/>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="reuniaoResposnavelEvento" value="Responsável pelo evento" />
            <h:outputText id="tipoAlterarLabel" value="#{dto.responsavel}"/>
        </div>
    </div>
</ui:fragment>