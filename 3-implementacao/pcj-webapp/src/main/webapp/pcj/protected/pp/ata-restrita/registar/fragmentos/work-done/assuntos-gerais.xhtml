<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">

    <ui:param name="ordenacao" value="#{bean.obterTrabalhoGuardadoDTO(idBlock)}"/>
    <ui:param name="dto" value="#{ordenacao.blocoGeralRestrita}"/>

    <div class="accordion basic">
        <p:accordionPanel multiple="true"
                          dynamic="true"
                          varStatus="myIndex" id="pcjAtaBlocoAssuntosGerais_#{idBlock}"
                          widgetVar="#{accordionWidgetId}"
                          styleClass="accordion-container" activeIndex="-1">
            <p:tab titleStyleClass="accordion-label">
                <f:facet name="title" styleClass="accordion-body">
                    <h:panelGroup layout="block" styleClass="accordion-body">
                        <div class="accordion-body-description">
                            <div class="accordion-body-description-left">
                                <span class="accordion-body-description-left-title">Assuntos gerais</span>
                            </div>
                        </div>
                    </h:panelGroup>
                </f:facet>

                <div class="row-fluid mb-3">
                    <div class="span10">
                        <p:outputLabel for="pcjAtaVisualizacaoAssuntosGeraisTextoIndrotorio"
                                       value="Texto introdutório"/>
                        <h:outputText id="pcjAtaVisualizacaoAssuntosGeraisTextoIndrotorio"
                                      value="#{dto.textoIntrodutorio}"/>
                    </div>
                </div>

                <div class="row-fluid mb-3">
                    <div class="span10">
                        <p:outputLabel for="pcjAtaVisualizacaoAssuntosGeraisAssunto"
                                       value="Assunto"/>
                        <h:outputText id="pcjAtaVisualizacaoAssuntosGeraisAssunto"
                                      value="#{dto.assunto}"/>
                    </div>
                </div>

                <div class="row-fluid mb-5">
                    <div class="table-list select-check-box-2 sm">
                        <p:dataTable id="pcjAtaVisualizacaoAssuntosGeraisTableCheckBox"
                                     var="p" rowStyleClass="resultado"
                                     value="#{dto.documentoPCJ}"
                                     widgetVar="pcjAtaVisualizacaoAssuntosGeraisTableCheckboxWidgetVar"
                                     draggableColumns="false"
                                     paginator="false" emptyMessage="Nenhum documento encontrado."
                                     rowIndexVar="rowIndexVar">

                            <p:column headerText="Nome do documento" styleClass="texto">
                                <h:outputText value="#{p.nomeDocumento}"/>
                            </p:column>
                            <p:column headerText="Data do documento" styleClass="texto">
                                <h:outputText value="#{bean.retornarDataFormatada(p.dataUpload)}"/>
                            </p:column>
                            <p:column id="datatableAtaActionsColumn" headerText="Ações" styleClass="acoes">
                                <p:commandLink id="consultarDocumentoBotao" value="Consultar"
                                               immediate="true" process="@this"
                                               actionListener="#{bean.consultarDocumento(p.identificadorFicheiro)}"
                                               onstart="PF('frawPageBlocker').show()"
                                               oncomplete="abrirEmNovaAba(xhr, status, args);PF('frawPageBlocker').hide()"/>
                            </p:column>
                        </p:dataTable>
                    </div>
                </div>

                <div class="row-fluid mb-3">
                    <div class="span10">
                        <p:outputLabel for="pcjAtaVisualizacaoAssuntosGeraisObservacao"
                                       value="Observações"/>
                        <h:outputText id="pcjAtaVisualizacaoAssuntosGeraisObservacao"
                                      value="#{dto.observacao}"/>
                    </div>
                </div>

                <div class="row-fluid mt-5">
                    <div class="span12 text-right">
                        <p:commandButton value="Remover" onclick="PF('dlg3').show();"
                                         styleClass="ui-priority-secondary" icon="fa fa-trash" process="@this">
                            <f:setPropertyActionListener target="#{bean.idBlockRemove}" value="#{idBlock}" />
                        </p:commandButton>
                        <p:commandButton value="Alterar"
                                         action="#{bean.setarTrabalhoAlterar(ordenacao)}"
                                         icon="fa fa-edit"
                                         process="@this"
                                         update="@this pcjRegistarAtaForm:pcjAtaTabView:pcjAtaBlocoAssuntosGerais_#{idBlock}
                                                 pcjRegistarAtaForm:pcjAtaTabView:pcjBlocoTrabalhoPanel pcjRegistarAtaForm:pcjAtaTabView:pcjPanelWork"
                                         onstart="PF('frawPageBlocker').show()"
                                         onerror="PF('frawPageBlocker').hide()"
                                         oncomplete="PF('frawPageBlocker').hide();window.scrollTo(0, 0);"/>

                    </div>
                </div>

            </p:tab>
        </p:accordionPanel>
    </div>

</ui:fragment>