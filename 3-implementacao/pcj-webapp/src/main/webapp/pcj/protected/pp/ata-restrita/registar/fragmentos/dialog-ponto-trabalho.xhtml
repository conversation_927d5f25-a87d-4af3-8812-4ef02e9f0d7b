<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:o="http://omnifaces.org/ui">

    <o:importConstants type="pt.segsocial.pcj.ppp.enums.BlocosTrabalhoAtaRestritaEnum" var="BlocosTrabalhoAtaRestritaEnum" />

    <p:dialog header="Adicionar trabalho" widgetVar="addTrab" modal="true" resizable="false" draggable="false"
              style="width:528px;">
        <p:outputPanel id="pcjModalSelectTipoTrabalho">
            <div class="row-fluid">
                <div class="dialog-content">
                    <p:outputPanel id="pcjInfoMessagesAvisoRegAtaPanel">
                        <p:outputPanel id="pcjInfoMessagesAvisoRegAta" rendered="#{bean.mostarMensagemPendencia()}">
                            <div class="area-mensagens">
                                <div class="area-info">
                                    <div class="aviso" role="status" aria-live="polite">
                                        <p>#{bean.getAlertMessage()}</p>
                                    </div>
                                </div>
                            </div>
                        </p:outputPanel>
                    </p:outputPanel>
                    <div class="row-fluid mb-3">
                        <p> Adicione pontos de trabalho para ficarem registados em ata.</p>
                    </div>
                    <div class="row-fluid mb-5">
                        <p:outputPanel id="pcjSelectTipoTrabalho">
                            <p:outputLabel value="Trabalhos"/>
                            <p:selectOneRadio id="pcjAddPontoTrabRadio1" value="#{bean.nomeBloco}"
                                              layout="grid" columns="1" styleClass="select-radio-2 sm">
                                <f:selectItem itemLabel="Comunicação" itemValue="#{BlocosTrabalhoAtaRestritaEnum.COMUNICACAO}"/>
                                <f:selectItem itemLabel="Assuntos gerais" itemValue="#{BlocosTrabalhoAtaRestritaEnum.ASSUNTOS_GERAIS}"/>
                                <p:ajax event="change"
                                        update="@this pontosComunicacaoPanel"
                                        onstart="PF('frawPageBlocker').show()"
                                        onerror="PF('frawPageBlocker').hide()"
                                        onsuccess="PF('frawPageBlocker').hide()"
                                        process="@this"/>
                            </p:selectOneRadio>
                        </p:outputPanel>
                    </div>
                    <div class="row-fluid mb-5">
                        <p:outputPanel id="pontosComunicacaoPanel">
                            <p:outputPanel id="pontosComunicacao" rendered="#{bean.nomeBloco eq BlocosTrabalhoAtaRestritaEnum.COMUNICACAO}">
                                <p:outputLabel value="Ponto de trabalho"/>
                                <p:selectOneMenu id="pcjPontoTrabPendenteSelector"
                                                 value="#{bean.blocoComunicacaoRestrita}" style="width:561px;"
                                                 converter="omnifaces.SelectItemsConverter">
                                    <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue="" noSelectionOption="true"/>
                                    <f:selectItems value="#{bean.listaComunicacoes}"
                                                   var="item"
                                                   itemLabel="#{item.nomeComunicacao}"
                                                   itemValue="#{item.idComunicacao}"
                                                   required="true"/>
                                </p:selectOneMenu>
                            </p:outputPanel>
                        </p:outputPanel>

                    </div>
                </div>
            </div>

        </p:outputPanel>
        <f:facet name="footer">
            <p:commandButton id="cancelStateBtn" value="Cancelar"
                             styleClass="ui-priority-secondary"
                             process="@this"
                             onstart="PF('frawPageBlocker').show()"
                             onerror="PF('frawPageBlocker').hide()"
                             onsuccess="PF('frawPageBlocker').hide()"
                             oncomplete="PF('addTrab').hide(); window.scrollTo(0, 0); if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]); }"/>

            <p:commandButton id="confirmStateBtn" value="Adicionar trabalho"
                             styleClass="ui-confirmdialog-yes"
                             actionListener="#{bean.adicionarTrabalhoBtn()}"
                             process="@this"
                             update="@form pontosComunicacaoPanel pcjRegistarAtaForm:pcjAtaTabView:pcjBlocoTrabalhoPanel pcjRegistarAtaForm:pcjAtaTabView:pcjPanelWork"
                             onstart="PF('frawPageBlocker').show()"
                             onerror="PF('frawPageBlocker').hide()"
                             onsuccess="PF('frawPageBlocker').hide()"
                             oncomplete="PF('addTrab').hide(); window.scrollTo(0, 0); if (PF('tabViewWv').getActiveIndex() === 2) { fecharTodosAccordions([#{bean.widgetVarsAsJSArray}]); }"/>
        </f:facet>
    </p:dialog>
</ui:fragment>