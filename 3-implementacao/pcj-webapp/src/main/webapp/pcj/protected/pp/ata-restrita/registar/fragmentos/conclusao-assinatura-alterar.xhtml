<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns="http://www.w3.org/1999/xhtml">

    <div class="table-list select-check-box-2 sm">
        <p:dataTable id="pcjAtaConclusaoTableCheckBox"
                     var="p"
                     selection="#{bean.ataRestritaDTO.listaAssinaturaSelected}"
                     value="#{ata.listaConclusaoDTO}"
                     rowStyleClass="resultado"
                     widgetVar="pcjAtaConclusaoDataTableCheckboxWidgetVar"
                     rows="10"
                     rowKey="#{p.id}" draggableColumns="false"
                     paginator="true" emptyMessage="Nenhum elemento encontrado."
                     rowsPerPageTemplate="#{bean.rowsPerPageTemplate}"
                     paginatorPosition="#{bean.paginatorPosition}"
                     paginatorTemplate="{PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="Página {currentPage}"
                     ariaRowLabel="Linha #{rowIndexVar + 1}"
                     rowIndexVar="rowIndexVar">

            <p:ajax event="page" onstart="PF('frawPageBlocker').show()"
                    oncomplete="PF('frawPageBlocker').hide();"/>

            <f:facet name="caption">Tabela selecionável</f:facet>

            <p:column id="pcjAtaConclusaoAssinaturaSelect" selectionMode="multiple"/>

            <p:column headerText="Nome" styleClass="texto">
                <abbr title="#{p.nome}">
                    <h:outputText value="#{p.nome}">
                        <i class="tooltip fa fa-fw fa-info-circle"/>
                        <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                    </h:outputText>
                </abbr>
            </p:column>
            <p:column headerText="Cargo" styleClass="texto">
                <abbr title="#{p.cargo}">
                    <h:outputText value="#{p.cargo}">
                        <i class="tooltip fa fa-fw fa-info-circle"/>
                        <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                    </h:outputText>
                </abbr>
            </p:column>
            <p:column headerText="Entidade representada" styleClass="texto">
                <abbr title="#{p.entidade}">
                    <h:outputText value="#{p.entidade}">
                        <i class="tooltip fa fa-fw fa-info-circle"/>
                        <f:converter converterId="pcjAbreviaturaNomeConverter"/>
                    </h:outputText>
                </abbr>
            </p:column>
        </p:dataTable>
    </div>

</ui:fragment>