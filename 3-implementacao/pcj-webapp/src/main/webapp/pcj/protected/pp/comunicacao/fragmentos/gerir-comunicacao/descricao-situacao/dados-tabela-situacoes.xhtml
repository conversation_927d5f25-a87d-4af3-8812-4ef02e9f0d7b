<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui"
             xmlns="http://www.w3.org/1999/xhtml">

    <h:panelGroup id="tabelaSituacoes">

        <p:outputPanel id="tableSituacoesInterno" rendered="#{not empty descricaoSituacaoStep.situacoes}">
            <div class="row-fluid mb-5">
                <div class="table-list">
                    <p:dataTable
                            id="dataTableSituacoes"
                            var="situacao"
                            value="#{descricaoSituacaoStep.situacoes}"
                            rowKey="#{situacao.id}"
                            rows="10"
                            resizableColumns="true"
                            draggableColumns="false"
                            paginator="true"
                            rowsPerPageTemplate="10,25,50"
                            paginatorPosition="bottom"
                            paginatorTemplate="{PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}"
                            currentPageReportTemplate="Página {currentPage}"
                            rowIndexVar="rowIndexVar"
                            rowStyleClass="resultado"
                            style="overflow: hidden;">

                        <f:facet name="caption">#{pcjMessages['pp.comunicacao.crianca.situacao.facet.situacaes']}
                        </f:facet>

                        <p:column>
                            <p:rowToggler/>
                        </p:column>

                        <p:column sortBy="#{situacao.codigoSituacaoSinalizada}" styleClass="texto"
                                  id="situacaoSinalizada">
                            <f:facet name="header">
                                <h:outputText
                                        value="#{pcjMessages['pp.comunicacao.crianca.table.label.situacao-sinalizada']}"/>
                            </f:facet>

                            <h:outputText
                                    value="#{descricaoSituacaoStep.getValorDominioSituacaoRealizada(situacao.codigoSituacaoSinalizada)}"/>
                        </p:column>

                        <p:column sortBy="#{situacao.codigoSituacaoAtribuida}" styleClass="texto" id="atribuida">
                            <f:facet name="header">
                                <h:outputText value="#{pcjMessages['pp.comunicacao.crianca.table.label.atribuida']}"/>
                            </f:facet>

                            <h:outputText
                                    value="#{utilBean.getValorDominio('ATRIBUIDOA', situacao.codigoSituacaoAtribuida)}"/>
                        </p:column>

                        <p:column sortBy="#{situacao.intervencaoUrgente}" styleClass="texto"
                                  id="intervencaoUrgente">
                            <f:facet name="header">
                                <h:outputText
                                        value="#{pcjMessages['pp.comunicacao.label.comunicacao-urgente']}"/>
                            </f:facet>

                            <h:outputText
                                    value="#{descricaoSituacaoStep.getIntervencaoUrgente(situacao.intervencaoUrgente)}"/>
                        </p:column>

                        <p:column id="menuAcoes" styleClass="acoes" headerText="Ações">
                            <p:commandButton id="acoes" icon="fa fa-ellipsis-v" type="button"/>
                            <p:overlayPanel id="acoesList" for="acoes" hideEffect="fade" my="right top"
                                            at="right bottom">
                                <p:outputPanel id="carDetail">
                                    <ul>
                                        <li>
                                            <p:commandLink id="alterar-situacao"
                                                           value="Alterar situação"
                                                           process="@this"
                                                           immediate="true"
                                                           update="#{bean.idPainelDescricaoSituacao}"
                                                           onstart="PF('frawPageBlocker').show()"
                                                           oncomplete="PF('frawPageBlocker').hide()"
                                                           onsuccess="PF('frawPageBlocker').hide()"
                                                           actionListener="#{descricaoSituacaoStep.alterarSituacao(rowIndexVar)}">
                                            </p:commandLink>
                                        </li>
                                        <li>
                                            <p:commandLink id="remover-situacao"
                                                           value="Eliminar situação"
                                                           process="@this"
                                                           immediate="true"
                                                           update="#{bean.idPainelDescricaoSituacao}"
                                                           onstart="PF('frawPageBlocker').show()"
                                                           oncomplete="PF('frawPageBlocker').hide()"
                                                           onsuccess="PF('frawPageBlocker').hide()"
                                                           actionListener="#{descricaoSituacaoStep.eliminarSituacao(rowIndexVar)}">
                                            </p:commandLink>
                                        </li>
                                    </ul>
                                </p:outputPanel>
                            </p:overlayPanel>
                        </p:column>

                        <p:rowExpansion>
                            <h:panelGroup layout="block">
                                <p:outputPanel rendered="#{descricaoSituacaoStep.exibirMoradaSituacao(situacao)}">
                                    <div class="row-fluid mb-5">
                                        <div class="span12">
                                            <h:outputText class="bold"
                                                          value="#{pcjMessages['pp.comunicacao.label.comunicacao-morada-situacao-perigo']}"/>
                                            <div class="descricao-factos-container">
                                                <h:outputText value="#{situacao.morada}"
                                                              style="white-space: pre-wrap;"/>
                                            </div>
                                        </div>
                                    </div>
                                </p:outputPanel>
                                <div class="row-fluid mb-5">
                                    <div class="span12">
                                        <h:outputText class="bold"
                                                      value="#{pcjMessages['pp.comunicacao.crianca.table.label.situacao-descricao-factos']}"/>
                                        <div class="descricao-factos-container">
                                            <h:outputText
                                                    value="#{empty situacao.descricaoFactos ? '-' : situacao.descricaoFactos}"
                                                    style="white-space: pre-wrap;"/>
                                        </div>
                                    </div>
                                </div>
                            </h:panelGroup>
                        </p:rowExpansion>

                    </p:dataTable>
                </div>
            </div>
        </p:outputPanel>
    </h:panelGroup>
</ui:fragment>