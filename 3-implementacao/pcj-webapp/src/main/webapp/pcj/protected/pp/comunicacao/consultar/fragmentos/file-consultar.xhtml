<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui">

    <div id="fileUploadMultiple" class="row-fluid">
        <div class="span12 ui-fileupload-border">
            <div class="fileUpload-card-name">
                <span class="description">#{pcjMessages['pp.comunicacao.crianca.table.label.documento-comunicacao']}</span>
            </div>
            <hr/>

            <p:fragment id="singleFileTable" rendered="#{participante.documentosConsultaComunicacao.size() gt 0}">
                <ui:repeat value="#{participante.documentosConsultaComunicacao}" var="item" varStatus="myVarStatus">
                    <div class="fileUpload-card">
                        <div class="fileUpload-card--info">
                            <div class="fileUpload-card--name-area">
                                <p:fragment>
                                    <i class="fa fa-#{bean.fileUploadBean.iconHandler(item.uploadedFile.getContentType())}"/>
                                </p:fragment>

                                <div class="fileUpload-card--name">
                                    <p:fragment> #{item.uploadedFile.getFileName()}</p:fragment>
                                    <p class="fileUpload-card--sub-name">
                                        #{bean.fileUploadBean.convertbytesToMB(item.uploadedFile.getSize())}
                                    </p>
                                </div>
                            </div>

                            <div class="fileUpload-card--link-group">
                                <div class="fileUpload-card--link-left">
                                    <p:commandButton id="showFile"
                                                     value="#{pcjMessages['label.consultar']}"
                                                     process="@this"
                                                     immediate="true"
                                                     onstart="PF('frawPageBlocker').show()"
                                                     oncomplete="PF('frawPageBlocker').hide();"
                                                     ajax="false"
                                                     styleClass="ui-priority-terciary"
                                                     action="#{bean.fileUploadBean.verDocumentoMultiple(item.uploadedFile)}"
                                                     onclick="this.form.target = '_blank'"/>

                                </div>
                            </div>

                        </div>
                    </div>

                </ui:repeat>
            </p:fragment>
        </div>
    </div>

</ui:fragment>