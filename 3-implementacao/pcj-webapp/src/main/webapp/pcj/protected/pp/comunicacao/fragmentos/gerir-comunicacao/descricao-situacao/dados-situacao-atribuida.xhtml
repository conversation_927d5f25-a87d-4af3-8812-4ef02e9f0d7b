<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <div class="row-fluid mb-5">
        <div class="span6" style="margin-right: 20px;">
            <p:outputLabel for="pcjInputSituacaoAtribuida" value="#{pcjMessages['pp.comunicacao.crianca.table.label.situacao-atribuida']}"/>
            <p:selectOneMenu id="pcjInputSituacaoAtribuida"
                             styleClass="advanced-dropdown"
                             value="#{descricaoSituacaoStep.situacaoPerigo.codigoSituacaoAtribuida}"
                             effect="fade" var="t"
                             filter="true" filterMatchMode="contains"
                             converter="omnifaces.SelectItemsConverter"
                             required="#{!descricaoSituacaoStep.ignorarValidacaoSituacao}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue="" noSelectionOption="true"/>
                <f:selectItems value="#{descricaoSituacaoStep.situacoesAtribuidas}" var="m" itemLabel="#{m.designacao}" itemValue="#{m.codigoDetalheDominio}" />

                <p:ajax event="change"
                        onstart="PF('frawPageBlocker').show();"
                        oncomplete="PF('frawPageBlocker').hide();"
                        onerror="PF('frawPageBlocker').hide();"
                        process="@this" update="@this pcjPanelSituacaoAtribuidaOutro"/>

            </p:selectOneMenu>
            <p:message for="pcjInputSituacaoAtribuida"/>
            <div id="selectMenuSituacaoAtribuidaErrorMessage" aria-live="polite" class="ui-message ui-message-error ui-widget ui-corner-all" style="display: none;">
                <span class="ui-message-error-icon"/>
                <span class="ui-message-error-detail">Campo obrigatório</span>
            </div>
        </div>
        <p:outputPanel id="pcjPanelSituacaoAtribuidaOutro" layout="block">
            <p:outputPanel id="pcjPanelSituacaoAtribuidaOutroInterno" rendered="#{descricaoSituacaoStep.situacaoPerigo.codigoSituacaoAtribuida eq '0378'}">
                <!-- Outra Situação Atribuída -->
                <div class="span6">
                    <p:outputLabel for="pcjInputSituacaoAtribuidaOutro" value="#{pcjMessages['pp.comunicacao.crianca.table.label.situacao-atribuida-outro']}"/>
                    <p:inputText id="pcjInputSituacaoAtribuidaOutro"
                                 value="#{descricaoSituacaoStep.situacaoPerigo.outraSituacaoAtribuida}"
                                 maxlength="150"
                                 styleClass="max-field-size"
                                 required="#{descricaoSituacaoStep.situacaoPerigo.codigoSituacaoAtribuida eq '0378'}"
                                 requiredMessage="#{pcjComponentMessage['obrigatorio']}"/>
                    <p:message for="pcjInputSituacaoAtribuidaOutro"/>
                    <div id="selectMenuSituacaoAtribuidaOutroErrorMessage" aria-live="polite" class="ui-message ui-message-error ui-widget ui-corner-all" style="display: none;">
                        <span class="ui-message-error-icon"/>
                        <span class="ui-message-error-detail">Campo obrigatório</span>
                    </div>
                </div>
            </p:outputPanel>
        </p:outputPanel>
    </div>

</ui:fragment>