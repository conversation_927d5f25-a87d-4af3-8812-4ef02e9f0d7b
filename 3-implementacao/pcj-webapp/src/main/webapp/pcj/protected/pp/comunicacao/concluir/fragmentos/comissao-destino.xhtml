<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <div class="row-fluid mb-m">
        <div class="span12">
            <p:outputLabel for="pcjDropdownComissaoDestinoSelector" value="#{pcjMessages['pp.comunicacao.label.comunicacao-comissao-destino']}" />
            <p:selectOneMenu id="pcjDropdownComissaoDestinoSelector" styleClass="advanced-dropdown"
                             value="#{comunicacao.comissao}"
                             effect="fade" var="t"
                             filter="true" filterMatchMode="contains"
                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                <f:selectItem noSelectionOption="true" itemLabel="Selecione uma opção"/>
                <f:selectItems value="#{bean.comissoes}"
                               var="item" itemLabel="#{item.nome}" itemValue="#{item.id}"/>

                <p:column>
                    <h:outputText id="displayAdvanced" value="#{t.nome}" />
                </p:column>
            </p:selectOneMenu>
            <p:message id="pcjFormDestinoSelectorMessage" display="text" for="pcjDropdownComissaoDestinoSelector" />
        </div>
    </div>
</ui:fragment>