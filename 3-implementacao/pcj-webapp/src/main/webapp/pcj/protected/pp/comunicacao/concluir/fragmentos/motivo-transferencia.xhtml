<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <div class="row-fluid mb-m">
<!--        <div class="span12">-->
<!--            <p:outputLabel for="pcjShowcaseTransferirOutraSelector"-->
<!--                           value="#{pcjMessages['pp.comunicacao.label.comunicacao-motivo-tranferencia']}"/>-->
<!--            <p:selectOneMenu id="pcjShowcaseTransferirOutraSelector"-->
<!--                             value="#{concluirComunicacao.codigoEstadoAnalise}"-->
<!--                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">-->
<!--                <f:selectItem noSelectionOption="true" itemLabel="Selecione uma opção"/>-->
<!--                <f:selectItems value="#{bean.motivosTranferencia}"-->
<!--                               var="item" itemLabel="#{item.designacao}"-->
<!--                               itemValue="#{item.codigoDetalheDominio}"/>-->

<!--            </p:selectOneMenu>-->
<!--            <p:message id="pcjComunicacaoTranferirSelectorMessage" display="text"-->
<!--                       for="pcjShowcaseTransferirOutraSelector"/>-->

<!--        </div>-->
        <div class="span6">
            <p:outputLabel for="pcjShowcaseTransferirOutraSelector"
                           value="#{pcjMessages['pp.comunicacao.label.comunicacao-motivo-tranferencia']}"/>
            <p:selectOneRadio id="pcjConcluirMotivoTranfRB"
                              value="#{bean.cpcjdto.detalheInfraestruturaDTO.codAreaTrabalho}"
                              styleClass="select-radio-2 sm" layout="grid" columns="1"
                              selected="true">
                <f:selectItems value="#{bean.motivosTranferencia}"
                               var="item"
                               itemValue="#{item.codigoDetalheDominio}"
                               itemLabel="#{item.designacao}"/>
                <p:ajax event="change" process="@this"/>
            </p:selectOneRadio>
        </div>
    </div>
</ui:fragment>