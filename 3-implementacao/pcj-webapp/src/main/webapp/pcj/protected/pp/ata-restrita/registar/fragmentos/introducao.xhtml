<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns:ii="http://java.sun.com/jsf/composite/iies"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Cabeçalho</h2>
    </div>
    <div class="row-fluid mb-5">
        <div class="span3">
            <p:outputLabel for="pcjAtaIntroducaoTitulo" value="Tipo de ata"/>
            <h:outputText id="pcjAtaIntroducaoTitulo" escape="false"
                          value="#{bean.tituloAta}"/>
        </div>
        <div class="span2">
            <p:outputLabel for="pcjAtaIntroducaoAtaNumero" value="Ata número"/>
            <h:outputText id="pcjAtaIntroducaoAtaNumero" escape="false"
                          value="#{ata.numeroAtaRestrita}"/>
        </div>
        <div class="span2">
            <p:outputPanel id="pcjMensagemDataSuperiorAtuallPnl">
                <p:outputLabel for="pcjAtaIntroducaoData" value="Data*"/>
                <ii:calendar id="pcjAtaIntroducaoData"
                             required="true"
                             value="#{ata.dataRealizacao}"
                             maxDate="{LocalDate.now()}"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             converterMessage="#{pcjComponentMessage['data.invalida']}">
                    <p:ajax process="@this" event="change"
                            update="@this pcjRegistarAtaForm:pcjAtaTabView:pcjMensagemDataSuperiorAtuallPnl"/>
                    <f:validator for="calendar"  validatorId="pcjDataPosteriorRestritaValidator" />
                </ii:calendar>
                <p:message for="pcjAtaIntroducaoData:calendar"/>
            </p:outputPanel>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Introdução</h2>
    </div>

    <div class="row-fluid mb-5">
        <div class="span12">
            <p:outputLabel for="pcjAtaTextoIntrodutorio" value="Texto introdutório"/>
            <p:inputTextarea id="pcjAtaTextoIntrodutorio"
                             value="#{ata.textoIntrodutorio}"
                             maxlength="4000"
                             rows="15"
                             styleClass="max-field-size"
                             counter="displayTextoIntrodutorio"
                             required="true"
                             requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             counterTemplate="{0} caracteres restante.">
                <p:ajax event="change"/>
            </p:inputTextarea>
            <h:outputText id="displayTextoIntrodutorio"/>
            <p:message id="pcjAtaTextoIntrodutorioMsg" for="pcjAtaTextoIntrodutorio"/>
        </div>
    </div>

    <div class="row-fluid mb-5">
        <h2 class="mb-3">Pontos de trabalho</h2>
    </div>

    <div class="table-list">
        <p:dataTable id="pcjAtaTableIntroducao" var="trabalho" lazy="true"
                     widgetVar="pcjAtaDataTableActionsWidgetVar"
                     value="#{bean.trabalhosDTO}"
                     draggableColumns="false" paginator="true"
                     emptyMessage="Nenhum ponto de trabalho encontrado."
                     rows="10"
                     rowsPerPageTemplate="#{bean.rowsPerPageTemplate}"
                     paginatorPosition="#{bean.paginatorPosition}"
                     paginatorTemplate="{PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="Página {currentPage}"
                     ariaRowLabel="Linha #{rowIndexVar + 1}"
                     rowIndexVar="rowIndexVar">

            <p:ajax event="page" onstart="PF('frawPageBlocker').show()"
                    oncomplete="PF('frawPageBlocker').hide();"/>

            <p:column id="datatableAtaPontoTrabalho" headerText="Ponto Trabalho" styleClass="texto">
                <h:outputText value="#{trabalho.assunto}"/>
            </p:column>

            <p:column id="datatableAtaTipo" headerText="Estado" styleClass="texto">
                <h:outputText value="#{trabalho.tipo}"/>
            </p:column>
        </p:dataTable>
    </div>

</ui:fragment>