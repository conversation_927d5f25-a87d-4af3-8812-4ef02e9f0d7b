<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core">

    <p:dialog header="Eliminar comunicação" widgetVar="removerComunicacao" modal="true" resizable="false" draggable="false" >
        <div class="dialog-content">
            <h:outputText value="Deseja eliminar a comunicação nº#{comunicacao.numeroComunicacao}? Esta ação é irreversível." />
        </div>

        <f:facet name="footer">
            <p:commandButton id="cancelStateBtnRemove"
                             value="#{pcjMessages['botao.cancelar']}"
                             styleClass="ui-priority-secondary "
                             oncomplete="PF('removerComunicacao').hide()"/>
            <p:commandButton id="pcjEliminarComunicacaoBtn"
                             value="Eliminar comunicação" styleClass="destroy ui-confirmdialog-yes"
                             action="#{bean.removerComunicacao()}"
                             process="@this"
                             update="@this"
                             onstart="PF('frawPageBlocker').show(); PF('removerComunicacao').show()"
                             onerror="PF('frawPageBlocker').hide()"
                             oncomplete="PF('frawPageBlocker').hide();PF('removerComunicacao').hide();window.scrollTo(0, 0);"/>
        </f:facet>

    </p:dialog>
</ui:fragment>