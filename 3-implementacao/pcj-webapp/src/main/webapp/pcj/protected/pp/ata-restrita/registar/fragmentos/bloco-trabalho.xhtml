<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns="http://www.w3.org/1999/xhtml">

    <p:outputPanel id="pcjBlocoTrabalhoPanel">
        <div class="row-fluid mb-5">
            <h2 class="mb-3">Trabalho em execução</h2>
        </div>
        <h:panelGroup id="pcjPanelGroupWork">
            <p:outputPanel rendered="#{bean.exibeBotaoAddTrabalho}" id="pcjPanelWorkNew">
                <div class="row-fluid text-center">
                    <p class="text-center">Não existem nenhum trabalho em execução.</p>
                    <p:commandButton id="pcjAtaBlocoTrabalhoAdicionarTrabalhoBtn" value="Adicionar trabalho"
                                     styleClass="mr-4 mt-3"
                                     actionListener="#{bean.limparBlocoTrabalho()}"
                                     onstart="PF('frawPageBlocker').show()"
                                     onerror="PF('frawPageBlocker').hide()"
                                     onsuccess="PF('frawPageBlocker').hide()"
                                     oncomplete="PF('addTrab').show();"
                                     process="@this"
                                     update="pcjRegistarAtaForm:pcjAtaTabView:pcjPanelGroupWork pcjRegistarAtaForm:pcjModalSelectTipoTrabalho"/>
                </div>
            </p:outputPanel>
            <p:outputPanel id="pcjPanelWork">
                <p:outputPanel rendered="#{!bean.exibeBotaoAddTrabalho}" id="pcjInternalPanelWork">
                    <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/blocos.xhtml"/>
                    <ui:include src="/pcj/protected/pp/ata-restrita/registar/work-blocks/botoes-ponto-trabalho.xhtml"/>
                </p:outputPanel>
            </p:outputPanel>
        </h:panelGroup>

        <div class="row-fluid mb-5 mt-6">
            <ui:include src="/pcj/protected/pp/ata-restrita/registar/fragmentos/trabalhos-guardados.xhtml"/>
        </div>

    </p:outputPanel>
</ui:fragment>