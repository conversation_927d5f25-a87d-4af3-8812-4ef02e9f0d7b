<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:c="http://java.sun.com/jsp/jstl/core"
             xmlns:p="http://primefaces.org/ui">
    <script type="text/javascript">
		function onCompleteRowExpansionHideAcoes(row) {
			hideAcoes(row);
		}

		function hideAcoes(row) {
			const acoes = $('.file-upload-acoes');
			$(acoes).hide();
		}

		function onClickCancelarEsconderRow(grupo) {
			PF('frawPageBlocker').show();
			setTimeout( function() {
				PF(grupo).collapseAllRows();
				PF('frawPageBlocker').hide();
			}, 500)
		}

    </script>

    <c:set var="modelDocumento" value="#{pcjProcessoPaeBean.stepDocumento}"/>

    <div class="row-fluid mb-5">
        <div>
            <h2>Novos documentos</h2>
        </div>
    </div>

    <h:panelGroup layout="block" styleClass="row-fluid" id="pcjPanelDocumentos">
        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcjLabelDocumentos"
                               for="pcjDocumentosRadio"
                               value="Deseja substituir algum documento?" styleClass="dica mr-6">
                </p:outputLabel>

                <p:selectOneRadio id="pcjDocumentosRadio"
                                  value="#{stepDocumento.alteracaoDocumento}"
                                  styleClass="select-radio-2 sm">
                    <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                    <f:selectItem itemLabel="Não" itemValue="#{false}"/>

                    <p:ajax event="change"
                            process="@this"
                            listener="#{pcjConsultarProcessoBean.limparDocumentosNaoUsados()}"
                            update="pcjPanelDocumentos"
                            onstart="PF('frawPageBlocker').show();"
                            oncomplete="PF('frawPageBlocker').hide();"
                    />
                </p:selectOneRadio>
            </div>
        </div>

        <div class="row-fluid mb-5">
            <h:panelGroup id="pcjDocumentoAlteracaoPanel" rendered="#{stepDocumento.mostrarDocumentoAlteracao()}">
                <div class="table-list select-check-box-2 sm">
                    <p:dataTable id="dataTableAlteracao"
                                 var="documento"
                                 value="#{stepDocumento.mapaDocumentosAlteracao}"
                                 reflow="true"
                                 rowIndexVar="rowId"
                                 widgetVar="pcjFileUploadTableGrupoAlteracao">

                        <f:facet name="caption">Documentos de apoio ao processo de alteração de participação.</f:facet>

                        <p:column headerText="Tipo de documento" styleClass="texto">
                            <h:outputText value="#{documento.tipoDocumento.descricao}"/>

                            <ui:fragment>
                                <h:outputText styleClass="dica" id="pcjToolTipTipoDocumentoAlteracao" escape="false"/>
                                <p:tooltip for="pcjToolTipTipoDocumentoAlteracao" value="#{documento.tipoDocumento.msgInfo}"/>
                            </ui:fragment>
                        </p:column>

                        <p:column headerText="Estado" styleClass="texto">
                            <h:outputText value="#{documento.estado.descricao}"/>
                        </p:column>

                        <p:column id="menuAcoesAlteracao" styleClass="acoes" headerText="Ações">
                            <p:commandButton id="acoesAlteracao" icon="fa fa-ellipsis-v" type="button"/>
                            <p:overlayPanel for="acoesAlteracao" hideEffect="fade" my="right top"
                                            at="right bottom"
                                            styleClass="file-upload-acoes">
                                <p:outputPanel id="carDetailAlteracao">
                                    <ul>
                                        <li>
                                            <h:panelGroup rendered="#{stepDocumento.isUploadRendered(documento.estado)}">
                                                <p:commandLink id="upload-documento-alteracao"
                                                               value="Entregar documento"
                                                               action="#{stepDocumento.setTipoSelecionado(documento.tipoDocumento)}"
                                                               process="@this"
                                                               onstart="PF('frawPageBlocker').show();"
                                                               onclick="PF('pcjFileUploadTableGrupoAlteracao').toggleRowExpansion(#{rowId});"
                                                               oncomplete="PF('frawPageBlocker').hide(); onCompleteRowExpansionHideAcoes(#{rowId});">
                                                </p:commandLink>
                                            </h:panelGroup>
                                        </li>
                                        <li>
                                            <h:panelGroup rendered="#{stepDocumento.isDownloadRendered(documento.estado)}">
                                                <p:commandLink id="preview-documento-alteracao"
                                                               value="Ver documento"
                                                               action="#{stepDocumento.preview(documento.idFicheiro)}"
                                                               ajax="false" target="_blank">
                                                </p:commandLink>
                                            </h:panelGroup>
                                        </li>

                                        <li>
                                            <h:panelGroup rendered="#{stepDocumento.isReplaceRendered(documento.estado)}">
                                                <p:commandLink id="change-documento-alteracao"
                                                               value="Substituir documento" immediate="true"
                                                               action="#{stepDocumento.setTipoSelecionado(documento.tipoDocumento)}"
                                                               process="@this" update="@this"
                                                               onerror="PF('dialog-upload-replace').hide()"
                                                               onstart="PF('frawPageBlocker').show()"
                                                               onclick="PF('pcjFileUploadTableGrupoAlteracao').toggleRowExpansion(#{rowId});"
                                                               oncomplete="PF('frawPageBlocker').hide(); onCompleteRowExpansionHideAcoes(#{rowId})">
                                                </p:commandLink>
                                            </h:panelGroup>
                                        </li>
                                    </ul>
                                </p:outputPanel>
                            </p:overlayPanel>
                        </p:column>

                        <p:rowExpansion styleClass="file-upload-row">
                            <p:fileUpload id="fileUploaderAlteracao"
                                          widgetVar="#{documento.tipoDocumento}"
                                          fileUploadListener="#{pcjConsultarProcessoBean.upload}"
                                          sizeLimit="3000000" mode="advanced" dragDropSupport="true"
                                          allowTypes="/(\.|\/)(jpg|jpeg|png|pdf)$/" auto="true"
                                          accept=".jpg, .jpeg, .png, .pdf" label="Selecionar ficheiro"
                                          cancelLabel="Cancelar" uploadLabel="Carregar"
                                          invalidFileMessage="Formato Inválido. Formato suportado: .jpg, .jpeg, .png, .pdf"
                                          invalidSizeMessage="O ficheiro é demasiado grande. Deve ter no máximo 3 MB."
                                          onstart="PF('frawPageBlocker').show()"
                                          oncomplete="PF('frawPageBlocker').hide();"
                                          fileLimitMessage="Só pode carregar um ficheiro"
                                          update="dataTableAlteracao" multiple="false"/>

                            <div class="file-upload-row-footer" style="display: flex; justify-content: space-between;">
                                <h:outputLabel value="Tamanho máximo: 3 Mb | Formato suportado: .jpg, .jpeg, .png, .pdf"
                                               style="font-weight: normal; font-size: 1.3rem;"/>
                                <p:commandButton value="Cancelar" style="margin-bottom: 1.5rem;"
                                                 styleClass="ui-priority-secondary"
                                                 type="button"
                                                 onclick="onClickCancelarEsconderRow('pcjFileUploadTableGrupoAlteracao')"/>
                            </div>
                        </p:rowExpansion>
                        <p:rowExpansion styleClass="file-upload-row">
                            <p:fileUpload id="fileUploaderAlteracaoReplace"
                                          widgetVar="#{documento.tipoDocumento}"
                                          fileUploadListener="#{pcjConsultarProcessoBean.replace}"
                                          sizeLimit="3000000" mode="advanced" dragDropSupport="true"
                                          allowTypes="/(\.|\/)(jpg|jpeg|png|pdf)$/" auto="true"
                                          accept=".jpg, .jpeg, .png, .pdf" label="Selecionar ficheiro"
                                          cancelLabel="Cancelar" uploadLabel="Carregar"
                                          invalidFileMessage="Formato Inválido. Formato suportado: .jpg, .jpeg, .png, .pdf"
                                          invalidSizeMessage="O ficheiro é demasiado grande. Deve ter no máximo 3 MB."
                                          onstart="PF('frawPageBlocker').show()"
                                          oncomplete="PF('frawPageBlocker').hide();"
                                          fileLimitMessage="Só pode carregar um ficheiro"
                                          update="dataTableAlteracao" multiple="false">
                            </p:fileUpload>

                            <div class="file-upload-row-footer" style="display: flex; justify-content: space-between;">
                                <h:outputText value="Tamanho máximo: 3 Mb | Formato suportado: .jpg, .jpeg, .png, .pdf"/>
                                <p:commandButton value="Cancelar"
                                                 oncomplete="PF('dialog-upload-replace').hide()"
                                                 styleClass="ui-priority-secondary"
                                                 onclick="onClickCancelarEsconderRow('pcjFileUploadTableGrupoAlteracao')"/>
                            </div>
                        </p:rowExpansion>
                    </p:dataTable>
                </div>
            </h:panelGroup>
        </div>
    </h:panelGroup>
</ui:fragment>