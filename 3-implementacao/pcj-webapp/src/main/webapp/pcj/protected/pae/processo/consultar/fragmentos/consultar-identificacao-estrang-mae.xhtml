<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns="http://www.w3.org/1999/xhtml">


    <div class="row-fluid mb-3">
        <div class="span10">
            <p:outputLabel for="pcjNomeMaeEstrangeira" value="Nome"/>
            <p>
                <h:outputText id="pcjNomeMaeEstrangeira"
                              value="#{pcjConsultarProcessoBean.identificacao.getNomeMae()}"/>
            </p>
        </div>
    </div>
    <div class="row-fluid mb-3">
        <div class="span3">
            <p:outputLabel for="pcjDocumentoMaeEstrangeiro"
                           value="Documento Identificação"/>
            <p>
                <h:outputText id="pcjDocumentoMaeEstrangeiro"
                              value="#{pcjConsultarProcessoBean.identificacao.getMae().tipoDocumento.descricao}"/>
            </p>
        </div>
        <div class="span3">
            <p:outputLabel for="pcjNumeroDocumentoMaeEstrangeiro"
                           value="Nº documento de identificação"/>
            <p>
                <h:outputText id="pcjNumeroDocumentoMaeEstrangeiro"
                              value="#{pcjConsultarProcessoBean.identificacao.getMae().numeroIdentificacao}"/>
            </p>
        </div>
        <div class="span3">
            <p:outputLabel for="pcjNascimentoMaeEstrangeiro" value="Data de nascimento"/>
            <p>
                <h:outputText id="pcjNascimentoMaeEstrangeiro"
                              value="#{pcjConsultarProcessoBean.identificacao.getMae().dataNascimento}"/>
            </p>
        </div>
        <div class="span3">
            <p:outputLabel for="pcjTelefoneMaeEstrangeiro" value="Telefone"/>
            <p>
                <h:outputText id="pcjTelefoneMaeEstrangeiro"
                              value="#{pcjConsultarProcessoBean.identificacao.getMae().contacto.telemovel}"/>
            </p>
        </div>
    </div>

    <div class="row-fluid mb-3">
        <div class="span3">
            <p:outputLabel for="pcjPaisMaeEstrangeiro" value="Nacionalidade"/>
            <p>
                <h:outputText id="pcjPaisMaeEstrangeiro"
                              value="#{pcjConsultarProcessoBean.identificacao.getMae().morada.pais}"/>
            </p>
        </div>
    </div>

</ui:fragment>