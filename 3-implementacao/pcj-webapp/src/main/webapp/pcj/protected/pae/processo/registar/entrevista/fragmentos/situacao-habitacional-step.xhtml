<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui"
             xmlns:f="http://java.sun.com/jsf/core">

    <p:outputPanel id="pcjEntrevistaEspacoProprioPnl" rendered="#{pcjEntrevistaBean.wizard.editing}">
        <div class="row-fluid mb-5">
            <div class="span4">
                <p:outputLabel id="cpjHabitacionalHabitacaoLabel"
                               for="cpjHabtacionalHabitacaoRadio"
                               value="Situação habitacional" />
                <p:selectOneRadio id="cpjHabtacionalHabitacaoRadio"
                                  value="#{pcjEntrevistaBean.entrevistaHabitacao.habitacao}"
                                  styleClass="select-radio-2 sm" layout="grid" columns="1"
                                  selected="true" required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <f:selectItem itemLabel="Casa própria" itemValue="Casa própria" checked="true" />
                    <f:selectItem itemLabel="Casa arrendada" itemValue="Casa arrendada" />
                    <f:selectItem itemLabel="Habitação social" itemValue="Habitação social" />
                </p:selectOneRadio>
                <p:message for="cpjHabtacionalHabitacaoRadio"/>
            </div>
        </div>
        <div class="row-fluid mb-5">
            <div class="span4">
                <p:outputLabel id="cpjHabitacionalTipologiaLabel"
                               for="cpjHabtacionalTipologiaRadio"
                               value="Tipologia" />
                <p:selectOneRadio id="cpjHabtacionalTipologiaRadio"
                                  value="#{pcjEntrevistaBean.entrevistaHabitacao.tipologia}"
                                  styleClass="select-radio-2 sm" layout="grid" columns="1"
                                  selected="true" required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <f:selectItem itemLabel="T1" itemValue="T1" checked="true" />
                    <f:selectItem itemLabel="T2" itemValue="T2" />
                    <f:selectItem itemLabel="T3" itemValue="T3" />
                    <f:selectItem itemLabel="T4+" itemValue="T4+" />
                </p:selectOneRadio>
                <p:message for="cpjHabtacionalTipologiaRadio"/>
            </div>
        </div>
        <div class="row-fluid mb-5">
            <div class="span4">
                <p:outputLabel id="cpjHabitacionalEspacoProprioLabel"
                               for="cpjHabtacionalEspacoProprioRadio"
                               value="A criança tem espaço próprio?" />
                <p:selectOneRadio id="cpjHabtacionalEspacoProprioRadio"
                                  value="#{pcjEntrevistaBean.entrevistaHabitacao.espacoProprio}"
                                  styleClass="select-radio-2 sm" layout="grid" columns="1"
                                  selected="true" required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <f:selectItem itemLabel="Sim" itemValue="#{true}" checked="true" />
                    <f:selectItem itemLabel="Não" itemValue="#{false}" />

                    <p:ajax event="change"
                            process="@this"
                            onstart="PF('frawPageBlocker').show();"
                            oncomplete="PF('frawPageBlocker').hide();"
                            listener="#{pcjEntrevistaBean.limparLocalEntrevista()}"/>

                </p:selectOneRadio>
            </div>
        </div>

        <p:outputPanel autoUpdate="true">
            <p:outputPanel rendered="#{pcjEntrevistaBean.entrevistaHabitacao.isEspacoProprio()}">
                <div class="row-fluid mb-5">
                    <div class="span12">
                        <p:outputLabel id="pcjEntrevistaHabitacaoOndeLbl"
                                       for="pcjEntrevistaHabitacaoOndeText"
                                       value="Onde?" />
                        <p:inputText id="pcjEntrevistaHabitacaoOndeText"
                                     maxlength="100" styleClass="max-field-size"
                                     value="#{pcjEntrevistaBean.entrevistaHabitacao.localEspacoProprio}"
                                     required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"/>
                        <p:message for="pcjEntrevistaHabitacaoOndeText"/>
                    </div>
                </div>
            </p:outputPanel>
        </p:outputPanel>
        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel id="pcjEntrevistaResponsalResidenciaLabel"
                               for="pcjEntrevistaResponsalResidenciaText"
                               value="Pessoa a quem está atribuída a residência" />
                <p:inputText id="pcjEntrevistaResponsalResidenciaText"
                             maxlength="100" styleClass="max-field-size"
                             value="#{pcjEntrevistaBean.entrevistaHabitacao.responsavelResidencia}"
                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"/>
                <p:message for="pcjEntrevistaResponsalResidenciaText"/>
            </div>
        </div>
    </p:outputPanel>
</ui:fragment>
