<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <div id="pcjTabConsultaDiligenciaPanel">
        <div class="row-fluid mb-5">
            <h3>Diligências</h3>
        </div>

        <div class="row-fluid mb-5">
            <p:commandButton id="btnAdicionarDiligencia"
                             value="Adicionar diligência"
                             action="#{pcjSubsystem.getAdicionarDiligencia()}"
                             styleClass="ui-priority-secondary float-left"
                             onstart="PF('frawPageBlocker').show()"
                             oncomplete="PF('frawPageBlocker').hide(); #{pcjDiligenciaBean.informarNomeEtiquetas('Adicionar')}"
                             process="@this"
                             rendered="#{pcjDiligenciaBean.mostrarAdicionarDiligencia()}"
            />
        </div>

        <p:outputPanel>
            <div class="row-fluid mb-5">
                <div class="table-list">
                    <p:dataTable id="pcjDataTableDiligencia" var="diligencia"
                                 value="#{pcjDiligenciaBean.diligencias}"
                                 draggableColumns="false"
                                 emptyMessage="Não existem diligências."
                                 rowIndexVar="rowIndexVar"
                                 rowStyleClass="resultado"
                                 rows="5"
                                 rowsPerPageTemplate="5,10,15"
                                 paginator="true"
                                 paginatorPosition="bottom"
                                 paginatorTemplate="{RowsPerPageDropdown} {PreviousPageLink} {CurrentPageReport} {NextPageLink}"
                                 currentPageReportTemplate="Página {currentPage}"
                                 reflow="true">

                        <f:facet name="caption">Diligências associadas ao processo</f:facet>

                        <p:column  id="pcjDataHoraColuna"  headerText="Data / Hora" styleClass="texto">
                            <h:outputText id="pcjdataHorautputText"
                                          value="#{pcjDiligenciaBean.dataHoraFormatada(diligencia.data, diligencia.hora)}"/>
                        </p:column>

                        <p:column id="pcjTipoParticipacaoColuna" headerText="Tipo de diligência" styleClass="texto">
                            <h:outputText id="pcjtipoParticipacaoOutputText"
                                          value="#{diligencia.tipoDiligencia.descricao}"/>
                        </p:column>

                        <p:column id="menuAcoes" styleClass="acoes" headerText="Ações">
                            <p:commandButton id="acoes" icon="fa fa-ellipsis-v" type="button"/>
                            <p:overlayPanel id="acoesList" for="acoes" hideEffect="fade" my="right top" at="right bottom">
                                <p:outputPanel id="carDetail">
                                    <ul>
                                        <li>
                                            <p:commandLink id="consultarDiligencia"
                                                           value="Consultar diligência"
                                                           onstart="PF('frawPageBlocker').show()"
                                                           oncomplete="PF('frawPageBlocker').hide();"
                                                           process="@this"
                                                           immediate="true"
                                                           actionListener="#{pcjDiligenciaBean.setDiligencia(diligencia)}"
                                                           action="#{pcjSubsystem.getConsultarDiligencia()}"/>
                                        </li>
                                        <p:outputPanel rendered="#{pcjDiligenciaBean.estadoProcessoPermitidoEntrevista(diligencia)}">
                                            <li>
                                                <p:commandLink id="registarEntrevista"
                                                               rendered="#{pcjDiligenciaBean.verificaEntrevista(diligencia) eq null}"
                                                               value="Registar entrevista"
                                                               process="@this"
                                                               action="#{pcjSubsystem.registarEntrevista()}"
                                                               actionListener="#{pcjEntrevistaBean.setIdDiligencia(diligencia.id)}"
                                                               onstart="PF('frawPageBlocker').show(); "
                                                               oncomplete="PF('frawPageBlocker').hide();"
                                                               onsuccess="PF('frawPageBlocker').hide()">
                                                </p:commandLink>
                                            </li>
                                            <li>
                                                <p:commandLink id="retomarEntrevista"
                                                               rendered="#{pcjDiligenciaBean.verificaEntrevista(diligencia) != null and !pcjDiligenciaBean.verificaEntrevista(diligencia)}"
                                                               value="Retomar entrevista"
                                                               process="@this"
                                                               action="#{pcjSubsystem.registarEntrevista()}"
                                                               actionListener="#{pcjEntrevistaBean.setIdDiligencia(diligencia.id)}"
                                                               onstart="PF('frawPageBlocker').show(); "
                                                               oncomplete="PF('frawPageBlocker').hide();"
                                                               onsuccess="PF('frawPageBlocker').hide()">
                                                </p:commandLink>
                                            </li>
                                            <li>
                                                <p:commandLink id="pcjConsultarEntrevista"
                                                               rendered="#{pcjDiligenciaBean.verificaEntrevista(diligencia)}"
                                                               value="Consultar entrevista"
                                                               process="@this"
                                                               action="#{pcjSubsystem.consultarEntrevista()}"
                                                               actionListener="#{pcjEntrevistaBean.setIdDiligencia(diligencia.id)}"
                                                               onstart="PF('frawPageBlocker').show(); "
                                                               oncomplete="PF('frawPageBlocker').hide();"
                                                               onsuccess="PF('frawPageBlocker').hide()">
                                                </p:commandLink>
                                            </li>
                                        </p:outputPanel>
                                    </ul>
                                </p:outputPanel>
                            </p:overlayPanel>
                        </p:column>
                    </p:dataTable>
                </div>
            </div>
        </p:outputPanel>
    </div>
</ui:fragment>