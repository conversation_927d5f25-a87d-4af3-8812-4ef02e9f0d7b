<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:p="http://primefaces.org/ui">

    <p:outputPanel id="pcjPainelInformacaoesRendimentos">
        <div class="row-fluid mb-5">
            <div class="row-fluid mb-5">
                <div class="span12">
                    <p:outputLabel for="pcjEntrevistaValor " value="Qual a verba que irá receber?"/>
                    <h:outputText id="pcjEntrevistaValor" value="#{pcjEntrevistaBean.entrevistaRendimento.getValor()}">
                        <f:converter converterId="pcjMoneyConverter" />
                    </h:outputText>
                </div>
            </div>
            <div class="row-fluid mb-5">
                <div class="span12">
                    <p:outputLabel for="pcjEntrevistaDescricaoRendimentos " value="De que forma a família vai utilizar o rendimento que a criança vai receber?"/>
                    <h:outputText id="pcjEntrevistaDescricaoRendimentos" value="#{pcjEntrevistaBean.entrevistaRendimento.getDescricaoRedimento()}"/>
                </div>
            </div>
        </div>
    </p:outputPanel>
</ui:fragment>