<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui">

    <p:outputPanel id="pcjPainelInformacaoesMotivacao">
        <div class="row-fluid mb-5">
            <div class="span12">
                <p:outputLabel for="pcjEntrevistaDescricaoMotivacao " value="Qual a motivação da criança para a participação no presente evento?"/>
                <h:outputText id="pcjEntrevistaDescricaoMotivacao" value="#{pcjEntrevistaBean.entrevistaMotivacao.getDescricaoMotivo()}"/>
            </div>
        </div>
        <div class="row-fluid mb-5">
            <div class="span12">
                    <p:outputLabel for="pcjEntrevistaFrequenciaParticipacao " value="Com que frequência a criança participa em atividades de natureza artística, cultural ou publicitária?"/>
                    <h:outputText id="pcjEntrevistaFrequenciaParticipacao" value="#{pcjEntrevistaBean.entrevistaMotivacao.getFrequenciaParticipacao()}"/>
            </div>
        </div>
    </p:outputPanel>
</ui:fragment>