<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui">

    <div id="pcjFileuploadSingle" class="row-fluid ">
        <div class="span12 ui-fileupload-border" id="fileUploadDiv">
            <div class="fileUpload-card-name">
                <span class="description">Submeter consentimento informado assinado*</span>
            </div>
            <hr/>
            <p:fileUpload
                    id="pcjAtaFileUploadSingle"
                    rendered="#{bean.filesSingle.size() eq 0}"
                    immediate="true"
                    required="true"
                    widgetVar="fileUploadVarS"
                    fileUploadListener="#{bean.handleFileSingleUpload}"
                    invalidSizeMessage="#{bean.invalidSizeMessageSingle}"
                    invalidFileMessage="#{bean.invalidFileMessageSinglePdf}"
                    fileLimitMessage="#{bean.fileLimitMessageSingle}"
                    sizeLimit="3000000"
                    mode="advanced"
                    dragDropSupport="false"
                    auto="true"
                    fileLimit="1"
                    label="Selecionar documento"
                    allowTypes="/(\.|\/)(pdf)$/"
                    accept=".pdf"
                    update="@parent pcjPaeAutorizacaoUploadPanel pcjPaeAutorizacaoBtnPanel"
                    styleClass="single-upload">
            </p:fileUpload>

            <h:panelGroup styleClass="ui-fileupload-footer" rendered="#{bean.filesSingle.size() eq 0}">
                <div class="maxSizeText">Tamanho máximo do ficheiro: 3MB | Formato aceite: PDF</div>
            </h:panelGroup>
            <p:fragment id="singleFileTable" rendered="#{bean.filesSingle.size() gt 0}">
                <ui:repeat value="#{bean.filesSingle}" var="item" varStatus="myVarStatus">
                    <div class="fileUpload-card">
                        <div class="fileUpload-card--info">
                            <div class="fileUpload-card--name-area">

                                <p:fragment>
                                    <i class="fa fa-#{bean.iconHandler(bean.filesSingle.get(0).getContentType())}"/>
                                </p:fragment>

                                <div class="fileUpload-card--name">
                                    <p:fragment> #{item.getFileName()}</p:fragment>
                                    <p class="fileUpload-card--sub-name">#{bean.convertbytesToMB(item.getSize())}</p>
                                </div>
                            </div>

                            <div class="fileUpload-card--link-group">
                                <div class="fileUpload-card--link-left">
                                    <p:commandButton id="showSingleFile"
                                                     value="Consultar"
                                                     process="@this"
                                                     immediate="true"
                                                     onstart="PF('frawPageBlocker').show()"
                                                     oncomplete="PF('frawPageBlocker').hide();"
                                                     ajax="false"
                                                     styleClass="ui-priority-terciary"
                                                     action="#{bean.verDocumentoAutorizacao()}"
                                                     onclick="this.form.target = '_blank'"/>
                                </div>
                                <div class="fileUpload-card--link-right">
                                    <p:commandButton id="removeSingleFile"
                                                     value="Remover"
                                                     immediate="true"
                                                     onstart="PF('frawPageBlocker').show()"
                                                     onerror="PF('frawPageBlocker').hide()"
                                                     oncomplete="PF('frawPageBlocker').hide();"
                                                     styleClass="ui-priority-terciary"
                                                     action="#{bean.deleteFileSingle(myVarStatus.index)}"
                                                     update="@this pcjAutorizacaoForm:pcjPaeAutorizacaoUploadPanel
                                                             pcjAutorizacaoForm:pcjPaeAutorizacaoBtnPanel"/>
                                </div>
                            </div>
                        </div>

                    </div>
                </ui:repeat>
            </p:fragment>
        </div>
    </div>

</ui:fragment>