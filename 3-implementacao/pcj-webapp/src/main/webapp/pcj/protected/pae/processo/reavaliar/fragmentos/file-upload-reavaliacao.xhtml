<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:p="http://primefaces.org/ui"
             xmlns="http://www.w3.org/1999/xhtml">

    <div id="pcjFileuploadSingle" class="row-fluid ">
        <div class="span12 ui-fileupload-border" id="fileUploadDiv">
            <div class="fileUpload-card-name">
                <span class="description">Submeter comprovativo assinado *</span>
            </div>
            <hr/>
            <p:fileUpload
                    id="pcjProcessoFileUploadSingleReavaliacao"
                    rendered="#{pcjConsultarReavaliacaoBean.filesSingle.size() eq 0}"
                    immediate="true" required="true" widgetVar="fileUploadVarS"
                    fileUploadListener="#{pcjConsultarReavaliacaoBean.fileSingleUpload}"
                    invalidSizeMessage="#{pcjConsultarReavaliacaoBean.invalidSizeMessageMultiple}"
                    invalidFileMessage="#{pcjConsultarReavaliacaoBean.invalidFileMessageMultiple}"
                    fileLimitMessage="#{pcjConsultarReavaliacaoBean.fileLimitMessageMultiple}"
                    sizeLimit="3000000" mode="advanced"
                    dragDropSupport="false" auto="true" fileLimit="1"
                    label="Selecionar documento"
                    allowTypes="/(\.|\/)(pdf)$/" accept=".pdf"
                    update="@this @parent pcjConsultarReavaliacaoProcessoPaeForm"
                    styleClass="single-upload">
            </p:fileUpload>

            <h:panelGroup styleClass="ui-fileupload-footer"
                          rendered="#{pcjConsultarReavaliacaoBean.filesSingle.size() eq 0}">
                <div class="maxSizeText">Tamanho máximo do ficheiro: 3MB | Formatos aceites: PDF</div>
            </h:panelGroup>
            <p:fragment id="singleFileTableReavaliacao"
                        rendered="#{pcjConsultarReavaliacaoBean.filesSingle.size() gt 0}">
                <ui:repeat value="#{pcjConsultarReavaliacaoBean.filesSingle}" var="item" varStatus="myVarStatus">
                    <div class="fileUpload-card">
                        <div class="fileUpload-card--info">
                            <div class="fileUpload-card--name-area">

                                <p:fragment>
                                    <i class="fa fa-#{pcjConsultarReavaliacaoBean.iconHandler(pcjConsultarReavaliacaoBean.filesSingle.get(0).getContentType())}"/>
                                </p:fragment>

                                <div class="fileUpload-card--name">
                                    <p:fragment> #{item.getFileName()}</p:fragment>
                                    <p class="fileUpload-card--sub-name">
                                        #{pcjReavaliacaorProcessoBean.convertbytesToMB(item.getSize())}</p>
                                </div>
                            </div>

                            <div class="fileUpload-card--link-group">
                                <div class="fileUpload-card--link-left">
                                    <p:commandButton id="showSingleFile"
                                                     value="Consultar"
                                                     ajax="false"
                                                     styleClass="ui-priority-terciary"
                                                     onstart="PF('frawPageBlocker').show()"
                                                     oncomplete="PF('frawPageBlocker').hide();"
                                                     action="#{pcjConsultarReavaliacaoBean.verDocumentoSingle()}"
                                                     onclick="this.form.target = '_blank'"/>
                                </div>
                                <div class="fileUpload-card--link-right">
                                    <p:commandButton id="removeSingleFile"
                                                     value="Remover"
                                                     immediate="true"
                                                     onstart="PF('frawPageBlocker').show()"
                                                     oncomplete="PF('frawPageBlocker').hide();"
                                                     styleClass="ui-priority-terciary"
                                                     action="#{pcjConsultarReavaliacaoBean.deleteFileSingle(myVarStatus.index)}"
                                                     update="@parent pcjConsultarReavaliacaoProcessoPaeForm:pcjProcessoReavaliacaoAssinaturaPnlUpload
                                                     pcjConsultarReavaliacaoProcessoPaeForm:pcjProcessoReavaliacaoAssinaturaPnlUploadBtn
                                                     pcjConsultarReavaliacaoProcessoPaeForm:pcjProcessoFileUploadSingleReavaliacao
                                                     pcjConsultarReavaliacaoProcessoPaeForm:singleFileTableReavaliacao"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </ui:repeat>
            </p:fragment>
        </div>
    </div>

</ui:fragment>