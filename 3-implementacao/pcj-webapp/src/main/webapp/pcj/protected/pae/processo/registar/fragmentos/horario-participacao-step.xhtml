<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:f="http://java.sun.com/jsf/core"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui">

    <h:panelGroup id="pcjPanelHorarioParticipacao" layout="block">
        <div class="row-fluid mb-5">
            <div class="span4">
                <p:outputLabel for="pcjTipoParticipacao" value="Tipo de participação"/>
                <p:selectOneMenu id="pcjTipoParticipacao"
                                 value="#{modelHorarioParticipacao.horarioParticipacao.tipoParticipacao}"
                                 converter="omnifaces.SelectItemsConverter"
                                 required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">

                    <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                    <f:selectItems value="#{modelHorarioParticipacao.tipoParticipacoes}" var="tipoParticipacao"
                                   itemValue="#{tipoParticipacao}" itemLabel="#{tipoParticipacao.descricao}"/>
                </p:selectOneMenu>
                <p:message for="pcjTipoParticipacao"/>
            </div>

            <div class="span4">
                <p:outputLabel for="pcjTipoPeriodo" value="Período"/>
                <p:selectOneMenu id="pcjTipoPeriodo" value="#{modelHorarioParticipacao.horarioParticipacao.periodo}"
                                 converter="omnifaces.SelectItemsConverter"
                                 required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">

                    <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                    <f:selectItems value="#{modelHorarioParticipacao.tiposPeriodo}" var="periodo"
                                   itemValue="#{periodo}" itemLabel="#{periodo.descricao}"/>
                </p:selectOneMenu>
                <p:message for="pcjTipoPeriodo"/>
            </div>

            <div class="span4">
                <p:outputLabel for="pcjDiaSemana" value="Dia da semana"/>
                <p:selectOneMenu id="pcjDiaSemana" value="#{modelHorarioParticipacao.horarioParticipacao.diaSemana}"
                                 converter="omnifaces.SelectItemsConverter"
                                 required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">

                    <f:selectItem noSelectionOption="true" itemLabel="#{pcjComponentMessage['selecione']}"/>
                    <f:selectItems value="#{modelHorarioParticipacao.diasSemana}" var="diaSemana"
                                   itemValue="#{diaSemana}" itemLabel="#{diaSemana.descricao}"/>
                </p:selectOneMenu>
                <p:message for="pcjDiaSemana"/>
            </div>
        </div>

        <div class="row-fluid mb-4">
            <div class="span3">
                <p:outputLabel for="pcjDataHoraEntradaParticipacao" value="Horário de entrada"/>
                <p:inputMask id="pcjDataHoraEntradaParticipacao"
                             mask="99:99" placeholder="HH:mm"
                             value="#{modelHorarioParticipacao.horarioParticipacao.horaEntradaToString}"
                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             validatorMessage="#{pcjComponentMessage['hora.invalida']}">
                    <f:validateRegex pattern="^([01]?\d|2[0-3])(?::([0-5]?\d))?$"/>
                </p:inputMask>
                <p:message for="pcjDataHoraEntradaParticipacao"/>
            </div>

            <div class="span2 ">
                <p:outputLabel for="pcjDataHoraSaidaParticipacao" value="Horário de saída"/>
                <p:inputMask id="pcjDataHoraSaidaParticipacao"
                             mask="99:99" placeholder="HH:mm"
                             value="#{modelHorarioParticipacao.horarioParticipacao.horaSaidaToString}"
                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                             validatorMessage="#{pcjComponentMessage['hora.invalida']}">
                    <f:validateRegex pattern="^([01]?\d|2[0-3])(?::([0-5]?\d))?$"/>
                </p:inputMask>
                <p:message for="pcjDataHoraSaidaParticipacao"/>
            </div>

            <div class="span2">
                <p:outputLabel id="pcjTolltipNumPausasEnsaio" for="pcjNumPausasParticipacao"
                               value="Nº de pausas" styleClass="dica"/>
                <p:tooltip for="pcjTolltipNumPausasEnsaio" value="#{pcjComponentMessage['tooltip.pausas.horario']}"/>
                <p:inputText id="pcjNumPausasParticipacao"
                             value="#{modelHorarioParticipacao.horarioParticipacao.numeroPausas}"
                             maxlength="2"
                             required="true" requiredMessage="#{pcjComponentMessage['obrigatorio']}">
                    <p:keyFilter regEx="/[0-9]+/i"/>
                </p:inputText>
                <p:message for="pcjNumPausasParticipacao"/>
            </div>
        </div>

        <div class="row-fluid mb-6">
            <div class="span6">
                <p:commandButton id="pcjAdicionarHorarioParticipacao" value="Adicionar horário de participação"
                                 update="@this pcjPanelHorarioParticipacao" styleClass="ui-priority-secondary"
                                 process="@this pcjPanelHorarioParticipacao"
                                 action="#{modelHorarioParticipacao.adicionarParticipacao(requerimento)}"
                                 onstart="PF('frawPageBlocker').show()"
                                 oncomplete="PF('frawPageBlocker').hide()"/>
            </div>
        </div>

        <div class="mb-5">
            <h3>Ensaios, actos preparatórios e atuações</h3>
        </div>

        <div class="row-fluid mb-4">
            <h:panelGroup layout="block" rendered="#{modelHorarioParticipacao.isCriancaMaior5Anos(requerimento)}">
                <div class="span6">
                    <div class="row-fluid">
                        <h:outputText style="font-weight:bold" value="Máximo de horas diárias (aulas): "/>
                        <h:outputText id="pcjSomatorioMaximoAulas"
                                      value="#{modelHorarioParticipacao.obterHorasDiariasPeriodoAula(requerimento)} horas"/>
                    </div>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" rendered="#{modelHorarioParticipacao.temFerias(requerimento)}">
                <div class="row-fluid span6">
                    <h:outputText style="font-weight:bold" value="Máximo de horas diárias (férias): "/>
                    <h:outputText id="pcjMaximoHorasDiarias"
                                  value="#{modelHorarioParticipacao.obterHorasDiariasPeriodoFerias(requerimento)} horas "/>
                </div>
            </h:panelGroup>
        </div>

        <div class="row-fluid mb-5">
            <h:panelGroup layout="block" rendered="#{not modelHorarioParticipacao.isCriancaMaior5Anos(requerimento)}">
                <div class="span6">
                    <h:panelGroup layout="block"
                                  rendered="#{modelHorarioParticipacao.isCriancaEntre3e6Anos(requerimento)}">
                        <div class="row-fluid mb-4">
                            <h:outputText style="font-weight:bold" value="Nº horas máximas diárias: "/>
                            <h:outputText id="pcjMaximoHorasDiariasSemEscola" value="2 horas "/>
                        </div>
                    </h:panelGroup>
                    <div class="row-fluid">
                        <h:outputText style="font-weight:bold" value="Nº horas semanais disponíveis: "/>
                        <h:outputText id="pcjSomatorioHorasEnsaio"
                                      value="#{modelHorarioParticipacao.obterTotalHorasPeriodoSemana(false, requerimento)}"/>
                    </div>
                </div>
            </h:panelGroup>

            <h:panelGroup layout="block" rendered="#{modelHorarioParticipacao.isCriancaMaior5Anos(requerimento)}">
                <div class="span6">
                    <div class="row-fluid">
                        <h:outputText style="font-weight:bold" value="Nº horas semanais disponíveis (aulas): "/>
                        <h:outputText id="pcjSomatorioHorasAulasEnsaio"
                                      value="#{modelHorarioParticipacao.obterTotalHorasPeriodoSemana(true, requerimento)}"/>
                    </div>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" rendered="#{modelHorarioParticipacao.temFerias(requerimento)}">
                <div class="row-fluid span6">
                    <h:outputText style="font-weight:bold" value="Nº horas semanais disponíveis (férias): "/>
                    <h:outputText id="pcjSomatorioHorasFeriasEnsaio"
                                  value="#{modelHorarioParticipacao.obterTotalHorasPeriodoSemana(false, requerimento)}"/>
                </div>
            </h:panelGroup>
        </div>


        <h:panelGroup layout="block" styleClass="row-fluid text-center"
                      rendered="#{modelHorarioParticipacao.participacoes.size()==0}">
            <p>Não existem horários de participação adicionados.</p>
        </h:panelGroup>
        <div class="table-list mb-4">
            <p:dataTable id="pcjDataTableListHorarioParticipacao"
                         draggableColumns="false" paginator="false"
                         paginatorTemplate="{RowsPerPageDropdown} {PreviousPageLink} {CurrentPageReport} {NextPageLink}"
                         currentPageReportTemplate="Página {currentPage}"
                         rowIndexVar="rowIndexVar"
                         emptyMessage="Não existem horários de participação adicionados."
                         var="participacao" value="#{modelHorarioParticipacao.getParticacoes(requerimento)}"
                         rowStyleClass="resultado"
                         rendered="#{modelHorarioParticipacao.participacoes.size()>0}">

                <f:facet name="caption">Horários semanais de ensaios e atuações durante aulas e férias</f:facet>

                <p:column headerText="Tipo de participação" styleClass="data">
                    <h:outputText value="#{participacao.tipoParticipacao.descricao}"/>
                </p:column>

                <p:column headerText="Período" styleClass="data">
                    <h:outputText value="#{participacao.periodo.descricao}"/>
                </p:column>

                <p:column headerText="Dia da semana" styleClass="data">
                    <h:outputText value="#{participacao.diaSemana.descricao}"/>
                </p:column>

                <p:column headerText="Horário da atividade" styleClass="data">
                    <h:outputText value="#{participacao.horaEntrada}">
                        <f:convertDateTime pattern="HH:mm"/>
                    </h:outputText>
                    <h:outputText value=" - "/>
                    <h:outputText value="#{participacao.horaSaida}">
                        <f:convertDateTime pattern="HH:mm"/>
                    </h:outputText>
                </p:column>

                <p:column headerText="Nº de pausas" styleClass="data">
                    <h:outputText value="#{participacao.numeroPausas}"/>
                </p:column>

                <p:column id="pcjMenuAcoesHorarioParticipacao" headerText="Ações" styleClass="acoes">
                    <p:commandButton id="acoes" icon="fa fa-ellipsis-v" type="button" style="float: right;"/>
                    <p:overlayPanel id="acoesList" for="acoes" hideEffect="fade" my="right top" at="right bottom"
                                    dynamic="true">
                        <p:outputPanel id="pcjAcoesHorarioParticipacao">
                            <ul>
                                <li>
                                    <p:commandLink id="pcjAcaoEliminarParticipacao"
                                                   value="Eliminar participação"
                                                   actionListener="#{modelHorarioParticipacao.eliminarParticipacao(participacao, participacao.getRequerimentoPAE())}"
                                                   immediate="true"
                                                   process="@this pcjDataTableListHorarioParticipacao"
                                                   onstart="PF('frawPageBlocker').show()"
                                                   oncomplete="PF('frawPageBlocker').hide()"
                                                   update="@form @this pcjDataTableListHorarioParticipacao">

                                        <p:confirm header="Eliminar participação"
                                                   message="Deseja eliminar o horário de participação?"/>
                                    </p:commandLink>
                                </li>
                            </ul>
                        </p:outputPanel>
                    </p:overlayPanel>
                </p:column>
            </p:dataTable>
        </div>
    </h:panelGroup>
</ui:fragment>