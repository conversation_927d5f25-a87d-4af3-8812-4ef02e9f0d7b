<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
			 xmlns:f="http://java.sun.com/jsf/core"
			 xmlns:ui="http://java.sun.com/jsf/facelets"
			 xmlns:p="http://primefaces.org/ui">

    <p:outputPanel id="pcjPesquisaGrupoPanel">
        <div class="row-fluid mb-5">
            <h2>Grupos</h2>
        </div>
        <p:outputPanel id="pcjGrupoPertencePanel" layout="block">
            <div class="row-fluid mb-5">
                <p:outputLabel value="Este processo pertence a um grupo?"/>

                <p:selectOneRadio id="pcjPertenceGrupoRadio"
                                  value="#{pcjProcessoPaeBean.pertenceGrupo}"
                                  styleClass="select-radio-2 sm">
                    <f:selectItem itemLabel="Sim" itemValue="#{true}"/>
                    <f:selectItem itemLabel="Não" itemValue="#{false}" checked="true"/>

                    <p:ajax event="change"
                            update="@form"
                            process="@this"
                            onstart="PF('frawPageBlocker').show();"
                            oncomplete="PF('frawPageBlocker').hide();"
                            listener="#{pcjProcessoPaeBean.limparGrupoSelecionado()}"
                    />
                </p:selectOneRadio>
            </div>
        </p:outputPanel>

        <p:outputPanel id="pcjGrupoPanel" rendered="#{pcjProcessoPaeBean.pertenceGrupo}">
            <div class="row-fluid mb-5">
                <div class="span12">
                    <p:selectOneMenu id="pcjGrupoProcessoSelect"
                                     value="#{pcjProcessoPaeBean.grupoSelecionado}"
                                     filter="true"
                                     filterMatchMode="contains"
                                     converter="omnifaces.SelectItemsConverter"
                                     required="#{pcjProcessoPaeBean.pertenceGrupo}"
                                     requiredMessage="#{pcjComponentMessage['obrigatorio']}"
                    >
                        <p:ajax event="change"
                                update="@form pcjIdentificacaoAtividade pcjAtividadeRegistradaPanel pcjNovaAtividade"
                                process="@this"
                                onstart="PF('frawPageBlocker').show()"
                                onsuccess="PF('frawPageBlocker').hide()"
                                listener="#{pcjProcessoPaeBean.buscarAtividadeParticipacaoMoradaPorGrupo()}"
                        />

                        <f:selectItem itemLabel="#{pcjComponentMessage['selecione']}" itemValue=""
                                      noSelectionOption="true"/>
                        <f:selectItems value="#{pcjProcessoPaeBean.grupos}"
                                       var="grupo"
                                       itemValue="#{grupo}" itemLabel="#{grupo.nome}"/>
                    </p:selectOneMenu>
                    <p:message id="pcjMessageGrupo" for="pcjGrupoProcessoSelect"/>
                </div>
            </div>
        </p:outputPanel>
    </p:outputPanel>
</ui:fragment>
