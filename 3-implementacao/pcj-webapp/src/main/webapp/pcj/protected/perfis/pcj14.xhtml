<?xml version="1.0" encoding="ISO-8859-15"?>
<ui:fragment xmlns="http://www.w3.org/1999/xhtml"
             xmlns:h="http://java.sun.com/jsf/html"
             xmlns:ui="http://java.sun.com/jsf/facelets"
             xmlns:p="http://primefaces.org/ui">

    <div class="row-fluid mb-5">
        <div class="span6">
            <p:outputLabel for="pcjPerfil" value="Perfil 14"/>
            <h:outputText id="pcjPerfil"
                          value="#{bean.getPerfilDTO().getNome()}"/>
        </div>
    </div>
    <p:commandButton style="visibility: hidden;" aria-disabled="true" />
</ui:fragment>