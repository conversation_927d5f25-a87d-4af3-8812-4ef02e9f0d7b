<?xml version="1.0" encoding="ISO-8859-15"?>
<ejb-jar xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	version="3.1"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/ejb-jar_3_1.xsd"
	metadata-complete="false">
	<module-name>pcj</module-name>
	<enterprise-beans>
		<message-driven>
			<ejb-name>fraw-async-event-queue-dispatcher</ejb-name>
			<mapped-name>jms/FrawWebQueue</mapped-name>
			<ejb-class>pt.segsocial.fraw.cdi.ejb.FrawBackgroundMDB</ejb-class>
			<transaction-type>Container</transaction-type>
			<message-destination-link>fraw-bg-event-queue</message-destination-link>
			<activation-config>
				<activation-config-property>
					<activation-config-property-name>destinationType</activation-config-property-name>
					<activation-config-property-value>javax.jms.Queue</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>messageSelector</activation-config-property-name>
					<activation-config-property-value>ApplicationName = '${ii.project.acronym}'</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>subscriptionDurability</activation-config-property-name>
					<activation-config-property-value>NonDurable</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>acknowledgeMode</activation-config-property-name>
					<activation-config-property-value>Auto-acknowledge</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>endpointExceptionRedeliveryInterval</activation-config-property-name>
					<activation-config-property-value>500</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>endpointExceptionRedeliveryAttempts</activation-config-property-name>
					<activation-config-property-value>3</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>sendUndeliverableMsgsToDMQ</activation-config-property-name>
					<activation-config-property-value>true</activation-config-property-value>
				</activation-config-property>
				<activation-config-property>
					<activation-config-property-name>options</activation-config-property-name>
					<activation-config-property-value>imqConsumerFlowLimit=20,imqConsumerFlowThreshold=50</activation-config-property-value>
				</activation-config-property>
			</activation-config>
		</message-driven>
		<session>
			<ejb-name>fraw-foreground-event-queue-dispatcher</ejb-name>
			<ejb-class>pt.segsocial.fraw.cdi.ejb.ForegroundListenerDispatcher</ejb-class>
			<resource-ref>
				<res-ref-name>jms/FrawBackgroundConnectionFactory</res-ref-name>
				<res-type>javax.jms.ConnectionFactory</res-type>
				<mapped-name>jms/FrawWebConnectionFactory</mapped-name>
			</resource-ref>
			<message-destination-ref>
				<message-destination-ref-name>jms/FrawBackgroundEventQueue</message-destination-ref-name>
				<message-destination-type>javax.jms.Queue</message-destination-type>
				<message-destination-usage>Produces</message-destination-usage>
				<message-destination-link>fraw-bg-event-queue</message-destination-link>
			</message-destination-ref>
		</session>
	</enterprise-beans>
	<assembly-descriptor>
		<message-destination>
			<message-destination-name>fraw-bg-event-queue</message-destination-name>
			<mapped-name>jms/FrawWebQueue</mapped-name>
			<lookup-name>jms/FrawBackgroundEventQueue</lookup-name>
		</message-destination>
	</assembly-descriptor>
</ejb-jar>