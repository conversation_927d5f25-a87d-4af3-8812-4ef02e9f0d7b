package pt.segsocial.pcj.ppp.bean.cpcj;

public enum RegistarCPCJWizardStep {

	CPCJ("CPCJ") {
		@Override
		public RegistarCPCJWizardStep previous() {
			return null;
		}

		@Override
		public RegistarCPCJWizardStep next() {
			return COMPETENCIA_TERRITORIAL;
		}

		@Override
		public RegistarCPCJWizardStep last() {return null;}

	},

	COMPETENCIA_TERRITORIAL("Competência territorial") {
		@Override
		public RegistarCPCJWizardStep previous() {
			return CPCJ;
		}

		@Override
		public RegistarCPCJWizardStep next() {
			return PRESIDENTE;
		}

		@Override
		public RegistarCPCJWizardStep last() {return null;}

	},

//	INFRAESTRUTURA("Infraestruturas") {
//		@Override
//		public RegistarCPCJWizardStep previous() {
//			return COMPETENCIA_TERRITORIAL;
//		}
//
//		@Override
//		public RegistarCPCJWizardStep next() {
//			return PRESIDENTE;
//		}
//
//		@Override
//		public RegistarCPCJWizardStep last() {return null;}
//
//	},

	PRES<PERSON>EN<PERSON>("Presidente") {
		@Override
		public RegistarCPCJWizardStep previous() {
			return COMPETENCIA_TERRITORIAL;
		}

		@Override
		public RegistarCPCJWizardStep next() {
			return SECRETARIO;
		}

		@Override
		public RegistarCPCJWizardStep last() {return null;}

	},

	SECRETARIO("Secretário") {
		@Override
		public RegistarCPCJWizardStep previous() {
			return PRESIDENTE;
		}

		@Override
		public RegistarCPCJWizardStep next() {
			return null;
		}

		@Override
		public RegistarCPCJWizardStep last() {return SUBMIT;}

	},

	SUBMIT("Registar CPCJ") {

		@Override
		public RegistarCPCJWizardStep previous() {
			return null;
		}

		@Override
		public RegistarCPCJWizardStep next() {
			return null;
		}

		@Override
		public RegistarCPCJWizardStep last() {
			return SUBMIT;
		}

	};

	private final String title;

	RegistarCPCJWizardStep(String title) {
		this.title = title;
	}

	public String getTitle() {
		return this.title;
	}

	public abstract RegistarCPCJWizardStep previous();

	public abstract RegistarCPCJWizardStep next();

	public abstract RegistarCPCJWizardStep last();
	public boolean nextStepPresidente() {
		return this.next().equals(PRESIDENTE);
	}
}
