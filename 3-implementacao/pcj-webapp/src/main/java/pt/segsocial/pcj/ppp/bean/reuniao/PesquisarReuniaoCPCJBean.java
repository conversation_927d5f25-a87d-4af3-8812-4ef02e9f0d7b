package pt.segsocial.pcj.ppp.bean.reuniao;


import ch.qos.logback.classic.Level;
import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.model.DefaultScheduleModel;
import org.primefaces.model.ScheduleModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.configuration.Configuration;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.bean.AbstractPppBean;
import pt.segsocial.pcj.ppp.dto.*;
import pt.segsocial.pcj.ppp.jsf.lazy.ReuniaoPCJDataModel;
import pt.segsocial.pcj.ppp.service.ReuniaoCPCJService;
import pt.segsocial.pcj.core.util.JsfMessageUtil;

import javax.annotation.PostConstruct;
import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Named(value = "pcjConvocatoriaReuniaoBean")
@Stateful(name = "pcjConvocatoriaReuniaoBean")
@LocalBean
@ViewAccessScoped
public class PesquisarReuniaoCPCJBean extends AbstractPppBean implements Serializable {

    private static final int PAGE_SIZE = 10;
    public static final String TAB_MAPA = "tabMapa";

    private int currentTab = 0;
    private ScheduleModel gerenciadorEventos;
    private FiltroPesquisaReuniaoDTO filtroPesquisa;
    private Collection<DetalheDominioVO> modalidades;
    private Collection<DetalheDominioVO> tipos;
    private ReuniaoPCJDataModel dataModel;
    private List<SalaReuniaoDTO> salasReuniao = new ArrayList<>();
    private ResultadoReuniaoSelecionadoMapaDTO reuniaoSelecionada;

    @Inject
    private ReuniaoCPCJService reuniaoCPCJService;

    @Inject
    private PCJServiceDomain pcjServiceDomain;

    @PostConstruct
    public void init() {
        gerenciadorEventos = new DefaultScheduleModel();
        modalidades = pcjServiceDomain.getDominioHolderModalidadesReuniao().get().getDetalhes().values();
        tipos = pcjServiceDomain.getDominioHolderTiposReuniao().get().getDetalhes().values();
        mensagemSemResultado = EFETUE_PESQUISA;
    }

    @URLAction(mappingId = PCJSubsystem.OP_PESQUISAR_REUNIAO, onPostback = false)
    public void loadPesquisarReuniaoCPCJ() {
        try {
            filtroPesquisa = inicialiazarFiltroPesquisa();
            dataModel = new ReuniaoPCJDataModel(entityManager, filtroPesquisa, getPerfilDTO());
            dataModel.load(0, PAGE_SIZE, null, null, null);
            salasReuniao = carregaSalasCPCJ();
            checkResultadoPesquisaVazioAtualizaMensagem();
        } catch (Exception e) {
            JsfMessageUtil.mostraMensagemErro(e.getMessage());
        }
    }

    private FiltroPesquisaReuniaoDTO inicialiazarFiltroPesquisa() {
        FiltroPesquisaReuniaoDTO novoFiltro = new FiltroPesquisaReuniaoDTO();

        LocalDate hoje = LocalDate.now();
        DateTime dataInicio = hoje.toDateTimeAtStartOfDay();
        DateTime dataFim = dataInicio.plusDays(30);

        novoFiltro.setDataInicio(dataInicio.toDate());
        novoFiltro.setDataFim(dataFim.toDate());
        novoFiltro.setEstados(Collections.singletonList(1));

        return novoFiltro;
    }

    public void pesquisar() {
        LoggingHelper.logEntrada(LOGGER, filtroPesquisa);
        dataModel.setFiltroPesquisaReuniao(filtroPesquisa);
        dataModel.load(0, PAGE_SIZE);
        checkResultadoPesquisaVazioAtualizaMensagem();
        LoggingHelper.logSaida(LOGGER);
    }

    public void limparPesquisa() {
        filtroPesquisa.limpar();
        dataModel = new ReuniaoPCJDataModel(entityManager, filtroPesquisa, getPerfilDTO());
    }

    private List<SalaReuniaoDTO> carregaSalasCPCJ() {
        PerfilDTO perfilDTO = pcjSubsystem.getLogin().getPerfilDTO();
        LoggingHelper.logEntrada(LOGGER, Level.DEBUG, perfilDTO);
        return reuniaoCPCJService.carregaSalasCPCJ(perfilDTO);
    }

    private void checkResultadoPesquisaVazioAtualizaMensagem() {
        if(dataModel.isNoResults()) {
            LoggingHelper.logEntrada(LOGGER, Level.DEBUG, SEM_RESULTADO);
            mensagemSemResultado = SEM_RESULTADO;
        }
    }

    public void onTabChange(TabChangeEvent event) {
        String tabId = (event != null && event.getTab() != null) ? event.getTab().getId() : "";
        LoggingHelper.logEntrada(LOGGER, tabId);
        if(TAB_MAPA.equals(tabId)) {
            carregaListaEventosMapa(new Date(), new Date());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void carregaListaEventosMapa(Date dataInicioParam, Date dataFimParam) {
        Calendar cal = Calendar.getInstance();

        cal.setTime(dataInicioParam != null ? dataInicioParam : new Date());
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date dataInicio = cal.getTime();

        cal.setTime(dataFimParam != null ? dataFimParam : new Date());
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date dataFim = cal.getTime();

        filtroPesquisa = new FiltroPesquisaReuniaoDTO();
        filtroPesquisa.setDataInicio(dataInicio);
        filtroPesquisa.setDataFim(dataFim);
        filtroPesquisa.adicionaIdComissao(getPerfilDTO());

        List<ResultadoPesquisaReuniaoDTO> resultadoEventosMaps = reuniaoCPCJService.retornaEventosPorPeridodo(filtroPesquisa);

        for (ResultadoPesquisaReuniaoDTO eventoMapa : resultadoEventosMaps) {
            this.gerenciadorEventos.addEvent(getReuniaoScheduleEvent(eventoMapa));
        }
    }

    public void onViewChange() {
        this.gerenciadorEventos.clear();
        final FacesContext context = FacesContext.getCurrentInstance();
        final Map<String, String> params = context.getExternalContext().getRequestParameterMap();
        LoggingHelper.logEntrada(LOGGER, params);

        String dataInicioParam = params.get("dataInicio");
        String dataFimParam = params.get("dataFim");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date dataInicio = dataInicioParam != null ? sdf.parse(dataInicioParam) : null;
            Date dataFim = dataFimParam != null ? sdf.parse(dataFimParam) : null;
            carregaListaEventosMapa(dataInicio, dataFim);
        } catch (ParseException e) {
            carregaListaEventosMapa(null, null);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private ReuniaoScheduleEvent getReuniaoScheduleEvent(ResultadoPesquisaReuniaoDTO resultadoPesquisaReuniaoDTO) {
        final LocalDate dataFim = getDataFimAdicionandoUmDia(resultadoPesquisaReuniaoDTO);
        return new ReuniaoScheduleEvent(
                resultadoPesquisaReuniaoDTO.getId(),
                resultadoPesquisaReuniaoDTO.getAssunto(),
                resultadoPesquisaReuniaoDTO.getDataInicio(),
                dataFim.toDate(),
                resultadoPesquisaReuniaoDTO);
    }

    private LocalDate getDataFimAdicionandoUmDia(ResultadoPesquisaReuniaoDTO resultadoPesquisaReuniaoDTO) {
        //Necessario adicionar 1 dia a mais, devido ao componente de Agenda nao incluir o ultimo dia do evento
        LocalDate dataFim = new LocalDate(resultadoPesquisaReuniaoDTO.getDataFim());
        return dataFim.plusDays(1);
    }

    public void onEventSelect(SelectEvent selectEvent) {
        ReuniaoScheduleEvent reuniaoScheduleEvent = (ReuniaoScheduleEvent) selectEvent.getObject();
        LoggingHelper.logEntrada(LOGGER, Level.DEBUG, reuniaoScheduleEvent);
        reuniaoSelecionada = reuniaoCPCJService.consultaReuniaoPorId(reuniaoScheduleEvent.getCustomId());
    }

    /*Date Select*/
    public void onDateSelect(SelectEvent selectEvent) {}

    public void setFiltroPesquisa(FiltroPesquisaReuniaoDTO filtroPesquisa) {
        this.filtroPesquisa = filtroPesquisa;
    }

    public FiltroPesquisaReuniaoDTO getFiltroPesquisa() {
        return filtroPesquisa;
    }

    public Collection<DetalheDominioVO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(Collection<DetalheDominioVO> modalidades) {
        this.modalidades = modalidades;
    }

    public void setTipos(Collection<DetalheDominioVO> tipos) {
        this.tipos = tipos;
    }

    public Collection<DetalheDominioVO> getTipos() {
        return tipos;
    }

    public void setSalasReuniao(List<SalaReuniaoDTO> salasReuniao) {
        this.salasReuniao = salasReuniao;
    }

    public List<SalaReuniaoDTO> getSalasReuniao() {
        return salasReuniao;
    }

    public ReuniaoPCJDataModel getDataModel() {
        return dataModel;
    }

    public int getPageSize() {
        return PAGE_SIZE;
    }

    public String getRowsPerPageTemplate() {
        return Configuration.DataTable.ROWS_PER_PAGE_TEMPLATE.value();
    }

    public String getPaginatorPosition() {
        return Configuration.DataTable.PAGINATOR_POSITION.value();
    }

    public int getCurrentTab() {
        return currentTab;
    }

    public void setCurrentTab(int currentTab) {
        this.currentTab = currentTab;
    }

    public ScheduleModel getGerenciadorEventos() {
        return gerenciadorEventos;
    }

    public void setGerenciadorEventos(ScheduleModel gerenciadorEventos) {
        this.gerenciadorEventos = gerenciadorEventos;
    }

    public void setReuniaoSelecionada(ResultadoReuniaoSelecionadoMapaDTO reuniaoSelecionada) {
        this.reuniaoSelecionada = reuniaoSelecionada;
    }

    public ResultadoReuniaoSelecionadoMapaDTO getReuniaoSelecionada() {
        return reuniaoSelecionada;
    }
}
