package pt.segsocial.pcj.ppp.jpa.dao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.gvr.api.util.StringUtil;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.pae.jpa.dao.PCJDao;
import pt.segsocial.pcj.ppp.dto.ConsultaReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.FiltroPesquisaReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.ResultadoPesquisaReuniaoDTO;
import pt.segsocial.pcj.ppp.dto.ResultadoReuniaoSelecionadoMapaDTO;
import pt.segsocial.pcj.ppp.enums.TipoConvocatoriaEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;

import javax.inject.Inject;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static pt.segsocial.pcj.core.util.Constants.MODALIDADE_RESTRITA;

public class ReuniaoPCJDAO extends PCJDao<ReuniaoPCJ, Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Logger LOGGER = LoggerFactory.getLogger(ReuniaoPCJDAO.class);

    public static final String PAGE_SIZE = "pageSize";
    public static final String OFFSET = "offset";
    public static final String ID_COMISSAO = "idComissao";
    private static final String BASE_QUERY =
            "FROM pcj.REUNIAO_PCJ rp " +
                    "LEFT JOIN pcj.SALA_REUNIAO_PCJ srp ON srp.ID_SALA_REUNIAO_PCJ = rp.ID_SALA_REUNIAO_PCJ " +
                    "LEFT JOIN pcj.CONVOCATORIA_ALARGADA_PCJ cap ON cap.ID_REUNIAO_PCJ = rp.ID_REUNIAO_PCJ " +
                    "LEFT JOIN pcj.LOCAL_REUNIAO_PCJ lrp ON lrp.ID_LOCAL_REUNIAO_PCJ  = rp.ID_LOCAL_REUNIAO_PCJ ";
    private static final String CONSULTA_REUNIOES_PAGINADAS =
            "SELECT " +
                    "rp.ID_REUNIAO_PCJ AS id, " +
                    "rp.DATA_INICIO AS dataInicio, " +
                    "rp.DATA_FIM AS dataFim, " +
                    "rp.HORA_INICIO AS horaInicio, " +
                    "rp.HORA_FIM AS horaFim, " +
                    "srp.NOME AS sala, " +
                    "rp.ASSUNTO AS assunto, " +
                    "rp.CODIGO_MODALIDADE AS modalidade " +
                    BASE_QUERY +
                    "WHERE rp.ID_COMISSAO_PCJ = :idComissao ";
    private static final String CONSULTA_REUNIOES_MAPA =
            "SELECT " +
                    "rp.ID_REUNIAO_PCJ AS id, " +
                    "rp.DATA_INICIO AS dataInicio, " +
                    "rp.DATA_FIM AS dataFim, " +
                    "rp.ASSUNTO AS assunto, " +
                    "srp.NOME AS sala " +
                    BASE_QUERY +
                    "WHERE rp.ID_COMISSAO_PCJ = :idComissao ";
    private static final String CONSULTA_REUNIAO =
            "SELECT " +
                    "rp.ID_REUNIAO_PCJ AS id, " +
                    "rp.DATA_INICIO AS dataInicio, " +
                    "rp.DATA_FIM AS dataFim, " +
                    "rp.CODIGO_MODALIDADE AS modalidade, " +
                    "rp.CODIGO_TIPO as tipo, " +
                    "lrp.TITULO as localReuniao, " +
                    "srp.NOME as nomeSala, " +
                    "rp.ASSUNTO AS assunto " +
                    BASE_QUERY +
                    "WHERE rp.ID_REUNIAO_PCJ = :idReuniaoPcj ";

    private static final String CONSULTAR_REUNIAO =
            "SELECT " +
                    "rp.ID_REUNIAO_PCJ AS id, " +
                    "rp.CODIGO_MODALIDADE AS modalidade, " +
                    "rp.CODIGO_TIPO as tipo, " +
                    "rp.ID_Elemento_PCJ as responsavel, " +
                    "lrp.TITULO as tituloLocal, " +
                    "lrp.DESCRICAO as descricaoLocal," +
                    "srp.NOME as nomeSala, " +
                    "rp.DATA_INICIO AS dataInicio, " +
                    "rp.DATA_FIM AS dataFim, " +
                    "rp.HORA_INICIO AS horaInicio, " +
                    "rp.HORA_FIM AS horaFim, " +
                    "rp.ID_Comissao_PCJ as idComissao, " +
                    "rp.ASSUNTO AS assunto ," +
                    "rp.DESCRICAO AS descricao ," +
                    "rp.ATIVO AS ativo ," +
                    "rp.CODIGO_TIPO_CONVOCATORIA AS idTipoConvocatoria, " +
                    "cap.CONVOCATORIA_ENVIADA AS convocatoriaEnviada " +

                    BASE_QUERY +
                    "WHERE rp.ID_REUNIAO_PCJ = :idReuniaoPcj ";

    private static final String CONSULTA_QUANTIDADE_REUNIAO =
            "SELECT COUNT(rp.ID_REUNIAO_PCJ) " +
                    BASE_QUERY +
                    "WHERE rp.ID_COMISSAO_PCJ = :idComissao ";

    @Inject
    public ReuniaoPCJDAO(EntityManager entityManager) {
        super(entityManager, ReuniaoPCJ.class);
    }

    public PaginacaoResultado<ResultadoPesquisaReuniaoDTO> pesquisaReunioesComPaginacao(FiltroPesquisaReuniaoDTO filtro, int first, int pageSize, String sortField, String sortOrder) {
        StringBuilder sql = constroiConsultaDeEventos(filtro, sortField, sortOrder);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        query.setParameter(PAGE_SIZE, pageSize);
        query.setParameter(OFFSET, first);
        query.setParameter(ID_COMISSAO, filtro.getIdComissao());
        defineParametros(query, filtro);
        List<Object[]> results = query.getResultList();
        List<ResultadoPesquisaReuniaoDTO> resultadosPesquisa = new ArrayList<>();
        for (Object[] result : results) {
            resultadosPesquisa.add(new ResultadoPesquisaReuniaoDTO(
                    (BigDecimal) result[0],
                    (Date) result[1],
                    (Date) result[2],
                    (Date) result[3],
                    (Date) result[4],
                    (String) result[5],
                    (String) result[6],
                    (String) result[7]
            ));
        }
        int totalRegistros = contarTotalReunioes(filtro);
        return new PaginacaoResultado<>(resultadosPesquisa, totalRegistros, first, pageSize);
    }

    public List<ResultadoPesquisaReuniaoDTO> pesquisaReunioesMapa(FiltroPesquisaReuniaoDTO filtro) {
        StringBuilder sql = new StringBuilder(CONSULTA_REUNIOES_MAPA);
        adicionaFiltrosNaConsulta(sql, filtro);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        query.setParameter("idComissao", filtro.getIdComissao());
        defineParametros(query, filtro);
        List<Object[]> results = query.getResultList();
        List<ResultadoPesquisaReuniaoDTO> resultadosPesquisa = new ArrayList<>();
        for (Object[] result : results) {
            resultadosPesquisa.add(new ResultadoPesquisaReuniaoDTO(
                    (BigDecimal) result[0],
                    (Date) result[1],
                    (Date) result[2],
                    (String) result[3],
                    (String) result[4]
            ));
        }
        return resultadosPesquisa;
    }

    public ResultadoReuniaoSelecionadoMapaDTO consultaReuniaoPorId(Long idReuniao) {
        Query query = getEntityManager().createNativeQuery(CONSULTA_REUNIAO);
        query.setParameter("idReuniaoPcj", idReuniao);

        try {
            Object[] result = (Object[]) query.getSingleResult();
            return mapToResultadoReuniaoSelecionadoMapaDTO(result);
        } catch (NoResultException e) {
            return new ResultadoReuniaoSelecionadoMapaDTO();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao consultar reunião por ID", e);
        }
    }

    private ResultadoReuniaoSelecionadoMapaDTO mapToResultadoReuniaoSelecionadoMapaDTO(Object[] result) {
        return new ResultadoReuniaoSelecionadoMapaDTO(
                (BigDecimal) result[0],
                (Date) result[1],
                (Date) result[2],
                (String) result[3],
                (String) result[4],
                (String) result[5],
                (String) result[6],
                (String) result[7]
        );
    }

    private StringBuilder constroiConsultaDeEventos(FiltroPesquisaReuniaoDTO filtros, String sortField, String sortOrder) {
        StringBuilder sql = new StringBuilder(CONSULTA_REUNIOES_PAGINADAS);
        adicionaFiltrosNaConsulta(sql, filtros);
        adicionaOrdenacaoNaConsulta(sql, sortField, sortOrder);
        sql.append(" OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY ");
        return sql;
    }

    private void adicionaFiltrosNaConsulta(StringBuilder sql, FiltroPesquisaReuniaoDTO filtros) {
        if (filtros.existemFiltrosValidos()) {
            adicionaFiltro(sql, "LOWER(rp.ASSUNTO)", filtros.getAssunto(), "LIKE", "assunto");
            adicionaFiltro(sql, "TRUNC(rp.DATA_INICIO)", filtros.getDataInicio(), ">=", "dataInicio");
            adicionaFiltro(sql, "TRUNC(rp.DATA_FIM)", filtros.getDataFim(), "<=", "dataFim");
            adicionaFiltro(sql, "rp.CODIGO_MODALIDADE", filtros.getModalidade(), "=", "codigoModalidade");
            adicionaFiltro(sql, "rp.CODIGO_TIPO", filtros.getTipo(), "=", "codigoTipo");
            adicionaFiltro(sql, "rp.ID_SALA_REUNIAO_PCJ", filtros.getIdSala(), "=", "idSalaReuniao");
            adicionaFiltro(sql, "rp.ATIVO", filtros.getEstados(), "IN", "estados");
            adicionaFiltro(sql, "rp.CODIGO_TIPO_CONVOCATORIA", TipoConvocatoriaEnum.codigosConvocatorias(filtros.getConvocatorias()), "IN", "convocatorias");
        } else {
            sql.append(" AND TRUNC(rp.DATA_INICIO) >= TRUNC(CURRENT_DATE) ");
            sql.append(" AND rp.ATIVO = 1 ");
        }
    }

    private void adicionaFiltro(StringBuilder sql, String campo, Object valor, String operador, String parametro) {
        if (valor != null && !valor.toString().isEmpty()) {
            if (valor instanceof List) {
                List<?> lista = (List<?>) valor;
                if (!lista.isEmpty()) {
                    sql.append(" AND ").append(campo).append(" IN (:").append(parametro).append(") ");
                }
            } else {
                if (operador.equalsIgnoreCase("LIKE")) {
                    sql.append(" AND ").append(campo).append(" LIKE :").append(parametro).append(" ");
                } else if (operador.equalsIgnoreCase("IN")) {
                    sql.append(" AND ").append(campo).append(" IN (:").append(parametro).append(") ");
                } else {
                    sql.append(" AND ").append(campo).append(" ").append(operador).append(" :").append(parametro).append(" ");
                }
            }
        }
    }

    private void adicionaOrdenacaoNaConsulta(StringBuilder sql, String sortField, String sortOrder) {
        if (StringUtil.isNotNullOrEmpty(sortField)) {
            sql.append(" ORDER BY ").append(sortField).append(" ");
            sql.append(sortOrder != null && sortOrder.equalsIgnoreCase("ASCENDING") ? "ASC" : "DESC");
        } else {
            sql.append(" ORDER BY rp.DATA_INICIO ASC ");
        }
    }

    private void defineParametros(Query query, FiltroPesquisaReuniaoDTO filtros) {
        adicionaParametro(query, "assunto", filtros.getAssunto(), true);
        adicionaParametro(query, "dataInicio", filtros.getDataInicio(), false);
        adicionaParametro(query, "dataFim", filtros.getDataFim(), false);
        adicionaParametro(query, "codigoModalidade", filtros.getModalidade(), false);
        adicionaParametro(query, "codigoTipo", filtros.getTipo(), false);
        adicionaParametro(query, "idSalaReuniao", filtros.getIdSala(), false);
        adicionaParametro(query, "estados", filtros.getEstados(), false);
        adicionaParametro(query, "convocatorias", TipoConvocatoriaEnum.codigosConvocatorias(filtros.getConvocatorias()), false);
    }

    private void adicionaParametro(Query query, String parametro, Object valor, boolean like) {
        if (valor != null && !valor.toString().isEmpty()) {
            if (valor instanceof List) {
                List<?> lista = (List<?>) valor;
                if (!lista.isEmpty()) {
                    query.setParameter(parametro, lista);
                }
            } else {
                query.setParameter(parametro, like ? "%" + valor.toString().toLowerCase() + "%" : valor);
            }
        }
    }

    private int contarTotalReunioes(FiltroPesquisaReuniaoDTO filtro) {
        StringBuilder sql = new StringBuilder(CONSULTA_QUANTIDADE_REUNIAO);
        adicionaFiltrosNaConsulta(sql, filtro);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        query.setParameter("idComissao", filtro.getIdComissao());
        defineParametros(query, filtro);
        return ((Number) query.getSingleResult()).intValue();
    }

    public ConsultaReuniaoDTO consultarReuniaoPorId(Long idReuniao) {
        Query query = getEntityManager().createNativeQuery(CONSULTAR_REUNIAO);
        query.setParameter("idReuniaoPcj", idReuniao);

        try {
            Object[] result = (Object[]) query.getSingleResult();
            return mapToConsultaReuniaoDTO(result);
        } catch (NoResultException e) {
            return new ConsultaReuniaoDTO();
        } catch (Exception e) {
            throw new RuntimeException("Erro ao consultar reunião por ID", e);
        }
    }

    public ReuniaoPCJ buscaReuniaoPorId(Long id) {
        return super.find(id);
    }

    private ConsultaReuniaoDTO mapToConsultaReuniaoDTO(Object[] result) {
        BigDecimal campoConvocatoriaEnviada = (BigDecimal) result[16];
        boolean convocatoriaEnviada = campoConvocatoriaEnviada != null && campoConvocatoriaEnviada.longValue() == 1L;
        return new ConsultaReuniaoDTO(
                (BigDecimal) result[0],
                (String) result[1],
                (String) result[2],
                (BigDecimal) result[3],
                (String) result[4],
                (String) result[5],
                (String) result[6],
                (Date) result[7],
                (Date) result[8],
                (Timestamp) result[9],
                (Timestamp) result[10],
                (BigDecimal) result[11],
                (String) result[12],
                (String) result[13],
                (BigDecimal) result[14],
                (String) result[15],
                convocatoriaEnviada
        );
    }

    public List<ReuniaoPCJ> buscarReunioesPorResponsavel(Long idElementoResponsavel) {
        String sql = "SELECT r FROM ReuniaoPCJ r WHERE r.elemento.id = :idElementoResponsavel";
        try {
            return getEntityManager().createQuery(sql, ReuniaoPCJ.class)
                    .setParameter("idElementoResponsavel", idElementoResponsavel)
                    .getResultList();
        } catch(NoResultException e) {
            return null;
        }
    }

    public List<ReuniaoPCJ> buscarReunioesDataAmanha() {
        try {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.DAY_OF_MONTH, 1);
            Date dataInicio = cal.getTime();

            cal.add(Calendar.DAY_OF_MONTH, 1);
            Date dataFim = cal.getTime();

            String jpql = "SELECT r FROM ReuniaoPCJ r WHERE r.dataInicio >= :inicio and r.dataInicio < :fim";
            TypedQuery<ReuniaoPCJ> query = getEntityManager().createQuery(jpql, ReuniaoPCJ.class);
            query.setParameter("inicio", dataInicio);
            query.setParameter("fim", dataFim);

            return query.getResultList();
        } catch (PersistenceException e) {
            LOGGER.error("Erro ao consultar reuniões: ", e);
            return Collections.emptyList();
        } catch (Exception e) {
            LOGGER.error("Erro inesperado ao buscar reuniões: ", e);
            return Collections.emptyList();
        }
    }

    public ReuniaoPCJ buscarProximaReuniaoRestritaDoTipoOrdinaria(Long idComissao) {
        try {
            String jpql = "SELECT r FROM ReuniaoPCJ r WHERE " +
                    "r.dataInicio >= :dataAtual AND " +
                    "r.codigoModalidade = :modalidade AND " +
                    "comissao.id = :idComissao AND " +
                    "tipoConvocatoria = :tipoConvocatoria " +
                    "AND r.ativo = 1 " +
                    "ORDER BY r.dataInicio ASC";

            TypedQuery<ReuniaoPCJ> query = getEntityManager().createQuery(jpql, ReuniaoPCJ.class);
            query.setParameter("dataAtual", PCJDateUtil.retornaDateSemHora());
            query.setParameter("modalidade", MODALIDADE_RESTRITA);
            query.setParameter("idComissao", idComissao);
            query.setParameter("tipoConvocatoria", TipoConvocatoriaEnum.ORDINARIA.getCodigoConvocatoria());
            query.setMaxResults(1);

            return query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        } catch (Exception e) {
            LOGGER.error("Erro ao buscar última reunião restrita: ", e);
            return null;
        }
    }
}
