package pt.segsocial.pcj.ppp.jpa.dao.comunicacao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.gvr.api.util.StringUtil;
import pt.segsocial.pcj.pae.jpa.dao.PCJDao;
import pt.segsocial.pcj.ppp.dto.FiltroPesquisaComunicacaoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ResultadoPesquisaComunicacaoDTO;
import pt.segsocial.pcj.ppp.jpa.dao.PaginacaoResultado;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;
import pt.segsocial.pcj.ppp.jpa.factory.FiltroComunicacaoFactory;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ComunicacaoPesquisaDAO extends PCJDao<Comunicacao, Long> implements Serializable {
    public static final String PAGE_SIZE = "pageSize";
    public static final String OFFSET = "offset";
    protected static final Logger LOGGER = LoggerFactory.getLogger(ComunicacaoPesquisaDAO.class);
    private static final long serialVersionUID = 1L;
    private static final String BASE_QUERY =
            "FROM pcj.COMUNICACAO_PPP c " +
                    "LEFT JOIN pcj.COMUNICACAO_CRIANCA_PPP  cc ON cc.ID_COMUNICACAO_PPP = c.ID_COMUNICACAO_PPP  " +
                    "LEFT JOIN pcj.MORADA  m ON m.ID_MORADA = cc.ID_MORADA " +
                    "LEFT JOIN pcj.ELEMENTO_PPP e ON e.ID_CRIANCA_PPP = cc.ID_CRIANCA_PPP  " +
                    "LEFT JOIN pcj.SITUACAO_PERIGO_PPP  sp ON sp.ID_COMUNICACAO_PPP = c.ID_COMUNICACAO_PPP  " +
                    "LEFT JOIN pcj.CONVIDADO_PCJ  cp ON cp.ID_CONVIDADO_PCJ = c.ID_CONVIDADO_PCJ  " +
                    "LEFT JOIN pcj.BLOCO_COMUNICACAO_RESTRITA bcr ON bcr.ID_COMUNICACAO_PPP = c.ID_COMUNICACAO_PPP ";
    private static final String CONSULTA_PAGINADAS =
            "SELECT " +
                    "DISTINCT(c.ID_COMUNICACAO_PPP) AS id, " +
                    "c.NUMERO_COMUNICACAO AS numero, " +
                    "cc.NOME AS nomeCrianca, " +
                    "  (select count(1) from pcj.situacao_perigo_ppp p where p.id_comunicacao_ppp = c.id_comunicacao_ppp) AS situacoesReportadas, " +
                    "CASE " +
                    "WHEN c.URGENTE IS NULL THEN 0 " +
                    "ELSE c.URGENTE " +
                    "END AS urgente, " +
                    "c.CODIGO_ESTADO AS estado, " +
                    "CASE " +
                    "WHEN c.COMUNICACAO_TRANSFERIDA IS NULL THEN 0 " +
                    "ELSE c.COMUNICACAO_TRANSFERIDA " +
                    "END AS transferida, " +
                    "CASE " +
                    "WHEN bcr.ID_REUNIAO_PCJ IS NOT NULL THEN 1 " +
                    "ELSE 0 " +
                    "END AS temReuniaoVinculada " +
                    BASE_QUERY +
                    "WHERE 1=1 ";
    private static final String CONSULTA_QUANTIDADE =
            "SELECT COUNT(cc.ID_COMUNICACAO_PPP ) " +
                    BASE_QUERY +
                    "WHERE 1=1 ";

    private final FiltroComunicacaoFactory filtroRepo = new FiltroComunicacaoFactory();


    @Inject
    public ComunicacaoPesquisaDAO(EntityManager entityManager) {
        super(entityManager, Comunicacao.class);
    }

    public PaginacaoResultado<ResultadoPesquisaComunicacaoDTO> pesquisaComPaginacao(FiltroPesquisaComunicacaoDTO filtro, int first, int pageSize,
                                                                                    String sortField, String sortOrder) {
        try {
            StringBuilder sql = new StringBuilder(CONSULTA_PAGINADAS);
            Set<String> parametrosIncluidos = new HashSet<>();
            int totalRegistros = contarTotal(filtro, parametrosIncluidos);
            if (totalRegistros > 0) {
                filtroRepo.adicionaFiltrosNaConsulta(sql, filtro, parametrosIncluidos);
                adicionaOrdenacaoNaConsulta(sql, sortField, sortOrder);

                sql.append(" OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY ");

                Query query = getEntityManager().createNativeQuery(sql.toString());
                query.setParameter(OFFSET, first);
                query.setParameter(PAGE_SIZE, pageSize);

                filtroRepo.defineParametrosSePresentes(query, filtro, parametrosIncluidos);

                List<Object[]> results = query.getResultList();
                List<ResultadoPesquisaComunicacaoDTO> resultadosPesquisa = new ArrayList<>();
                for (Object[] result : results) {
                    resultadosPesquisa.add(new ResultadoPesquisaComunicacaoDTO(
                            (BigDecimal) result[0],
                            (String) result[1],
                            (String) result[2],
                            (BigDecimal) result[3],
                            ((BigDecimal) result[4]).intValue() == 1,
                            (String) result[5],
                            ((BigDecimal) result[6]).intValue() == 1,
                            ((BigDecimal) result[7]).intValue() == 1

                    ));
                }
                return new PaginacaoResultado<>(resultadosPesquisa, totalRegistros, first, pageSize);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new PaginacaoResultado<>(new ArrayList<ResultadoPesquisaComunicacaoDTO>(), 0, 0, 0);
    }

    private int contarTotal(FiltroPesquisaComunicacaoDTO filtro, Set<String> parametrosIncluidos) {
        try {
            StringBuilder sql = new StringBuilder(CONSULTA_QUANTIDADE);

            filtroRepo.adicionaFiltrosNaConsulta(sql, filtro, parametrosIncluidos);
            Query query = getEntityManager().createNativeQuery(sql.toString());

            filtroRepo.defineParametrosSePresentes(query, filtro, parametrosIncluidos);

            return ((Number) query.getSingleResult()).intValue();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return -1;
    }

    private void adicionaOrdenacaoNaConsulta(StringBuilder sql, String sortField, String sortOrder) {
        if (StringUtil.isNotNullOrEmpty(sortField)) {
            sql.append(" ORDER BY ").append(sortField).append(" ");
            sql.append("ASCENDING".equalsIgnoreCase(sortOrder) ? "ASC" : "DESC");
        } else {
            sql.append(" ORDER BY c.URGENTE DESC, c.CODIGO_ESTADO ASC, c.ID_COMUNICACAO_PPP ASC ");
        }
    }
}
