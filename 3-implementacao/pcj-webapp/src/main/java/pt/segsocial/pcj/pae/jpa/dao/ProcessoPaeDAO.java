package pt.segsocial.pcj.pae.jpa.dao;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PcjMessages;
import pt.segsocial.pcj.core.util.PesquisaUtil;
import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;
import pt.segsocial.pcj.pae.jpa.entity.*;
import pt.segsocial.pcj.pae.jpa.filter.AtribuirProcessoFilter;
import pt.segsocial.pcj.pae.jpa.filter.PesquisaProcessoFilter;
import pt.segsocial.pcj.ppp.dto.HomepageDTO;
import pt.segsocial.pcj.ppp.jpa.dao.ElementoDAO;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;

public class ProcessoPaeDAO extends PCJDao<ProcessoPae, Long> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessoPaeDAO.class);
    private static final String SELECT_NEXT_VAL = "SELECT NVL(MAX(NUMERO_PROCESSO),0) + 1 FROM PCJ.PROCESSO_PAE";
    private static final String SELECT_LAST_VAL_DATE = "SELECT DATA_INICIO FROM PCJ.PROCESSO_PAE ORDER BY DATA_INICIO DESC OFFSET 1 ROWS FETCH NEXT 1 ROWS ONLY";

    private static final String COUNT_QUERY = "SELECT COUNT(p) FROM ProcessoPae p";
    private static final String JOIN_PROCESSO_REQUERIMENTO = " , RequerimentoPae r";
    private static final String COUNT_QUERY_JOIN_WHERE_ID_COMISSAO = "SELECT COUNT(p) FROM ProcessoPae p, Elemento e " +
            "  JOIN e.comissao c " +
            "  JOIN c.competencias t " +
            "  WHERE c.id = :idComissao " +
            "  AND c.ativo = 1 " +
            "  AND c.id = t.comissao.id " +
            "  AND t.territorioPCJ.id = p.territorioPCJ.id";
    private static final String WHERE_NISS_ENTIDADE = " WHERE p.nissEntidade = :niss ";
    private static final String COMUNICACAO = "comunicacao";
    private static final String ESTADOS = "estados";
    private String QUERY_HOME_PRES_TOTAL_E_ARQUIVADOS = "select Tot.Total, Arq.Arquivado, (Ana.Analisado - DPend.Deferido_Pendencia - PAssin.Pendente_Assinatura) as Analisado, PAna.Por_Analisar, EAna.Em_Analise, (DPend.Deferido_Pendencia+PAssin.Pendente_Assinatura) as Pendentes ";
    private String FROM_HOME_TOTAL = " from (select count(1) as Total from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado not in ('RASCUNHO', 'ANULADO')) Tot, ";
    private String QUERY_ARQUIVADOS = " (select count(1) as Arquivado from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado = 'ARQUIVADO') Arq, ";
    private String QUERY_ANALISADOS = " (select count(1) as Analisado from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado IN ('REVOGADO','DEFERIDO','INDEFERIDO')) Ana, ";
    private String QUERY_POR_ANALISAR = " (select count(1) as Por_Analisar from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado ='POR_ANALISAR') PAna, ";
    private String QUERY_EM_ANALISE = " (select count(1) as Em_Analise from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado ='EM_ANALISE') EAna, ";
    private String QUERY_DEFERIDO_PENDENCIA = " (select count(distinct (p.numero_processo)) as Deferido_Pendencia  from pcj.processo_pae p, pcj.requerimento_pae r  where p.id_processo_pae = r.id_processo_pae  and r.estado_requerimento is null and r.tipo in ('ALTERACAO_EDUCACAO','ALTERACAO_VIGILANCIA', 'ALTERACAO_PARTICIPACAO', 'ALTERACAO_HORARIO','RENOVACAO')  and p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado ='DEFERIDO' ) DPend, ";
    private String QUERY_PENDENCIA_ASSINATURA = " (select count(distinct (p.numero_processo)) as Pendente_Assinatura  ";
    private String FROM_HOME_REQ = " from pcj.processo_pae p, pcj.requerimento_pae r ";
    private String WHERE_HOME = " where p.id_processo_pae = r.id_processo_pae ";
    private String QUERY_HOME_MEMBROS_TOTAL_E_ARQUIVADOS = " select Tot.Total, Arq.Arquivado, (Ana.Analisado - DPend.Deferido_Pendencia -  PAssin.Pendente_Assinatura) as Analisado, EAna.Em_Analise, (DPend.Deferido_Pendencia+PAssin.Pendente_Assinatura) as Pendentes  ";
    private String FROM_HOME_MEMBRO_TOTAL = " from (select count(1) as Total from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and id_elemento_pcj = :idElemento and estado not in ('RASCUNHO', 'ANULADO')) Tot, ";
    private String QUERY_MEMBRO_ARQUIVADOS = " (select count(1) as Arquivado from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado = 'ARQUIVADO' and id_elemento_pcj = :idElemento ) Arq, ";
    private String QUERY_MEMBRO_ANALISADOS = " (select count(1) as Analisado from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado IN ('REVOGADO','DEFERIDO','INDEFERIDO') and id_elemento_pcj = :idElemento ) Ana, ";
    private String QUERY_MEMBRO_EM_ANALISE = " (select count(1) as Em_Analise from pcj.processo_pae p where p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado ='EM_ANALISE' and id_elemento_pcj = :idElemento ) EAna, ";
    private String QUERY_MEMBRO_DEFERIDO_PENDENCIA = " (select count(distinct (p.numero_processo)) as Deferido_Pendencia  from pcj.processo_pae p, pcj.requerimento_pae r  where p.id_processo_pae = r.id_processo_pae  and r.estado_requerimento is null and r.tipo in ('ALTERACAO_EDUCACAO','ALTERACAO_VIGILANCIA', 'ALTERACAO_PARTICIPACAO', 'ALTERACAO_HORARIO','RENOVACAO') and id_elemento_pcj = :idElemento  and p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado ='DEFERIDO' ) DPend, ";
    private String QUERY_MEMBRO_PENDENCIA_ASSINATURA = " (select count(distinct (p.numero_processo)) as Pendente_Assinatura  ";
    private String QUERY_COUNT_PROCESSO_PENDENTE_ASSINATURA = "select count(distinct (p.numero_processo))  from pcj.processo_pae p, pcj.requerimento_pae r  where p.id_processo_pae = r.id_processo_pae \n" +
            " and p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado in ('DEFERIDO', 'INDEFERIDO', 'REVOGADO')  \n" +
            " and  exists (select 1 from pcj.comprovativo co where co.id_requerimento_pae = r.id_requerimento_pae and ficheiro_assinado is null)";

    public ProcessoPaeDAO(EntityManager entityManager) {
        super(entityManager, ProcessoPae.class);
    }

    public List<ProcessoPae> findProcessoByFilter(AtribuirProcessoFilter filter) throws PCJException {
        StringBuilder hql = new StringBuilder();
        StringBuilder join = new StringBuilder(
                "  JOIN e.utilizadorPCJ u " +
                        "  JOIN e.comissao c " +
                        "  JOIN c.competencias t ");
        hql.append("SELECT distinct p FROM ProcessoPae p, Elemento e ");
        Map<String, Object> parameters = makeWhere(hql.append(join), filter);
        hql.append(" ORDER BY p.dataInicio ");

        return asList(hql.toString(), parameters, ProcessoPae.class);
    }

    private Map<String, Object> makeWhere(StringBuilder hql, AtribuirProcessoFilter filter) throws PCJException {
        Map<String, Object> parameters = new LinkedHashMap<>();
        hql.append(" WHERE 1=1 ");
        hql.append(" AND u.niss = :niss " +
                " AND e.utilizadorPCJ.id = u.id " +
                " AND c.ativo = 1" +
                " AND c.id = t.comissao.id" +
                " AND t.territorioPCJ.id = p.territorioPCJ.id");
        parameters.put("niss", filter.getNissEntidade().toString());
        if (filter.getEstado() != null) {
            hql.append(" AND p.estado = :estado ");
            parameters.put("estado", filter.getEstado());
        } else {
            hql.append(" AND p.estado NOT IN (:estados) ");
            parameters.put(ESTADOS, Arrays.asList(EstadoProcessoEnum.RASCUNHO, EstadoProcessoEnum.ARQUIVADO, EstadoProcessoEnum.TERMINADO, EstadoProcessoEnum.ANULADO));
        }

        if (filter.getNumeroProcesso() != null) {
            try {
                Long processNumber = PesquisaUtil.getProcessNumber(filter.getNumeroProcesso());
                hql.append(" AND p.numeroProcesso like concat(:processNumber, '%')");
                hql.append(" AND p.dataInicio BETWEEN");
                hql.append(" TO_DATE(:startDate").append(", '").append("yyyy-MM-dd").append("')");
                hql.append(" AND TO_DATE(:endDate").append(", '").append("yyyy-MM-dd").append("')");

                parameters.put("processNumber", processNumber);
                parameters.put("startDate", PesquisaUtil.getYearOfOpeningOfTheProcess(filter.getNumeroProcesso()).concat("-01-01"));
                parameters.put("endDate", PesquisaUtil.getYearOfOpeningOfTheProcess(filter.getNumeroProcesso()).concat("-12-31"));
            } catch (Exception e) {
                throw new PCJException(PcjMessages.getMessage("M010958"));
            }
        }

        if (filter.getCodigoServico() == null) {
            hql.append(" AND p.elemento IS NULL ");
        }

        if (filter.getCodigoGestor() != null) {
            Elemento elemento = new ElementoDAO(getEntityManager()).find(filter.getCodigoGestor());
            hql.append(" AND p.elemento = :elemento ");
            parameters.put("elemento", elemento);
        }

        return parameters;
    }

    private boolean ehDeferidoPendencia(PesquisaProcessoFilter filter) {
        return filter.getEstados().contains(EstadoProcessoEnum.DEFERIDO_PENDENCIA);
    }

    private boolean ehPendenteAssinatura(PesquisaProcessoFilter filter) {
        return filter.getEstados().contains(EstadoProcessoEnum.PENDENTE_ASSINATURA);
    }

    private boolean ehRevogado(PesquisaProcessoFilter filter) {
        return filter.getEstados().contains(EstadoProcessoEnum.REVOGADO);
    }

    private boolean ehDeferidoOuIndeferido(PesquisaProcessoFilter filter) {
        return filter.getEstados().contains(EstadoProcessoEnum.DEFERIDO) ||
               filter.getEstados().contains(EstadoProcessoEnum.INDEFERIDO);
    }
    public Long count(String query, Map<String, Object> parameters) {
        return (Long) getSingleResultWithParameters(query, parameters);
    }

    public List<ProcessoPae> findComunicacao(String nissCrianca, Date dataLimite, List<EstadoProcessoEnum> listaEstadoProcesso) {
        StringBuilder queryBuilder = new StringBuilder("SELECT p FROM ");
        queryBuilder.append(ProcessoPae.class.getName()).append(" p, ");
        queryBuilder.append(RequerimentoPae.class.getName()).append(" req, ");
        queryBuilder.append(Participacao.class.getName()).append(" par ");
        queryBuilder.append("WHERE req.processoPae.id = p.id ");
        queryBuilder.append("AND par.requerimentoPAE.id = req.id ");
        queryBuilder.append("AND p.comunicacao = :comunicacao ");
        queryBuilder.append("AND p.nissCrianca = :nissCrianca ");
        queryBuilder.append("AND par.dataInicioParticipacao >= :dataLimite ");
        queryBuilder.append("AND p.estado IN (:estados)");

        try {
            return this.getEntityManager()
                    .createQuery(queryBuilder.toString(), ProcessoPae.class)
                    .setParameter(COMUNICACAO, Boolean.TRUE)
                    .setParameter("nissCrianca", nissCrianca)
                    .setParameter("dataLimite", dataLimite)
                    .setParameter(ESTADOS, listaEstadoProcesso)
                    .getResultList();

        } catch (NoResultException e) {
            return new ArrayList<>();
        }
    }

    public List<ProcessoPae> findComunicacaoCriancaEstrangeira(String numeroIdentificacao, Date dataLimite, List<EstadoProcessoEnum> listaEstadoProcesso) {
        StringBuilder queryBuilder = new StringBuilder("SELECT p FROM ");
        queryBuilder.append(ProcessoPae.class.getName()).append(" p, ");
        queryBuilder.append(RequerimentoPae.class.getName()).append(" req, ");
        queryBuilder.append(CriancaEstrangeira.class.getName()).append(" me, ");
        queryBuilder.append(Participacao.class.getName()).append(" par ");
        queryBuilder.append("WHERE p.criancaEstrangeira.id = me.id ");
        queryBuilder.append("AND req.processoPae.id = p.id ");
        queryBuilder.append("AND par.requerimentoPAE.id = req.id ");
        queryBuilder.append("AND p.comunicacao = :comunicacao ");
        queryBuilder.append("AND me.pessoaEstrangeira.numeroIdentificacao = :numeroIdentificacao ");
        queryBuilder.append("AND par.dataInicioParticipacao >= :dataLimite ");
        queryBuilder.append("AND p.estado IN (:estados)");

        try {
            return this.getEntityManager()
                    .createQuery(queryBuilder.toString(), ProcessoPae.class)
                    .setParameter(COMUNICACAO, Boolean.TRUE)
                    .setParameter("numeroIdentificacao", numeroIdentificacao)
                    .setParameter("dataLimite", dataLimite)
                    .setParameter(ESTADOS, listaEstadoProcesso)
                    .getResultList();
        } catch (NoResultException e) {
            return new ArrayList<>();
        }
    }

    public Long countProcessoPaeByCodigoservicoAndNotEstado(Long codigoservico, EstadoProcessoEnum estado) {
        StringBuilder queryBuilder = new StringBuilder();

        queryBuilder.append(COUNT_QUERY_JOIN_WHERE_ID_COMISSAO);
        queryBuilder.append(" AND p.estado NOT IN :estado");
        Query query = getEntityManager().createQuery(queryBuilder.toString());

        if (null != codigoservico) {
            query.setParameter("codigo", codigoservico);
        }

        query.setParameter("estado", estado);

        return (Long) query.getSingleResult();
    }

    public Long countProcessoPaeByCodigoServicoAndEstado(Long idComissao, EstadoProcessoEnum estado) {
//        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        StringBuilder queryBuilder = new StringBuilder();

        queryBuilder.append(COUNT_QUERY_JOIN_WHERE_ID_COMISSAO);
        queryBuilder.append(" AND p.estado = :estado");
        Query query = getEntityManager().createQuery(queryBuilder.toString());
        query.setParameter("estado", estado);

        if (null != idComissao) {
            query.setParameter("idComissao", idComissao);
        }

        return (Long) query.getSingleResult();
    }

    public Long countProcessoPaeBystado(EstadoProcessoEnum estado) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(" WHERE p.estado = :estado");

        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("estado", estado)
                .getSingleResult();
    }

    public Long countProcessoPaeByCodigoServicoAndRascunhoOrIndeferido(Long codigoservico) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(" AND ( p.estado = Rascunho ");
        queryBuilder.append(" OR p.estado = Indeferido )");
        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("codigo", codigoservico)
                .getSingleResult();
    }

    public Long countProcessoPaeByNissEntidadeAndRascunhoOrIndeferido(Long nissEntidade) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(WHERE_NISS_ENTIDADE);
        queryBuilder.append(" AND ( p.estado = Rascunho ");
        queryBuilder.append(" OR p.estado = Indeferido )");
        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("niss", nissEntidade)
                .getSingleResult();
    }

    public long countProcessoPaeByNissAndRascunho(String niss) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(WHERE_NISS_ENTIDADE);
        queryBuilder.append(" AND  p.estado = :rascunho ");
        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("niss", niss)
                .setParameter("rascunho", EstadoProcessoEnum.RASCUNHO)
                .getSingleResult();
    }

    public long countProcessoPaeByNissAndEmAnalise(String niss) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(WHERE_NISS_ENTIDADE);
        queryBuilder.append(" AND (p.estado = :estadoPorAnalisar ");
        queryBuilder.append(" OR p.estado = :estadoEmAnalise) ");

        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("niss", niss)
                .setParameter("estadoPorAnalisar", EstadoProcessoEnum.POR_ANALISAR)
                .setParameter("estadoEmAnalise", EstadoProcessoEnum.EM_ANALISE)
                .getSingleResult();
    }

    public long countProcessoPaeByNissAndOutros(String niss) {
        StringBuilder queryBuilder = new StringBuilder(COUNT_QUERY);
        queryBuilder.append(WHERE_NISS_ENTIDADE);
        queryBuilder.append(" AND p.estado != :rascunho ");
        queryBuilder.append(" AND p.estado != :por_analisar ");
        queryBuilder.append(" AND p.estado != :em_analise ");

        return (Long) getEntityManager().createQuery(queryBuilder.toString())
                .setParameter("niss", niss)
                .setParameter("rascunho", EstadoProcessoEnum.RASCUNHO)
                .setParameter("por_analisar", EstadoProcessoEnum.POR_ANALISAR)
                .setParameter("em_analise", EstadoProcessoEnum.EM_ANALISE)
                .getSingleResult();
    }

    public List<ProcessoPae> findByBetweenDataFim(Date dataInicio, Date dataFim) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_BETWEEN_DATA_FIM, ProcessoPae.class)
                    .setParameter("pDataInicio", dataInicio)
                    .setParameter("pDataFim", dataFim)
                    .getResultList();
        } catch (Exception e) {
            return null;
        }
    }

    public List<ProcessoPae> findByDataInicioAndIsComunicacao(Date dataInicio) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_DATA_INICIO_AND_IS_COMUNICACAO, ProcessoPae.class)
                    .setParameter("dataInicio", dataInicio)
                    .getResultList();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public List<ProcessoPae> findByDataInicioAndIsAutorizacao(Date dataInicio) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_DATA_INICIO_AND_IS_AUTORIZACAO, ProcessoPae.class)
                    .setParameter("dataInicio", dataInicio)
                    .getResultList();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public List<ProcessoPae> findByProcessosElegiveis(EnumSet<EstadoProcessoEnum> estadosProcessos) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_PROCESSOS_ELEGIVEIS, ProcessoPae.class)
                    .setParameter("estadosProcessos", estadosProcessos)
                    .getResultList();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public List<ProcessoPae> findProcessosRascunho(Date dataScheduler) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_PROCESSOS_RASCUNHO, ProcessoPae.class)
                    .setParameter("dataScheduler", dataScheduler)
                    .getResultList();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public ProcessoPae findByNissCrianca(String nissCrianca) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_NISS_CRIANCA, ProcessoPae.class)
                    .setParameter("nissCrianca", nissCrianca)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public ProcessoPae findByCriancaEstrangeira(PessoaEstrangeira pessoaEstrangeira) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_BY_CRIANCA_ESTRANGEIRA, ProcessoPae.class)
                    .setParameter("nome", pessoaEstrangeira.getNome())
                    .setParameter("dataNascimento", pessoaEstrangeira.getDataNascimento())
                    .setParameter("numeroIdentificacao", pessoaEstrangeira.getNumeroIdentificacao())
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<ProcessoPae> findAllPosRascunhoByCrianca(CriancaEstrangeira criancaEstrangeira) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(ProcessoPae.FIND_ALL_POS_RASCUNHO_BY_CRIANCA, ProcessoPae.class)
                    .setParameter("criancaEstrangeira", criancaEstrangeira)
                    .getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }

    public boolean existProcessoComElemento(long id) {
        return !this.getEntityManager()
                .createNamedQuery(ProcessoPae.FIND_PROCESSO_BY_ELEMENTO, ProcessoPae.class)
                .setParameter("id", id)
                .getResultList().isEmpty();
    }

    public boolean existeProcessoPendenteAssinatura(Long idComissao) {
        StringBuilder queryBuilder = new StringBuilder(QUERY_COUNT_PROCESSO_PENDENTE_ASSINATURA);
        int count = ((BigDecimal) this.getEntityManager()
                .createNativeQuery(queryBuilder.toString())
                .setParameter("idComissao", idComissao)
                .getSingleResult()).intValue();
        return count > 0;
    }

    @NotNull
    private HomepageDTO getHomepageDTO(List objectList) {
        Iterator it = objectList.iterator();
        HomepageDTO homepageDTO = new HomepageDTO();

        while (it.hasNext()) {
            Object[] result = (Object[]) it.next();
            homepageDTO.setValorTotal(result[0].toString());
            homepageDTO.setTotalArquivados(result[1].toString());
            homepageDTO.setTotalAnalisados(result[2].toString());
            homepageDTO.setTotalSemGestor(result[3].toString());
            homepageDTO.setTotalEmAnalise(result[4].toString());
            homepageDTO.setTotalDeferidoPendencia(result[5].toString());
        }
        return homepageDTO;
    }

    @NotNull
    private HomepageDTO getHomepageMembroDTO(List objectList) {
        Iterator it = objectList.iterator();
        HomepageDTO homepageDTO = new HomepageDTO();

        while (it.hasNext()) {
            Object[] result = (Object[]) it.next();
            homepageDTO.setValorTotal(result[0].toString());
            homepageDTO.setTotalArquivados(result[1].toString());
            homepageDTO.setTotalAnalisados(result[2].toString());
            homepageDTO.setTotalEmAnalise(result[3].toString());
            homepageDTO.setTotalDeferidoPendencia(result[4].toString());
        }
        return homepageDTO;
    }

    public Date getLastValDate() {
        Query query = getEntityManager().createNativeQuery(SELECT_LAST_VAL_DATE);
        try {
            return (Date) query.getSingleResult();
        } catch (NoResultException e) {
            return new Date();
        }
    }

    public synchronized Long getNextVal() {
        Query query = getEntityManager().createNativeQuery(SELECT_NEXT_VAL);
        return ((BigDecimal) query.getSingleResult()).longValue();
    }

    public List<ProcessoPae> getProcessoPaginado(int first, PesquisaProcessoFilter filter, boolean entidade) throws PCJException {
        Map<String, Object> parameters = new LinkedHashMap<>();
        String queryCount = entidade ? COUNT_QUERY : "SELECT COUNT(distinct p) FROM ProcessoPae p, Elemento e ";
        String queryStr = entidade ? "SELECT p FROM ProcessoPae p" : "SELECT distinct p FROM ProcessoPae p, Elemento e ";

        StringBuilder join = new StringBuilder(
                "  JOIN e.utilizadorPCJ u " +
                        "  JOIN e.comissao c " +
                        "  JOIN c.competencias t ");

        List<EstadoProcessoEnum> estados = filter.getEstados();
        boolean temEstados = estados != null;
        boolean contemDeferido = temEstados && estados.contains(EstadoProcessoEnum.DEFERIDO);
        boolean contemIndeferido = temEstados && estados.contains(EstadoProcessoEnum.INDEFERIDO);
        boolean temPendencia = temEstados && (ehDeferidoPendencia(filter) || ehPendenteAssinatura(filter));

            if ((contemDeferido || temPendencia || contemIndeferido) && !filter.isUtilizadorExterno()) {
                queryCount = queryCount + JOIN_PROCESSO_REQUERIMENTO;
                queryStr = queryStr + JOIN_PROCESSO_REQUERIMENTO;
            }

            String queryCountUtilizada = entidade ? queryCount : queryCount + join;
            String queryUtilizada = entidade ? queryStr : queryStr + join;
            StringBuilder query = new StringBuilder(queryUtilizada);
            StringBuilder where = new StringBuilder(" WHERE 1 = 1 ");
            if (!entidade) {
                where.append(" AND u.niss = :niss " +
                        " AND e.utilizadorPCJ.id = u.id " +
                        " AND c.ativo = 1" +
                        " AND c.id = t.comissao.id" +
                        " AND t.territorioPCJ.id = p.territorioPCJ.id");
                parameters.put("niss", filter.getNissEntidade());

            }
            if (filter.getNissEntidade() != null && filter.isUtilizadorExterno()) {
                where.append(" AND p.nissEntidade = :nissEntidade ");
                parameters.put("nissEntidade", filter.getNissEntidade());
            }
            if (filter.getAtividadeSelecionada() != null) {
                where.append(" AND p.atividadePCJ = :atividade ");
                parameters.put("atividade", filter.getAtividadeSelecionada());
            }
            if (filter.getNissCrianca() != null) {
                where.append(" AND p.nissCrianca = :nissCrianca ");
                parameters.put("nissCrianca", filter.getNissCrianca());
            }
            if (filter.getNumeroProcesso() != null) {
                try {
                    Long processNumber = PesquisaUtil.getProcessNumber(filter.getNumeroProcesso());
                    where.append(" AND p.numeroProcesso like concat(:processNumber, '%')");
                    where.append(" AND p.dataInicio BETWEEN");
                    where.append(" TO_DATE(:startDate").append(", '").append("yyyy-MM-dd").append("')");
                    where.append(" AND TO_DATE(:endDate").append(", '").append("yyyy-MM-dd").append("')");

                    parameters.put("processNumber", processNumber);
                    parameters.put("startDate", PesquisaUtil.getYearOfOpeningOfTheProcess(filter.getNumeroProcesso()).concat("-01-01"));
                    parameters.put("endDate", PesquisaUtil.getYearOfOpeningOfTheProcess(filter.getNumeroProcesso()).concat("-12-31"));
                } catch (Exception e) {
                    throw new PCJException(PcjMessages.getMessage("M010958"));
                }
            }

            if (filter.isUtilizadorExterno()) {
                if (CollectionUtils.isNotEmpty(filter.getEstados()) && filter.getEstados().contains(EstadoProcessoEnum.EM_ANALISE)) {
                    where.append(" AND p.estado IN (:estados) ");
                    parameters.put(ESTADOS, EstadoProcessoEnum.getStatusToAnalysisByExternalEntity());
                } else if (CollectionUtils.isNotEmpty(filter.getEstados())) {
                    where.append(" AND p.estado in ( :estados) ");
                    parameters.put(ESTADOS, filter.getEstados());
                } else {
                    where.append(" AND p.estado IN (:estados) ");
                    parameters.put(ESTADOS, EstadoProcessoEnum.getStatusByExternalEntity());
                }
            } else {
                if (CollectionUtils.isNotEmpty(filter.getEstados())) {
                    if (ehDeferidoOuIndeferido(filter)) {
                        where.append(" AND r.processoPae.id = p.id ");
                        where.append(" AND p.estado IN (:estados) ");
                        parameters.put(ESTADOS, filter.getEstados());
                        obterQueryComRequerimentoIgualZero(where);
                        obterQueryComComprovativoIgualZero(where);
                    } else if (ehRevogado(filter)) {
                        where.append(" AND p.estado IN (:estados) ");
                        parameters.put(ESTADOS, filter.getEstados());
                        obterQueryComRequerimentoIgualZero(where);
                        obterQueryComComprovativoIgualZero(where);
                    } else if (ehPendenteAssinatura(filter)) {
                        where.append(" AND p.estado IN (:estadoPendendes)");
                        parameters.put("estadoPendendes", EstadoProcessoEnum.getStatusDecisao());
                        where.append(" AND r.processoPae.id = p.id ");
                        obterQueryComRequerimentoIgualZero(where);
                        obterQueryComComprovativoMaiorZero(where);
                    } else if (ehDeferidoPendencia(filter)) {
                        where.append(" AND p.estado = 'DEFERIDO' ");
                        where.append(" AND r.processoPae.id = p.id ");
                        obterQueryComRequerimentoMaiorZero(where);
                    } else {
                        where.append(" AND p.estado IN (:estados) ");
                        parameters.put(ESTADOS, filter.getEstados());
                    }
                } else {
                    where.append(" AND p.estado IN (:estados) ");
                    parameters.put(ESTADOS, EstadoProcessoEnum.getStatusByCPCJEntity());
                }
            }

            // Pesquisa Avançada
            if (null != filter.getNomeCrianca()) {
                where.append(" AND upper(p.criancaEstrangeira.pessoaEstrangeira.nome) like concat(upper(:nomeCrianca), '%') ");
                parameters.put("nomeCrianca", filter.getNomeCrianca());
            }

            if (filter.getTipoAtividade() != null) {
                where.append(" AND p.atividadePCJ.tipoAtividade = :tipoAtividade ");
                parameters.put("tipoAtividade", filter.getTipoAtividade());
            }

            if (filter.getNissEntidadePesquisaAvancada() != null) {
                where.append(" AND p.nissEntidade = :nissEntidade ");
                parameters.put("nissEntidade", filter.getNissEntidadePesquisaAvancada());
            }

            if (filter.getTipoProcesso() != null) {
                where.append(" AND p.comunicacao = :comunicacao ");
                parameters.put(COMUNICACAO, filter.getTipoProcesso().getValor());
            }

            if (filter.getCodigoGestor() != null) {
                Elemento elemento = new ElementoDAO(getEntityManager()).find(filter.getCodigoGestor());
                where.append(" AND p.elemento = :elemento ");
                parameters.put("elemento", elemento);
            }

            query.append(where);

        if (CollectionUtils.isNotEmpty(filter.getRoles()) && filter.getRoles().contains("PCJ03") || filter.getRoles().contains("PCJ02")) {
            query.append(" order by p.elemento desc, p.estado desc, p.dataInicio");
        } else {
            query.append(" order by p.dataInicio");
        }
        try {
            return asListPaginate(query.toString(), first, count(queryCountUtilizada + where, parameters).intValue() + 1, parameters, ProcessoPae.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage() + query, e);
            LoggingHelper.logSaida(query.toString());
            return null;
        }
    }

    private void obterQueryComRequerimentoIgualZero(StringBuilder where) {
        where.append(
                " AND (select count(r1) from RequerimentoPae r1 where r1.id " +
                " in (select r2.id from RequerimentoPae r2 where r2.processoPae.id = p.id)" +
                " AND r1.estadoRequerimento is null and r1.tipoRequerimento <> 'INICIAL' )=0");
    }

    private void obterQueryComRequerimentoMaiorZero(StringBuilder where) {
        where.append(
                " AND (select count(r1) from RequerimentoPae r1 where r1.id " +
                " in (select r2.id from RequerimentoPae r2 where r2.processoPae.id = p.id)" +
                " AND r1.estadoRequerimento is null and r1.tipoRequerimento <> 'INICIAL' ) > 0");
    }

    private void obterQueryComComprovativoIgualZero(StringBuilder where) {
        where.append(
                " AND (select count(co) from Comprovativo co where co.requerimentoPAE.id " +
                " in (select id from RequerimentoPae r3 where r3.processoPae.id  = p.id)" +
                " AND co.ficheiroAssinado is null )=0");
    }

    private void obterQueryComComprovativoMaiorZero(StringBuilder where) {
        where.append(
                " AND (select count(co) from Comprovativo co where co.requerimentoPAE.id " +
                " in (select id from RequerimentoPae r3 where r3.processoPae.id  = p.id)" +
                " AND co.ficheiroAssinado is null )>0");
    }

    public HomepageDTO getValuesHomepage(long idComissao) {
        StringBuilder queryBuilder = new StringBuilder(QUERY_HOME_PRES_TOTAL_E_ARQUIVADOS);
        queryBuilder.append(FROM_HOME_TOTAL);
        queryBuilder.append(QUERY_ARQUIVADOS).append(QUERY_ANALISADOS).append(QUERY_POR_ANALISAR).append(QUERY_EM_ANALISE).append(QUERY_DEFERIDO_PENDENCIA).append(QUERY_PENDENCIA_ASSINATURA);
        queryBuilder.append(FROM_HOME_REQ);
        queryBuilder.append(WHERE_HOME);
        queryBuilder.append(" and p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) " +
                " and estado in ('DEFERIDO', 'INDEFERIDO', 'REVOGADO') " +
                " and (select count(1) from pcj.requerimento_pae p2 where p2.id_requerimento_pae " +
                " in (select id_requerimento_pae from PCJ.requerimento_pae r2 where r2.id_processo_pae = p.id_processo_pae) " +
                " and p2.estado_requerimento is null and p2.tipo <>'INICIAL' )=0" +
                " and exists (select 1 from pcj.comprovativo co where co.id_requerimento_pae = r.id_requerimento_pae and ficheiro_assinado is null)) PAssin");
        List objectList = this.getEntityManager()
                .createNativeQuery(queryBuilder.toString())
                .setParameter("idComissao", idComissao)
                .getResultList();

        return getHomepageDTO(objectList);
    }

    public HomepageDTO getValuesHomepageMembros(long idComissao, long idElemento) {
        StringBuilder queryBuilder = new StringBuilder(QUERY_HOME_MEMBROS_TOTAL_E_ARQUIVADOS);
        queryBuilder.append(FROM_HOME_MEMBRO_TOTAL);
        queryBuilder.append(QUERY_MEMBRO_ARQUIVADOS).append(QUERY_MEMBRO_ANALISADOS).append(QUERY_MEMBRO_EM_ANALISE).append(QUERY_MEMBRO_DEFERIDO_PENDENCIA).append(QUERY_MEMBRO_PENDENCIA_ASSINATURA);
        queryBuilder.append(FROM_HOME_REQ);
        queryBuilder.append(WHERE_HOME);
        queryBuilder.append(" and p.id_territorio_pcj in (select id_territorio_pcj from PCJ.competencia_territorial_pcj where id_comissao_pcj = :idComissao) and estado in ('DEFERIDO', 'INDEFERIDO', 'REVOGADO') and id_elemento_pcj = :idElemento and  exists (select 1 from pcj.comprovativo co where co.id_requerimento_pae = r.id_requerimento_pae and ficheiro_assinado is null)) PAssin");
        List objectList = this.getEntityManager()
                .createNativeQuery(queryBuilder.toString())
                .setParameter("idComissao", idComissao)
                .setParameter("idElemento", idElemento)
                .getResultList();

        return getHomepageMembroDTO(objectList);
    }
}