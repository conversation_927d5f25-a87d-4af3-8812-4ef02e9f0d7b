package pt.segsocial.pcj.ppp.mapper.comunicacao;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.comunicacao.DocumentoComunicacaoDTOId;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.DocumentoComunicacaoPPPId;

@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DocumentoComunicacaoPPPIdMapper {

    DocumentoComunicacaoPPPId toEntity(DocumentoComunicacaoDTOId documentoComunicacaoDTOId);

    DocumentoComunicacaoDTOId toDTO(DocumentoComunicacaoPPPId documentoComunicacaoPPP);

}
