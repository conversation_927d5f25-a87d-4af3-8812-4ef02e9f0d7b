package pt.segsocial.pcj.pae.bean;

import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;

public enum EstadoProcessoFilter {
    PCJ01 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.EM_ANALISE;
        }
    },

    PCJ02 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.RASCUNHO;
        }
    },

    PCJ03 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ04 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ05 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.DEFERIDO;
        }
    },

    PCJ06 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ07 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ08 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ09 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ10 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ11 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ12 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ13 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ14 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ15 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ16 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    },

    PCJ17 {
        @Override
        EstadoProcessoEnum execute() {
            return EstadoProcessoEnum.POR_ANALISAR;
        }
    };

    abstract EstadoProcessoEnum execute();
}
