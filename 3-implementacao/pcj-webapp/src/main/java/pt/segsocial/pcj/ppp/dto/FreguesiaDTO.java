package pt.segsocial.pcj.ppp.dto;

import java.io.Serializable;
import java.util.Objects;

public class FreguesiaDTO implements Serializable {

    private Long id;
    private Integer codigoFreguesia;
    private String nome;

    public FreguesiaDTO(Integer nivel3, String designacaoDivisaoAdmin) {
        this.codigoFreguesia = nivel3;
        this.nome = designacaoDivisaoAdmin;
    }

    public FreguesiaDTO() {

    }

    public Integer getCodigoFreguesia() {
        return codigoFreguesia;
    }

    public void setCodigoFreguesia(Integer codigoFreguesia) {
        this.codigoFreguesia = codigoFreguesia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        FreguesiaDTO that = (FreguesiaDTO) o;
        return Objects.equals(codigoFreguesia, that.codigoFreguesia);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(codigoFreguesia);
    }

    @Override
    public String toString() {
        return "FreguesiaDTO{" +
                "id=" + id +
                ", codigoFreguesia=" + codigoFreguesia +
                ", nome='" + nome + '\'' +
                '}';
    }
}
