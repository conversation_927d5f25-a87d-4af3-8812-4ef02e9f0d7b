package pt.segsocial.pcj.ppp.enums;

import org.apache.commons.lang.NotImplementedException;
import pt.segsocial.pcj.ppp.bean.ata.restrita.BaseAtaRestritaBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoBaseRestritaBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoGeralRestritaBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoComunicacaoRestritaBean;
import pt.segsocial.pcj.ppp.dto.*;
import pt.segsocial.pcj.ppp.dto.ata.restrita.*;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaDTO;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

public enum BlocosTrabalhoAtaRestritaEnum {
    COMUNICACAO(true, "comunicacao",
            "Comunicação",
            BlocoComunicacaoRestritaBean.class,
            new AtaBiConsumer<BaseAtaRestritaBean, BlocoBaseRestritaBean>() {
                @Override
                public void accept(BaseAtaRestritaBean instancia, BlocoBaseRestritaBean bean) {
                    instancia.setBlocoComunicacaoRestritaBean((BlocoComunicacaoRestritaBean) bean);
                }
            }, BlocoRestritaComunicacaoDTO.class) {
    },

    ASSUNTOS_GERAIS(true, "assuntos-gerais",
            "Assuntos Gerais",
            BlocoGeralRestritaBean.class,
            new AtaBiConsumer<BaseAtaRestritaBean, BlocoBaseRestritaBean>() {
                @Override
                public void accept(BaseAtaRestritaBean instancia, BlocoBaseRestritaBean bean) {
                    instancia.setBlocoGeralRestritaBean((BlocoGeralRestritaBean) bean);
                }
            }, BlocoGeralRestritaDTO.class) {
    };

    private final boolean ativo;
    private final String nome;
    private final String descricao;
    private final Class<? extends BlocoBaseRestritaBean> classeBean;
    private final AtaBiConsumer<BaseAtaRestritaBean, BlocoBaseRestritaBean> setter;
    private final Class<? extends BlocoBaseRestritaDTO> dtoClass;

    private static final Map<String, BlocosTrabalhoAtaRestritaEnum> nomeEnum = new HashMap<>();

    static {
        for (BlocosTrabalhoAtaRestritaEnum bloco : BlocosTrabalhoAtaRestritaEnum.values()) {
            nomeEnum.put(bloco.getNome(), bloco);
        }
    }

    BlocosTrabalhoAtaRestritaEnum(boolean ativo, String nome, String descricao,
                                  Class<? extends BlocoBaseRestritaBean> classeBean,
                                  AtaBiConsumer<BaseAtaRestritaBean, BlocoBaseRestritaBean> setter,
                                  Class<? extends BlocoBaseRestritaDTO> dtoClass) {
        this.ativo = ativo;
        this.nome = nome;
        this.descricao = descricao;
        this.classeBean = classeBean;
        this.setter = setter;
        this.dtoClass = dtoClass;
    }

    public BlocoBaseRestritaDTO criarDTO() {
        try {
            return dtoClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException("Erro ao criar DTO para o bloco: " + this.name(), e);
        }
    }

    public BlocoBaseRestritaBean criarBean(AtaRestritaDTO ata, BlocoBaseRestritaDTO dto) {
        try {
            return classeBean.getDeclaredConstructor(AtaRestritaDTO.class, dto.getClass())
                    .newInstance(ata, dto);
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException("Erro ao criar Bean para o bloco: " + this.name(), e);
        }
    }

    public static BlocosTrabalhoAtaRestritaEnum getWorkBlock(String nomeBloco) {
        return nomeEnum.get(nomeBloco);
    }

    public void accept(BaseAtaRestritaBean baseAtaBean, BlocoBaseRestritaBean blocoBaseBean) {
        this.setter.accept(baseAtaBean, blocoBaseBean);
    }

    public String getNome() {
        return nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public boolean isAtivo() {
        return this.ativo;
    }
}