package pt.segsocial.pcj.ppp.bean.elemento;

import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.primefaces.event.FileUploadEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.api.security.exception.SecurityException;
import pt.segsocial.fraw.jsf.util.JsfUtil;
import pt.segsocial.fraw.util.StringUtils;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PcjMessages;
import pt.segsocial.pcj.ppp.bean.AbstractPppBean;
import pt.segsocial.pcj.ppp.bean.cpcj.RegistarCPCJWizard;
import pt.segsocial.pcj.ppp.bean.cpcj.RegistarCPCJWizardStep;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoRegistoElementoDTO;
import pt.segsocial.pcj.ppp.dto.HistoricoElementoDTO;
import pt.segsocial.pcj.ppp.dto.ParecerDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoConhecimentoDTO;
import pt.segsocial.pcj.ppp.dto.elemento.*;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.enums.CargoMandatoEnum;
import pt.segsocial.pcj.ppp.enums.TipoElementoEnum;
import pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum;
import pt.segsocial.pcj.ppp.exceptions.InvalidInputException;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.service.*;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static pt.segsocial.pcj.core.util.Constants.ELEMENTO;
import static pt.segsocial.pcj.ppp.enums.TipoDocumentoEnum.NISS;

@Named(value = "pcjElementoBean")
@Stateful(name = "pcjElementoBean")
@LocalBean
@ViewAccessScoped
public class ElementoBean extends AbstractPppBean implements Serializable, EntidadeCedente {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElementoBean.class);

    private final List<String> TIPOS_APOIO
            = Arrays.asList(
            TipoElementoEnum.APOIO_TECNICO.getCodigo(),
            TipoElementoEnum.APOIO_ADMINISTRATIVO.getCodigo());
    private ElementoDTO elementoDTO = new ElementoDTO();
    private FiltroPesquisaElementoDTO filtroPesquisaElementoDTO = new FiltroPesquisaElementoDTO();
    private FiltroPesquisaDadosPessoaisDTO filtroPesquisaDadosPessoaisDTO = new FiltroPesquisaDadosPessoaisDTO();
    private ElementoDTO elementoConsultaDTO = new ElementoDTO();
    private List<HistoricoElementoDTO> historicosElemento = new ArrayList<>();
    private List<ResultadoPesquisaElementoDTO> listaElementos = new ArrayList<>();
    private List<ElementoDTO> listaConsultaElementos = new ArrayList<>();
    private List<ResultadoPesquisaElementoDadosPessoaisDTO> listaElementosDadosPessoaisDTO = new ArrayList<>();
    private Collection<DetalheDominioVO> listaEntidadesRepresentantesOriginal;
    private RegistarCPCJWizard wizard = new RegistarCPCJWizard();
    private ParecerDTO parecerDTO = new ParecerDTO();
    private String entidadeOriginal;
    private boolean secretarioOriginal;
    private boolean exibirBtnAlterar = false;
    private String formName = "pcjRegistarElementoForm";
    private boolean existeMaisUmaCPCJTerritorio;

    @Inject
    private ElementoService elementoService;
    @Inject
    private ElementoVersaoService elementoVersaoService;
    @Inject
    private PCJServiceDomain pcjServiceDomain;
    @Inject
    private EntidadeExternaService entidadeExternaService;
    @Inject
    private DocumentoPCJService documentoService;
    @Inject
    private BlocoConhecimentoService blocoConhecimentoService;

    @URLAction(mappingId = PCJSubsystem.OP_PPP_REGISTAR_ELEMENTO, onPostback = false)
    public void onLoad() {
        LoggingHelper.logEntrada(LOGGER);
        elementoDTO = new ElementoDTO();
        limparPesquisaBuscaPorDocumento(elementoDTO.getUtilizadorPCJ());
        setMostrarEcran(false);
        ehInicioEcraRegistarElemento = true;
        listaEntidadesRepresentantes = new ArrayList<>();
        limparBuscarPessoaSingular(elementoDTO, elementoDTO.getUtilizadorPCJ());
        if (!login.isUtilizadorComissao()) {
            carregarComissao(login.getComissaoPCJUtilizadorLogado().getNome());
        }
        carregarCombos();
        LoggingHelper.logSaida(LOGGER);
    }

    @URLAction(mappingId = PCJSubsystem.OP_PPP_EDITAR_ELEMENTO, onPostback = false)
    public void initEditar() {
        LoggingHelper.logEntrada(LOGGER, "Metodo init de Editar Elemento");
        listaComissoesEditarDTO = carregarTodasComissoes();
        listaTiposDocumentos = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderTipoDocumento().get().getDetalhes().values()));
        listaTipoElementosEditar = elementoService.carregarListaTipoElementos(elementoDTO, pcjServiceDomain.getDominioHolderTipoElementos());
        listaValenciasTecnicasEditar = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderValenciaTecnica().get().getDetalhes().values()));
        listaCargosMandatosEditar = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderCargo().get().getDetalhes().values()));
        listaTiposDocumentosEditar = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderTipoDocumento().get().getDetalhes().values()));
        existeMaisUmaCPCJTerritorio = elementoService.existeMaisDeUmaCpcjNoTerritorio(elementoDTO.getComissao().getMorada().getTerritorioPCJDTO().getCodigoDistrito(), elementoDTO.getComissao().getMorada().getTerritorioPCJDTO().getCodigoConcelho(), elementoDTO.getComissao().getCodigo());
        listaEntidadesRepresentantes = elementoService.carregarEntidadesRepresentantes(pcjServiceDomain.getDominioHolderEntidades(), elementoDTO.getTipoElemento(), existeMaisUmaCPCJTerritorio);
        listaEntidadesRepresentantesOriginal = new ArrayList<>(listaEntidadesRepresentantes);
        secretarioOriginal = ehSecretario(elementoDTO);
        configuraVariaveisRegistoElemento(elementoDTO);
        LoggingHelper.logSaida(LOGGER, "Metodo init de Editar Elemento");
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void registarElemento() throws PCJException {
        LoggingHelper.logEntrada(LOGGER, elementoDTO);
        try {
            sucesso = false;

            validarPermissaoDe(PCJSubsystem.OP_PPP_REGISTAR_ELEMENTO);

            super.validarNumeroDocumento(elementoDTO.getUtilizadorPCJ().getCodigoDocumento(), elementoDTO.getUtilizadorPCJ().getNumeroDocumento());

            if (elementoService.existeEmailCadastrado(elementoDTO)) {
                JsfMessageUtil.mostraMensagemErro(String.format(
                        PcjMessages.getMessage("MSG042"), elementoDTO.getEmail(), elementoDTO.getComissao().getNome()));
                return;
            }

            if (elementoService.verificarSeExcedeuHoraAfetacaoMaximaPermitida(elementoDTO, false)) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("MSG039"));
                return;
            }


            if (diferenteApoioAdministrativo(elementoDTO)) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("MSG044"));
                return;
            }

            if (elementoDTO.getDocumentoDTO().getIdentificadorFicheiro() == null) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("MSG043"));
                return;
            }

            elementoService.registar(elementoDTO, getPerfilDTO());

            JsfMessageUtil.mostraMensagemSucesso(ELEMENTO + " registado " + COM_SUCESSO);
            limparElemento();

            if (!login.isUtilizadorComissao()) {
                carregarComissao(login.getComissaoPCJUtilizadorLogado().getNome());
            }
            sucesso = true;
            pcjSubsystem.redirectTo(pcjSubsystem.pesquisarElementos(), null);
            LoggingHelper.logSaida(LOGGER, "Registar elemento salvo com sucesso");

        } catch (DomainException | IOException e) {
            LOGGER.error("Erro ao tentar registar elemento: " + e.getMessage());
            JsfMessageUtil.mostraMensagemErro(ERRO_TENTAR + " registar elemento.");
            LoggingHelper.logError(e);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void editarElemento() throws PCJException {
        LoggingHelper.logEntrada(LOGGER, elementoDTO);
        try {

            validarPermissaoDe(PCJSubsystem.OP_PPP_EDITAR_ELEMENTO);

            super.validarNumeroDocumento(elementoDTO.getUtilizadorPCJ().getCodigoDocumento(), elementoDTO.getUtilizadorPCJ().getNumeroDocumento());

            if (elementoService.verificarSeExcedeuHoraAfetacaoMaximaPermitida(elementoDTO, true)) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("MSG039"));
                return;
            }

            processaValidacaoDeDocumentosAnexados();
            utilizadorPCJService.editar(elementoDTO.getUtilizadorPCJ());
            elementoService.editar(elementoDTO, getPerfilDTO(), TipoRegistoLogEnum.ALTERACAO_ELEMENTO);

            if (elementoDTO.getSecretario() && !secretarioOriginal) {
                BlocoConhecimentoDTO blocoConhecimentoDTO = processaBlocoConhecimentoDTO();
                blocoConhecimentoService.salvarBlocoConhecimento(blocoConhecimentoDTO);
            }

            JsfMessageUtil.mostraMensagemSucesso(ELEMENTO + " alterado " + COM_SUCESSO);
            limparElemento();
            pcjSubsystem.redirectTo(pcjSubsystem.pesquisarElementos(), null);
            LoggingHelper.logSaida(LOGGER);
        } catch (DomainException | IOException e) {
            LoggingHelper.logError(e);
            JsfMessageUtil.mostraMensagemErro(ERRO_TENTAR + " alterar elemento.");
        }
    }

    private BlocoConhecimentoDTO processaBlocoConhecimentoDTO() {
        String nome = (elementoDTO.getUtilizadorPCJ() != null && StringUtils.isNotBlank(elementoDTO.getUtilizadorPCJ().getNomeProfissional())) ?
                elementoDTO.getUtilizadorPCJ().getNomeProfissional() :
                elementoDTO.getUtilizadorPCJ().getNome();

        return new BlocoConhecimentoDTO.Builder()
                .assunto("Conhecimento - Designação de secretário")
                .observacao(String.format("Designação do membro %s a secretário", nome))
                .comissao(elementoDTO.getComissao())
                .executado(false)
                .ataExterna(true).build();
    }

    @Override
    public void limparPesquisaBuscaPorDocumento(UtilizadorPCJDTO utilizadorPCJDTO) {
        LoggingHelper.logEntrada(LOGGER, utilizadorPCJDTO);
        super.limparPesquisaBuscaPorDocumento(utilizadorPCJDTO);
        limparBuscarPessoaSingular(elementoDTO, elementoDTO.getUtilizadorPCJ());
        setMostrarEcran(false);
        LoggingHelper.logSaida(LOGGER);
    }

    private void validarPermissaoDe(String opPppEditarElemento) {
        if (!hasPerfilOperacao()) {
            throw new SecurityException("O utilizador " + login.getNomeUtilizador() + " não tem permissão para aceder à operação: " + opPppEditarElemento);
        }
    }

    public void consultarElementoPorNiss(ElementoDTO entityDto) {
        LoggingHelper.logEntrada(LOGGER, entityDto);
        UtilizadorPCJDTO utilizadorPCJDTO = entityDto.getUtilizadorPCJ();
        utilizadorPCJDTO.configurarBuscaDocumentoPorNiss();
        consultarElementoPorNumeroTipoDocumento(utilizadorPCJDTO);
        entityDto.naoEncontrouNissOuNumeroDocumento();
        LoggingHelper.logSaida(LOGGER);
    }

    public void consultarElementoEdicaoPorNiss(ElementoDTO entityDto) {
        LoggingHelper.logEntrada(LOGGER, entityDto);
        try {
            String niss = entityDto.getUtilizadorPCJ().getNiss();
            DocumentoRegistoElementoDTO documentoRegistoElementoDTO = new DocumentoRegistoElementoDTO(NISS.getCodigo(), niss);

            if (!super.validarNiss(documentoRegistoElementoDTO)) {
                throw new DomainException("NISS invalido");
            }

            ElementoDTO proprioElemento = elementoService.buscaElementoPorIdEhNiss(entityDto.getId(), niss);
            if (proprioElemento.getId() != null) {
                elementoDTO = proprioElemento;
            } else {
                UtilizadorPCJDTO utilizadorPCJDTO = super.getUtilizadorPCJDTO(documentoRegistoElementoDTO, null);
                if (utilizadorPCJDTO.getId() == null) {
                    elementoDTO.limparDadosPessoais();
                    elementoDTO.setUtilizadorPCJ(utilizadorPCJDTO);
                } else {
                    if (elementoService.existeVinculoDoNissCpcj(utilizadorPCJDTO, entityDto.getComissao())) {
                        JsfMessageUtil.mostraMensagemErro(String.format(PcjMessages.getMessage("MSG049"), niss, elementoDTO.getComissao().getNome()));
                        throw new DomainException("Vinculo do NISS com a CPCJ existente");
                    }

                    if (existeRegistoEntidadeExterna(elementoDTO.getUtilizadorPCJ())) {
                        JsfMessageUtil.mostraMensagemErro("Esta pessoa já tem um registo ativo no sistema como um utilizador externo. Caso necessário consulte a Comissão Nacional");
                        throw new DomainException("O utilizador esta ativo no sistema como uma Entidade");
                    }

                    elementoDTO.setUtilizadorPCJ(utilizadorPCJDTO);
                }
            }
        } catch (DomainException e) {
            LoggingHelper.logError(e);
            elementoDTO.limparDadosNiss();
            configuraVariaveisRegistoElemento(elementoDTO);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public void consultarElementoPorNumeroTipoDocumento(UtilizadorPCJDTO dto) {
        LoggingHelper.logEntrada(LOGGER, dto);
        try {
            super.validarNumeroDocumento(dto.getCodigoDocumento(), dto.getNumeroDocumento());

            String numeroDocumento = dto.getDocumentoRegistoElementoDTO().getNumeroDocumento();

            elementoDTO = super.buscarPessoaPorNumeroDocumento(dto);

            if (hasErrorToProcessSearchElemento(numeroDocumento)) {
                return;
            }

            JsfMessageUtil.mostraMensagemSucessoCondicional(elementoDTO.getUtilizadorPCJ().getId() != null, PcjMessages.getMessage("M_SUCESSO_ELEMENTO_ENCONTRADO"), PcjMessages.getMessage("M_AUX_ELEMENTO_ENCONTRADO"));
        } catch (DomainException | PCJException e) {
            LoggingHelper.logError(e);
            ehInicioEcraRegistarElemento = true;
            setMostrarEcran(false);
            setPesquisaSucesso(false);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private boolean hasErrorToProcessSearchElemento(String numeroDocumento) {
        listaComissoesDTO = comissaoPCJService.carregarComissoesAtivasDiferenteElemento(numeroDocumento);

        if (elementoDTO.getComissao() == null && listaComissoesDTO.isEmpty()) {
            JsfMessageUtil.mostraMensagemErro(String.format(PcjMessages.getMessage("MSG045"), numeroDocumento));
            setMostrarEcran(false);
            return true;
        }

        if (elementoDTO.getComissao() != null && elementoDTO.getComissao().getId() != null && !listaComissoesDTO.contains(elementoDTO.getComissao())) {
            JsfMessageUtil.mostraMensagemErro(String.format(PcjMessages.getMessage("MSG049"), numeroDocumento, elementoDTO.getComissao().getNome()));
            setMostrarEcran(false);
            return true;
        }

        if (existeRegistoEntidadeExterna(elementoDTO.getUtilizadorPCJ())) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_UTLIZADOR_EXTERNO_ATIVO"));
            setMostrarEcran(false);
            return true;
        }

        configuraVariaveisRegistoElemento(elementoDTO);
        return false;
    }

    private boolean existeRegistoEntidadeExterna(UtilizadorPCJDTO utilizadorPCJDTO) {
        return entidadeExternaService.existeEntidadeExternaVinculadoUtilizador(utilizadorPCJDTO.getNiss());
    }

    private void configuraVariaveisRegistoElemento(ElementoDTO dto) {
        dto.setNissValidado(true);
        atualizarNifObrigatorio(dto.getUtilizadorPCJ());
        setMostrarEcran(true);
        setEcraUtilizador(false);
        setPesquisaSucesso(true);
        setEhInicioEcraRegistarElemento(false);
    }

    public void carregarEditarElemento(ElementoDTO dto) {
        LoggingHelper.logEntrada(LOGGER, dto);
        elementoDTO = dto;
        TipoElementoEnum tipoElemento = TipoElementoEnum.buscaTipoElementoPorCodigo(elementoDTO.getTipoElemento());
        elementoDTO.setSecretario(CargoMandatoEnum.SECRETARIO.getCodigo().equals(dto.getCargoMandato()));
        elementoDTO.setTipoElementoOriginal(dto.getTipoElemento());
        elementoDTO.setEntidadeOriginal(dto.getEntidade());
        if (tipoElemento != null) {
            elementoDTO.setNomeTipoElemento(tipoElemento.getDescricao());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void carregarCombos() {
        listaTipoElementos = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderTipoElementos().get().getDetalhes().values()));
        listaValenciasTecnicas = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderValenciaTecnica().get().getDetalhes().values()));
        listaCargosMandatos = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderCargo().get().getDetalhes().values()));
        listaTiposDocumentos = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderTipoDocumento().get().getDetalhes().values()));
        listaTiposDocumentosRegisto = elementoService.getTipoDeDocumentos(pcjServiceDomain.getDominioHolderTipoDocumento().get().getDetalhes().values());
    }

    private void carregarComissao(String nomeComissao) {
        elementoDTO.setComissao(comissaoPCJService.carregarComissaoPorNome(nomeComissao));
    }

    @Override
    public boolean ehComissaoNacional() {
        return login.isUtilizadorComissao();
    }

    public boolean verificaApoioCN(ElementoDTO elementoDTO) {
        LoggingHelper.logEntrada(LOGGER, elementoDTO);
        if (elementoDTO.getTipoElemento() != null) {
            if (TipoElementoEnum.APOIO_ADMINISTRATIVO.getCodigo().equals(elementoDTO.getTipoElemento())) {
                elementoDTO.setApoioCn(true);
                return false;
            } else if (TipoElementoEnum.APOIO_TECNICO.getCodigo().equals(elementoDTO.getTipoElemento())) {
                elementoDTO.setApoioCn(elementoService.findApoioCnById(elementoDTO.getId()));
                return true;
            } else {
                elementoDTO.setApoioCn(false);
                return false;
            }
        }
        LoggingHelper.logSaida(LOGGER);
        return false;
    }

    public void limparPesquisaDadosPessoais() {
        filtroPesquisaDadosPessoaisDTO = new FiltroPesquisaDadosPessoaisDTO();
        listaElementosDadosPessoaisDTO = new ArrayList<>();
        mensagemSemResultado = EFETUE_PESQUISA;
    }

    private List<ComissaoPCJDTO> carregarTodasComissoes() {
        return comissaoPCJService.carregarComissoesAtivas();
    }

    public boolean existeElementos() {
        return !listaElementos.isEmpty();
    }

    public boolean existeElementosDadosPessoais() {
        return !listaElementosDadosPessoaisDTO.isEmpty();
    }

    public void atualizarTipoElementoEntidade(ElementoDTO dto) {
        LoggingHelper.logEntrada(LOGGER, dto);
        existeMaisUmaCPCJTerritorio = elementoService.existeMaisDeUmaCpcjNoTerritorio
                (dto.getComissao().getMorada().getTerritorioPCJDTO().getCodigoDistrito(),
                        dto.getComissao().getMorada().getTerritorioPCJDTO().getCodigoConcelho(), dto.getComissao().getCodigo());
        dto.setTipoElemento(null);
        dto.setEntidade(null);
        listaEntidadesRepresentantes = new ArrayList<>();
        LoggingHelper.logSaida(LOGGER);
    }

    public void listarEntidades(ElementoDTO elementoDTO) {
        if (elementoDTO.getComissao() == null) {
            return;
        }

        elementoDTO.setEntidade(null);
        calculaHorasMensais(elementoDTO);
        calculaHorasSemanais(elementoDTO);
        elementoDTO.setApoioCn(false);

        if (elementoDTO.getId() != null) {
            listaEntidadesRepresentantes = new ArrayList<>();
        }

        carregarComissao(elementoDTO.getComissao().getNome());
        if (null != elementoDTO.getTipoElemento()) {
            Collection<DetalheDominioVO> lista =
                    elementoService.carregarEntidadesRepresentantes(pcjServiceDomain.getDominioHolderEntidades(), elementoDTO, existeMaisUmaCPCJTerritorio);

            listaEntidadesRepresentantes = elementoService.carregarListaEntidadesRepresentantes(lista);
        }
    }

    public void upload(FileUploadEvent event) {
        LoggingHelper.logEntrada(LOGGER, event);
        try {

            if ("pcjSecretariofileUploaderDocumentoDesignacao".equalsIgnoreCase(event.getComponent().getId())) {
                documentoSecretarioDTO = documentoService.upload(event);
                elementoDTO.setDocumentoDTO(documentoSecretarioDTO);

                if (null != elementoDTO.getId()) {
                    elementoDTO.setDocumentoDTO(documentoSecretarioDTO);
                }
            } else {
                documentoDTO = documentoService.upload(event);
                elementoDTO.setDocumentoDTO(documentoDTO);
                if (isEditarElemento()) {
                    elementoDTO.setDocumentoDTO(documentoDTO);
                }
            }
            elementoDTO.setAtributoControlaObrigatoriedadeDocumento("doc");
        } catch (PCJException e) {
            LoggingHelper.logError(e);
            LOGGER.error("[ PCJ ] Erro ao fazer upload storage: ", e);
            JsfUtil.addErrorMessage(null, e.getMessage(), null);
        }
        LoggingHelper.logSaida(LOGGER);
    }


    public void uploadPicture(FileUploadEvent event) {
        elementoDTO.getUtilizadorPCJ().setAtributoControlaObrigatoriedadeFoto("foto");
        elementoDTO.setNomeFoto(event.getFile().getFileName());
        elementoDTO.getUtilizadorPCJ().setFoto(event.getFile().getContents());

        if (null != elementoDTO.getId()) {
            elementoDTO.getUtilizadorPCJ().setFoto(event.getFile().getContents());
        }
    }

    public void voltar() {
        limparPesquisaDadosPessoais();
    }

    private void limparElemento() {
        elementoDTO = new ElementoDTO();
    }

    public String getNomeComissao() {
        if (elementoDTO.getComissao() == null) {
            ComissaoPCJDTO comissaoPCJDTO = login.getComissaoPCJUtilizadorLogado();
            this.elementoDTO.setComissao(comissaoPCJDTO);
        }
        return StringUtils.isNotBlank(this.elementoDTO.getComissao().getNome()) ? this.elementoDTO.getComissao().getNome() : HIFEN;
    }

    public boolean mostrarTooltipEntidade(String valor) {
        return elementoService.mostrarTooltipEntidade(valor);
    }

    public void carregarElemento(Long idElemento) throws InvalidInputException {
        LoggingHelper.logEntrada(LOGGER, "Carregar elemento com id: " + idElemento);
        elementoConsultaDTO = elementoService.findById(idElemento);
        if (elementoConsultaDTO != null) {
            if (ehComissaoNacional()) {
                processaFiltroElementosPorNissDoUtilizador(elementoConsultaDTO);
            }
            historicosElemento = elementoService.carregarHistoricoElemento(idElemento);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void processaFiltroElementosPorNissDoUtilizador(ElementoDTO dto) {
        listaConsultaElementos = filtraElementosPorNissDoUtilizados(dto);
    }

    private List<ElementoDTO> filtraElementosPorNissDoUtilizados(ElementoDTO dto) {
        FiltroPesquisaElementoDTO filtroPesquisaElementoDTO = new FiltroPesquisaElementoDTO();
        filtroPesquisaElementoDTO.setNissPesquisa(dto.getUtilizadorPCJ().getNiss());
        filtroPesquisaElementoDTO.setGestor(true);
        return elementoService.buscaElementosVisaoCN(filtroPesquisaElementoDTO);
    }

    public boolean mostrarEliminarElemento(long id) {
        return elementoService.elementoPodeSerEliminado(id);
    }

    public boolean mostrarAtivarElemento(ResultadoPesquisaElementoDTO dto) {
        return dto.isAtivo();
    }

    public boolean mostrarComponenteComissaoNacional() {
        return login.isUtilizadorComissao();
    }

    public String mudarPagina() {
        return sucesso ? pcjSubsystem.pesquisarElementos() : StringUtils.EMPTY;
    }

    public void designarSecretario(ElementoDTO dto) {
        LoggingHelper.logEntrada(LOGGER, dto);
        if (elementoService.naoEhPossivelAtualizarMembroComoSecretario(dto)) {
            dto.setSecretario(false);
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("MSG051"));
            return;
        }

        elementoService.designarSecretario(dto, parecerDTO);

        if (dto.getSecretario()) {
            Collection<DetalheDominioVO> lista =
                    elementoService.carregarEntidadesRepresentantes(pcjServiceDomain.getDominioHolderEntidades(), dto.getTipoElemento(), existeMaisUmaCPCJTerritorio);
            listaEntidadesRepresentantes = elementoService.carregarListaEntidadesRepresentantes(lista);
            calculaHorasSemanais(elementoDTO);
        } else {
            listaEntidadesRepresentantes = new ArrayList<>(listaEntidadesRepresentantesOriginal);
            calculaHorasMensais(elementoDTO);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public boolean ehHoraAfetacaoSemanalMenorQueMinima(float valor) {
        return elementoService.ehHoraAfetacaoSemanalMenorQueMinima(valor);
    }

    public boolean ehHoraAfetacaoMensalMenorQueMinima(float valor) {
        return elementoService.ehHoraAfetacaoMensalMenorQueMinima(valor);
    }

    public boolean ehHoraAfetacaoSemanalMaiorQueMaxima(float valor) {
        return elementoService.ehHoraAfetacaoSemanalMaiorQueMaxima(valor);
    }

    public boolean ehHoraAfetacaoMensalMaiorQueMaxima(float valor) {
        return elementoService.ehHoraAfetacaoMensalMaiorQueMaxima(valor);
    }

    public String mensagemHoraAfetacaoMensalDiferentes(float valor) {
        return elementoService.mensagemHoraAfetacaoMensal(valor);
    }

    public String mensagemHoraAfetacaoSemanalDiferentes(float valor) {
        return elementoService.mensagemHoraAfetacaoSemanal(valor);
    }

    public boolean existeMaisDeUmaEntidadeRepresentante(ElementoDTO dto, List<DetalheDominioVO> entidades) {
        return elementoService.verificarSeExisteMaisDeUmaEntidade(dto, entidades);
    }

    public boolean mostrarOpcaoSecretario(ElementoDTO dto) {
        return elementoService.podeSerSecretario(dto);
    }

    public boolean ehTipoElementoApoio(String codigoTipoElemento) {
        return TipoElementoEnum.APOIO_TECNICO.getCodigo().equals(codigoTipoElemento) ||
                TipoElementoEnum.APOIO_ADMINISTRATIVO.getCodigo().equals(codigoTipoElemento);
    }

    public boolean ehSecretario(ElementoDTO dto) {
        return CargoMandatoEnum.SECRETARIO.getCodigo().equals(dto.getCargoMandato());
    }

    public void verificaEcraSecretario() {
        setNissIgualPresidente(RegistarCPCJWizardStep.SECRETARIO.equals(wizard.getCurrentStep()));
        setRegistoCpcj(false);
    }

    public void cancelarDesativarSecretario(ElementoDTO dto) {
        listaEntidadesRepresentantes = new ArrayList<>(listaEntidadesRepresentantesOriginal);
        dto.setTipoElemento(TipoElementoEnum.MEMBRO_RESTRITA.getCodigo());
        dto.setEntidade(entidadeOriginal);
        dto.setCargoMandato(CargoMandatoEnum.SECRETARIO.getCodigo());
        dto.setSecretario(true);
    }

    /************************************ GETTERS SETTERS *************************************************************/


    public List<ElementoDTO> getListaConsultaElementos() {
        return listaConsultaElementos;
    }

    public void setListaConsultaElementos(List<ElementoDTO> listaConsultaElementos) {
        this.listaConsultaElementos = listaConsultaElementos;
    }

    public FiltroPesquisaElementoDTO getFiltroPesquisaElementoDTO() {
        return filtroPesquisaElementoDTO;
    }

    public void setFiltroPesquisaElementoDTO(FiltroPesquisaElementoDTO filtroPesquisaElementoDTO) {
        this.filtroPesquisaElementoDTO = filtroPesquisaElementoDTO;
    }

    public List<ResultadoPesquisaElementoDadosPessoaisDTO> getListaElementosDadosPessoaisDTO() {
        return listaElementosDadosPessoaisDTO;
    }

    public void setListaElementosDadosPessoaisDTO(List<ResultadoPesquisaElementoDadosPessoaisDTO> listaElementosDadosPessoaisDTO) {
        this.listaElementosDadosPessoaisDTO = listaElementosDadosPessoaisDTO;
    }

    public ElementoDTO getElementoConsultaDTO() {
        return elementoConsultaDTO;
    }

    public void setElementoConsultaDTO(ElementoDTO elementoConsultaDTO) {
        this.elementoConsultaDTO = elementoConsultaDTO;
    }

    public String getMensagemSemResultado() {
        return mensagemSemResultado;
    }

    public void setMensagemSemResultado(String mensagemSemResultado) {
        this.mensagemSemResultado = mensagemSemResultado;
    }

    public String getAlinhamento() {
        return alinhamento;
    }

    public void setAlinhamento(String alinhamento) {
        this.alinhamento = alinhamento;
    }

    public boolean getEhInicioEcraRegistarElemento() {
        return ehInicioEcraRegistarElemento;
    }

    public void setEhInicioEcraRegistarElemento(boolean ehInicioEcraRegistarElemento) {
        this.ehInicioEcraRegistarElemento = ehInicioEcraRegistarElemento;
    }

    public ElementoDTO getElementoDTO() {
        return elementoDTO;
    }

    public void setElementoDTO(ElementoDTO elementoDTO) {
        this.elementoDTO = elementoDTO;
    }

    public boolean exibeBotaoComparar() {
        return elementoVersaoService.obterElementosVersao(elementoConsultaDTO.getId()).size() > 1 ||
                elementoVersaoService.obterUtilizadorVersao(elementoConsultaDTO.getUtilizadorPCJ().getId()).size() > 1;
    }

    public boolean getSecretarioOriginal() {
        return secretarioOriginal;
    }

    public void setSecretarioOriginal(boolean secretarioOriginal) {
        this.secretarioOriginal = secretarioOriginal;
    }

    private boolean isEditarElemento() {
        return elementoDTO.getId() != null;
    }

    private void processaValidacaoDeDocumentosAnexados() throws PCJException {
        if (existeDocumentosNaoAnexados()) {
            JsfMessageUtil.mostraMensagemErro("Não é possível editar um elemento, pois existem documentos de designação não anexados!");
            throw new PCJException("Validação: Documentos não anexado!");
        }
    }

    private boolean existeDocumentosNaoAnexados() {
        return elementoDTO.getDocumentoDTO().documentoNaoEstaAnexado();
    }

    @Override
    public void verificarEntidadeCedente(ElementoDTO elementoDTO) {
        LoggingHelper.logEntrada(LOGGER, elementoDTO);
        List<Elemento> elementos = elementoService.getListaEntidadeCedente(elementoDTO);

        if (!TIPOS_APOIO.contains(elementoDTO.getTipoElemento()) && !elementoService.verificarPodeVincularEntidadeCedente(elementoDTO, elementos.size())) {
            try {
                JsfMessageUtil.mostraMensagemErro(elementoService.getTextoValidacaoEntidades(elementos, elementoDTO, listaEntidadesRepresentantes));
                elementoDTO.setEntidade(null);
                throw new PCJException("Entidade já associada a outro elemento");
            } catch (PCJException e) {
                LoggingHelper.logError(e);
                e.printStackTrace();
            }
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public List<HistoricoElementoDTO> getHistoricosElemento() {
        return historicosElemento;
    }

    public void setHistoricosElemento(List<HistoricoElementoDTO> historicosElemento) {
        this.historicosElemento = historicosElemento;
    }

    public void setExibirBtnAlterar(boolean exibirBtnAlterar) {
        this.exibirBtnAlterar = exibirBtnAlterar;
    }

    @Override
    public boolean mostrarEcraRegistarElementoComComponente() {
        return isMostrarEcran();
    }

    public List<ResultadoPesquisaElementoDTO> getListaElementos() {
        return listaElementos;
    }

    public void setListaElementos(List<ResultadoPesquisaElementoDTO> listaElementos) {
        this.listaElementos = listaElementos;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public boolean isPaginatorVisible() {
        return historicosElemento != null && historicosElemento.size() > 10;
    }
}
