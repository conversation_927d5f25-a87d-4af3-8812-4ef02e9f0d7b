package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.restrita.ElementoFolhaRestritaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.ElementoFolhaRestrita;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = ElementoFolhaIdMapper.class)
public interface ElementoFolhaRestritaMapper {

    ElementoFolhaRestritaDTO toDTO(ElementoFolhaRestrita elementoFolhaPCJ);

    ElementoFolhaRestrita toEntity(ElementoFolhaRestritaDTO elementoFolhaPCJDTO);

}
