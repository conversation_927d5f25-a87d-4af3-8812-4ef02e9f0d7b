package pt.segsocial.pcj.ppp.enums;

public enum RececaoExpedienteEnum {

    D("Sim", "Dentro do horáro da CPCJ"),
    F("<PERSON><PERSON>", "Fora do horário da CPCJ");

    private final String descricao;
    private final String descricaoCompleta;

    RececaoExpedienteEnum(String descricao, String descricaoCompleta) {
        this.descricao = descricao;
        this.descricaoCompleta = descricaoCompleta;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getDescricaoCompleta() {
        return descricaoCompleta;
    }
}
