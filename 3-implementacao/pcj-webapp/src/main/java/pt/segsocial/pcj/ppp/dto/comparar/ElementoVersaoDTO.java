package pt.segsocial.pcj.ppp.dto.comparar;

import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;

import java.io.Serializable;
import java.util.Date;

public class ElementoVersaoDTO extends BaseCompararVersaoDTO implements Serializable {

    private Long id;
    private Long idElemento;

    private String nomeProfissional;
    private String nomeTipoDocumento;
    private String nomeComissao;
    private String tipoElemento;
    private String email;
    private Date dataInicioVigencia;
    private Date dataFimVigencia;
    private String valenciaTecnica;
    private String outraValencia;
    private String codigoEntidadeEnum;
    private boolean apoioCn;
    private Date dataPrimeiraRenovacaoVigencia;
    private Date dataSegundaRenovacaoVigencia;
    private Double horasSemanal;
    private Double horasMensal;
    private String entidade;
    private Date dataVersao;
    private String nissUtilizadorResponsavel;
    private String cargoMandato;
    private Date dataInicioMandato;
    private Date dataRenovacaoMandato;
    private Date dataFimMandato;
    private Date dataFimProlongamento;
    private DocumentoPCJ documentoPCJ;
    private UtilizadorPCJDTO utilizadorPCJ;
    private Boolean ativo;

    private ElementoDTO elemento;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getNomeTipoDocumento() {
        return nomeTipoDocumento;
    }

    public void setNomeTipoDocumento(String tipoDocumento) {
        this.nomeTipoDocumento = nomeTipoDocumento;
    }

    public String getNomeComissao() {
        return nomeComissao;
    }

    public void setNomeComissao(String nomeComissao) {
        this.nomeComissao = nomeComissao;
    }

    public String getTipoElemento() {
        return tipoElemento;
    }

    public void setTipoElemento(String tipoElemento) {
        this.tipoElemento = tipoElemento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Date getDataFimVigencia() {
        return dataFimVigencia;
    }

    public void setDataFimVigencia(Date dataFimVigencia) {
        this.dataFimVigencia = dataFimVigencia;
    }

    public String getValenciaTecnica() {
        return valenciaTecnica;
    }

    public void setValenciaTecnica(String valenciaTecnica) {
        this.valenciaTecnica = valenciaTecnica;
    }

    public String getOutraValencia() {
        return outraValencia;
    }

    public void setOutraValencia(String outraValencia) {
        this.outraValencia = outraValencia;
    }

    public String getCodigoEntidadeEnum() {
        return codigoEntidadeEnum;
    }

    public void setCodigoEntidadeEnum(String codigoEntidadeEnum) {
        this.codigoEntidadeEnum = codigoEntidadeEnum;
    }

    public boolean isApoioCn() {
        return apoioCn;
    }

    public void setApoioCn(boolean apoioCn) {
        this.apoioCn = apoioCn;
    }

    public Date getDataPrimeiraRenovacaoVigencia() {
        return dataPrimeiraRenovacaoVigencia;
    }

    public void setDataPrimeiraRenovacaoVigencia(Date dataPrimeiraRenovacaoVigencia) {
        this.dataPrimeiraRenovacaoVigencia = dataPrimeiraRenovacaoVigencia;
    }

    public Date getDataSegundaRenovacaoVigencia() {
        return dataSegundaRenovacaoVigencia;
    }

    public void setDataSegundaRenovacaoVigencia(Date dataSegundaRenovacaoVigencia) {
        this.dataSegundaRenovacaoVigencia = dataSegundaRenovacaoVigencia;
    }

    public Double getHorasSemanal() {
        return horasSemanal;
    }

    public void setHorasSemanal(Double horasSemenal) {
        this.horasSemanal = horasSemenal;
    }

    public Double getHorasMensal() {
        return horasMensal;
    }

    public void setHorasMensal(Double horasMensal) {
        this.horasMensal = horasMensal;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public Date getDataVersao() {
        return dataVersao;
    }

    public void setDataVersao(Date dataVersao) {
        this.dataVersao = dataVersao;
    }

    public String getNissUtilizadorResponsavel() {
        return nissUtilizadorResponsavel;
    }

    public void setNissUtilizadorResponsavel(String nissUtilizadorResponsavel) {
        this.nissUtilizadorResponsavel = nissUtilizadorResponsavel;
    }

    public String getCargoMandato() {
        return cargoMandato;
    }

    public void setCargoMandato(String cargoMandato) {
        this.cargoMandato = cargoMandato;
    }

    public Date getDataInicioMandato() {
        return dataInicioMandato;
    }

    public void setDataInicioMandato(Date dataInicioMandato) {
        this.dataInicioMandato = dataInicioMandato;
    }

    public Date getDataRenovacaoMandato() {
        return dataRenovacaoMandato;
    }

    public void setDataRenovacaoMandato(Date dataRenovacaoMandato) {
        this.dataRenovacaoMandato = dataRenovacaoMandato;
    }

    public Date getDataFimMandato() {
        return dataFimMandato;
    }

    public void setDataFimMandato(Date dataFimMandato) {
        this.dataFimMandato = dataFimMandato;
    }

    public DocumentoPCJ getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentoPCJ documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public UtilizadorPCJDTO getUtilizadorPCJ() {
        return utilizadorPCJ;
    }

    public void setUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJ) {
        this.utilizadorPCJ = utilizadorPCJ;
    }

    public Date getDataFimProlongamento() {
        return dataFimProlongamento;
    }

    public void setDataFimProlongamento(Date dataFimProlongamento) {
        this.dataFimProlongamento = dataFimProlongamento;
    }

    public Long getIdElemento() {
        return idElemento;
    }

    public void setIdElemento(Long idElemento) {
        this.idElemento = idElemento;
    }

    public ElementoDTO getElemento() {
        return elemento;
    }

    public void setElemento(ElementoDTO elemento) {
        this.elemento = elemento;
    }
}
