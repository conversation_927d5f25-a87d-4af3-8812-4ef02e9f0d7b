package pt.segsocial.pcj.pae.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.pae.jpa.entity.Historico;
import pt.segsocial.pcj.pae.jpa.entity.ProcessoPae;
import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.util.List;

public class HistoricoDAO extends DAO<Historico, Long> {

    public HistoricoDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<Historico> findByProcesso(Long idProcesso) {

        try {
            return this.getEntityManager()
                    .createNamedQuery(Historico.FIND_BY_ID_PROCESSO, Historico.class)
                    .setParameter("idProcesso", idProcesso)
                    .getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }

    public boolean existeProcessoEstadoIndeferidoContestadoMenorOuIgualDezDiasDataAtual(ProcessoPae processoPae) {
        return !getEntityManager()
                .createNamedQuery(Historico.FIND_BY_ID_PROCESSO_AND_DATA_AND_ESTADO, Historico.class)
                .setParameter("processoPae", processoPae)
                .setParameter("estadoAtual", EstadoProcessoEnum.INDEFERIDO)
                .getResultList().isEmpty();
    }

}
