package pt.segsocial.pcj.ppp.jpa.dao;

import org.apache.commons.collections.CollectionUtils;
import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static pt.segsocial.pcj.core.util.PCJDateUtil.returnDatePlus;

public class PerfilElementoDAO extends DAO<PerfilElemento, Long> {

    public PerfilElementoDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public PerfilElemento buscarPerfilElementoPorElemento(Elemento elemento) {
        Date dataAtual = returnDatePlus(0);
        StringBuilder queryBuilder = new StringBuilder("Select pe From PerfilElemento pe where pe.elemento = :elemento AND (pe.fimVigencia IS NULL OR pe.fimVigencia >= :dataAtual)");

        try {
            return this.getEntityManager()
                    .createQuery(queryBuilder.toString(), PerfilElemento.class)
                    .setParameter("elemento", elemento)
                    .setParameter("dataAtual", dataAtual)
                    .getSingleResult();
        } catch(NoResultException e) {
            LoggingHelper.logSaida("Elemento com id " + elemento.getId() + " nao tem perfil associado.");
            LoggingHelper.logError(e);
            return null;
        }
    }

    public PerfilElemento buscarPerfilElementoPorElementoVigente(Elemento elemento) {
        Date dataAtual = returnDatePlus(0);
        StringBuilder queryBuilder = new StringBuilder( " Select pe From PerfilElemento pe " +
                                                        " JOIN FETCH pe.elemento e" +
                                                        " JOIN FETCH e.comissao c" +
                                                        " JOIN FETCH c.morada m" +
                                                        " JOIN FETCH m.territorioPCJ " +
                                                        " JOIN FETCH c.competencias " +
                                                        " WHERE pe.elemento = :elemento " +
                                                        " AND pe.fimVigencia >= :dataAtual");

        try {
            List<PerfilElemento> perfilElementos = this.getEntityManager()
                    .createQuery(queryBuilder.toString(), PerfilElemento.class)
                    .setParameter("elemento", elemento)
                    .setParameter("dataAtual", dataAtual)
                    .setMaxResults(1)
                    .getResultList();

            if(CollectionUtils.isEmpty(perfilElementos)) {
                return buscarPerfilCNElementoPorElementoVigente(elemento);
            }

            return perfilElementos.get(0);
        } catch(NoResultException e) {
            LoggingHelper.logSaida("Elemento com id " + elemento.getId() + " nao tem perfil associado.");
            LoggingHelper.logError(e);
            return null;
        }
    }

    private PerfilElemento buscarPerfilCNElementoPorElementoVigente(Elemento elemento) {
        Date dataAtual = returnDatePlus(0);
        StringBuilder queryBuilder = new StringBuilder( " Select pe From PerfilElemento pe where pe.elemento = :elemento AND pe.fimVigencia >= :dataAtual" );

        try {
            List<PerfilElemento> perfilElementos = this.getEntityManager()
                    .createQuery(queryBuilder.toString(), PerfilElemento.class)
                    .setParameter("elemento", elemento)
                    .setParameter("dataAtual", dataAtual)
                    .setMaxResults(1)
                    .getResultList();

            if(CollectionUtils.isEmpty(perfilElementos)) {
                return new PerfilElemento();
            }

            return perfilElementos.get(0);
        } catch(NoResultException e) {
            LoggingHelper.logSaida("Elemento com id " + elemento.getId() + " nao tem perfil associado.");
            LoggingHelper.logError(e);
            return null;
        }
    }

    public List<PerfilElemento> buscarPerfisElementoPorElemento(Elemento elemento) {
        StringBuilder queryBuilder = new StringBuilder("Select pe From PerfilElemento pe where pe.elemento = :elemento and pe.elemento.ativo = 1 ");

        try {
            return this.getEntityManager()
                    .createQuery(queryBuilder.toString(), PerfilElemento.class)
                    .setParameter("elemento", elemento)
                    .getResultList();
        } catch(NoResultException e) {
            return new ArrayList<>();
        }
    }

    public List<PerfilElemento> buscarPerfisElementoPorElemento(Long idElemento) {
        StringBuilder queryBuilder = new StringBuilder("Select pe From PerfilElemento pe where pe.elemento.id = :idElemento and pe.elemento.ativo = 1 and pe.fimVigencia >= :dataAtual");

        try {
            return this.getEntityManager()
                    .createQuery(queryBuilder.toString(), PerfilElemento.class)
                    .setParameter("idElemento", idElemento)
                    .setParameter("dataAtual", PCJDateUtil.returnDatePlus(0))
                    .getResultList();
        } catch(NoResultException e) {
            return new ArrayList<>();
        }
    }
}
