package pt.segsocial.pcj.pae.bean.service;

import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.pae.jpa.dao.ProcessoPaeDAO;
import pt.segsocial.pcj.pae.jpa.entity.RequerimentoPae;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.Date;

public class SubmissaoProcessoPaeService implements Serializable {

	private static final long serialVersionUID = 8520548323291609531L;
	
	private final EntityManager entityManager;
    private final RequerimentoPae requerimentoPae;

	public SubmissaoProcessoPaeService(EntityManager entityManager, RequerimentoPae requerimentoPae) {
		this.entityManager = entityManager;
		this.requerimentoPae = requerimentoPae;
	}
	
	public void registar() throws PCJException {
		try {
			this.requerimentoPae.getProcessoPae().setDataInicio(new Date());
			new ProcessoPaeDAO(entityManager).update(this.requerimentoPae.getProcessoPae());
		} catch (DomainException e) {
			throw new PCJException(e);
		}
	}
}
