package pt.segsocial.pcj.ppp.bean.comunicacao.wizard;

import ch.qos.logback.classic.Level;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.primefaces.event.SelectEvent;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.gvr.api.dto.mia.CodigoPostalDTO;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.bean.comunicacao.ComunicacaoUtil;
import pt.segsocial.pcj.ppp.dto.*;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoCriancaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.CriancaDTO;
import pt.segsocial.pcj.ppp.service.comunicacao.CriancaPPPService;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Named(value = "pcjCriancaJovemStepBean")
@ViewAccessScoped
public class CriancaJovemStepBean implements Serializable {

    private ComunicacaoCriancaDTO comunicacaoCrianca = new ComunicacaoCriancaDTO();

    private List<ConcelhoDTO> concelhos = new ArrayList<>();

    private List<DistritoDTO> distritos;

    private List<FreguesiaDTO> freguesias;

    private List<DetalheDominioVO> informacaoIdades;

    private List<DetalheDominioVO> generos;

    private List<DetalheDominioVO> idadesCrianca;

    private List<CriancaDTO> criancasAutoComplete = new ArrayList<>();

    private Long idCrianca;

    private String nomeCrianca;

    @Inject
    private PCJServiceDomain pcjServiceDomain;

    @Inject
    private PCJServiceLocator pcjServiceLocator;

    @Inject
    private CriancaPPPService criancaPPPService;

    @PostConstruct
    public void init() throws PCJException {
        LoggingHelper.logEntrada();
        if(idCrianca != null) {
            CriancaDTO criancaDTO = criancaPPPService.buscaCriancaPorId(idCrianca);
            comunicacaoCrianca.setCrianca(criancaDTO);
            comunicacaoCrianca.setNome(criancaDTO.getNome());
        }
        distritos = pcjServiceLocator.getGvrDelegate().getDistritos();
        informacaoIdades = pcjServiceDomain.informacaoIdadesOrdenada();
        generos = new ArrayList<>(pcjServiceDomain.getDominioHolderGenero().get().getDetalhes().values());
        idadesCrianca = new ArrayList<>(pcjServiceDomain.getDominioHolderIdadeCrianca().get().getDetalhes().values());

        Collections.sort(idadesCrianca, new Comparator<DetalheDominioVO>() {
            @Override
            public int compare(DetalheDominioVO o1, DetalheDominioVO o2) {
                return o1.getCodigoDetalheDominio().compareTo(o2.getCodigoDetalheDominio());
            }
        });

        LoggingHelper.logSaida();
    }

    public List<CriancaDTO> buscarCriancas(String query) {
        if (query == null || query.isEmpty()) {
            return new ArrayList<>();
        }
        criancasAutoComplete = criancaPPPService.listarCrianca(query);
        return criancasAutoComplete;
    }

    public void aoSelecionarCrianca(SelectEvent event) {
        if(this.comunicacaoCrianca == null) {
            this.comunicacaoCrianca = new ComunicacaoCriancaDTO();
        }
        this.comunicacaoCrianca.setCrianca((CriancaDTO) event.getObject());
    }

    public void alterouOpcaoDataConhecida() {
        if(!comunicacaoCrianca.isDataNascimentoConhecida()) {
            comunicacaoCrianca.setDataNascimento(null);
            comunicacaoCrianca.setCodigoInformacaoIdade(null);
        }
    }

    public void alterouOpcaoMoradaConhecida() {
        MoradaDTO moradaDTO = new MoradaDTO();
        moradaDTO.setAtribuirMorada(comunicacaoCrianca.isMoradaConhecida());
        comunicacaoCrianca.setMorada(moradaDTO);
    }

    public void distritoSelecionadoListener() {
        LoggingHelper.logEntrada();
        try {
            if (comunicacaoCrianca.getMorada() != null && comunicacaoCrianca.getMorada().getDistritoDto() != null) {
                this.concelhos = (pcjServiceLocator.getGvrDelegate().getConcelhos(comunicacaoCrianca.getMorada().getDistritoDto().getCodigo()));
            }
            LoggingHelper.logSaida(Level.DEBUG,"Concelhos: " + concelhos);
        } catch (PCJException e) {
            JsfMessageUtil.mostraMensagemErro("Erro ao obter concelhos");
            LoggingHelper.logError(e);
        }
        LoggingHelper.logSaida();
    }

    public void concelhoSelecionadoListener() {
        try {
            if (comunicacaoCrianca.getMorada() != null && comunicacaoCrianca.getMorada().getDistritoDto() != null && comunicacaoCrianca.getMorada().getConcelhoDto() != null) {
                freguesias = pcjServiceLocator.getGvrDelegate().getFreguesias(comunicacaoCrianca.getMorada().getDistritoDto().getCodigo(), comunicacaoCrianca.getMorada().getConcelhoDto().getCodigoConcelho());
            }
            LoggingHelper.logSaida(Level.DEBUG,"Freguesias: " + freguesias);
        } catch (PCJException e) {
            LoggingHelper.logError(e);
        }
    }

    public void verificaCodigoPostal() {
        if (comunicacaoCrianca.getMorada() != null && comunicacaoCrianca.getMorada().getCodigoPostal() != null && comunicacaoCrianca.getMorada().getCodigoPostal().replace("-", "").length() == 7) {
            buscarMorada();
        }
    }

    public void buscarMorada() {
        try {
            CodigoPostalDTO dto = preencherDadosMoradaPorCodigoPostal();
            if (dto == null) {
                ComunicacaoUtil.preencherLocalizacaoDTO(comunicacaoCrianca.getMorada(), pcjServiceLocator.getGvrDelegate());
            }
            distritoSelecionadoListener();
            concelhoSelecionadoListener();

            LoggingHelper.logSaida("Morada: " + comunicacaoCrianca.getMorada());
        } catch (PCJException e) {
            JsfMessageUtil.mostraMensagemErro("Código postal não encontrado ou inexistente.");
            LoggingHelper.logError(e);
        }
    }

    private CodigoPostalDTO preencherDadosMoradaPorCodigoPostal() throws PCJException {
        if(comunicacaoCrianca == null || comunicacaoCrianca.getMorada() == null || comunicacaoCrianca.getMorada().getCodigoPostal() == null) {
            return null;
        }

        String codigoPostal = comunicacaoCrianca.getMorada().getCodigoPostal();
        CodigoPostalDTO dto = pcjServiceLocator.getIdDelegate().obterMoradaPortuguesa(codigoPostal);

        if (dto != null) {
            LoggingHelper.logEntrada(dto);

            MoradaDTO morada = comunicacaoCrianca.getMorada();
            morada.setCodigoPostal(dto.getNumeroCodigoPostal());
            ComunicacaoUtil.preencherLocalizacaoDTO(morada, pcjServiceLocator.getGvrDelegate());
        }
        return dto;
    }

    public ComunicacaoCriancaDTO getComunicacaoCrianca() {
        return comunicacaoCrianca;
    }

    public void setComunicacaoCrianca(ComunicacaoCriancaDTO comunicacaoCrianca) {
        this.comunicacaoCrianca = comunicacaoCrianca;
    }

    public Long getIdCrianca() {
        return idCrianca;
    }

    public void setIdCrianca(Long idCrianca) {
        this.idCrianca = idCrianca;
    }

    public List<DistritoDTO> getDistritos() {
        return distritos;
    }

    public void setDistritos(List<DistritoDTO> distritos) {
        this.distritos = distritos;
    }

    public List<ConcelhoDTO> getConcelhos() {
        return concelhos;
    }

    public void setConcelhos(List<ConcelhoDTO> concelhos) {
        this.concelhos = concelhos;
    }

    public List<FreguesiaDTO> getFreguesias() {
        return freguesias;
    }

    public void setFreguesias(List<FreguesiaDTO> freguesias) {
        this.freguesias = freguesias;
    }

    public String getNomeCrianca() {
        return nomeCrianca;
    }

    public void setNomeCrianca(String nomeCrianca) {
        this.nomeCrianca = nomeCrianca;
    }

    public List<DetalheDominioVO> getInformacaoIdades() {
        return informacaoIdades;
    }

    public void setInformacaoIdades(List<DetalheDominioVO> informacaoIdades) {
        this.informacaoIdades = informacaoIdades;
    }

    public List<DetalheDominioVO> getGeneros() {
        return generos;
    }

    public void setGeneros(List<DetalheDominioVO> generos) {
        this.generos = generos;
    }

    public List<DetalheDominioVO> getIdadesCrianca() {
        return idadesCrianca;
    }

    public void setIdadesCrianca(List<DetalheDominioVO> idadesCrianca) {
        this.idadesCrianca = idadesCrianca;
    }

    public List<CriancaDTO> getCriancasAutoComplete() {
        return criancasAutoComplete;
    }

    public void setCriancasAutoComplete(List<CriancaDTO> criancasAutoComplete) {
        this.criancasAutoComplete = criancasAutoComplete;
    }
}
