package pt.segsocial.pcj.ppp.bean.entidadeexterna;

import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.primefaces.event.FileUploadEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.util.StringUtils;
import pt.segsocial.id.api.dto.PessoaSingularDTO;
import pt.segsocial.id.api.exception.IDException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.bean.AbstractPppBean;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.entidadeexterna.EntidadeExternaDTO;
import pt.segsocial.pcj.ppp.dto.entidadeexterna.FiltroPesquisaEntidadeExternaDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.enums.CodCartaoMembroEnum;
import pt.segsocial.pcj.ppp.enums.TipoDocumentoEnum;
import pt.segsocial.pcj.ppp.service.ElementoService;
import pt.segsocial.pcj.ppp.service.ElementoVersaoService;
import pt.segsocial.pcj.ppp.service.EntidadeExternaService;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.PcjMessages;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static ch.qos.logback.classic.Level.DEBUG;

@Named(value = "pcjEntidadeExternaBean")
@Stateful(name = "pcjEntidadeExternaBean")
@LocalBean
@ViewAccessScoped
public class EntidadeExternaBean extends AbstractPppBean implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(EntidadeExternaBean.class);
    private final List<String> ENTIDADE_TIPO_MP
            = Arrays.asList(
            CodCartaoMembroEnum.MP_SEIVD.getCodigoDominio(),
            CodCartaoMembroEnum.MP_INTERLOCUTOR.getCodigoDominio());
    @Inject
    private EntidadeExternaService entidadeExternaService;

    @Inject
    private ElementoService elementoService;

    @Inject
    private PCJServiceDomain pcjServiceDomain;

    @Inject
    private ElementoVersaoService elementoVersaoService;

    private FiltroPesquisaEntidadeExternaDTO filtroPesquisaEntidadeExternaDTO = new FiltroPesquisaEntidadeExternaDTO();
    private List<EntidadeExternaDTO> listaEntidadeExternaDTO = new ArrayList<>();
    private Collection<DetalheDominioVO> listaTipoEntidadeExternaPesquisa;
    private EntidadeExternaDTO entidadeExternaDTO;
    private EntidadeExternaDTO entidadeExternaConsultaDTO = new EntidadeExternaDTO();
    private Collection<DetalheDominioVO> listaTipoEntidadesRepresentantes = new ArrayList<>();
    private Collection<DetalheDominioVO> listaTiposDocumentos = new ArrayList<>();
    private boolean ehInicioEcraRegistarUtilizador = true;
    private String nomeComissao;
    private ComissaoPCJDTO comissaoPCJDTO = new ComissaoPCJDTO();
    private boolean edicao = false;
    private boolean exibirSelectCpcj = false;
    private boolean exibirBtnAddCpcj = false;
    private boolean exibirBtnAlterar = false;
    private boolean tipoPesquisa = true;

    @URLAction(mappingId = PCJSubsystem.OP_PPP_PESQUISAR_UTILIZADOR, onPostback = false)
    public void init() {
        LoggingHelper.logEntrada(LOGGER);
        ehInicioEcraRegistarUtilizador = true;
        listaTipoEntidadeExternaPesquisa = new ArrayList<>(pcjServiceDomain.getDominioHolderEntidadesExternas().get().getDetalhes().values());
        listaComissoesPesquisaDTO = carregarTodasComissoes();

        mensagemSemResultado = EFETUE_PESQUISA;
        alinhamento = CENTER;
        LoggingHelper.logSaida(LOGGER);
    }

    @URLAction(mappingId = PCJSubsystem.OP_PPP_REGISTAR_UTILIZADOR, onPostback = false)
    public void onLoad() {
        LoggingHelper.logEntrada(LOGGER, "Alterar elemento: " + isEdicao());
        ehInicioEcraRegistarUtilizador = true;
        setEcraUtilizador(true);
        carregarCombos();
        if (isEdicao()) {
            carregarEntidade(entidadeExternaDTO);
            verificaTipoEntidade();
            entidadeExternaDTO.setNissValidado(true);
        } else {
            entidadeExternaDTO = new EntidadeExternaDTO();
        }
        atualizarNifObrigatorio(entidadeExternaDTO.getUtilizadorPCJ());
        LoggingHelper.logEntrada(LOGGER);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void registarUtilizador() {
        LoggingHelper.logEntrada(LOGGER, entidadeExternaDTO.getUtilizadorPCJ());
        try {
            verificaComissoes();
            if (condicaoCpcjNaoAtendida()) {
                JsfMessageUtil.mostraMensagemErro("Adicione pelo menos uma CPCJ.");
                return;
            }
            realizarRegistroOuEdicao();
            redirecionarUsuario();
        } catch (DomainException | IOException | PCJException e) {
            LoggingHelper.logError(e);
            JsfMessageUtil.mostraMensagemErro("Erro ao tentar registar utilizador.");
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void resetForm() {
        sucesso = true;
    }

    private void redirecionarUsuario() throws IOException {
        setEdicao(false);
        pcjSubsystem.redirectTo(pcjSubsystem.pesquisarUtilizador(), null);
    }

    private void realizarRegistroOuEdicao() throws DomainException, PCJException {
        LoggingHelper.logEntrada(LOGGER, entidadeExternaDTO);
        super.validarNumeroDocumento(entidadeExternaDTO.getUtilizadorPCJ().getCodigoDocumento(), entidadeExternaDTO.getUtilizadorPCJ().getNumeroDocumento());
        if (isEdicao()) {
            entidadeExternaService.editar(entidadeExternaDTO, getPerfilDTO(), entidadeExternaDTO.getTipoEntidadeExterna());
            JsfMessageUtil.mostraMensagemSucesso("Utilizador alterado com sucesso.");
        } else {
            entidadeExternaService.registar(entidadeExternaDTO, getPerfilDTO());
            JsfMessageUtil.mostraMensagemSucesso("Utilizador registado com sucesso.");
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private boolean verificaSeONissDaEntidadeEhValido() {
        LoggingHelper.logEntrada(LOGGER, entidadeExternaDTO.getUtilizadorPCJ());
        if (entidadeExternaDTO == null || entidadeExternaDTO.getUtilizadorPCJ() == null) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_NISS_INVALIDO"));
            return false;
        }

        boolean isValid = validarNiss(entidadeExternaDTO.getUtilizadorPCJ().getNiss());
        if (!isValid) {
            LoggingHelper.logEntrada(LOGGER, "NISS: " + entidadeExternaDTO.getUtilizadorPCJ().getNiss() + " invalido.");
            entidadeExternaDTO.setNissValidado(false);
            entidadeExternaDTO.clearNissInvalido();
            return false;
        }
        LoggingHelper.logSaida(LOGGER);
        return true;
    }

    private boolean condicaoCpcjNaoAtendida() {
        return isExibirSelectCpcj() && isExibirBtnAddCpcj() && entidadeExternaDTO.getComissoes().isEmpty();
    }

    private void verificaComissoes() {
        if (entidadeExternaService.getCOD_ENTIDADE_TIPO_CN().contains(entidadeExternaDTO.getTipoEntidadeExterna())) {
            entidadeExternaDTO.setComissao(null);
            entidadeExternaDTO.getComissoes().clear();
        } else if (ENTIDADE_TIPO_MP.contains(entidadeExternaDTO.getTipoEntidadeExterna())) {
            entidadeExternaDTO.getComissoes().clear();
            entidadeExternaDTO.getComissoes().add(entidadeExternaDTO.getComissao());
        }
    }

    public boolean isComissaoNacional() {
        return entidadeExternaService.getCOD_ENTIDADE_TIPO_CN().contains(entidadeExternaDTO.getTipoEntidadeExterna());
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void ativaCessarUtilizador(EntidadeExternaDTO dto, boolean status) {
        LoggingHelper.logEntrada(LOGGER, "Ativar/Cessar utilizador com id: " + dto.getId() + " atribuindo status: " + status);
        dto.setAtivo(status);

        entidadeExternaService.atualizarStatus(dto, status, getPerfilDTO());

        JsfMessageUtil.mostraMensagemSucesso("Utilizador ativado/cessado " + COM_SUCESSO);
        LoggingHelper.logSaida(LOGGER);
    }

    public void pesquisar() throws PCJException {
        listaEntidadeExternaDTO = entidadeExternaService.buscarPorFiltro(filtroPesquisaEntidadeExternaDTO);

        if (listaEntidadeExternaDTO.isEmpty()) {
            mensagemSemResultado = SEM_RESULTADO;
            alinhamento = CENTER;
        } else {
            alinhamento = LEFT;
        }
    }

    public void carregarEntidade(EntidadeExternaDTO dto) {
        LoggingHelper.logEntrada(LOGGER, dto);
        setEntidadeExternaDTO(dto);
        entidadeExternaConsultaDTO = dto;
        entidadeExternaConsultaDTO.setComissoes(entidadeExternaService.carregarComissoesDTO(dto));
        LoggingHelper.logSaida(LOGGER);
    }

    public void verificaTipoEntidade() {
        LoggingHelper.logEntrada(LOGGER, "Tipo entidade: " + entidadeExternaDTO.getTipoEntidadeExterna());
        if (entidadeExternaDTO.getTipoEntidadeExterna().equals(CodCartaoMembroEnum.TEC_REGIONAL.getCodigoDominio()) ||
                entidadeExternaDTO.getTipoEntidadeExterna().equals(CodCartaoMembroEnum.TEC_REGIONAL_MASTER.getCodigoDominio())) {
            setExibirSelectCpcj(true);
            setExibirBtnAddCpcj(true);
            entidadeExternaDTO.setComissao(!entidadeExternaDTO.getComissoes().isEmpty() ? entidadeExternaDTO.getComissoes().get(0) : null);
        } else if (entidadeExternaDTO.getTipoEntidadeExterna().equals(CodCartaoMembroEnum.MP_INTERLOCUTOR.getCodigoDominio()) ||
                entidadeExternaDTO.getTipoEntidadeExterna().equals(CodCartaoMembroEnum.MP_SEIVD.getCodigoDominio())) {
            setExibirSelectCpcj(true);
            setExibirBtnAddCpcj(false);
            entidadeExternaDTO.setComissao(entidadeExternaService.getComissaoDto(entidadeExternaDTO.getComissoes()));
        } else {
            setExibirSelectCpcj(false);
            setExibirBtnAddCpcj(false);
            entidadeExternaDTO.setComissao(null);
            entidadeExternaDTO.setComissoes(new ArrayList());
            entidadeExternaDTO.getListaCpcjAdicionar().clear();
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public void limparBtnPesquisaNissEmail() {
        resetForm();
        entidadeExternaDTO.setNissValidado(false);
        ehInicioEcraRegistarUtilizador = true;
        entidadeExternaDTO = new EntidadeExternaDTO();
    }

    public void consultarElementoPorNiss(EntidadeExternaDTO entityDto) throws IDException {
        LoggingHelper.logEntrada(LOGGER, entityDto);
        consultaPorNiss();
        if (entityDto.getUtilizadorPCJ() != null) {
            LoggingHelper.logEntrada(LOGGER, DEBUG, entityDto.getUtilizadorPCJ());
            atualizarNifObrigatorio(entityDto.getUtilizadorPCJ());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public void buscarPessoaPorNissEmailEntidadeExterna() throws PCJException {
        LoggingHelper.logEntrada(LOGGER, "Tipo pesquisa: " + tipoPesquisa);
        try {
            boolean resultValido = tipoPesquisa ? consultaPorNiss() : consultaPorEmail();

            if (existeRegistoElemento(entidadeExternaDTO)) {
                LoggingHelper.logEntrada(LOGGER, DEBUG,"Existe registo elemento com id: " + entidadeExternaDTO.getId());
                clearAttributtes();
                JsfMessageUtil.mostraMensagemErro("Esta pessoa já tem um registo ativo no sistema como um elemento de uma comissão");
                resultValido = false;
            }

            if (resultValido) {
                LoggingHelper.logEntrada(LOGGER, DEBUG,"Resultado valido para busca");
                atualizarNifObrigatorio(entidadeExternaDTO.getUtilizadorPCJ());
                resultOk();
            }
        } catch (IDException e) {
            LoggingHelper.logError(e);
            handleIDException(e, entidadeExternaDTO);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void resultOk() {
        ehInicioEcraRegistarUtilizador = false;
        if (entidadeExternaDTO.getUtilizadorPCJ().getId() != null) {
            JsfMessageUtil.mostraMensagemSucesso("Foi encontrado o elemento com o NISS inserido, alguns campos foram preenchidos automaticamente.");
        }
    }

    private boolean consultaPorEmail() {
        if (StringUtils.isBlank(entidadeExternaDTO.getEmail()) || !isValidEmail(entidadeExternaDTO.getEmail())) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_AUX_EMAIL_INVALIDO"));
            entidadeExternaDTO.setEmail(null);
            clearAttributtes();
            return false;
        }
        entidadeExternaDTO.setUtilizadorPCJ(entidadeExternaService.buscarUtilizadorPorEmail(entidadeExternaDTO.getEmail()));
        prepararEntidadeExternaDTO(entidadeExternaDTO, null);
        verificaHabilitaNiss();
        return true;
    }

    private boolean consultaPorNiss() throws IDException {
        String nissConsulta = entidadeExternaDTO.getUtilizadorPCJ().getNiss();
        setTipoDocumento(TipoDocumentoEnum.NISS.getCodigo());
        setNumeroDeDocumento(nissConsulta);
        if (!verificaSeONissDaEntidadeEhValido()) {
            clearAttributtes();
            return false;
        }

        if (StringUtils.isNotBlank(nissConsulta)) {

            if (pcjServiceLocator.servicePsEnable()) {
                PessoaSingularDTO pessoaSingularDTOApi = buscarPessoaSingularServicoPorNiss(nissConsulta);
                validarPessoaSingularDTOApi(pessoaSingularDTOApi, nissConsulta);
                prepararEntidadeExternaDTO(entidadeExternaDTO, pessoaSingularDTOApi);

            } else {
                UtilizadorPCJDTO utilizadorPCJDTO = entidadeExternaService.buscarUtilizadorPorNiss(nissConsulta);
                if (utilizadorPCJDTO.getId() != null) {
                    entidadeExternaDTO.setUtilizadorPCJ(utilizadorPCJDTO);
                } else {
                    entidadeExternaDTO.setUtilizadorPCJ(new UtilizadorPCJDTO(nissConsulta));
                }
                prepararEntidadeExternaDTO(entidadeExternaDTO, null);
                verificaHabilitaNiss();
            }
        }
        return true;
    }

    private void clearAttributtes() {
        resetForm();
        entidadeExternaDTO.setNissValidado(false);
        ehInicioEcraRegistarUtilizador = true;
    }

    private boolean existeRegistoElemento(EntidadeExternaDTO entidadeExternaDTO) {
        if (tipoPesquisa) {
            return elementoService.retornaElementoPorNissVinculoEntidadeEnterna(entidadeExternaDTO.getUtilizadorPCJ().getNiss());
        }
        return elementoService.retornaElementoPorEmailVinculoEntidadeEnterna(entidadeExternaDTO.getEmail());
    }

    private void verificaHabilitaNiss() {
        entidadeExternaDTO.setHabilitaNiss(entidadeExternaDTO.getUtilizadorPCJ().getNiss() == null);
    }

    private void prepararEntidadeExternaDTO(EntidadeExternaDTO entidadeExternaDTO, pt.segsocial.id.api.dto.PessoaSingularDTO pessoaSingularDTOApi) {
        if (entidadeExternaDTO.getUtilizadorPCJ().getNiss() != null) {
            String niss = pessoaSingularDTOApi != null ? String.valueOf(pessoaSingularDTOApi.getNiss()) : entidadeExternaDTO.getUtilizadorPCJ().getNiss();
            listaComissoesDTO = comissaoPCJService.carregarComissoesAtivasDiferenteEntidadeExterna(niss);
        } else {
            listaComissoesDTO = comissaoPCJService.carregarComissoesAtivasDiferenteEntidadeExternaPorEmail(entidadeExternaDTO.getEmail());
        }
    }

    private void handleIDException(IDException e, EntidadeExternaDTO entidadeExternaDTO) throws PCJException {
        LOGGER.error(String.format("Erro ao consultar NISS: %s", e.getMessage()));
        entidadeExternaDTO.setNissValidado(false);
        ehInicioEcraRegistarUtilizador = true;
        JsfMessageUtil.mostraMensagemAviso("Não foram encontrados elementos com o NISS inserido.");
        throw new PCJException("Não foram encontrados elementos com o NISS inserido.");
    }

    private void validarPessoaSingularDTOApi(PessoaSingularDTO pessoaSingularDTOApi, String niss) throws IDException {
        if (pessoaSingularDTOApi == null) {
            throw new IDException(niss);
        }
    }

    private void carregarCombos() {
        listaComissoesDTO = carregarTodasComissoes();
        listaTipoEntidadesRepresentantes = new ArrayList<>(pcjServiceDomain.getDominioHolderEntidadesExternas().get().getDetalhes().values());
        listaTiposDocumentos = elementoService.ordenarPorNome(new ArrayList<>(pcjServiceDomain.getDominioHolderTipoDocumento().get().getDetalhes().values()));
    }

    private List<ComissaoPCJDTO> carregarTodasComissoes() {
        return comissaoPCJService.carregarComissoesAtivas();
    }

    public void uploadPicture(FileUploadEvent event) {
        LoggingHelper.logEntrada(LOGGER, event);
        entidadeExternaDTO.setNomeFoto(event.getFile().getFileName());
        entidadeExternaDTO.getUtilizadorPCJ().setFoto(event.getFile().getContents());

        if (null != entidadeExternaDTO.getId()) {
            entidadeExternaDTO.getUtilizadorPCJ().setFoto(event.getFile().getContents());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public boolean mostrarEcraRegistarUtilizadorComComponentes() {
        return !ehInicioEcraRegistarUtilizador;
    }

    public boolean existeMaisDeUmaEntidadeRepresentante(EntidadeExternaDTO dto, List<DetalheDominioVO> entidades) {
        return entidadeExternaService.verificarSeExisteMaisDeUmaEntidade(dto, entidades);
    }

    public boolean existeEntidadeExterna() {
        return !listaEntidadeExternaDTO.isEmpty();
    }

    @Override
    public void limparUtilizador(UtilizadorPCJDTO utilizadorPCJDTO) {
        LoggingHelper.logEntrada(LOGGER, utilizadorPCJDTO);
        utilizadorPCJDTO.setId(null);
        utilizadorPCJDTO.setNiss(null);
        utilizadorPCJDTO.setFoto(null);
        utilizadorPCJDTO.setNomeProfissional(null);

        utilizadorPCJDTO.getPessoaSingularDTO().setTelemovel(null);
        utilizadorPCJDTO.getPessoaSingularDTO().setNomeCompleto(null);
        utilizadorPCJDTO.getPessoaSingularDTO().setTipoDocumento(null);
        utilizadorPCJDTO.getPessoaSingularDTO().setNumeroDocumento(null);
        utilizadorPCJDTO.getPessoaSingularDTO().setNumeroIdentificacaoFiscal(null);
        utilizadorPCJDTO.getPessoaSingularDTO().setNomeTipoDocumento(null);
        LoggingHelper.logSaida(LOGGER);
    }

    public void limparPesquisa() {
        filtroPesquisaEntidadeExternaDTO = new FiltroPesquisaEntidadeExternaDTO();
        listaEntidadeExternaDTO = new ArrayList<>();
        mensagemSemResultado = EFETUE_PESQUISA;
        alinhamento = CENTER;
        setEdicao(false);
    }

    public String traduzirAtivo(boolean valor) {
        return valor ? "Ativo" : "Cessado";
    }

    public void voltar() {
        limparPesquisa();
    }

    public void btnAlterar(EntidadeExternaDTO dto) {
        setEntidadeExternaDTO(dto);
        setEdicao(true);
    }

    public void btnAdicionarCPCJ() {
        if (!entidadeExternaDTO.getComissoes().contains(entidadeExternaDTO.getComissao())) {
            entidadeExternaDTO.getListaCpcjAdicionar().add(entidadeExternaDTO.getComissao());
            entidadeExternaDTO.getComissoes().add(entidadeExternaDTO.getComissao());
        }
    }

    public void btnRemoveCPCJ(ComissaoPCJDTO comissao) {
        LoggingHelper.logEntrada(LOGGER, comissao);
        entidadeExternaDTO.getListaCpcjRemover().add(comissao);
        entidadeExternaDTO.getComissoes().remove(comissao);
        if (entidadeExternaDTO.getComissoes().isEmpty()) {
            entidadeExternaDTO.getListaCpcjAdicionar().clear();
            entidadeExternaDTO.setTipoEntidadeExterna("");
            entidadeExternaDTO.setComissao(new ComissaoPCJDTO());
            verificaTipoEntidade();
        }
        LoggingHelper.logSaida(LOGGER);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void eliminarUtilizadorBtn() throws DomainException {
        LoggingHelper.logEntrada(LOGGER, entidadeExternaDTO);
        try {
            entidadeExternaService.eliminar(entidadeExternaDTO, getPerfilDTO());
            pesquisar();
            JsfMessageUtil.mostraMensagemSucesso("Utilizador eliminado com sucesso.");
        } catch (PCJException | DomainException e) {
            LoggingHelper.logError(e);
            JsfMessageUtil.mostraMensagemErro("Erro ao tentar eliminar utilizador.");
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public void setarItemEliminar(EntidadeExternaDTO item) {
        setEntidadeExternaDTO(item);
    }

    public String carregarNomeTipoEntidade(String tipoEntidade) {
        return !tipoEntidade.isEmpty() ? retornarDesignacaoDominio(pcjServiceDomain.getDominioHolderEntidadesExternas(), tipoEntidade) : HIFEN;
    }

    public boolean mostrarEliminarUtilizador(EntidadeExternaDTO dto) {
        return entidadeExternaService.existeOcorrenciaElementoEmLog(dto);
    }

    public boolean mostrarAtivarUtilizador(EntidadeExternaDTO dto) {
        return dto.getAtivo();
    }

    private List<EntidadeExternaDTO> carregarListaEntidadeExterna(List<EntidadeExternaDTO> lista) {
        return listaEntidadeExternaDTO;
    }

    public String mudarPagina() {
        return sucesso ? pcjSubsystem.pesquisarUtilizador() : StringUtils.EMPTY;
    }

    public List<EntidadeExternaDTO> getListaEntidadeExternaDTO() {
        return listaEntidadeExternaDTO;
    }

    public void setListaEntidadeExternaDTO(List<EntidadeExternaDTO> listaEntidadeExternaDTO) {
        this.listaEntidadeExternaDTO = listaEntidadeExternaDTO;
    }

    public EntidadeExternaDTO getEntidadeExternaDTO() {
        return entidadeExternaDTO;
    }

    public void setEntidadeExternaDTO(EntidadeExternaDTO entidadeExternaDTO) {
        this.entidadeExternaDTO = entidadeExternaDTO;
    }

    public Collection<DetalheDominioVO> getListaTipoEntidadesRepresentantes() {
        return listaTipoEntidadesRepresentantes;
    }

    public void setListaTipoEntidadesRepresentantes(Collection<DetalheDominioVO> listaTipoEntidadesRepresentantes) {
        this.listaTipoEntidadesRepresentantes = listaTipoEntidadesRepresentantes;
    }

    @Override
    public Collection<DetalheDominioVO> getListaTiposDocumentos() {
        return listaTiposDocumentos;
    }

    @Override
    public void setListaTiposDocumentos(Collection<DetalheDominioVO> listaTiposDocumentos) {
        this.listaTiposDocumentos = listaTiposDocumentos;
    }

    public boolean exibeBotaoComparar() {
        return elementoVersaoService.obterUtilizadorVersao(entidadeExternaDTO.getUtilizadorPCJ().getId()).size() > 1;
    }

    public String getNomeComissao() {
        return nomeComissao;
    }

    public void setNomeComissao(String nomeComissao) {
        this.nomeComissao = nomeComissao;
    }

    public String getMensagemSemResultado() {
        return mensagemSemResultado;
    }

    public void setMensagemSemResultado(String mensagemSemResultado) {
        this.mensagemSemResultado = mensagemSemResultado;
    }

    public Collection<DetalheDominioVO> getListaTipoEntidadeExternaPesquisa() {
        return listaTipoEntidadeExternaPesquisa;
    }

    public void setListaTipoEntidadeExternaPesquisa(Collection<DetalheDominioVO> listaTipoEntidadeExternaPesquisa) {
        this.listaTipoEntidadeExternaPesquisa = listaTipoEntidadeExternaPesquisa;
    }

    public FiltroPesquisaEntidadeExternaDTO getFiltroPesquisaEntidadeExternaDTO() {
        return filtroPesquisaEntidadeExternaDTO;
    }

    public void setFiltroPesquisaEntidadeExternaDTO(FiltroPesquisaEntidadeExternaDTO filtroPesquisaEntidadeExternaDTO) {
        this.filtroPesquisaEntidadeExternaDTO = filtroPesquisaEntidadeExternaDTO;
    }

    public EntidadeExternaDTO getEntidadeExternaConsultaDTO() {
        return entidadeExternaConsultaDTO;
    }

    public void setEntidadeExternaConsultaDTO(EntidadeExternaDTO entidadeExternaConsultaDTO) {
        this.entidadeExternaConsultaDTO = entidadeExternaConsultaDTO;
    }

    public ComissaoPCJDTO getComissaoPCJDTO() {
        return comissaoPCJDTO;
    }

    public void setComissaoPCJDTO(ComissaoPCJDTO comissaoPCJDTO) {
        this.comissaoPCJDTO = comissaoPCJDTO;
    }

    public boolean isEdicao() {
        return edicao;
    }

    public void setEdicao(boolean edicao) {
        this.edicao = edicao;
    }

    public String getTextoTitulo() {
        return isEdicao() ? "Alterar utilizador" : "Registar utilizador";
    }

    public String getTextoInfo() {
        return isEdicao() ? "Altere os dados do utilizador." : "Registe o utilizador preenchendo os campos.";
    }

    public boolean isExibirSelectCpcj() {
        return exibirSelectCpcj;
    }

    public void setExibirSelectCpcj(boolean exibirSelectCpcj) {
        this.exibirSelectCpcj = exibirSelectCpcj;
    }

    public boolean isExibirBtnAddCpcj() {
        return exibirBtnAddCpcj;
    }

    public void setExibirBtnAddCpcj(boolean exibirBtnAddCpcj) {
        this.exibirBtnAddCpcj = exibirBtnAddCpcj;
    }

    public boolean isExibirBtnAlterar() {
        return entidadeExternaConsultaDTO.getAtivo();
    }

    public void setExibirBtnAlterar(boolean exibirBtnAlterar) {
        this.exibirBtnAlterar = exibirBtnAlterar;
    }

    public boolean isTipoPesquisa() {
        return tipoPesquisa;
    }

    public void setTipoPesquisa(boolean tipoPesquisa) {
        ehInicioEcraRegistarUtilizador = true;
        this.tipoPesquisa = tipoPesquisa;
    }

}
