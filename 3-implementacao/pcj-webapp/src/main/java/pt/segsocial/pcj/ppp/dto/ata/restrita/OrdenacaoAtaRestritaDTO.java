package pt.segsocial.pcj.ppp.dto.ata.restrita;


import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaDTO;
import pt.segsocial.pcj.ppp.enums.BlocosTrabalhoAtaRestritaEnum;

public class OrdenacaoAtaRestritaDTO {

    private Long id;
    private AtaRestritaDTO ataRestrita;
    private BlocoGeralRestritaDTO blocoGeralRestrita;
    private BlocoRestritaComunicacaoDTO blocoComunicacaoRestrita;
    private BlocosTrabalhoAtaRestritaEnum nomeBloco;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public AtaRestritaDTO getAtaRestrita() {
        return ataRestrita;
    }

    public void setAtaRestrita(AtaRestritaDTO ataRestrita) {
        this.ataRestrita = ataRestrita;
    }

    public BlocoGeralRestritaDTO getBlocoGeralRestrita() {
        return blocoGeralRestrita;
    }

    public void setBlocoGeralRestrita(BlocoGeralRestritaDTO blocoGeralRestrita) {
        this.blocoGeralRestrita = blocoGeralRestrita;
    }

    public BlocosTrabalhoAtaRestritaEnum getNomeBloco() {
        return nomeBloco;
    }

    public void setNomeBloco(BlocosTrabalhoAtaRestritaEnum nomeBloco) {
        this.nomeBloco = nomeBloco;
    }

    public BlocoRestritaComunicacaoDTO getBlocoComunicacaoRestrita() {
        return blocoComunicacaoRestrita;
    }

    public void setBlocoComunicacaoRestrita(BlocoRestritaComunicacaoDTO blocoComunicacaoRestrita) {
        this.blocoComunicacaoRestrita = blocoComunicacaoRestrita;
    }
}
