package pt.segsocial.pcj.ppp.dto;

import org.primefaces.model.UploadedFile;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

public class DocumentoDTO implements Serializable {

    private Long id;
    private Date dataUpload;
    private Long identificadorFicheiro;
    private Boolean uploadDocumento;
    private String nomeDocumento;
    private UploadedFile uploadedFile;

    public DocumentoDTO() {}

    private DocumentoDTO(Builder builder) {
        this.id = builder.id;
        this.dataUpload = builder.dataUpload;
        this.identificadorFicheiro = builder.identificadorFicheiro;
        this.uploadDocumento = builder.uploadDocumento;
        this.nomeDocumento = builder.nomeDocumento;
        this.uploadedFile = builder.uploadedFile;
    }

    public UploadedFile getUploadedFile() {
        return uploadedFile;
    }

    public void setUploadedFile(UploadedFile uploadedFile) {
        this.uploadedFile = uploadedFile;
    }

    public static class Builder {
        private Long id;
        private Date dataUpload;
        private Long identificadorFicheiro;
        private Boolean uploadDocumento;
        private String nomeDocumento;
        private UploadedFile uploadedFile;

        public Builder() {}

        public Builder comId(Long id) {
            this.id = id;
            return this;
        }

        public Builder comDataUpload(Date dataUpload) {
            this.dataUpload = dataUpload;
            return this;
        }

        public Builder comIdentificadorFicheiro(Long identificadorFicheiro) {
            this.identificadorFicheiro = identificadorFicheiro;
            return this;
        }

        public Builder comUploadDocumento(Boolean uploadDocumento) {
            this.uploadDocumento = uploadDocumento;
            return this;
        }

        public Builder comNomeDocumento(String nomeDocumento) {
            this.nomeDocumento = nomeDocumento;
            return this;
        }

        public Builder comUploadedFile(UploadedFile uploadedFile) {
            this.uploadedFile = uploadedFile;
            return this;
        }

        public DocumentoDTO build() {
            return new DocumentoDTO(this);
        }

        public UploadedFile getUploadedFile() {
            return uploadedFile;
        }

        public void setUploadedFile(UploadedFile uploadedFile) {
            this.uploadedFile = uploadedFile;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getDataUpload() {
        return dataUpload;
    }

    public void setDataUpload(Date dataUpload) {
        this.dataUpload = dataUpload;
    }

    public Long getIdentificadorFicheiro() {
        return identificadorFicheiro;
    }

    public void setIdentificadorFicheiro(Long identificadorFicheiro) {
        this.identificadorFicheiro = identificadorFicheiro;
    }

    public Boolean getUploadDocumento() {
        return uploadDocumento;
    }

    public void setUploadDocumento(Boolean uploadDocumento) {
        this.uploadDocumento = uploadDocumento;
    }

    public String getNomeDocumento() {
        return nomeDocumento;
    }

    public void setNomeDocumento(String nomeDocumento) {
        this.nomeDocumento = nomeDocumento;
    }

    public boolean documentoNaoEstaAnexado() {
        return this.uploadDocumento == null || !this.uploadDocumento;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        DocumentoDTO that = (DocumentoDTO) o;
        return Objects.equals(dataUpload, that.dataUpload) && Objects.equals(identificadorFicheiro, that.identificadorFicheiro) && Objects.equals(nomeDocumento, that.nomeDocumento);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataUpload, identificadorFicheiro, nomeDocumento);
    }

    @Override
    public String toString() {
        return "DocumentoDTO{" +
                "id=" + id +
                ", dataUpload=" + dataUpload +
                ", identificadorFicheiro=" + identificadorFicheiro +
                ", uploadDocumento=" + uploadDocumento +
                ", nomeDocumento='" + nomeDocumento + '\'' +
                '}';
    }
}
