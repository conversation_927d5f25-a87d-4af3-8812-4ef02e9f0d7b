package pt.segsocial.pcj.core.rest;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@XmlRootElement(name = "Error")
@XmlAccessorType(XmlAccessType.FIELD)
public class PCJRestException extends Exception implements Serializable {
    private static final long serialVersionUID = 2397237153973970651L;

    @XmlElement(name = "errorCode", required = true)
    private String code;

    @XmlElement(name = "errorMessage", required = false)
    private String message;

    public PCJRestException (){
    }

    public PCJRestException (String code, String message){
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}