package pt.segsocial.pcj.pae.vo;

import java.util.Date;

public final class ComprovativoProcessoVO {

    private String numeroProcesso;
    private String numeroIdentificacaoCivil;
    private Date dataNascimento;
    private String moradaEntidade;
    private String nomeCompletoCrianca;
    private String nomePessoaEntidadePromotora;
    private String telefoneEntidade;
    private String correioEletronicoEntidade;

    public static ComprovativoProcessoVO builder() {
        return new ComprovativoProcessoVO();
    }

    public String getNumeroProcesso() {
        return numeroProcesso;
    }

    public ComprovativoProcessoVO addNumeroProcesso(String numeroProcesso) {
        this.numeroProcesso = numeroProcesso;
        return this;
    }

    public String getNumeroIdentificacaoCivil() {
        return numeroIdentificacaoCivil;
    }

    public ComprovativoProcessoVO addNumeroIdentificacaoCivil(String numeroIdentificacaoCivil) {
        this.numeroIdentificacaoCivil = numeroIdentificacaoCivil;
        return this;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public ComprovativoProcessoVO addDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
        return this;
    }

    public String getMoradaEntidade() {
        return moradaEntidade;
    }

    public ComprovativoProcessoVO addMoradaEntidade(String moradaEntidade) {
        this.moradaEntidade = moradaEntidade;
        return this;
    }

    public String getNomeCompletoCrianca() {
        return nomeCompletoCrianca;
    }

    public ComprovativoProcessoVO addNomeCompletoCrianca(String nomeCompletoCrianca) {
        this.nomeCompletoCrianca = nomeCompletoCrianca;
        return this;
    }

    public String getNomePessoaEntidadePromotora() {
        return nomePessoaEntidadePromotora;
    }

    public ComprovativoProcessoVO addNomePessoaEntidadePromotora(String nomePessoaEntidadePromotora) {
        this.nomePessoaEntidadePromotora = nomePessoaEntidadePromotora;
        return this;
    }

    public String getTelefoneEntidade() {
        return telefoneEntidade;
    }

    public ComprovativoProcessoVO addTelefoneEntidade(String telefoneEntidade) {
        this.telefoneEntidade = telefoneEntidade;
        return this;
    }

    public String getCorreioEletronicoEntidade() {
        return correioEletronicoEntidade;
    }

    public ComprovativoProcessoVO addCorreioEletronicoEntidade(String correioEletronicoEntidade) {
        this.correioEletronicoEntidade = correioEletronicoEntidade;
        return this;
    }
}
