package pt.segsocial.pcj.ppp.service;

import pt.segsocial.pcj.ppp.jpa.dao.ata.alargada.BlocoParecerDAO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoParecer;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.List;

@RequestScoped
public class BlocoParecerService {

    @Inject
    private BlocoParecerDAO blocoParecerDAO;

    public List<BlocoParecer> obterListaBlocosPendentes(Long idComissao) {
        return blocoParecerDAO.listarPendentesPorComissao(idComissao);
    }
}
