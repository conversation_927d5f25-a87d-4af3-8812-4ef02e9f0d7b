package pt.segsocial.pcj.ppp.bean.reuniao;

import ch.qos.logback.classic.Level;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.dto.ConsultaReuniaoDTO;
import pt.segsocial.pcj.ppp.service.ControleReuniaoService;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;

@Named(value = "pcjControleAtaRestritaBean")
@ViewAccessScoped
public class ControleAtaRestritaBean implements Serializable {

    public static final String REGISTAR_ATA = "Registar Ata";
    public static final String RETOMAR_ATA = "Retomar Ata";

    @Inject
    private ControleReuniaoService controleReuniaoService;

    @Inject
    private ConsultarReuniaoCPCJBean consultarReuniaoCPCJBean;

    private Long idReuniao;
    private Long idCpcj;

    private String nomeBotao;
    private String action;
    private boolean exibeNomeBotaoRegistarAta = true;
    private boolean estadoAtaAssinadaOuConcluida = false;
    private String idBotaoRetomarRegistarAta = "pcjRegistarAtaRestritaBtn";
    private boolean reuniaoRestritaTipoModalidade;


    @PostConstruct
    public void init() {
        ConsultaReuniaoDTO reuniao = consultarReuniaoCPCJBean.getReuniao();
        LoggingHelper.logEntrada(Level.INFO, reuniao);

        if (reuniao != null) {
            this.idReuniao = reuniao.getId().longValue();
            this.idCpcj = reuniao.getIdComissao().longValue();
            this.reuniaoRestritaTipoModalidade = reuniao.isReuniaoComissaoRestrita();

            if(reuniaoRestritaTipoModalidade) {
                LoggingHelper.logEntrada(Level.INFO, "Reuniao restrita tipo modalidade");
                tratarEstadosAtaRestrita();
            }
        }

        LoggingHelper.logSaida();
    }

    private void tratarEstadosAtaRestrita() {

    }

    public boolean naoExisteAtaIniciadaParaReuniao(Long idReuniao) {
        return controleReuniaoService.naoExisteAtaIniciadaParaReuniao(idReuniao);
    }

    public Long getIdReuniao() {
        return idReuniao;
    }
    public void setIdReuniao(Long idReuniao) {
        this.idReuniao = idReuniao;
    }

    public Long getIdCpcj() {
        return idCpcj;
    }
    public void setIdCpcj(Long idCpcj) {
        this.idCpcj = idCpcj;
    }
    public void setNomeBotao(String nomeBotao) {
        this.nomeBotao = nomeBotao;
    }

    public String getNomeBotao() {
        return exibeNomeBotaoRegistarAta ? REGISTAR_ATA : RETOMAR_ATA;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public boolean isEstadoAtaAssinadaOuConcluida() {
        return estadoAtaAssinadaOuConcluida;
    }

    public void setEstadoAtaAssinadaOuConcluida(boolean estadoAtaAssinadaOuConcluida) {
        this.estadoAtaAssinadaOuConcluida = estadoAtaAssinadaOuConcluida;
    }

    public boolean exibirBotaoRegistarRetomarAta() {
        return !estadoAtaAssinadaOuConcluida;
    }

    public String getIdBotaoRetomarRegistarAta() {
        return idBotaoRetomarRegistarAta;
    }

    public void setIdBotaoRetomarRegistarAta(String idBotaoRetomarRegistarAta) {
        this.idBotaoRetomarRegistarAta = idBotaoRetomarRegistarAta;
    }

    public void setReuniaoRestritaTipoModalidade(boolean reuniaoRestritaTipoModalidade) {
        this.reuniaoRestritaTipoModalidade = reuniaoRestritaTipoModalidade;
    }

    public boolean isReuniaoRestritaTipoModalidade() {
        return reuniaoRestritaTipoModalidade;
    }
}
