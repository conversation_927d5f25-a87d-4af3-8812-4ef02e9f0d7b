package pt.segsocial.pcj.core.delegate;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.cdi.grs.dominio.DominioVO;
import pt.segsocial.fraw.grs.ejb.GRSServices;
import pt.segsocial.gvr.api.cdi.config.ClassificacaoPortuguesaProfissao;
import pt.segsocial.gvr.api.dto.ClassificacaoPortuguesaProfissaoDTO;
import pt.segsocial.gvr.api.dto.grs.InterrupcaoTrabalhoDTO;
import pt.segsocial.gvr.api.dto.grs.ServicoSSDTO;
import pt.segsocial.gvr.api.dto.mia.DivisaoAdministrativaDTO;
import pt.segsocial.gvr.api.dto.mia.PaisDTO;
import pt.segsocial.gvr.api.exception.GvrException;
import pt.segsocial.gvr.api.grs.cdi.config.InterrupcaoTrabalho;
import pt.segsocial.gvr.api.grs.cdi.config.ServicoSS;
import pt.segsocial.gvr.api.grs.holder.InterrupcaoTrabalhoHolder;
import pt.segsocial.gvr.api.grs.holder.ServicoSSHolder;
import pt.segsocial.gvr.api.holder.ClassificacaoPortuguesaProfissaoHolder;
import pt.segsocial.gvr.api.mia.cdi.config.DivisaoAdministrativa;
import pt.segsocial.gvr.api.mia.cdi.config.Pais;
import pt.segsocial.gvr.api.mia.holder.DivisaoAdministrativaHolder;
import pt.segsocial.gvr.api.mia.holder.PaisHolder;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.ppp.dto.ConcelhoDTO;
import pt.segsocial.pcj.ppp.dto.DistritoDTO;
import pt.segsocial.pcj.ppp.dto.FreguesiaDTO;
import pt.segsocial.pcj.ppp.dto.FreguesiaTesteDTO;

import javax.enterprise.context.RequestScoped;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequestScoped
public class GVRDelegate implements Serializable {

    private static final long serialVersionUID = -6525710216876514750L;
    private static final Logger LOGGER = LoggerFactory.getLogger(GVRDelegate.class);
    private static final String TIPO_SERVICO = "CPCJ";

    @Inject
    @ClassificacaoPortuguesaProfissao
    private Instance<ClassificacaoPortuguesaProfissaoHolder> gvrProfissaoHolder;

    @Inject
    @Pais
    private Instance<PaisHolder> paisHolder;

    @Inject
    @InterrupcaoTrabalho
    private Instance<InterrupcaoTrabalhoHolder> interrupcaoTrabalho;

    @Inject
    @DivisaoAdministrativa
    private Instance<DivisaoAdministrativaHolder> divisaoAdministrativa;

    @Inject
    @ServicoSS
    private Instance<ServicoSSHolder> servicoSSHolder;

    @Inject
    private GRSServices grsService;

    public List<ClassificacaoPortuguesaProfissaoDTO> getProfissoes() throws GvrException {
        return gvrProfissaoHolder.get().withIdentificacaoNivel(1).list();
    }

    public List<PaisDTO> getPaises() throws GvrException {
        return paisHolder.get().list();
    }

    public List<InterrupcaoTrabalhoDTO> getInterrupcoesTrabalho(String tipo, Character ambito, Date inicio, Date fim)
            throws PCJException {
        try {
            return interrupcaoTrabalho.get()
                    .withTipo(tipo)
                    .withAmbito(ambito)
                    .withInicio(inicio)
                    .withFim(fim)
                    .list();
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getInterrupcoesTrabalho", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da interrupcao de trabalho no momento, por favor tente novamente!");
        }
    }

    public int getInterrupcaoTrabalho(String tipo, Character ambito, Date inicio, Date fim) throws PCJException {
        List<InterrupcaoTrabalhoDTO> itList = getInterrupcoesTrabalho(tipo, ambito, inicio, fim);
        return CollectionUtils.isNotEmpty(itList) ? itList.size() : 0;
    }

    /**
     * Servico devolve uma CPCJ com base no codigo passado por parametro.
     * @param codigo Integer
     * @return ServicoSSDTO
     * @throws PCJException se ocorrer exception de validacao.
     */
    public ServicoSSDTO getServicoByCodigo(Integer codigo) throws PCJException {
        try {
            return servicoSSHolder.get().withCodigoServico(codigo).get();
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getServicoByCodigo", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es, " +
                    "indisponibilidade nos servi\u00E7os de GVR");
        }
    }

    /**
     * Servico devolve lista de CPCJs para preencher comboBox.
     * @return List<ServicoSSDTO>
     * @throws PCJException se ocorrer exception de validacao.
     */
    public List<ServicoSSDTO> getServicosCPCJ() throws PCJException {
        try {
            return servicoSSHolder.get().findByTipoServico(TIPO_SERVICO, Boolean.TRUE);
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getServicoByCodigo", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es, " +
                    "indisponibilidade nos servi\u00E7os de GVR");
        }
    }

    /**
     * Serico para obter a designacao do distrito, conselho e freguesia com base no codigo do distrito obtido em servico SS.
     *    Uso:
     *      ServicoSSDTO servicoSSDTO = getServicoByCodigo(Integer codigo);
     *      morada.setDistrito(getGvrDelegate().getDivisaoAdministrativa(servicoSSDTO.nivel1, servicoSSDTO.nivel2, servicoSSDTO.nivel3));
     *      morada.setConcelho(getGvrDelegate().getDivisaoAdministrativa(morada.getDistrito(), servicoSSDTO.nivel2, servicoSSDTO.nivel3));
     *      morada.setFreguesia(getGvrDelegate().getDivisaoAdministrativa(morada.getDistrito(), morada.getConcelho(), servicoSSDTO.nivel3));
     *
     * @param distrito Integer
     * @param conselho Integer
     * @param freguesia Integer
     * @return DivisaoAdministrativaDTO
     * @throws PCJException se ocorrer exception de validacao.
     */
    public String getDivisaoAdministrativa(Integer distrito, Integer conselho, Integer freguesia) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoesAdministrativa = divisaoAdministrativa.get()
                    .withNivel1(distrito)
                    .withNivel2(conselho)
                    .withNivel3(freguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();
            if(CollectionUtils.isEmpty(divisoesAdministrativa) || "DESCONHECIDO".equals(divisoesAdministrativa.get(0).getDesignacaoDivisaoAdmin())) {
                return "-";
            }

            return divisoesAdministrativa.get(0).getDesignacaoDivisaoAdmin();
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }

    public DivisaoAdministrativaDTO getDivisaoAdministrativaDTO(Integer distrito, Integer conselho, Integer freguesia) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoesAdministrativa = divisaoAdministrativa.get()
                    .withNivel1(distrito)
                    .withNivel2(conselho)
                    .withNivel3(freguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();
            return CollectionUtils.isNotEmpty(divisoesAdministrativa) ? divisoesAdministrativa.get(0) : null;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }
    public String detalheDominioByCodigo(String codigoDominio, String codigoDetalheDominio) {
        DominioVO dominio = grsService.getDominio(codigoDominio);
        if (dominio == null) {
            return "";
        }

        Map<String, DetalheDominioVO> detalhes = dominio.getDetalhes();
        DetalheDominioVO detalhe = detalhes.get(codigoDetalheDominio);

        return detalhe != null ? detalhe.getDesignacao() : "";
    }

    public DetalheDominioVO dominioByCodigo(String codigoDominio, String codigoDetalheDominio) {

        return grsService.getDominio(codigoDominio).getDetalhes().get(codigoDetalheDominio);
    }

    public List<DistritoDTO> getDistritos() throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(null)
                    .withNivel2(null)
                    .withNivel3(null)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<DistritoDTO> distritos = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                DistritoDTO distritoDto = new DistritoDTO(divisao.getNivel1(), divisao.getDesignacaoDivisaoAdmin());
                distritos.add(distritoDto);
            }
            return distritos;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }

    }

    public List<ConcelhoDTO> getConcelhos(Integer codigoDistrito) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoDistrito)
                    .withNivel2(null)
                    .withNivel3(null)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<ConcelhoDTO> distritos = new
                    ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                ConcelhoDTO concelhoDto = new ConcelhoDTO(divisao.getNivel2(), divisao.getDesignacaoDivisaoAdmin());
                distritos.add(concelhoDto);
            }
            return distritos;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }

    }

    public List<FreguesiaDTO> getFreguesias(Integer codigoConcelho, Integer codigoDistrito) throws PCJException {

        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoConcelho)
                    .withNivel2(codigoDistrito)
                    .withNivel3(null)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<FreguesiaDTO> freguesias = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                FreguesiaDTO freguesiaDTO = new FreguesiaDTO(divisao.getNivel3(), divisao.getDesignacaoDivisaoAdmin());
                freguesias.add(freguesiaDTO);
            }
            return freguesias;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }
    public List<FreguesiaTesteDTO> getFreguesiasTeste(Integer codigoConcelho, Integer codigoDistrito) throws PCJException {

        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoConcelho)
                    .withNivel2(codigoDistrito)
                    .withNivel3(null)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<FreguesiaTesteDTO> freguesias = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                FreguesiaTesteDTO freguesiaDTO = new FreguesiaTesteDTO(divisao.getNivel3(), divisao.getDesignacaoDivisaoAdmin());
                freguesias.add(freguesiaDTO);
            }
            return freguesias;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }
    public DistritoDTO getDistrito(Integer codigoDistrito, Integer codigoConcelho, Integer codigoFreguesia) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoDistrito)
                    .withNivel2(codigoConcelho)
                    .withNivel3(codigoFreguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<DistritoDTO> distritos = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                DistritoDTO distritoDto = new DistritoDTO(divisao.getNivel1(), divisao.getDesignacaoDivisaoAdmin());
                distritos.add(distritoDto);
            }
            return !divisoes.isEmpty() ? distritos.get(0) : null;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }

    }

    public ConcelhoDTO getConcelho(Integer codigoDistrito, Integer codigoConcelho, Integer codigoFreguesia) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoDistrito)
                    .withNivel2(codigoConcelho)
                    .withNivel3(codigoFreguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<ConcelhoDTO> concelhoDtos = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                ConcelhoDTO concelhoDto = new ConcelhoDTO(divisao.getNivel2(), divisao.getDesignacaoDivisaoAdmin());
                concelhoDtos.add(concelhoDto);
            }
            return !concelhoDtos.isEmpty() ? concelhoDtos.get(0) : null;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }

    public FreguesiaDTO getFreguesia(Integer codigoConcelho, Integer codigoDistrito, Integer codigoFreguesia) throws PCJException {

        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoConcelho)
                    .withNivel2(codigoDistrito)
                    .withNivel3(codigoFreguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<FreguesiaDTO> freguesias = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                FreguesiaDTO freguesiaDTO = new FreguesiaDTO(divisao.getNivel3(), divisao.getDesignacaoDivisaoAdmin());
                freguesias.add(freguesiaDTO);
            }
            return !freguesias.isEmpty() ? freguesias.get(0) : null;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }

    public FreguesiaDTO getFreguesiaDTO(Integer codigoDistrito, Integer codigoConcelho, Integer codigoFreguesia) throws PCJException {

        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoDistrito)
                    .withNivel2(codigoConcelho)
                    .withNivel3(codigoFreguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();

            List<FreguesiaDTO> freguesias = new ArrayList<>();
            for (DivisaoAdministrativaDTO divisao : divisoes) {
                FreguesiaDTO freguesiaDTO = new FreguesiaDTO(divisao.getNivel3(), divisao.getDesignacaoDivisaoAdmin());
                freguesias.add(freguesiaDTO);
            }
            return !freguesias.isEmpty() ? freguesias.get(0) : null;
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }
    }

    public String getNomeDistritoConcelhoOuFreguesia(Integer codigoDistrito, Integer codigoConcelho, Integer codigoFreguesia) throws PCJException {
        try {
            List<DivisaoAdministrativaDTO> divisoes = divisaoAdministrativa.get()
                    .withNivel1(codigoDistrito)
                    .withNivel2(codigoConcelho)
                    .withNivel3(codigoFreguesia)
                    .isAtivo(Boolean.TRUE)
                    .list();

            return divisoes.get(0).getDesignacaoDivisaoAdmin();
        } catch (GvrException e) {
            LOGGER.error("Erro na chamada do servi\u00E7o GVR getDivisaoAdministrativa", e);
            throw new PCJException("N\u00E3o foi poss\u00EDvel obter informa\u00E7\u00E3es" +
                    " da divis\u00E3o administrativa no momento, por favor tente novamente!");
        }

    }
}
