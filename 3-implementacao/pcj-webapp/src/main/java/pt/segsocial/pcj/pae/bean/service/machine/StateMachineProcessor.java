package pt.segsocial.pcj.pae.bean.service.machine;

import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.pae.enums.TipoLogEnum;
import pt.segsocial.pcj.pae.jpa.dao.HistoricoDAO;
import pt.segsocial.pcj.pae.jpa.dao.ProcessoPaeDAO;
import pt.segsocial.pcj.pae.jpa.entity.Comprovativo;
import pt.segsocial.pcj.pae.jpa.entity.Historico;
import pt.segsocial.pcj.pae.jpa.entity.ProcessoPae;
import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.Date;

public class StateMachineProcessor implements Serializable {

    private static final long serialVersionUID = 8971578594125019745L;

    private final EntityManager entityManager;

    public StateMachineProcessor(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public void processor(ProcessoPae processoPae, EstadoProcessoEnum previousState,
                          String log, String responsavel, Comprovativo comprovativo, TipoLogEnum tipoLogEnum) throws DomainException {
        new ProcessoPaeDAO(entityManager).update(processoPae);
        new HistoricoDAO(entityManager).create(
                new Historico.Builder()
                        .processoPae(processoPae)
                        .data(new Date())
                        .estadoAntigo(previousState)
                        .estadoAtual(processoPae.getEstado())
                        .log(log)
                        .responsavel(responsavel)
                        .comprovativo(comprovativo)
                        .tipoLogEnum(tipoLogEnum)
                        .build());
    }

    public void processor(ProcessoPae processoPae, EstadoProcessoEnum previousState,
                          String log, String responsavel, TipoLogEnum tipoLogEnum) throws DomainException {
        this.processor(processoPae, previousState, log, responsavel, null, tipoLogEnum);
    }
}
