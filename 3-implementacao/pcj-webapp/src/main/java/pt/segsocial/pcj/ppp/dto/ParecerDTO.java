package pt.segsocial.pcj.ppp.dto;

import java.io.Serializable;

public class ParecerDTO implements Serializable {

    private String assunto;
    private String observacao;
    private boolean aprovado;

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean getAprovado() {
        return aprovado;
    }

    public void setAprovado(boolean aprovado) {
        this.aprovado = aprovado;
    }

    @Override
    public String toString() {
        return "ParecerDTO{" +
                "assunto='" + assunto + '\'' +
                ", observacao='" + observacao + '\'' +
                ", aprovado=" + aprovado +
                '}';
    }
}
