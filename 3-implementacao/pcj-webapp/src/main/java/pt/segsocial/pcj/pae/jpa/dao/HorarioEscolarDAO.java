package pt.segsocial.pcj.pae.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.pae.jpa.entity.HorarioEscolar;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.util.List;

public class HorarioEscolarDAO  extends DAO<HorarioEscolar, Long> {

    public HorarioEscolarDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<HorarioEscolar> findByRequerimento(Long idRequerimento) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(HorarioEscolar.FIND_BY_REQUERIMENTO, HorarioEscolar.class)
                    .setParameter("idRequerimento", idRequerimento)
                    .getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }
}
