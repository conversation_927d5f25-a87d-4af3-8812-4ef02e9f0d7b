package pt.segsocial.pcj.ppp.jpa.entity;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;

import javax.persistence.*;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;


@Entity
@Table(name = "DOCUMENTO_PCJ")
@AttributeOverride(name = "id", column = @Column(name = "ID_DOCUMENTO_PCJ"))
@NamedQueries({
		@NamedQuery(name = DocumentoPCJ.FIND_BY_IDENTIFICADOR_FICHEIRO,
				query = " SELECT d FROM DocumentoPCJ d " +
						" WHERE d.identificadorFicheiro = :identificadorFicheiro")
})
public class DocumentoPCJ extends EntityBase implements Serializable {

	public static final String FIND_BY_IDENTIFICADOR_FICHEIRO = "DocumentoPCJ.findByIdentificador";

	@Column(name = "DATA_UPLOAD")
	@Temporal(TemporalType.TIMESTAMP)
	private Date dataUpload;

	@Column(name = "IDENTIFICADOR_FICHEIRO")
	private Long identificadorFicheiro;

	@Column(name = "UPLOAD_DOCUMENTO")
	private Boolean uploadDocumento;

	@Column(name = "NOME_DOCUMENTO")
	private String nomeDocumento;

	public DocumentoPCJ() {}

	public DocumentoPCJ(Long identificadorFicheiro) {
		this.identificadorFicheiro = identificadorFicheiro;
	}

	@Override
	public void prePersist() {
		super.prePersist();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		try {
			if(this.dataUpload == null) {
				setDataUpload(df.parse(df.format(new Date())));
			}
		} catch (ParseException e) {
			setDataUpload(new Date());
		}
	}

	@Override
	public void preUpdate() {
		super.preUpdate();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            setDataUpload(df.parse(df.format(new Date())));
        } catch (ParseException e) {
			setDataUpload(new Date());
        }
    }

	public Date getDataUpload() {
		return dataUpload;
	}

	public void setDataUpload(Date dataUpload) {
		this.dataUpload = dataUpload;
	}

	public Long getIdentificadorFicheiro() {
		return identificadorFicheiro;
	}

	public void setIdentificadorFicheiro(Long identificadorFicheiro) {
		this.identificadorFicheiro = identificadorFicheiro;
	}

	public Boolean getUploadDocumento() {
		return uploadDocumento;
	}

	public void setUploadDocumento(Boolean uploadDocumento) {
		this.uploadDocumento = uploadDocumento;
	}

	public String getNomeDocumento() {
		return nomeDocumento;
	}

	public void setNomeDocumento(String nomeDocumento) {
		this.nomeDocumento = nomeDocumento;
	}
}