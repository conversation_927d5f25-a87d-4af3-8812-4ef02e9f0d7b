package pt.segsocial.pcj.ppp.jpa.dao.ata.alargada;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoParecer;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

public class BlocoParecerDAO extends DAO<BlocoParecer, Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BlocoParecerDAO.class);

    @Inject
    public BlocoParecerDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<BlocoParecer> listarPendentesPorComissao(Long idComissao) {
        try {
            String sql = "SELECT b FROM BlocoParecer b WHERE b.comissao.id = :idComissao and  b.executado = :executado ";
            return getEntityManager().createQuery(sql, BlocoParecer.class)
                    .setParameter("idComissao", idComissao)
                    .setParameter("executado", false)
                    .getResultList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
