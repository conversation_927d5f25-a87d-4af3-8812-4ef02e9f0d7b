package pt.segsocial.pcj.pae.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.pae.jpa.entity.Contestacao;
import pt.segsocial.pcj.pae.jpa.entity.DocumentoContestacao;

import javax.persistence.EntityManager;
import java.util.List;

public class DocumentoContestacaoDAO extends DAO<DocumentoContestacao, Long> {

    private static final String DEFAULT_QUERY = " SELECT dc FROM DocumentoContestacao dc ";
    private static final String WHERE_CODIGO_CONTESTACAO = " WHERE dc.contestacao.id = :id ";
    private static final String ORDER_BY = " ORDER BY dc.id ";

    public DocumentoContestacaoDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<DocumentoContestacao> getDocumentoContestacaoByContetacao(Contestacao contestacao) {
        StringBuilder queryBuilder = new StringBuilder(DEFAULT_QUERY);
        queryBuilder.append(WHERE_CODIGO_CONTESTACAO);
        queryBuilder.append(ORDER_BY);

        return getEntityManager()
                .createQuery(queryBuilder.toString(), DocumentoContestacao.class)
                .setParameter("id", contestacao.getId())
                .getResultList();
    }

}
