package pt.segsocial.pcj.ppp.jpa.entity.ata.alargada;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;

import javax.persistence.*;

@Entity
@Table(name = "BLOCO_ASSUNTO_GERAL")
@AttributeOverride(name = "id", column = @Column(name = "ID_BLOCO_ASSUNTO_GERAL"))
public class BlocoAssuntoGeral extends EntityBase {

    @Column(name = "TITULO")
    private String assunto;

    @Column(name = "OBSERVACAO")
    private String observacao;

    @Column(name = "TEXTO_INTRODUTORIO")
    private String textoIntrodutorio;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "ID_DOCUMENTO_PCJ")
    private DocumentoPCJ documentoPCJ;

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTextoIntrodutorio() {
        return textoIntrodutorio;
    }

    public void setTextoIntrodutorio(String textoIntrodutorio) {
        this.textoIntrodutorio = textoIntrodutorio;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public DocumentoPCJ getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentoPCJ documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }
}
