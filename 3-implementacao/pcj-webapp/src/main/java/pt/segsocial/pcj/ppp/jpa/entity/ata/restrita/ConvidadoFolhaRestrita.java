package pt.segsocial.pcj.ppp.jpa.entity.ata.restrita;

import pt.segsocial.fraw.api.domain.BooleanResult;
import pt.segsocial.fraw.jpa.PersistentDomainObject;
import pt.segsocial.pcj.ppp.jpa.entity.ConvidadoPCJ;

import javax.persistence.*;

@Entity
@Table(name = "CONVIDADO_FOLHA_RESTRITA")
@NamedQueries({
        @NamedQuery(name = ConvidadoFolhaRestrita.FIND_BY_ATA_RESTRITA,
                query = " SELECT c FROM ConvidadoFolhaRestrita c " +
                        " WHERE c.ataRestrita.id = :idAtaRestrita"),
        @NamedQuery(name = ConvidadoFolhaRestrita.FIND_BY_ID_CONVIDADO_ID_ATA_RESTRITA,
                query = " SELECT c FROM ConvidadoFolhaRestrita c " +
                        "  WHERE c.convidadoPCJ.id = :idConvidado" +
                        "    AND c.ataRestrita.id = :idAtaRestrita"),
        @NamedQuery(name = ConvidadoFolhaRestrita.FIND_ASSINADOS,
                query = " SELECT c FROM ConvidadoFolhaRestrita c " +
                        "  WHERE c.ataRestrita.id = :idAtaRestrita" +
                        "    AND c.assinatura = true")
})
public class ConvidadoFolhaRestrita extends PersistentDomainObject<ConvidadoFolhaRestritaId>  {

    public static final String FIND_BY_ATA_RESTRITA = "ConvidadoFolhaRestrita.findByAtaRestrita";
    public static final String FIND_BY_ID_CONVIDADO_ID_ATA_RESTRITA = "ConvidadoFolhaRestrita.findByIdElementoIdAta";
    public static final String FIND_ASSINADOS = "ConvidadoFolhaRestrita.findAssinados";


    @EmbeddedId
    private ConvidadoFolhaRestritaId id;

    @ManyToOne
    @MapsId("convidadoId")
    @JoinColumn(name = "ID_CONVIDADO_PCJ")
    private ConvidadoPCJ convidadoPCJ;

    @ManyToOne
    @MapsId("ataRestritaId")
    @JoinColumn(name = "ID_ATA_RESTRITA_PCJ")
    private AtaRestrita ataRestrita;

    @Column(name = "ASSINATURA")
    private Boolean assinatura;

    public ConvidadoFolhaRestrita() {}

    public ConvidadoFolhaRestrita(ConvidadoFolhaRestritaId id, ConvidadoPCJ convidadoPCJ, AtaRestrita ataRestrita, boolean assinatura) {
        this.id = id;
        this.convidadoPCJ = convidadoPCJ;
        this.ataRestrita = ataRestrita;
        this.assinatura = assinatura;
    }

    @Override
    public ConvidadoFolhaRestritaId getId() {
        return id;
    }

    public void setId(ConvidadoFolhaRestritaId id) {
        this.id = id;
    }

    public AtaRestrita getAtaRestrita() {
        return ataRestrita;
    }

    public void setAtaRestrita(AtaRestrita ataRestrita) {
        this.ataRestrita = ataRestrita;
    }

    public ConvidadoPCJ getConvidadoPCJ() {
        return convidadoPCJ;
    }

    public void setConvidadoPCJ(ConvidadoPCJ convidadoPCJ) {
        this.convidadoPCJ = convidadoPCJ;
    }

    public Boolean getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(Boolean assinatura) {
        this.assinatura = assinatura;
    }

    @Override
    public void delete() {
    }

    @Override
    public BooleanResult getIsDeletable() {
        return BooleanResult.TRUE;
    }
}
