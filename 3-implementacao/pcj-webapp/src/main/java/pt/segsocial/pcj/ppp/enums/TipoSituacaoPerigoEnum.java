package pt.segsocial.pcj.ppp.enums;

public enum TipoSituacaoPerigoEnum {

    ECR("0314"),
    ETI("0315"),
    CR("0312"),
    CPCA("0313"),
    SA("0310"),
    PDE("0321"),
    CT("0311"),
    PC("0320"),
    AS("0309"),
    MTPIA("0318"),
    NEG("0319"),
    MND("0316"),
    MT("0317");

    private final String codigo;

    TipoSituacaoPerigoEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public static TipoSituacaoPerigoEnum fromCodigo(String codigo) {
        for (TipoSituacaoPerigoEnum tipo : TipoSituacaoPerigoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}
