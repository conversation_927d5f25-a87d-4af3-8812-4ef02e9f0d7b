package pt.segsocial.pcj.ppp.enums;

public enum CamposUtilizadorVersaoEnum {

    nome("Nome", "comum"),
    nomeProfissional("Nome Profissional", "comum"),
    emailPessoal("E-mail pessoal", "comum"),
    telemovel("Telemóvel", "comum"),
    niss("NISS", "comum"),
    nif("NIF", "comum"),
    codigoDocumento("Tipo de documento", "comum"),
    numeroDocumento("Nº de documento", "comum"),
    nissUtilizadorResponsavel("Alterado pelo NISS", "comum");

    private final String descricao;
    private final String tipo;

    CamposUtilizadorVersaoEnum(String descricao, String tipo) {
        this.descricao = descricao;
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getTipo() {
        return tipo;
    }

}
