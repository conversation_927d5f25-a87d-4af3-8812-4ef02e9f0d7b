package pt.segsocial.pcj.ppp.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class MembroComissaoId implements Serializable {

    @Column(name = "ID_REUNIAO_PCJ")
    private Long reuniaoId;

    @Column(name = "ID_ELEMENTO_PCJ")
    private Long elementoId;

    public MembroComissaoId(){}

    public MembroComissaoId(Long reuniaoId, Long elementoId) {
        this.reuniaoId = reuniaoId;
        this.elementoId = elementoId;
    }

    public void setReuniaoId(Long reuniaoId) {
        this.reuniaoId = reuniaoId;
    }

    public Long getReuniaoId() {
        return reuniaoId;
    }

    public void setElementoId(Long elementoId) {
        this.elementoId = elementoId;
    }

    public Long getElementoId() {
        return elementoId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MembroComissaoId that = (MembroComissaoId) o;
        return Objects.equals(reuniaoId, that.reuniaoId) && Objects.equals(elementoId, that.elementoId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(reuniaoId, elementoId);
    }
}
