package pt.segsocial.pcj.ppp.bean.ata.alargada;

import ch.qos.logback.classic.Level;
import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.primefaces.event.SelectEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.configuration.Configuration;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PcjMessages;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.FiltroPesquisaAtaDTO;
import pt.segsocial.pcj.ppp.enums.EstadoAtaEnum;
import pt.segsocial.pcj.ppp.enums.TipoAtaEnum;
import pt.segsocial.pcj.ppp.enums.TipoConvocatoriaEnum;
import pt.segsocial.pcj.ppp.jsf.lazy.AtaDataModel;
import pt.segsocial.pcj.ppp.service.ComissaoPCJService;

import javax.annotation.PostConstruct;
import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Named(value = "pcjPesquisarAtaBean")
@Stateful(name = "pcjPesquisarAtaBean")
@LocalBean
@ViewAccessScoped
public class PesquisarAtaBean extends BaseAtaBean implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(PesquisarAtaBean.class);
    private static final int PAGE_SIZE = 10;
    private FiltroPesquisaAtaDTO filtroPesquisa;
    private List<ComissaoPCJDTO> listaCpcj = new ArrayList<>();
    private List<TipoAtaEnum> listaTiposAta;
    private List<TipoConvocatoriaEnum> listaTiposReuniao;
    private List<EstadoAtaEnum> listaEstados;
    private AtaDataModel dataModel;
    private boolean comissaoNacional = false;
    private boolean pesquisaRetornoAta = false;

    @Inject
    private ComissaoPCJService comissaoPCJService;

    @PostConstruct
    public void init() {
        super.init();
        if (ehComissaoNacional()) {
            try {
                comissaoNacional = true;
                listaCpcj = comissaoPCJService.carregarComissoesAtivas();
            } catch (Exception e) {
                LoggingHelper.logSaida(LOGGER);
            }
        }
        listaTiposAta = Arrays.asList(TipoAtaEnum.values());
        listaTiposReuniao = Arrays.asList(TipoConvocatoriaEnum.values());
        listaEstados = Arrays.asList(EstadoAtaEnum.values());
        mensagemSemResultado = EFETUE_PESQUISA;
    }

    @URLAction(mappingId = PCJSubsystem.OP_PESQUISAR_ATA, onPostback = false)
    public void loadPesquisarAta() {
        try {
            if (pesquisaRetornoAta) {
                filtroPesquisa.setIdComissao(getIdComissao());
            } else {
                filtroPesquisa = inicialiazarFiltroPesquisa();
            }
            dataModel = new AtaDataModel(entityManager, filtroPesquisa);
            dataModel.load(0, PAGE_SIZE);
            mensagemSemResultado = EFETUE_PESQUISA;
        } catch (Exception e) {
            JsfMessageUtil.mostraMensagemErro(e.getMessage());
        }
    }

    private FiltroPesquisaAtaDTO inicialiazarFiltroPesquisa() {
        FiltroPesquisaAtaDTO novoFiltro = new FiltroPesquisaAtaDTO();
        novoFiltro.setComissao(new ComissaoPCJDTO(0L));
        return novoFiltro;
    }

    public void pesquisar() {
        LoggingHelper.logEntrada(LOGGER, filtroPesquisa);
        try {
            validarPesquisa(filtroPesquisa);
            if (!this.comissaoNacional) {
                filtroPesquisa.setComissao(new ComissaoPCJDTO(getIdComissao()));
            }
            dataModel.setFiltroPesquisaAta(filtroPesquisa);
            dataModel.load(0, PAGE_SIZE);
            checkResultadoPesquisaVazioAtualizaMensagem();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    private void validarPesquisa(FiltroPesquisaAtaDTO filtro) throws PCJException {
        if ((filtro.getDataInicio() != null && filtro.getDataFim() == null) ||
                (filtro.getDataInicio() == null && filtro.getDataFim() != null)) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("pp.ata.msg.pesquisa.data-preenchida"));
        }
    }

    public void limparPesquisa() {
        filtroPesquisa.limpar();
        loadPesquisarAta();
    }

    private void checkResultadoPesquisaVazioAtualizaMensagem() {
        if (dataModel.isNoResults()) {
            LoggingHelper.logEntrada(LOGGER, Level.DEBUG, SEM_RESULTADO);
            mensagemSemResultado = SEM_RESULTADO;
        }
    }

    /*Date Select*/
    public void onDateSelect(SelectEvent selectEvent) {
    }

    public AtaDataModel getDataModel() {
        return dataModel;
    }

    public void setDataModel(AtaDataModel dataModel) {
        this.dataModel = dataModel;
    }

    public FiltroPesquisaAtaDTO getFiltroPesquisa() {
        return filtroPesquisa;
    }

    public void setFiltroPesquisa(FiltroPesquisaAtaDTO filtroPesquisa) {
        this.filtroPesquisa = filtroPesquisa;
    }

    public List<ComissaoPCJDTO> getListaCpcj() {
        return listaCpcj;
    }

    public void setListaCpcj(List<ComissaoPCJDTO> listaCpcj) {
        this.listaCpcj = listaCpcj;
    }

    public List<EstadoAtaEnum> getListaEstados() {
        return listaEstados;
    }

    public void setListaEstados(List<EstadoAtaEnum> listaEstados) {
        this.listaEstados = listaEstados;
    }

    public List<TipoAtaEnum> getListaTiposAta() {
        return listaTiposAta;
    }

    public void setListaTiposAta(List<TipoAtaEnum> listaTiposAta) {
        this.listaTiposAta = listaTiposAta;
    }

    public List<TipoConvocatoriaEnum> getListaTiposReuniao() {
        return listaTiposReuniao;
    }

    public void setListaTiposReuniao(List<TipoConvocatoriaEnum> listaTiposReuniao) {
        this.listaTiposReuniao = listaTiposReuniao;
    }

    public int getPageSize() {
        return PAGE_SIZE;
    }

    public String getPaginatorPosition() {
        return Configuration.DataTable.PAGINATOR_POSITION.value();
    }

    public String getRowsPerPageTemplate() {
        return Configuration.DataTable.ROWS_PER_PAGE_TEMPLATE.value();
    }

    public boolean isComissaoNacional() {
        return comissaoNacional;
    }

    public void setComissaoNacional(boolean comissaoNacional) {
        this.comissaoNacional = comissaoNacional;
    }

    public boolean isPesquisaRetornoAta() {
        return pesquisaRetornoAta;
    }

    public void setPesquisaRetornoAta(boolean pesquisaRetornoAta) {
        this.pesquisaRetornoAta = pesquisaRetornoAta;
    }
}
