package pt.segsocial.pcj.ppp.dto.entidadeexterna;

import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;

public class FiltroPesquisaEntidadeExternaDTO {

    private UtilizadorPCJDTO utilizadorPCJ;
    private String numeroCartao;
    private String nome;
    private String email;
    private Boolean ativo;
    private DetalheDominioVO tipoEntidade;
    private ComissaoPCJDTO comissaoPCJDTO;

    public FiltroPesquisaEntidadeExternaDTO() {
        utilizadorPCJ = new UtilizadorPCJDTO();
        comissaoPCJDTO = new ComissaoPCJDTO();
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public UtilizadorPCJDTO getUtilizadorPCJ() {
        return utilizadorPCJ;
    }

    public void setUtilizadorPCJ(UtilizadorPCJDTO utilizadorPCJ) {
        this.utilizadorPCJ = utilizadorPCJ;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public DetalheDominioVO getTipoEntidade() {
        return tipoEntidade;
    }

    public void setTipoEntidade(DetalheDominioVO tipoEntidade) {
        this.tipoEntidade = tipoEntidade;
    }

    public ComissaoPCJDTO getComissaoPCJDTO() {
        return comissaoPCJDTO;
    }

    public void setComissaoPCJDTO(ComissaoPCJDTO comissaoPCJDTO) {
        this.comissaoPCJDTO = comissaoPCJDTO;
    }

    @Override
    public String toString() {
        return "FiltroPesquisaEntidadeExternaDTO{" +
                "numeroCartao='" + numeroCartao + '\'' +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                ", ativo=" + ativo +
                '}';
    }
}
