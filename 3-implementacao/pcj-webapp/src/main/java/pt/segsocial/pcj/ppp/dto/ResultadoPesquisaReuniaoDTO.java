package pt.segsocial.pcj.ppp.dto;

import pt.segsocial.fraw.api.domain.Auditable;
import pt.segsocial.fraw.api.domain.BooleanResult;
import pt.segsocial.fraw.api.domain.Deletable;
import pt.segsocial.fraw.jpa.LongIdentifiable;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ResultadoPesquisaReuniaoDTO implements Serializable, LongIdentifiable, Deletable, Auditable {

    private BigDecimal id;

    private Date dataInicio;
    private Date dataFim;

    private Date horaInicio;

    private Date horaFim;

    private String horario;

    private String sala;

    private String assunto;

    private String modalidade;

    public ResultadoPesquisaReuniaoDTO() {}

    public ResultadoPesquisaReuniaoDTO(BigDecimal id, Date dataInicio, Date dataFim, String assunto, String sala) {
        this.id = id;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.assunto = assunto;
        this.sala = sala;
    }

    public ResultadoPesquisaReuniaoDTO(BigDecimal id, Date dataInicio, Date dataFim, Date horaInicio, Date horaFim, String sala, String assunto, String modalidade) {
        this.id = id;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.horaInicio = horaInicio;
        this.horaFim = horaFim;
        this.sala = sala;
        this.assunto = assunto;
        this.modalidade = modalidade;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataReuniao) {
        this.dataInicio = dataReuniao;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public String getHorario() {
        return formatHorario(horaInicio, horaFim);
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getSala() {
        return sala;
    }

    public void setSala(String sala) {
        this.sala = sala;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Date getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(Date horaInicio) {
        this.horaInicio = horaInicio;
    }

    public Date getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(Date horaFim) {
        this.horaFim = horaFim;
    }

    @Override
    public Long getId() {
        return id.longValue();
    }

    @Override
    public void delete() {

    }

    @Override
    public BooleanResult getIsDeletable() {
        return null;
    }

    @Override
    public boolean isAuditable() {
        return false;
    }

    private String formatHorario(Date horaInicio, Date horaFim) {
        if (horaInicio == null || horaFim == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("HH'h':mm");
        String formattedHoraInicio = formatter.format(horaInicio);
        String formattedHoraFim = formatter.format(horaFim);
        return formattedHoraInicio + " - " + formattedHoraFim;
    }

    @Override
    public String toString() {
        return "ResultadoPesquisaReuniaoDTO{" +
                "id=" + id +
                ", dataInicio=" + dataInicio +
                ", dataFim=" + dataFim +
                ", horaInicio=" + horaInicio +
                ", horaFim=" + horaFim +
                ", horario='" + horario + '\'' +
                ", sala='" + sala + '\'' +
                ", assunto='" + assunto + '\'' +
                ", modalidade='" + modalidade + '\'' +
                '}';
    }
}
