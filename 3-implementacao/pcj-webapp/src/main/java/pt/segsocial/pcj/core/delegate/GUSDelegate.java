package pt.segsocial.pcj.core.delegate;

import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.gus.service.export.ejb.GUSUtilizadorWebInterface;
import pt.segsocial.gus.service.export.vo.ServicoSSVO;
import pt.segsocial.gus.service.export.vo.UtilizadorVO;
import pt.segsocial.pcj.pae.bean.pojo.UtilizadorServico;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.pae.enums.PerfilEnum;
import pt.segsocial.pcj.core.util.PCJDateUtil;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.io.Serializable;
import java.util.*;

@RequestScoped
public class GUSDelegate implements Serializable {

	private static final long serialVersionUID = -2278902071772967441L;
	private static final Logger LOGGER = LoggerFactory.getLogger(GUSDelegate.class);

	@Inject
	private GUSUtilizadorWebInterface gusUtilizadorWebInterface;


    public List<UtilizadorVO> obtemListaUtilizadores(UtilizadorVO utilizadorVO) {
		List<UtilizadorVO> listUtilizador = new ArrayList<>();
		try {
			listUtilizador = gusUtilizadorWebInterface.findUtilizadorCriterias(utilizadorVO);
		} catch (Exception e) {
			LOGGER.error("Erro ao recuperar lista de utilizadaores para o serviço competente ");
		}
		return listUtilizador;
	}
    
	/**
	 * @param username
	 * @param codigoServico
	 * @param perfil
	 * @return o array de utilizadoresVO para ser usado por outros métodos neste
	 *         serviço
	 * @throws PCJException
	 */
	private List<UtilizadorVO> getUtilizadoresInterface(String username, Integer codigoServico,
			String perfil) throws PCJException {
		List<UtilizadorVO> servicoResponse;

		UtilizadorVO utilizadorVO = getUtilizadorVO(username, codigoServico, perfil, PCJDateUtil.getSysdate());

		try {
			servicoResponse = obtemListaUtilizadores(utilizadorVO);
		} catch (Exception e) {
			LOGGER.error("Erro na chamada ao serviço de idq getUtilizadores com o código utilizador " + username
					+ ", código perfil " + perfil + " e código serviço " + codigoServico, e);
			throw new PCJException("Erro na chamada ao serviço de idq getUtilizadores");
		}

		return servicoResponse;
	}

	@NotNull
	private UtilizadorVO getUtilizadorVO(String username, Integer codigoServico, String perfil, Date dataFim) {
		UtilizadorVO utilizadorVO = new UtilizadorVO();
		utilizadorVO.setCodigoUtilizador(username);
		utilizadorVO.setCodigoPerfil(perfil);
		utilizadorVO.setCodigoServico(codigoServico != null ? (long) codigoServico : null);
		utilizadorVO.setDataFim(dataFim);
		return utilizadorVO;
	}

	/**
	 * 
	 * @param username
	 * @return
	 * @throws PCJException
	 */
	public List<UtilizadorServico> getUtilizadorServicoNome(final String username) throws PCJException {
		List<UtilizadorServico> utilizadoresServico = new ArrayList<>();
		List<UtilizadorVO> servicoResponse = getUtilizadoresInterface(username, null, null);
		if (servicoResponse != null && !servicoResponse.isEmpty()) {
			UtilizadorServico us = null;
			for (UtilizadorVO utilizador : servicoResponse) {
				for (ServicoSSVO s : utilizador.getServicos()) {
					us = new UtilizadorServico(Integer.valueOf(s.getCodigoServico()), s.getDataInicio(), s.getDataFim());
					utilizadoresServico.add(us);
				}
			}
		}
		return utilizadoresServico;
	}

	/**
	 * @param codigoServico
	 * @param perfis
	 * @return lista de utilizadores que pertencem a um serviço com o perfil passado
	 *         em argumento
	 * @throws PCJException
	 */
	public List<UtilizadorServico> getUtilizadoresServicoPerfil(Integer codigoServico, final Set<String> perfis) throws PCJException {
		List<UtilizadorServico> utilizadoresServico = new ArrayList<>();
		Date currentTime = PCJDateUtil.getSysdate();
		for (String perfil : perfis) {
			List<UtilizadorVO> servicoResponse = getUtilizadoresInterface(null, codigoServico, perfil);
			if (servicoResponse != null && !servicoResponse.isEmpty()) {
				for (UtilizadorVO utilizador : servicoResponse) {
					if (utilizador.getDataFim().after(currentTime)) {
						ServicoSSVO servico = null;
						for (ServicoSSVO s : utilizador.getServicos()) {
							if (s.getCodigoServico().equals(String.valueOf(codigoServico)) || codigoServico == null) {
								servico = s;
								break;
							}
						}

						if (servico != null && servico.getDataFim().after(currentTime)) {
							UtilizadorServico utilizadorServico = new UtilizadorServico(utilizador.getCodigoUtilizador(), codigoServico, utilizador.getNome());
							if (!utilizadoresServico.contains(utilizadorServico)) {
								utilizadoresServico.add(utilizadorServico);
							}
						}
					}
				}
			}
		}
		return utilizadoresServico;
	}

	/**
	 * @param username
	 * @return o utilizador com o username passado em argumento
	 * @throws PCJException
	 */
	public UtilizadorServico getUtilizador(final String username) throws PCJException {
		UtilizadorServico utilizador = null;
		List<UtilizadorVO> servicoResponse = getUtilizadoresInterface(username, null, null);

		if (servicoResponse != null && servicoResponse.size() == 1) {
			UtilizadorVO utilizadorVOResponse = servicoResponse.get(0);
			ServicoSSVO servicoSSVO = utilizadorVOResponse.getServico(0);
			utilizador = new UtilizadorServico(utilizadorVOResponse.getCodigoUtilizador(),
					Integer.valueOf(servicoSSVO.getCodigoServico()),
					utilizadorVOResponse.getNome());
		}
		return utilizador;
	}

	public UtilizadorServico getUtilizadorGestor(String username, Date dataFim) throws PCJException {
		List<UtilizadorVO> servicoResponse;
		UtilizadorServico utilizador = null;
		UtilizadorVO utilizadorVO = getUtilizadorVO(username, null, null, dataFim);

		try {
			servicoResponse = obtemListaUtilizadores(utilizadorVO);
			if (servicoResponse != null && servicoResponse.size() == 1) {
				UtilizadorVO utilizadorVOResponse = servicoResponse.get(0);
				ServicoSSVO servicoSSVO = utilizadorVOResponse.getServico(0);
				utilizador = new UtilizadorServico(utilizadorVOResponse.getCodigoUtilizador(),
						Integer.valueOf(servicoSSVO.getCodigoServico()),
						utilizadorVOResponse.getNome());
			}
		} catch (Exception e) {
			LOGGER.error("Erro na chamada ao serviço de idq getUtilizadores com o código utilizador ", e);
			throw new PCJException("Erro na chamada ao serviço de idq getUtilizadores");
		}
		return utilizador;
	}

	public String getNomePresidenteByServico(Integer codigoServico) throws PCJException {
		List<UtilizadorServico> utilizadorServicoList = this.getUtilizadoresServicoPerfil(codigoServico, new HashSet<>(Collections.singletonList(PerfilEnum.PRESIDENTE.getDescricao())));
		return utilizadorServicoList.iterator().hasNext() ? utilizadorServicoList.iterator().next().getNome() : "";
	}

	public String getCodUtilizadorPresidenteByServico(Integer codigoServico) throws PCJException {
		List<UtilizadorServico> utilizadorServicoList = this.getUtilizadoresServicoPerfil(codigoServico, new HashSet<>(Collections.singletonList(PerfilEnum.PRESIDENTE.getDescricao())));
		return utilizadorServicoList.iterator().hasNext() ? utilizadorServicoList.iterator().next().getCodUtilizador() : null;
	}

	public String getCodUtilizadorSecretarioByServico(Integer codigoServico) throws PCJException {
		List<UtilizadorServico> utilizadorServicoList = this.getUtilizadoresServicoPerfil(codigoServico, new HashSet<>(Collections.singletonList(PerfilEnum.SECRETARIO.getDescricao())));
		return utilizadorServicoList.iterator().hasNext() ? utilizadorServicoList.iterator().next().getCodUtilizador() : null;
	}
}