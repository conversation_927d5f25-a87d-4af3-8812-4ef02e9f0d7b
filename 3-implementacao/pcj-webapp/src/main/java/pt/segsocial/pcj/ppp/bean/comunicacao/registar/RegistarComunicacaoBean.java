package pt.segsocial.pcj.ppp.bean.comunicacao.registar;


import ch.qos.logback.classic.Level;
import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.joda.time.LocalDate;
import org.primefaces.context.RequestContext;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.Constants;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.ppp.bean.comunicacao.ComunicacaoUtil;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.CriancaJovemStepBean;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.DescricaoSituacaoStepBean;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.ParticipanteStepBean;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.ValidationStep;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoCriancaDTO;
import pt.segsocial.pcj.ppp.dto.ParticipanteDTO;
import pt.segsocial.pcj.ppp.dto.PerfilDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.SituacaoPerigoDTO;
import pt.segsocial.pcj.ppp.service.comunicacao.ComunicacaoService;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Named(value = "pcjRegistarComunicacaoBean")
@Stateful(name = "pcjRegistarComunicacaoBean")
@LocalBean
@ViewAccessScoped
public class RegistarComunicacaoBean implements java.io.Serializable {

    public static final String DATA_ENTRADA_CALENDAR = "registarComunicacaoForm:pcjInputDataEntrada:calendar";
    private Long idCrianca;

    private String nomeCrianca;

    private ValidationStep currentStep = ValidationStep.CRIANCA_JOVEM;

    @Inject
    private CriancaJovemStepBean criancaJovemStepBean;

    @Inject
    private ParticipanteStepBean participanteStepBean;

    @Inject
    private DescricaoSituacaoStepBean descricaoSituacaoStepBean;

    @Inject
    private PCJSubsystem pcjSubsystem;

    @Inject
    private ComunicacaoService comunicacaoService;

    private boolean dataCriancaInvalida;

    @URLAction(mappingId = PCJSubsystem.OP_REGISTAR_COMUNICACAO, onPostback = false)
    public void onLoadRegistarComunicacao() {
        setDataCriancaInvalida(false);
        LoggingHelper.logEntrada();
    }

    public void registarComunicacao() {
        ComunicacaoCriancaDTO comunicacaoCrianca = criancaJovemStepBean.getComunicacaoCrianca();
        ParticipanteDTO participante = participanteStepBean.getParticipante();
        List<SituacaoPerigoDTO> situacoes = descricaoSituacaoStepBean.getSituacoes();
        PerfilDTO perfil = pcjSubsystem.getLogin() != null ? pcjSubsystem.getLogin().getPerfilDTO() : null;

        LoggingHelper.logEntrada(Level.DEBUG, comunicacaoCrianca);
        LoggingHelper.logEntrada(Level.DEBUG, participante);
        LoggingHelper.logEntrada(Level.DEBUG, situacoes);

        if (!validacaoRegistarComunicacao()) {
            LoggingHelper.logSaida("Validação falhou. Comunicação não registada.");
            return;
        }

        try {
            comunicacaoService.registarComunicacao(comunicacaoCrianca, participante, situacoes, perfil);
            JsfMessageUtil.mostraMensagemSucesso("Comunicação registada com sucesso.");
            LoggingHelper.logSaida("Comunicação registada com sucesso.");
            pcjSubsystem.redirectTo(pcjSubsystem.pesquisarComunicacoes(), null);
        } catch (IOException e) {
            handleIOException(e);
        } catch (Exception e) {
            handleGenericException(e);
        }
    }

    public List<ValidationStep> getSteps() {
        final List<ValidationStep> steps = new ArrayList<>();
        ValidationStep step = ValidationStep.CRIANCA_JOVEM;

        while (step != null) {
            steps.add(step);
            step = step.next();
        }
        return steps;
    }

    public String getNextBtnLabel() {
        if (hasNext())
            return "Seguinte: " + currentStep.next().getTitle();
        else if (isLast())
            return currentStep.last().getTitle();
        return "Unknown";
    }

    public String getPreviousBtnLabel() {
        return "Anterior: " + currentStep.previous().getTitle();
    }

    public boolean hasPrevious() {
        return currentStep.previous() != null;
    }

    public boolean hasNext() {
        return currentStep.next() != null;
    }

    public boolean isLast() {
        return currentStep.next() == null && currentStep.last() != null;
    }

    public void last() {
        currentStep = currentStep.last();
    }

    public void next() {
        if(currentStep == ValidationStep.PARTICIPANTE) {
            if(participanteStepBean.dataEntradaMaiorQueDataRegisto()) {
                ComunicacaoUtil.marcaCampoInputDataEntradaErro(DATA_ENTRADA_CALENDAR, "Data de entrada não pode ser maior que a data de registo.");
                return;
            }
        }
        currentStep = currentStep.next();
    }

    public void previous() {
        currentStep = currentStep.previous();
    }

    public boolean inStep(final ValidationStep step) {
        return currentStep == step;
    }

    private boolean validacaoRegistarComunicacao() {
        if (this.descricaoSituacaoStepBean.getSituacoes().isEmpty()) {
            LoggingHelper.logSaida("É preciso ter pelo menos uma situação antes de registar");
            FacesContext context = FacesContext.getCurrentInstance();
            context.getExternalContext().getFlash().setKeepMessages(true);
            JsfMessageUtil.mostraMensagemErro("É preciso ter pelo menos uma situação de perigo antes de registar.");
            return false;
        }

        if(participanteStepBean.dataEntradaMaiorQueDataRegisto()) {
            LoggingHelper.logSaida("A data de entrada não pode ser maior que a data de registo.");
            FacesContext context = FacesContext.getCurrentInstance();
            context.getExternalContext().getFlash().setKeepMessages(true);
            JsfMessageUtil.mostraMensagemErro("A data de entrada não pode ser maior que a data de registo.");
            return false;
        }

        return true;
    }

    public void validaIdedeCrianca() {
        LocalDate dataCrianca = LocalDate.fromDateFields(getCriancaJovemStepBean().getComunicacaoCrianca().getDataNascimento());
         if(PCJDateUtil.calcularIdade(dataCrianca) > Constants.IDADE_LIMITE_CRIANCA) {
             getCriancaJovemStepBean().getComunicacaoCrianca().setDataNascimento(null);
             setDataCriancaInvalida(true);
         } else {
             setDataCriancaInvalida(false);
         }
        RequestContext.getCurrentInstance().update("registarComunicacaoForm:pcjMensagemDataNascPnl");
        RequestContext.getCurrentInstance().update("comunicacaoAlterarForm:alterarComunicacaTabView:pcjMensagemDataNascPnl");
        RequestContext.getCurrentInstance().update("comunicacaoAnalisarForm:analisarComunicacaTabView:pcjMensagemDataNascPnl");
    }

    private void handleIOException(IOException e) {
        LoggingHelper.logError(e);
        JsfMessageUtil.mostraMensagemErro("Erro ao redirecionar para a página de pesquisa de comunicações.");
    }

    private void handleGenericException(Exception e) {
        LoggingHelper.logError(e);
        String mensagem = e.getMessage() != null ? e.getMessage() : "Ocorreu um erro ao registrar a comunicação.";
        JsfMessageUtil.mostraMensagemErro(mensagem);
    }

    public String getIdPainelDescricaoSituacao() {
        return "registarComunicacaoForm:tabelaSituacoes registarComunicacaoForm:descricaoSituacaoPanel";
    }

    public Long getIdCrianca() {
        return idCrianca;
    }

    public void setIdCrianca(Long idCrianca) {
        this.idCrianca = idCrianca;
    }

    public String getNomeCrianca() {
        return nomeCrianca;
    }

    public void setNomeCrianca(String nomeCrianca) {
        this.nomeCrianca = nomeCrianca;
    }

    public ValidationStep getCurrentStep() {
        return currentStep;
    }

    public CriancaJovemStepBean getCriancaJovemStepBean() {
        return criancaJovemStepBean;
    }

    public ParticipanteStepBean getParticipanteStepBean() {
        return participanteStepBean;
    }

    public DescricaoSituacaoStepBean getDescricaoSituacaoStepBean() {
        return descricaoSituacaoStepBean;
    }

    public boolean isDataCriancaInvalida() {
        return dataCriancaInvalida;
    }

    public void setDataCriancaInvalida(boolean dataCriancaInvalida) {
        this.dataCriancaInvalida = dataCriancaInvalida;
    }

}
