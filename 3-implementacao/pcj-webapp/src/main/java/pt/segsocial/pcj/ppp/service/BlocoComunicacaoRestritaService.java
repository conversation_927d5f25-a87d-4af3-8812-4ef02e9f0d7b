package pt.segsocial.pcj.ppp.service;

import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaParaRegistoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.FiltroPesquisaComunicacaoRegistoEvento;
import pt.segsocial.pcj.ppp.jpa.dao.BlocoComunicacaoRestritaDAO;
import pt.segsocial.pcj.ppp.jpa.entity.BlocoComunicacaoRestrita;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.PersistenceException;
import java.util.List;

@RequestScoped
public class BlocoComunicacaoRestritaService {

    @Inject
    private BlocoComunicacaoRestritaDAO blocoComunicacaoRestritaDAO;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarBlocoComunicacaoRestrita(BlocoComunicacaoRestrita blocoComunicacaoRestrita) throws DomainException {
        blocoComunicacaoRestritaDAO.create(blocoComunicacaoRestrita);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void atualizarBlocosComunicacaoRestrita(List<BlocoComunicacaoRestritaParaRegistoDTO> blocosRestrita, ReuniaoPCJ reuniaoPCJ) throws DomainException {
        for (BlocoComunicacaoRestritaParaRegistoDTO blocoRestrita : blocosRestrita) {
            if(blocoRestrita.getIdBlocoComunicacaoRestrita() == null) {
                LoggingHelper.logError(new DomainException("Bloco Restrita com ID nulo - " + blocoRestrita.getNomeComunicacao()));
                continue;
            }
            BlocoComunicacaoRestrita blocoComunicacaoRestrita = blocoComunicacaoRestritaDAO.find(blocoRestrita.getIdBlocoComunicacaoRestrita());
            blocoComunicacaoRestrita.setReuniao(reuniaoPCJ);
            blocoComunicacaoRestritaDAO.update(blocoComunicacaoRestrita);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void atualizarBlocoComunicacaoRestrita(Comunicacao comunicacao, ReuniaoPCJ reuniaoPCJ) throws DomainException {
        BlocoComunicacaoRestrita blocoComunicacaoRestrita = blocoComunicacaoRestritaDAO.findByComunicacaoId(comunicacao.getId());
        if(blocoComunicacaoRestrita == null) { //garante que havera um bloco de comunicacao restrita
            blocoComunicacaoRestrita = BlocoComunicacaoRestrita.builder()
                    .comunicacao(comunicacao)
                    .reuniao(reuniaoPCJ)
                    .executado(false).build();
            salvarBlocoComunicacaoRestrita(blocoComunicacaoRestrita);
        } else {
            blocoComunicacaoRestrita.setReuniao(reuniaoPCJ);
            blocoComunicacaoRestritaDAO.update(blocoComunicacaoRestrita);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarNovoBlocoAoSalvarNovoRegistoComunicacao(Comunicacao comunicacao, ReuniaoPCJ reuniaoPCJ) throws DomainException {
        BlocoComunicacaoRestrita novoBlocoComunicacaoRestrita = BlocoComunicacaoRestrita.builder()
                .comunicacao(comunicacao)
                .reuniao(reuniaoPCJ)
                .executado(false).build();
        salvarBlocoComunicacaoRestrita(novoBlocoComunicacaoRestrita);
    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> recuperarBlocosComunicacaoRestritaPorUrgencia(FiltroPesquisaComunicacaoRegistoEvento filtro) {
        return blocoComunicacaoRestritaDAO.recuperarBlocosComunicacaoRestritaPorUrgencia(filtro);
    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> recuperarBlocosComunicacaoRestritaPorIdReuniao(FiltroPesquisaComunicacaoRegistoEvento filtro) {
        return blocoComunicacaoRestritaDAO.recuperarBlocosComunicacaoRestritaPorFiltro(filtro);
    }

    public ReuniaoPCJ recuperaReuniaoBlocoComunicacao(Comunicacao comunicacao) {
        return blocoComunicacaoRestritaDAO.recuperarReuniaoBlocoComunicacao(comunicacao);
    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> recuperaBlocosComunicacaoRestritaPorIdReuniaoAndNovos(FiltroPesquisaComunicacaoRegistoEvento filtro) {
        return blocoComunicacaoRestritaDAO.recuperarBlocosComunicacaoRestritaPorIdReuniaoAndNovos(filtro);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void atualizarBlocosComunicacaoRestritaParaReuniaoCancelada(ReuniaoPCJ reuniaoPCJ) throws PersistenceException {
        blocoComunicacaoRestritaDAO.atualizarBlocosComunicacaoRestritaParaReuniaoCancelada(reuniaoPCJ);
    }

    public boolean existemComunicacoesVinculadas(ReuniaoPCJ reuniaoPCJ) {
        return blocoComunicacaoRestritaDAO.existemComunicacoesVinculadas(reuniaoPCJ);
    }
}
