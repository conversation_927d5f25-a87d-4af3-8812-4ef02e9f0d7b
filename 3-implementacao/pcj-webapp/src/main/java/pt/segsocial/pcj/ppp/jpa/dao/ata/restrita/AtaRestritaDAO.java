package pt.segsocial.pcj.ppp.jpa.dao.ata.restrita;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.gvr.api.util.StringUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.pae.jpa.dao.PCJDao;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.FiltroPesquisaAtaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ResultadoPesquisaAtaDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.AtaRestritaConsultaDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.TrabalhoDTO;
import pt.segsocial.pcj.ppp.enums.EstadoAtaEnum;
import pt.segsocial.pcj.ppp.jpa.dao.PaginacaoResultado;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.AtaRestrita;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.io.Serializable;
import java.util.*;

public class AtaRestritaDAO extends PCJDao<AtaRestrita, Long> implements Serializable {

    public static final String PAGE_SIZE = "pageSize";
    public static final String OFFSET = "offset";
    protected static final Logger LOGGER = LoggerFactory.getLogger(AtaRestritaDAO.class);
    private static final long serialVersionUID = 1L;
    private static final String BASE_QUERY =
            "FROM pcj.ATA_restrita_PCJ a " +
                    "LEFT JOIN pcj.REUNIAO_PCJ r ON r.ID_REUNIAO_PCJ = a.ID_REUNIAO_PCJ " +
                    "LEFT JOIN pcj.COMISSAO_PCJ c ON c.ID_COMISSAO_PCJ = a.ID_COMISSAO_PCJ ";
    private static final String CONSULTA_ATAS_PAGINADAS =
            "SELECT " +
                    "a.ID_ATA_restrita_PCJ AS id, " +
                    "a.NUMERO_ATA_restrita AS numeroAta, " +
                    "a.DATA_REALIZACAO AS data, " +
                    "c.NOME AS nomeCpcj, " +
                    "r.CODIGO_TIPO_CONVOCATORIA AS tipoAta, " +
                    "a.ESTADO_ATA AS estado,  " +
                    "r.ID_REUNIAO_PCJ AS idReuniao " +
                    BASE_QUERY +
                    "WHERE 1=1 ";
    private static final String CONSULTA_QUANTIDADE_ATAS =
            "SELECT COUNT(a.ID_ATA_restrita_PCJ) " +
                    BASE_QUERY +
                    "WHERE 1=1 ";

    @Inject
    public AtaRestritaDAO(EntityManager entityManager) {
        super(entityManager, AtaRestrita.class);
    }

    public AtaRestrita findByMeeting(Long idReuniao) {
        try {
            return this.getEntityManager().createNamedQuery(AtaRestrita.FIND_BY_MEETING, AtaRestrita.class)
                    .setParameter("idReuniao", idReuniao)
                    .getSingleResult();
        } catch (NoResultException e) {
            return new AtaRestrita();
        }
    }

    public List<TrabalhoDTO> obterListaTrabalhos(Long idComissao) {
        try {
            String sql = "select bp.assunto, 'Pendente' as Tipo  " +
                    "from pcj.bloco_parecer bp  " +
                    "where bp.executado = :executado  " +
                    "and bp.id_comissao_pcj = :idComissao  " +
                    "union " +
                    "select bc.assunto, 'Pendente' as Tipo  " +
                    "from pcj.bloco_conhecimento bc  " +
                    "where bc.executado = :executado  " +
                    "and bc.id_comissao_pcj = :idComissao   " +
                    "union " +
                    " select 'Aprovação do plano anual' as assunto, 'Pendente' as Tipo  " +
                    "from pcj.bloco_aprovacao_plano_anual bapa " +
                    "where bapa.executado = :executado  " +
                    "and bapa.id_comissao_pcj = :idComissao   ";

            Query query = getEntityManager().createNativeQuery(sql);
            query.setParameter("idComissao", idComissao);
            query.setParameter("executado", false);

            List<Object[]> results = query.getResultList();
            List<TrabalhoDTO> trabalhos = new ArrayList<>();
            for (Object[] row : results) {
                String assunto = (String) row[0];
                String tipo = (String) row[1];
                trabalhos.add(new TrabalhoDTO(assunto, tipo));
            }
            return trabalhos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public String obterNumeroSequencialAta(Long idComissao, String anoReuniao) {
        String sql = " select " +
                " substr ('00' || to_char(nvl(max(substr(numero_ata_restrita,1,3))+1,1)) || '/' || :anoReuniao, " +
                " length ('00' || to_char(nvl(max(substr(numero_ata_restrita,1,3))+1,1)) || '/' || :anoReuniao)-7, " +
                " length ('00' || to_char(nvl(max(substr(numero_ata_restrita,1,3))+1,1)) || '/' || :anoReuniao)) " +
                "   from pcj.ata_restrita_pcj " +
                "       where substr(numero_ata_restrita, 5,4) = :anoReuniao " +
                "       and id_comissao_pcj = :idComissao ";
        Query query = getEntityManager().createNativeQuery(sql);
        query.setParameter("idComissao", idComissao);
        query.setParameter("anoReuniao", anoReuniao);

        return (String) query.getSingleResult();
    }

    public PaginacaoResultado<ResultadoPesquisaAtaDTO> pesquisaAtasComPaginacao(FiltroPesquisaAtaDTO filtro, int first, int pageSize,
                                                                                String sortField, String sortOrder) {
        StringBuilder sql = constroiConsultaDeAtas(filtro, sortField, sortOrder);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        query.setParameter(PAGE_SIZE, pageSize);
        query.setParameter(OFFSET, first);
        defineParametros(query, filtro);
        List<Object[]> results = query.getResultList();
        List<ResultadoPesquisaAtaDTO> resultadosPesquisa = new ArrayList<>();
        int totalRegistros = contarTotalAtas(filtro);
        return new PaginacaoResultado<>(resultadosPesquisa, totalRegistros, first, pageSize);
    }

    private StringBuilder constroiConsultaDeAtas(FiltroPesquisaAtaDTO filtros, String sortField, String sortOrder) {
        StringBuilder sql = new StringBuilder(CONSULTA_ATAS_PAGINADAS);
        adicionaFiltrosNaConsulta(sql, filtros);
        adicionaOrdenacaoNaConsulta(sql, sortField, sortOrder);
        sql.append(" OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY ");
        return sql;
    }

    private void adicionaFiltrosNaConsulta(StringBuilder sql, FiltroPesquisaAtaDTO filtros) {
        if (filtros.existemFiltrosValidos()) {
            adicionaFiltro(sql, "TRUNC(a.DATA_REALIZACAO )", filtros.getDataInicio(), ">=", "dataInicio");
            adicionaFiltro(sql, "TRUNC(a.DATA_REALIZACAO )", filtros.getDataFim(), "<=", "dataFim");
            adicionaFiltro(sql, "a.ID_COMISSAO_PCJ", filtros.getIdComissao(), "=", "idComissao");
            adicionaFiltro(sql, "r.CODIGO_TIPO_CONVOCATORIA ", filtros.getTipoAta(), "=", "tipoAta");
            adicionaFiltro(sql, "a.ESTADO_ATA", filtros.getEstadoAta(), "=", "estadoAta");
        }
    }

    private void adicionaFiltro(StringBuilder sql, String campo, Object valor, String operador, String parametro) {
        if (valor != null && !valor.toString().isEmpty()) {
            if (valor instanceof List) {
                List<?> lista = (List<?>) valor;
                if (!lista.isEmpty()) {
                    sql.append(" AND ").append(campo).append(" IN (:").append(parametro).append(") ");
                }
            } else {
                if (operador.equalsIgnoreCase("LIKE")) {
                    sql.append(" AND ").append(campo).append(" LIKE :").append(parametro).append(" ");
                } else if (operador.equalsIgnoreCase("IN")) {
                    sql.append(" AND ").append(campo).append(" IN (:").append(parametro).append(") ");
                } else {
                    sql.append(" AND ").append(campo).append(" ").append(operador).append(" :").append(parametro).append(" ");
                }
            }
        }
    }

    private void defineParametros(Query query, FiltroPesquisaAtaDTO filtros) {
        adicionaParametro(query, "dataInicio", filtros.getDataInicio(), false);
        adicionaParametro(query, "dataFim", filtros.getDataFim(), false);
        adicionaParametro(query, "idComissao", filtros.getIdComissao(), false);
        adicionaParametro(query, "tipoAta", filtros.getTipoAta(), false);
        adicionaParametro(query, "estadoAta", filtros.getEstadoAta(), false);
    }

    private void adicionaParametro(Query query, String parametro, Object valor, boolean like) {
        if (valor != null && !valor.toString().isEmpty()) {
            if (valor instanceof List) {
                List<?> lista = (List<?>) valor;
                if (!lista.isEmpty()) {
                    query.setParameter(parametro, lista);
                }
            } else {
                query.setParameter(parametro, like ? "%" + valor.toString().toLowerCase() + "%" : valor);
            }
        }
    }

    private void adicionaOrdenacaoNaConsulta(StringBuilder sql, String sortField, String sortOrder) {
        if (StringUtil.isNotNullOrEmpty(sortField)) {
            sql.append(" ORDER BY ").append(sortField).append(" ");
            sql.append(sortOrder != null && sortOrder.equalsIgnoreCase("ASCENDING") ? "ASC" : "DESC");
        } else {
            sql.append(" ORDER BY a.NUMERO_ATA_restrita DESC ");
        }
    }

    private int contarTotalAtas(FiltroPesquisaAtaDTO filtro) {
        StringBuilder sql = new StringBuilder(CONSULTA_QUANTIDADE_ATAS);
        adicionaFiltrosNaConsulta(sql, filtro);
        Query query = getEntityManager().createNativeQuery(sql.toString());
        defineParametros(query, filtro);
        return ((Number) query.getSingleResult()).intValue();
    }

    public List<AtaRestrita> findByIdReuniaoEstadoAta(Long idReuniao, Long idComissao, EstadoAtaEnum estadoAta) {
        String sql = "SELECT a FROM AtaRestrita a WHERE a.reuniaoPCJ.id = :idReuniao " +
                "  AND a.comissao.id = :idComissao " +
                "  AND a.estadoAta = :estadoAta ";
        try {
            return getEntityManager().createQuery(sql, AtaRestrita.class)
                    .setParameter("idReuniao", idReuniao)
                    .setParameter("idComissao", idComissao)
                    .setParameter("estadoAta", estadoAta)
                    .getResultList();
        } catch (NoResultException e) {
            LOGGER.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public boolean findByIdReuniaoEstadosAssConcl(Long idReuniao, Long idComissao) {
        String sql = "SELECT COUNT(a) FROM AtaRestrita a WHERE a.reuniaoPCJ.id = :idReuniao " +
                "  AND a.comissao.id = :idComissao " +
                "  AND a.estadoAta in :estadoAta ";
        try {
            TypedQuery<Long> query = getEntityManager().createQuery(sql, Long.class);
            query.setParameter("idReuniao", idReuniao)
                    .setParameter("idComissao", idComissao)
                    .setParameter("estadoAta", Arrays.asList(EstadoAtaEnum.ASSINATURA, EstadoAtaEnum.CONCLUIDO));

            Long count = query.getSingleResult();
            return count > 0;
        } catch (NoResultException e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }
    }

    //    existem eventos anteriores para a CPCJ com ata Exibir botão registar Ata
    //    tem eventos anteriores sem estado de assinatura ou concluído
    public boolean existemEventosAnterioresParaCPCJComAtaEstadoDiffConcluidoOuAssinado(Long idReuniao, Long idCpcj) {
        List<EstadoAtaEnum> estados = Arrays.asList(EstadoAtaEnum.CONCLUIDO, EstadoAtaEnum.ASSINATURA);
        String hql = "SELECT COUNT(ap) FROM AtaRestrita ap " +
                "WHERE ap.reuniaoPCJ.comissao.id = :idComissao " +
                "AND ap.reuniaoPCJ.id = :idReuniao " +
                "AND ap.estadoAta NOT IN (:estados)";
        Query query = getEntityManager().createQuery(hql);
        query.setParameter("idComissao", idCpcj);
        query.setParameter("idReuniao", idReuniao);
        query.setParameter("estados", estados);

        Long count = (Long) query.getSingleResult();
        return count > 0;
    }

    public Long buscarIdReuniaoExcluindoConcluidoOuAssinado(Long idCpcj) {
        List<EstadoAtaEnum> estadosIgnorados = Arrays.asList(EstadoAtaEnum.CONCLUIDO, EstadoAtaEnum.ASSINATURA);

        String hql = "SELECT ap.reuniaoPCJ.id " +
                "FROM AtaRestrita ap " +
                "WHERE ap.reuniaoPCJ.comissao.id = :idComissao " +
                "  AND ap.estadoAta NOT IN (:estados)";

        TypedQuery<Long> query = getEntityManager().createQuery(hql, Long.class);
        query.setParameter("idComissao", idCpcj);
        query.setParameter("estados", estadosIgnorados);
        query.setMaxResults(1);

        List<Long> resultado = query.getResultList();
        return resultado.isEmpty() ? null : resultado.get(0);
    }

    public AtaRestritaConsultaDTO retornaAtaRegistadaParaReuniaoNaoAssinadaNaoConcluida(Long idReuniao) {
        List<EstadoAtaEnum> estados = Arrays.asList(EstadoAtaEnum.CONCLUIDO, EstadoAtaEnum.ASSINATURA);
        String hql = "select new pt.segsocial.pcj.ppp.dto.ata.restrita.AtaRestritaConsultaDTO(a.id, a.numeroAtaRestrita) " +
                "from AtaRestrita a " +
                "where a.reuniaoPCJ.id = :idReuniao and a.estadoAta NOT IN (:estados)";
        try {
            TypedQuery<AtaRestritaConsultaDTO> query = getEntityManager().createQuery(hql, AtaRestritaConsultaDTO.class);
            query.setParameter("idReuniao", idReuniao);
            query.setParameter("estados", estados);
            return query.getSingleResult();
        } catch (Exception e) {
            LoggingHelper.logError(e);
            return new AtaRestritaConsultaDTO();
        }
    }

    public List<AtaRestrita> buscarAtasMais10Dias() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -10);
            Date dataLimite = calendar.getTime();
            String concluido = EstadoAtaEnum.CONCLUIDO.name();
            String jpql = "SELECT a FROM AtaRestrita a WHERE a.dataRealizacao >= :dataLimite and a.estadoAta != :concluido ";
            TypedQuery<AtaRestrita> query = getEntityManager().createQuery(jpql, AtaRestrita.class);
            query.setParameter("dataLimite", dataLimite);
            query.setParameter("concluido", concluido);

            return query.getResultList();
        } catch (Exception e) {
            LOGGER.error("Erro inesperado ao buscar atas: ", e);
            LoggingHelper.logError(e);
            return Collections.emptyList();
        }
    }

    public boolean existeAtaIniciadaParaReuniao(Long idReuniao, Long idComissao, boolean urgente) {
        String hql = "select count(a) " +
                "from AtaRestrita a " +
                "where a.reuniaoPCJ.id = :idReuniao " +
                "and a.comissao.id = :idComissao ";
        try {
            TypedQuery<Long> query = getEntityManager().createQuery(hql, Long.class);
            query.setParameter("idReuniao", idReuniao);
            return query.getSingleResult() > 0;
        } catch (Exception e) {
            LoggingHelper.logError(e);
            return false;
        }
    }

    public boolean buscarAtasPendendeAssinatura(ComissaoPCJDTO comissao) {
        try {
            String assinatura = EstadoAtaEnum.ASSINATURA.name();
            String jpql = "SELECT count(a) FROM AtaRestrita a WHERE a.comissao.id = :comisaoId and a.estadoAta = :assinatura ";
            TypedQuery<Long> query = getEntityManager().createQuery(jpql, Long.class);
            query.setParameter("comisaoId", comissao.getId());
            query.setParameter("assinatura", EstadoAtaEnum.ASSINATURA);

            return query.getSingleResult() > 0;
        } catch (Exception e) {
            LOGGER.error("Erro ao consultar atas: ", e);
            LoggingHelper.logError(e);
            return false;
        }
    }

    public boolean updateAtaRestrita(AtaRestrita ataRestrita) {
        this.getEntityManager().merge(ataRestrita);
        this.getEntityManager().flush();
        return true;
    }

}
