package pt.segsocial.pcj.ppp.jpa.dao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.AtaRestritaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoAtaRestritaPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoAtaRestritaPCJId;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import java.io.Serializable;

public class DocumentoAtaRestritaPCJDAO extends DAO<DocumentoAtaRestritaPCJ, DocumentoAtaRestritaPCJId> implements Serializable {

    private static final long serialVersionUID = 8957628304084911276L;

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentoAtaRestritaPCJDAO.class);

    @Inject
    public DocumentoAtaRestritaPCJDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public DocumentoAtaRestritaPCJ obterDocumentoAta(AtaRestritaDTO ata) {
        try {
            String hql = "select d from DocumentoAtaAlargadaPCJ d where d.id.ataAlargadaId = :ataId order by d.documentoAssinado desc";
            TypedQuery<DocumentoAtaRestritaPCJ> query = getEntityManager().createQuery(hql, DocumentoAtaRestritaPCJ.class);
            query.setParameter("ataId", ata.getId());
            return query.getResultList().get(0);
        } catch (Exception e) {
            LOGGER.error("[ PCJ ] Erro ao obterDocumentoAta: ", e);
            return null;
        }
    }

}
