package pt.segsocial.pcj.core.util;

import ch.qos.logback.classic.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

import static ch.qos.logback.classic.Level.*;

public class LoggingHelper {

    public static final String DESCONHECIDO = "Desconhecido";

    private LoggingHelper() {}

    public static void logEntrada(Object... params) {
        logEntrada(Level.INFO, null, params);
    }

    public static void logEntrada(String customMessage, Object... params) {
        logEntrada(Level.INFO, customMessage, params);
    }

    public static void logEntrada(Level level, Object... params) {
        logEntrada(level, null, params);
    }

    public static void logSaida(Object... results) {
        logSaida(Level.INFO, null, results);
    }

    public static void logSaida(Level level, Object... results) {
        logSaida(level, null, results);
    }

    public static void logSaida(String customMessage, Object... results) {
        logSaida(Level.INFO, customMessage, results);
    }

    public static void logEntrada(Level level, String customMessage, Object... params) {
        CallerInfo callerInfo = getCallerInfo();
        Logger logger = LoggerFactory.getLogger(callerInfo.className);
        String className = callerInfo.simpleClassName;
        String methodName = callerInfo.methodName;
        StringBuilder message = new StringBuilder();

        message.append("[PCJ] Entrando no método: ").append(className).append(".").append(methodName);

        if (customMessage != null && !customMessage.isEmpty()) {
            message.append(" - ").append(customMessage);
        }

        if (params != null && params.length > 0) {
            String paramsStr = objectsToString(params);
            message.append(" com parâmetros: ").append(paramsStr);
        }

        log(logger, level, message.toString());
    }

    public static void logSaida(Level level, String customMessage, Object... results) {
        CallerInfo callerInfo = getCallerInfo();
        Logger logger = LoggerFactory.getLogger(callerInfo.className);
        String className = callerInfo.simpleClassName;
        String methodName = callerInfo.methodName;
        StringBuilder message = new StringBuilder();

        message.append("[PCJ] Saindo do método: ").append(className).append(".").append(methodName);

        if (customMessage != null && !customMessage.isEmpty()) {
            message.append(" - ").append(customMessage);
        }

        if (results != null && results.length > 0) {
            String resultsStr = objectsToString(results);
            message.append(" com resultados: ").append(resultsStr);
        }

        log(logger, level, message.toString());
    }

    public static void logError(Exception e) {
        CallerInfo callerInfo = getCallerInfo();
        Logger logger = LoggerFactory.getLogger(callerInfo.className);
        String className = callerInfo.simpleClassName;
        String methodName = callerInfo.methodName;
        logger.error("[PCJ] Exceção no método: {}.{}", className, methodName, e);
    }

    private static void log(Logger logger, Level level, String message) {
        if (level.equals(TRACE)) {
            if (logger.isTraceEnabled()) {
                logger.trace(message);
            }
        } else if (level.equals(DEBUG)) {
            if (logger.isDebugEnabled()) {
                logger.debug(message);
            }
        } else if (level.equals(WARN)) {
            if (logger.isWarnEnabled()) {
                logger.warn(message);
            }
        } else if (level.equals(ERROR)) {
            if (logger.isErrorEnabled()) {
                logger.error(message);
            }
        } else {
            if (logger.isInfoEnabled()) {
                logger.info(message);
            }
        }
    }

    private static String objectsToString(Object[] objects) {
        if (objects == null || objects.length == 0) {
            return "[]";
        } else {
            StringBuilder sb = new StringBuilder();
            sb.append("[");
            for (int i = 0; i < objects.length; i++) {
                sb.append(objectToString(objects[i]));
                if (i < objects.length - 1) {
                    sb.append(", ");
                }
            }
            sb.append("]");
            return sb.toString();
        }
    }

    private static String objectToString(Object object) {
        if (object == null) {
            return "null";
        } else if (object.getClass().isArray()) {
            if (object instanceof Object[]) {
                return Arrays.deepToString((Object[]) object);
            } else if (object instanceof int[]) {
                return Arrays.toString((int[]) object);
            } else if (object instanceof long[]) {
                return Arrays.toString((long[]) object);
            } else if (object instanceof double[]) {
                return Arrays.toString((double[]) object);
            } else if (object instanceof boolean[]) {
                return Arrays.toString((boolean[]) object);
            } else if (object instanceof char[]) {
                return Arrays.toString((char[]) object);
            } else if (object instanceof float[]) {
                return Arrays.toString((float[]) object);
            } else if (object instanceof short[]) {
                return Arrays.toString((short[]) object);
            } else if (object instanceof byte[]) {
                return Arrays.toString((byte[]) object);
            } else {
                return "Tipo de array desconhecido";
            }
        } else {
            return object.toString();
        }
    }

    private static class CallerInfo {
        String className;
        String simpleClassName;
        String methodName;

        CallerInfo(String className, String simpleClassName, String methodName) {
            this.className = className;
            this.simpleClassName = simpleClassName;
            this.methodName = methodName;
        }
    }

    private static CallerInfo getCallerInfo() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String loggingHelperClassName = LoggingHelper.class.getName();
        for (int i = 2; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            if (!element.getClassName().equals(loggingHelperClassName)) {
                String className = element.getClassName();
                String simpleClassName = className.substring(className.lastIndexOf('.') + 1);
                String methodName = element.getMethodName();
                return new CallerInfo(className, simpleClassName, methodName);
            }
        }
        return new CallerInfo(DESCONHECIDO, DESCONHECIDO, DESCONHECIDO);
    }
}
