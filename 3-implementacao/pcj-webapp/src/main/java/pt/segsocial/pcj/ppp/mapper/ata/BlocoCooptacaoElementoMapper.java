package pt.segsocial.pcj.ppp.mapper.ata;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoCooptacaoElementoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoCooptacaoElemento;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BlocoCooptacaoElementoMapper {

    BlocoCooptacaoElemento toEntity(BlocoCooptacaoElementoDTO blocoCooptacaoElementoDTO);

    BlocoCooptacaoElementoDTO toDTO(BlocoCooptacaoElemento blocoCooptacaoElemento);

}
