package pt.segsocial.pcj.ppp.service;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.util.StringUtils;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.ppp.dto.HistoricoElementoDTO;
import pt.segsocial.pcj.ppp.enums.CamposElementoVersaoEnum;
import pt.segsocial.pcj.ppp.exceptions.InvalidInputException;
import pt.segsocial.pcj.ppp.jpa.dao.HistoricoElementoDAO;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.ElementoVersao;
import pt.segsocial.pcj.ppp.jpa.entity.HistoricoElemento;
import pt.segsocial.pcj.ppp.mapper.HistoricoElementoMapper;

import javax.ejb.TransactionAttribute;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequestScoped
public class HistoricoElementoService {

    private final Logger LOGGER = LoggerFactory.getLogger(HistoricoElementoService.class);
    private static final String ELEMENTO_NAO_PODE_SER_NULO = "Elemento não pode ser nulo";
    private static final String MOTIVO_NAO_PODE_SER_NULO_OU_VAZIO = "Motivo não pode ser nulo ou vazio";
    public static final String REGISTO_DO_ELEMENTO = "Registo do elemento";
    public static final String PRIMEIRA_RENOVACAO_DE_MANDATO = "1ª renovação de mandato";
    public static final String SEGUNDA_RENOVACAO_DE_MANDATO = "2ª renovação de mandato";
    public static final String PROLONGAMENTO_MANDATO = "Prolongamento de mandato";
    public static final String ELEICAO_PRESIDENCIA = "Eleição a presidência";
    public static final String REELEICAO_PRESIDENCIA = "Reeleição a presidência";
    public static final String DESIGNACAO_SECRETARIO = "Designação a secretário";
    public static final String FIM_MANDATO = "Antecipação de final de mandato";
    public static final String ALTERACAO_FUNCAO_PRIMEIRA = "Alteração de tipo de membro: para %s";
    public static final String ALTERACAO_FUNCAO = "Alteração de tipo de membro: de %s para %s";


    @Inject
    private HistoricoElementoMapper historicoElementoMapper;

    @Inject
    private PCJServiceLocator paeServiceLocator;

    private HistoricoElementoDAO historicoElementoDAO;

    @Inject
    public HistoricoElementoService(HistoricoElementoDAO historicoElementoDAO) {
       this.historicoElementoDAO = historicoElementoDAO;
    }

    public HistoricoElementoService(){}

    protected HistoricoElementoService(HistoricoElementoDAO historicoElementoDAO, HistoricoElementoMapper historicoElementoMapper, PCJServiceLocator paeServiceLocator) {
        this.historicoElementoDAO = historicoElementoDAO;
        this.historicoElementoMapper = historicoElementoMapper;
        this.paeServiceLocator = paeServiceLocator;
    }

    @TransactionAttribute
    public void registar(Elemento elemento, String motivoDescricao) throws DomainException {
        validarEntradas(elemento, motivoDescricao);
        HistoricoElemento historicoElemento = criarNovoHistoricoElemento(elemento, motivoDescricao);
        historicoElementoDAO.create(historicoElemento);
    }

    public List<HistoricoElementoDTO> buscarHistoricosPorIdElemento(Long idElemento) throws InvalidInputException {
        if(idElemento == null) {
            throw new InvalidInputException("Id do Elemento não pode ser nulo");
        }
        return historicoElementoMapper.toDTOs(historicoElementoDAO.findAllByElementoId(idElemento));
    }

    @TransactionAttribute
    public void registarPorVersao(@NotNull ElementoVersao elementoVersaoAtual, @NotNull ElementoVersao elementoVersaoBase) throws DomainException {
        if(elementoVersaoBase != null) {
            List<HistoricoElemento> historyElementList = comparandoVersoes(elementoVersaoBase, elementoVersaoAtual);

            if (!CollectionUtils.isEmpty(historyElementList)) {
                historicoElementoDAO.registarInBatch(historyElementList);
            }
        }
    }

    private void processComparacaoDeFields(ElementoVersao elementoVersaoBase, String fieldName, String baseValue, String newValue, List<HistoricoElemento> listaDeDiferencas) {
        if (!CamposElementoVersaoEnum.getCampoDescricao(fieldName).isEmpty()) {
            if (CamposElementoVersaoEnum.isRenovacaoPrimeiraVigencia(fieldName) && !baseValue.equals(newValue)) {
                listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), PRIMEIRA_RENOVACAO_DE_MANDATO));
            } else if (CamposElementoVersaoEnum.isRenovacaoSegundaVigencia(fieldName) && !baseValue.equals(newValue)) {
                listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), SEGUNDA_RENOVACAO_DE_MANDATO));
            } else if (CamposElementoVersaoEnum.isProlongamentoDeMandato(fieldName) && !baseValue.equals(newValue)) {
                listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), PROLONGAMENTO_MANDATO));
            } else if(CamposElementoVersaoEnum.isCargoMandato(fieldName) && !baseValue.equals(newValue)) {
                listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), DESIGNACAO_SECRETARIO));
            }else if(CamposElementoVersaoEnum.isTipoElemento(fieldName) && !baseValue.equals(newValue)) {
                String deTipoElemento = paeServiceLocator.getGvrDelegate().detalheDominioByCodigo("TPELEMENTO", baseValue);
                String paraTipoElemento = paeServiceLocator.getGvrDelegate().detalheDominioByCodigo("TPELEMENTO", newValue);
                if(StringUtils.isBlank(baseValue)) {
                    listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), String.format(ALTERACAO_FUNCAO_PRIMEIRA, paraTipoElemento)));
                }else {
                    listaDeDiferencas.add(criarNovoHistoricoElemento(elementoVersaoBase.getElemento(), String.format(ALTERACAO_FUNCAO, deTipoElemento, paraTipoElemento)));
                }
            }
        }
    }

    private List<HistoricoElemento> comparandoVersoes(ElementoVersao elementoVersaoBase, ElementoVersao elementoVersao) {
        List<HistoricoElemento> listaDeDiferencas = new ArrayList<>();
        Field[] toCampos = elementoVersaoBase.getClass().getDeclaredFields();
        for (Field toField : toCampos) {
            toField.setAccessible(true);
            try {
                String versaoBaseValue = getObjectFieldValue(elementoVersaoBase, toField);
                String versaoCandidataAlterada = getObjectFieldValue(elementoVersao, toField);
                if(StringUtils.isNotBlank(versaoCandidataAlterada)) {
                    processComparacaoDeFields(elementoVersaoBase, toField.getName(), versaoBaseValue, versaoCandidataAlterada, listaDeDiferencas);
                }
            } catch (IllegalAccessException e) {
                LOGGER.error("Erro ao pegar o valor da propriedade " + toField, e);
            }
        }
        return listaDeDiferencas;
    }

    private HistoricoElemento criarNovoHistoricoElemento(Elemento elemento, String motivoDescricao) {
        HistoricoElemento historicoElemento = new HistoricoElemento();
        historicoElemento.setDescricao(motivoDescricao);
        historicoElemento.setElemento(elemento);
        historicoElemento.setData(new Date());

        return historicoElemento;
    }

    private void validarEntradas(Elemento elemento, String motivo) throws InvalidInputException {
        if (elemento == null) {
            throw new InvalidInputException(ELEMENTO_NAO_PODE_SER_NULO);
        }
        if (StringUtils.isBlank(motivo)) {
            throw new InvalidInputException(MOTIVO_NAO_PODE_SER_NULO_OU_VAZIO);
        }
    }

    private String getObjectFieldValue(Object object, Field field) throws IllegalAccessException {
        return field.get(object) != null ? field.get(object).toString() : "";
    }
}
