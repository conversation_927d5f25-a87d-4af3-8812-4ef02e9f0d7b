package pt.segsocial.pcj.pae.enums;

import java.util.Arrays;
import java.util.List;

public enum DiaSemanaEnum {

    TODOS("Todos"),
    DOMINGO("Domingo"),
    SEGUNDA("Segunda"),
    TERCA("Terça"),
    QUARTA("Quarta"),
    QUINTA("<PERSON>uin<PERSON>"),
    SEXTA("Sexta"),
    SABADO("Sábado");
    
    private final String descricao;
    
    DiaSemanaEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List<DiaSemanaEnum> filterDiasSemanaToHorarioEscolar() {
        return Arrays.asList(TODOS, SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA);
    }

    public static List<DiaSemanaEnum> diasUteis() {
        return Arrays.asList(SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA);
    }

    public static List<DiaSemanaEnum> filterDiasSemanaToHorarioParticipacao() {
        return Arrays.asList(TODOS, DOMINGO, SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA, SABADO);
    }
}
