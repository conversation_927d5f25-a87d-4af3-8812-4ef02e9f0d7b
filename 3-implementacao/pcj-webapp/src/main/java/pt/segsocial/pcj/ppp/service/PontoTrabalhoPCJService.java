package pt.segsocial.pcj.ppp.service;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.ppp.dto.PontoTrabalhoDTO;
import pt.segsocial.pcj.ppp.jpa.dao.DocumentoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.FicheiroPontoTrabalhoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.PontoTrabalhoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.entity.*;
import pt.segsocial.pcj.ppp.mapper.FicheiroPontoTrabalhoPCJMapper;
import pt.segsocial.pcj.ppp.mapper.PontoTrabalhoPCJMapper;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.faces.bean.RequestScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RequestScoped
public class PontoTrabalhoPCJService {

    private static final Logger LOG = LoggerFactory.getLogger(PontoTrabalhoPCJService.class);

    @Inject
    private DocumentoPCJService documentoPCJService;

    @Inject
    private PontoTrabalhoPCJMapper pontoTrabalhoPCJMapper;

    @Inject
    private FicheiroPontoTrabalhoPCJMapper ficheiroPontoTrabalhoPCJMapper;

    @Inject
    private PontoTrabalhoPCJDAO pontoTrabalhoPCJDAO;

    @Inject
    private FicheiroPontoTrabalhoPCJDAO ficheiroPontoTrabalhoPCJDAO;

    @Inject
    private DocumentoPCJDAO documentoPCJDAO;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarAtualizarPontosTrabalho(List<PontoTrabalhoDTO> pontoTrabalhosDTO, ConvocatoriaAlargadaPCJ reuniaoPCJ) throws PCJException, DomainException {
        LOG.info("Salvando/Atualizando pontos de trabalho para a reunião ID: {}", reuniaoPCJ.getId());
        List<PontoTrabalhoDTO> pontosDeTrabalhoExistentes = obterListaPontosTrabalhoReuniao(reuniaoPCJ.getId());

        excluirPontosTrabalhoRemovidos(pontosDeTrabalhoExistentes, pontoTrabalhosDTO);

        for (PontoTrabalhoDTO pontoTrabalhoDTO : pontoTrabalhosDTO) {
            if (pontoTrabalhoDTO.getId() == null) {
                criarPontoTrabalho(pontoTrabalhoDTO, reuniaoPCJ);
            } else {
                atualizarPontoTrabalho(pontoTrabalhoDTO, reuniaoPCJ);
            }
        }
        LOG.info("Pontos de trabalho salvos/atualizados com sucesso para a reunião ID: {}", reuniaoPCJ.getId());
    }

    public List<PontoTrabalhoDTO> obterListaPontosTrabalhoReuniao(Long idReuniao) {
        LOG.info("Obtendo lista de pontos de trabalho para a reunião ID: {}", idReuniao);
        List<PontoTrabalhoPCJ> pontoTrabalhoPCJS = pontoTrabalhoPCJDAO.listarPorIdReuniao(idReuniao);
        List<PontoTrabalhoDTO> pontoTrabalhoDTOS = new ArrayList<>();
        for (PontoTrabalhoPCJ pontoTrabalhoPCJ : pontoTrabalhoPCJS) {
            PontoTrabalhoDTO pontoDTO = new PontoTrabalhoDTO.Builder()
                    .id(pontoTrabalhoPCJ.getId())
                    .ficheirosPontoTrabalho(ficheiroPontoTrabalhoPCJMapper.toDTOs(pontoTrabalhoPCJ.getFicheirosPontoTrabalho()))
                    .codigoPontoTrabalho(pontoTrabalhoPCJ.getCodigoPontoTrabalho())
                    .observacao(pontoTrabalhoPCJ.getObservacao())
                    .build();
            pontoTrabalhoDTOS.add(pontoDTO);
        }
        LOG.debug("Pontos de trabalho obtidos: {}", pontoTrabalhoDTOS);
        return pontoTrabalhoDTOS;
    }

    private void atualizarPontoTrabalho(PontoTrabalhoDTO pontoTrabalhoDTO, ConvocatoriaAlargadaPCJ reuniaoPCJ) throws DomainException, PCJException {
        LOG.debug("Atualizando ponto de trabalho com ID: {}", pontoTrabalhoDTO.getId());
        PontoTrabalhoPCJ pontoTrabalhoPCJ = pontoTrabalhoPCJMapper.toEntidade(pontoTrabalhoDTO);
        pontoTrabalhoPCJ.setConvocatoriaAlargada(reuniaoPCJ);
        pontoTrabalhoPCJDAO.update(pontoTrabalhoPCJ);
        excluirTodosFicheirosPontoTrabalho(pontoTrabalhoPCJ);
        registrarFicheirosPontoTrabalho(pontoTrabalhoDTO, pontoTrabalhoPCJ);
        LOG.debug("Ponto de trabalho atualizado com sucesso: ID {}", pontoTrabalhoPCJ.getId());
    }

    private void excluirTodosFicheirosPontoTrabalho(PontoTrabalhoPCJ pontoTrabalhoPCJ) throws DomainException {
        LOG.debug("Excluindo todos os ficheiros associados ao ponto de trabalho ID: {}", pontoTrabalhoPCJ.getId());
        List<FicheiroPontoTrabalho> ficheiroPontoTrabalhos = ficheiroPontoTrabalhoPCJDAO.obterFicheiroPor(pontoTrabalhoPCJ);
        for (FicheiroPontoTrabalho ficheiroExistente : ficheiroPontoTrabalhos) {
            if (ficheiroExistente != null && ficheiroExistente.getId() != null) {
                DocumentoPCJ documento = ficheiroExistente.getDocumentoPCJ();
                if (documento != null) {
                    ficheiroExistente.setDocumentoPCJ(null);
                    ficheiroPontoTrabalhoPCJDAO.remove(ficheiroExistente);
                    documentoPCJService.removeDocumento(documento);
                    LOG.debug("Ficheiro e documento associados removidos: Ficheiro ID {}, Documento ID {}", ficheiroExistente.getId(), documento.getId());
                }
            }
        }

        if(CollectionUtils.isNotEmpty(pontoTrabalhoPCJ.getFicheirosPontoTrabalho())) {
            pontoTrabalhoPCJ.getFicheirosPontoTrabalho().clear();
        }
    }

    private void criarPontoTrabalho(PontoTrabalhoDTO pontoTrabalhoDTO, ConvocatoriaAlargadaPCJ reuniaoPCJ) throws DomainException, PCJException {
        LOG.debug("Criando novo ponto de trabalho para a reunião ID: {}", reuniaoPCJ.getId());
        PontoTrabalhoPCJ pontoTrabalhoPCJ = pontoTrabalhoPCJMapper.toEntidade(pontoTrabalhoDTO);
        pontoTrabalhoPCJ.setConvocatoriaAlargada(reuniaoPCJ);
        pontoTrabalhoPCJDAO.create(pontoTrabalhoPCJ);

        registrarFicheirosPontoTrabalho(pontoTrabalhoDTO, pontoTrabalhoPCJ);
        LOG.debug("Novo ponto de trabalho criado com ID: {}", pontoTrabalhoPCJ.getId());
    }

    private void registrarFicheirosPontoTrabalho(PontoTrabalhoDTO pontoTrabalhoDTO, PontoTrabalhoPCJ pontoTrabalhoPCJ) throws DomainException, PCJException {
        LOG.debug("Registrando ficheiros para o ponto de trabalho ID: {}", pontoTrabalhoPCJ.getId());
        List<DocumentoPCJ> documentoPCJS = documentoPCJService.salvaFicheiros(pontoTrabalhoDTO.getArquivos());
        for (DocumentoPCJ documentoPCJ : documentoPCJS) {
            FicheiroPontoTrabalho ficheiroPontoTrabalho = new FicheiroPontoTrabalho();
            FicheiroPontoTrabalhoId ficheiroPontoTrabalhoId = new FicheiroPontoTrabalhoId(documentoPCJ.getId(), pontoTrabalhoPCJ.getId());
            ficheiroPontoTrabalho.setId(ficheiroPontoTrabalhoId);
            ficheiroPontoTrabalho.setPontoTrabalhoPCJ(pontoTrabalhoPCJDAO.find(pontoTrabalhoPCJ.getId()));
            ficheiroPontoTrabalho.setDocumentoPCJ(documentoPCJ);
            ficheiroPontoTrabalhoPCJDAO.create(ficheiroPontoTrabalho);
            LOG.debug("Ficheiro registrado: ID {}, Documento ID {}", ficheiroPontoTrabalho.getId(), documentoPCJ.getId());
        }
    }

    private void excluirPontosTrabalhoRemovidos(List<PontoTrabalhoDTO> pontosExistentes, List<PontoTrabalhoDTO> pontosDTO) throws DomainException {
        LOG.debug("Excluindo pontos de trabalho removidos.");
        List<Long> idsPontosTrabalhoDTO = new ArrayList<>();
        for (PontoTrabalhoDTO pontoTrabalhoDTO : pontosDTO) {
            if (pontoTrabalhoDTO.getId() != null) {
                idsPontosTrabalhoDTO.add(pontoTrabalhoDTO.getId());
            }
        }

        for (PontoTrabalhoDTO pontoExistente : pontosExistentes) {
            if (!idsPontosTrabalhoDTO.contains(pontoExistente.getId())) {
                LOG.info("Excluindo ponto de trabalho removido: ID {}", pontoExistente.getId());
                removerPontoTrabalhoComDocumentos(pontoExistente);
            }
        }
    }

    private void removerPontoTrabalhoComDocumentos(PontoTrabalhoDTO pontoTrabalhoDTO) throws DomainException {
        LOG.debug("Excluindo ponto de trabalho com ID: {}", pontoTrabalhoDTO.getId());
        PontoTrabalhoPCJ pontoTrabalhoPCJ = pontoTrabalhoPCJDAO.find(pontoTrabalhoDTO.getId());
        if (pontoTrabalhoPCJ != null) {
            Set<FicheiroPontoTrabalho> ficheiros = pontoTrabalhoPCJ.getFicheirosPontoTrabalho();
            if(CollectionUtils.isNotEmpty(ficheiros)) {
                removerFicheirosDocumentos(ficheiros);
            }
            pontoTrabalhoPCJDAO.remove(pontoTrabalhoPCJ);
            LOG.info("Ponto de trabalho excluído: ID {}", pontoTrabalhoPCJ.getId());
        } else {
            LOG.warn("Ponto de trabalho com ID {} não encontrado para exclusão.", pontoTrabalhoDTO.getId());
        }
    }

    private void removerFicheirosDocumentos(Set<FicheiroPontoTrabalho> ficheiros) throws DomainException {
        for (FicheiroPontoTrabalho ficheiro : ficheiros) {
            ficheiroPontoTrabalhoPCJDAO.remove(ficheiro);
            LOG.debug("Ficheiro removido: ID {}", ficheiro.getId());
            DocumentoPCJ documento = ficheiro.getDocumentoPCJ();
            if (documento != null) {
                documentoPCJDAO.remove(documento);
                LOG.debug("Documento removido: ID {}", documento.getId());
            }
        }
    }
}