package pt.segsocial.pcj.ppp.jpa.dao.ata.restrita;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.jpa.entity.BlocoComunicacaoRestrita;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.BlocoGeralRestrita;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.Comunicacao;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import java.io.Serializable;

public class BlocoGeralRestritaDAO extends DAO<BlocoGeralRestrita, Long> implements Serializable {

    @Inject
    public BlocoGeralRestritaDAO(EntityManager entityManager) {
        super(entityManager);
    }


//    public BlocoGeralRestrita findByComunicacaoId(Long comunicacaoId) {
//        try {
//            return getEntityManager().createQuery(
//                            "SELECT b FROM BlocoGeralRestrita b WHERE b.comunicacao.id = :comunicacaoId",
//                            BlocoGeralRestrita.class)
//                    .setParameter("comunicacaoId", comunicacaoId)
//                    .getSingleResult();
//        } catch(Exception e) {
//            LoggingHelper.logError(e);
//            return null;
//        }
//    }

//    public ReuniaoPCJ recuperaReuniaoBlocoComunicacao(Comunicacao comunicacao) {
//        try {
//            return getEntityManager().createQuery(
//                            "SELECT b.reuniao FROM BlocoComunicacaoRestrita b WHERE b.comunicacao = :comunicacao",
//                            ReuniaoPCJ.class)
//                    .setParameter("comunicacao", comunicacao)
//                    .getSingleResult();
//        } catch (Exception e) {
//            LoggingHelper.logError(e);
//            return null;
//        }
//    }
}
