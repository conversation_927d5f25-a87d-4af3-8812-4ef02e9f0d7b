package pt.segsocial.pcj.ppp.dto;

import pt.segsocial.gvr.api.util.StringUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ResultadoReuniaoSelecionadoMapaDTO implements Serializable {

    private BigDecimal id;
    private Date dataInicio;
    private Date dataFim;
    private String sala;
    private String assunto;
    private String modalidade;
    private String tipo;
    private String local;

    private String localReuniao;


    public ResultadoReuniaoSelecionadoMapaDTO() {
    }

    public ResultadoReuniaoSelecionadoMapaDTO(BigDecimal idReuniao, Date dataInicio, Date dataFim, String codigoModalidade, String codigoTipo, String localReuniao, String nomeSala, String assunto) {
        this.id = idReuniao;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.modalidade = codigoModalidade;
        this.tipo = codigoTipo;
        this.localReuniao = localReuniao;
        this.sala = nomeSala;
        this.assunto = assunto;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getSala() {
        return sala;
    }

    public void setSala(String sala) {
        this.sala = sala;
    }

    public String getAssunto() {
        return StringUtil.isNotNullOrEmpty(assunto) ? assunto : "Evento Reunião";
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public void setLocalReuniao(String localReuniao) {
        this.localReuniao = localReuniao;
    }

    public String getLocalReuniao() {
        if (StringUtil.isNotNullOrEmpty(this.localReuniao)) {
            return this.localReuniao;
        } else if (StringUtil.isNotNullOrEmpty(this.sala)) {
            return this.sala;
        } else {
            return "-";
        }
    }

    @Override
    public String toString() {
        return "ResultadoReuniaoSelecionadoMapaDTO{" +
                "id=" + id +
                ", dataInicio=" + dataInicio +
                ", dataFim=" + dataFim +
                ", sala='" + sala + '\'' +
                ", assunto='" + assunto + '\'' +
                ", modalidade='" + modalidade + '\'' +
                ", tipo='" + tipo + '\'' +
                ", local='" + local + '\'' +
                ", localReuniao='" + localReuniao + '\'' +
                '}';
    }
}
