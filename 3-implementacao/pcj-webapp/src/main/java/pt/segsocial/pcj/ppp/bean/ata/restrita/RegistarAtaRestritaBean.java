package pt.segsocial.pcj.ppp.bean.ata.restrita;


import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.commons.collections.CollectionUtils;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.primefaces.context.RequestContext;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.configuration.Configuration;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.api.format.Format;
import pt.segsocial.fraw.api.service.reporting.CraftedReport;
import pt.segsocial.fraw.api.service.reporting.TableException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.Constants;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.bean.reuniao.ConsultarReuniaoCPCJBean;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.*;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaParaRegistoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoDTO;
import pt.segsocial.pcj.ppp.enums.BlocosTrabalhoAtaRestritaEnum;
import pt.segsocial.pcj.ppp.enums.InfoMessageAtaRestritaEnum;
import pt.segsocial.pcj.ppp.jpa.dao.BlocoComunicacaoRestritaDAO;
import pt.segsocial.pcj.ppp.service.DocumentoPCJService;
import pt.segsocial.pcj.ppp.service.ata.AtaRestritaFolhaPresencaService;
import pt.segsocial.pcj.ppp.service.ata.AtaRestritaService;

import javax.annotation.PostConstruct;
import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.inject.Named;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Named(value = "pcjRegistarAtaRestritaBean")
@Stateful(name = "pcjRegistarAtaRestritaBean")
@LocalBean
@ViewAccessScoped
public class RegistarAtaRestritaBean extends BaseAtaRestritaBean {

    private String mensagemDataSuperiorAtual = "A data não pode ser maior que o dia atual. ";

    private boolean exibeBotaoAddTrabalho;
    private boolean tipoTrabalho;
    private boolean novoConvidado = false;
    private boolean disableTabs = true;
    private boolean radioHorizontal = false;
    private boolean existeTrabalhoPendente = false;
    private boolean viewFileUpload = false;
    private int currentTab = 0;
    private boolean edicao = false;
    private boolean displayList = false;
    private boolean displayListConclusao = false;
    private boolean edicaoBlocoTrabalho;
    private boolean dataRealizacaoInvalida = false;
    private boolean edicaoAssinatura = false;
    private Long idBlockRemove;

    private ConvidadoPCJDTO convidadoNovo;
    private OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO;

    private List<BlocosTrabalhoAtaRestritaEnum> tiposTrabalhos = new ArrayList<>();

    private List<ElementoPresencaRestritaDTO> elementosPresenca = new ArrayList<>();
    private List<ConvidadoPCJDTO> listaConvidadosDTO = new ArrayList<>();
    private FolhaPresencaDTO folhaPresencaDTO = new FolhaPresencaDTO();
    private ConclusaoDTO conclusaoDTO = new ConclusaoDTO();
    private ConvidadoPCJDTO convidadoDTO = new ConvidadoPCJDTO();
    private List<TrabalhoDTO> trabalhosDTO = new ArrayList<>();
    private List<ConclusaoDTO> listaConclusaoDTO = new ArrayList<>();
    private List<ConvidadoPresencaDTO> listaConvidadosAddEdicao = new ArrayList<>();
    private List<ConclusaoDTO> listaAssinaturaAdd = new ArrayList<>();
    private List<BlocoComunicacaoRestritaParaRegistoDTO> listaComunicacoes = new ArrayList<>();
    private BlocoComunicacaoRestritaParaRegistoDTO blocoComunicacaoRestrita;
//    private ComunicacaoDTO comunicacaoDTO;
    private BlocoGeralRestritaDTO blocoGeralRestritaDTO = new BlocoGeralRestritaDTO();
    @Inject
    private AtaRestritaFolhaPresencaService ataRestritaFolhaPresencaService;

    @Inject
    private AtaRestritaService ataRestritaService;

    @Inject
    private DocumentoPCJService documentoPCJService;

    @Inject
    private ConsultarReuniaoCPCJBean consultarReuniaoCPCJBean;

    @Inject
    private BlocoComunicacaoRestritaDAO blocoComunicacaoRestritaDAO;

    @PostConstruct
    public void initRegistarAta() {
    }

    @URLAction(mappingId = PCJSubsystem.OP_REGISTAR_ATA_RESTRITA, onPostback = false)
    public void init() {
        LoggingHelper.logEntrada();
        super.init();
        setReuniao(reuniaoCPCJService.buscaReuniaoPorId(getIdReuniao()));
        setAtaRestritaDTO(ataRestritaService.carregarAtaRestrita(getIdReuniao()));
        verificaRedirectAtualizarPagina();
        setDisableTabs(getAtaRestritaDTO().getId() == null);
        setDisplayList(getAtaRestritaDTO().getId() != null);
        setEdicao(false);
        setExibeBotaoAddTrabalho(true);
        setTipoTrabalho(true);
        carregarListas();
    }

    public OrdenacaoAtaRestritaDTO obterTrabalhoGuardadoDTO(Long idBlock) {
        if (idBlock != null) {
            for (TrabalhosGuardadosDTO trabalhosDTO : getAtaRestritaDTO().getTrabalhosGuardadosDTO()) {
                if (trabalhosDTO.getOrdenacaoAtaRestrita().getId().equals(idBlock)) {
                    return trabalhosDTO.getOrdenacaoAtaRestrita();
                }
            }
        }
        return new OrdenacaoAtaRestritaDTO();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void adicionarConvidado() {
        if (convidadoDTO == null || convidadoDTO.getNome() == null) {
            return;
        }

        try {
            if (existeConvidado()) {
                JsfMessageUtil.mostraMensagemErro("Este convidado já foi adicionado anteriormente.");
                return;
            }

            convidadoDTO.setComissao(ataRestritaService.getComissao(getReuniao()));
            ataRestritaService.gravarConvidado(convidadoDTO);

            ConvidadoPresencaDTO convidadoConvertido = converterConvidadoDTO(convidadoDTO);
            getAtaRestritaDTO().getListaConvidadosAdd().add(convidadoConvertido);

            listaConvidadosDTO.remove(convidadoDTO);

            setNovoConvidado(false);
            convidadoNovo = new ConvidadoPCJDTO();
            convidadoDTO = new ConvidadoPCJDTO();

        } catch (DomainException e) {
            LOGGER.error("Erro ao guardar convidado: " + e.getMessage(), e);
            LoggingHelper.logError(e);
        }
    }

    public void setarConvidado() {
        if (convidadoNovo != null && convidadoNovo.getNome().equals("Novo")) {
            convidadoDTO = new ConvidadoPCJDTO();
            setNovoConvidado(true);
        } else if (convidadoNovo != null && convidadoNovo.getId() != null) {
            setConvidadoDTO(convidadoNovo);
            setNovoConvidado(true);
        } else {
            setNovoConvidado(false);
        }
    }

    public void removerConvidado(ConvidadoPresencaDTO convidadoDTO) {
        getAtaRestritaDTO().getListaConvidadosAdd().remove(convidadoDTO);
        this.convidadoDTO = new ConvidadoPCJDTO.Builder()
                .id(convidadoDTO.getId())
                .nome(convidadoDTO.getNome())
                .cargo(convidadoDTO.getCargo())
                .entidade(convidadoDTO.getEntidade())
                .codigoEntidadeParticipante(convidadoDTO.getCodigoEntidadeParticipante())
                .detalheEntidadeParticipante(convidadoDTO.getDetalheEntidadeParticipante())
                .comissao(getAtaRestritaDTO().getComissao())
                .build();
        listaConvidadosDTO.add(this.convidadoDTO);
        setNovoConvidado(false);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void guardarFolhaPresenca() {
        try {
            if (!ataRestritaService.validarFolhaPresenca(getElementosPresenca(), getAtaRestritaDTO())) {
                return;
            }
            if (!isDisplayList()) {
                ataRestritaService.criarFolhaPresenca(getAtaRestritaDTO(), getIdReuniao(), getIdComissao());
                JsfMessageUtil.mostraMensagemSucesso(edicao ? Constants.REGISTO_LISTA_PRESENCA_ATA :
                        String.format(Constants.EDICAO_LISTA_PRESENCA_ATA, getAtaRestritaDTO().getNumeroAtaRestrita()));
                controleInicialEcra();
            }
            getListaAssinaturaAdd().clear();
            getListaAssinaturaAdd().addAll(getAtaRestritaDTO().getListaAssinaturaSelected());
            LoggingHelper.logSaida(LOGGER, getAtaRestritaDTO());
        } catch (DomainException e) {
            LOGGER.error("Erro ao guardar folha presença" + e);
            LoggingHelper.logError(e);
        }
    }

    private void controleInicialEcra() {
        setDisableTabs(false);
        setDisplayList(true);
        setEdicao(false);
        listaConvidadosDTO.clear();
        listaConvidadosDTO.addAll(ataRestritaFolhaPresencaService.buscaListaConvidados(getIdComissao()));
        getListaConvidadosAddEdicao().addAll(getAtaRestritaDTO().getListaConvidadosAdd());
    }

    public void alterarListaPresencaBtn() {
        JsfMessageUtil.mostraMensagemAviso(Constants.AVISO_ALTERAR_LISTA_PRESENCA_ATA);
        getListaConvidadosAddEdicao().clear();
        getListaConvidadosAddEdicao().addAll(getAtaRestritaDTO().getListaConvidadosAdd());
        setDisableTabs(true);
        setEdicao(true);
        setDisplayList(false);
    }

    public List<DetalheDominioVO> entidadesPP() {
        return new ArrayList<>(getPcjServiceDomain().getDominioHolderEntidadePP().get().getDetalhes().values());
    }

    public void cancelarListaPresencaBtn() {
        setDisableTabs(false);
        setEdicao(false);
        setDisplayList(true);
        getAtaRestritaDTO().getListaConvidadosAdd().clear();
        getAtaRestritaDTO().getListaConvidadosAdd().addAll(getListaConvidadosAddEdicao());
        listaConvidadosDTO.clear();
        listaConvidadosDTO.addAll(ataRestritaFolhaPresencaService.buscaListaConvidados(getIdComissao()));
    }

    public void imprimirListaPresentes() {
        try {
            guardarFolhaPresenca();
            CraftedReport report = imprimirListaReport.gerarReport(getAtaRestritaDTO());
            imprimirListaReport.download(report.getDownloadURL());
        } catch (TableException | PCJException e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void concluirBtn() {
        try {
            if (validarAtaAlargada()) {
                ataRestritaService.guardarAtaFinal(getAtaRestritaDTO());
                pcjSubsystem.redirectTo(pcjSubsystem.consultarAta(), null);//todo: direcionar para consulta da ata restrita
                guardarFicheiroAtaAlargada();
                JsfMessageUtil.mostraMensagemSucesso(Constants.REGISTO_SUCESSO_ATA);
                LoggingHelper.logSaida(getAtaRestritaDTO());
            }
        } catch (Exception e) {
            LOGGER.error("Erro ao guardar ata" + e);
            LoggingHelper.logError(e);
        }
    }

    public void guardarFicheiroAtaAlargada() {
        try {
            CraftedReport report = imprimirAtaRestritaReport.gerarReport(getAtaRestritaDTO());
            guardarArquivoAta(report.getIdsReemissao().get(0));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void saveDraft() {
        try {
            ataRestritaService.saveDraft(getAtaRestritaDTO());
            if (CollectionUtils.isNotEmpty(getAtaRestritaDTO().getListaAssinaturaSelected())) {
                ataRestritaService.persistirConclusao(getAtaRestritaDTO().getListaAssinaturaSelected(), getAtaRestritaDTO().getId());
            }
            LoggingHelper.logSaida(getAtaRestritaDTO());
        } catch (DomainException e) {
            JsfMessageUtil.mostraMensagemErro("Erro ao guardar rascunho.");
            LoggingHelper.logError(e);
        }
    }

    private void guardarArquivoAta(Long id) throws DomainException, PCJException, ParseException {
        DocumentoDTO doc = new DocumentoDTO();
        doc.setIdentificadorFicheiro(id);
        doc.setUploadDocumento(false);
        doc.setDataUpload(new Date());
        doc.setNomeDocumento("Ata_" + getAtaRestritaDTO().getNumeroAtaRestrita());
        getAtaRestritaDTO().setAtaDocumento(documentoPCJService.salvarDocumento(doc));
        ataRestritaService.salvarDocumentoAta(getAtaRestritaDTO());
    }

    public boolean mostarMensagemPendencia() {
        return currentTab == InfoMessageAtaRestritaEnum.ORDEM_TRABALHO.getTab() && existeTrabalhoPendente;
    }

    public void setarTrabalhoAlterar(OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO) {
        obterTrabalhoGuardadoDTO(ordenacaoAtaRestritaDTO.getId());
        getAtaRestritaDTO().setOrdenacaoAtaRestritaDTO(ordenacaoAtaRestritaDTO);
        setExibeBotaoAddTrabalho(false);
        setBlocoTrabalho(ordenacaoAtaRestritaDTO.getNomeBloco().getNome());
        instanciarClasseBlocoTrabalho(ordenacaoAtaRestritaDTO);
        setViewFileUpload(blocoBaseBean.verificaExisteDoc());
        carregarFicheiroStorage();
        setEdicaoBlocoTrabalho(true);
    }

    public void adicionarTrabalhoBtn() {
        if (getNomeBloco() == null) {
            return;
        }

        inicializarTrabalho();

        BlocosTrabalhoAtaRestritaEnum bcAtual = determinarBlocoAtual();
        if (bcAtual == null) {
            return;
        }
        if (bcAtual.equals(BlocosTrabalhoAtaRestritaEnum.ASSUNTOS_GERAIS)) {
            instanciarBlocoSelecionado();
        } else if (bcAtual.equals(BlocosTrabalhoAtaRestritaEnum.COMUNICACAO)) {
            BlocoRestritaComunicacaoDTO bloco = BlocoRestritaComunicacaoDTO.builder()
                    .idComunicacao(getBlocoComunicacaoRestrita().getIdComunicacao())
                    .numeroComunicacao(getBlocoComunicacaoRestrita().getNumeroComunicacao())
                    .build();
            instanciarBlocoSelecionado(bloco);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void guardarBlocoTrabalho() {
        try {
            if (!edicaoBlocoTrabalho) {
                BlocosTrabalhoAtaRestritaEnum blocosTrabalhoAtaEnum = getNomeBloco();
                getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().setNomeBloco(blocosTrabalhoAtaEnum);
            }
            ataRestritaService.saveOrUpdateWorkBlock(getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO(), getAtaRestritaDTO().getTrabalhosGuardadosDTO());
            getAtaRestritaDTO().setOrdenacaoAtaRestritaDTO(obterTrabalhoGuardadoDTO(getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().getId()));
            limparBlocoTrabalho();
        } catch (DomainException e) {
            LOGGER.error("Erro ao guardar bloco de trabalho" + e);
            JsfMessageUtil.mostraMensagemErro("Erro ao guardar bloco de trabalho.");
            LoggingHelper.logError(e);
        } finally {
            getFilesSingle().clear();
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void removerBlocoTrabalho() {
        try {
            if (idBlockRemove == null) {
                throw new DomainException("Erro ao excluir bloco de trabalho");
            }
            OrdenacaoAtaRestritaDTO ordenacaoRemove = obterTrabalhoGuardadoDTO(idBlockRemove);
            List<TrabalhosGuardadosDTO> trabalhosGuardadosDTO = getAtaRestritaDTO().getTrabalhosGuardadosDTO();
            getAtaRestritaDTO().setOrdenacaoAtaRestritaDTO(ordenacaoRemove);
            ataRestritaService.removeWorkOrder(ordenacaoRemove, trabalhosGuardadosDTO);
            instanciarClasseBlocoTrabalho(getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO());
            blocoBaseBean.removerDocumentDTO();
            getFilesSingle().clear();
            setViewFileUpload(false);
            getAtaRestritaDTO().setOrdenacaoAtaRestritaDTO(new OrdenacaoAtaRestritaDTO());
            limparBlocoTrabalho();
            JsfMessageUtil.mostraMensagemSucesso("Ordem de trabalho removido com sucesso.");
        } catch (DomainException e) {
            LOGGER.error("Erro ao excluir bloco de trabalho" + e);
            LoggingHelper.logError(e);
        } catch (Exception e) {
            JsfMessageUtil.mostraMensagemErro("Erro ao excluir bloco de trabalho.");
            LoggingHelper.logError(e);
        }
        LoggingHelper.logSaida();
    }

    public void cancelarBlocoTrabalho() {
        limparBlocoTrabalho();
    }

    public String cancelarAta() {
        if (getReuniao() != null) {
            consultarReuniaoCPCJBean.setIdReuniao(getReuniao().getId());
            return pcjSubsystem.consultarReuniao();
        }
        return pcjSubsystem.pesquisarAgenda();
    }

    private void carregarListas() {
        setStandartWork();
        carregarComunicacoes();
        carregarElementos();
        carregarConvidados();
        getListaAssinaturaAdd().clear();
        getListaAssinaturaAdd().addAll(getAtaRestritaDTO().getListaAssinaturaSelected());
    }

    private void carregarComunicacoes() {
        if (CollectionUtils.isEmpty(listaComunicacoes)) {
            listaComunicacoes.addAll(ataRestritaService.carregarComunicacoes(getReuniao()));
        }
    }

    private void carregarConvidados() {
        if (CollectionUtils.isEmpty(listaConvidadosDTO)) {
            listaConvidadosDTO.addAll(ataRestritaFolhaPresencaService.buscaListaConvidados(getIdComissao()));
        }
    }

    private void carregarElementos() {
        if (CollectionUtils.isEmpty(elementosPresenca)) {
            elementosPresenca.addAll(ataRestritaFolhaPresencaService.buscaElementos(getIdComissao()));
        }
    }

    private ConvidadoPresencaDTO converterConvidadoDTO(ConvidadoPCJDTO convidadoPCJDTO) {
        return new ConvidadoPresencaDTO(
                convidadoPCJDTO.getId(), convidadoPCJDTO.getNome(),
                convidadoPCJDTO.getCargo(), convidadoPCJDTO.getCodigoEntidadeParticipante(),
                convidadoPCJDTO.getDetalheEntidadeParticipante()
        );
    }

    private boolean existeConvidado() {
        for (ConvidadoPresencaDTO convidadoPCJDTO : getAtaRestritaDTO().getListaConvidadosAdd()) {
            if (convidadoPCJDTO.getNome().equals(convidadoDTO.getNome()) &&
                    ((convidadoPCJDTO.getCargo() == null && convidadoDTO.getCargo() == null) || (convidadoPCJDTO.getCargo().equals(convidadoDTO.getCargo()))) &&
                    convidadoPCJDTO.getEntidade().equals(convidadoDTO.getEntidade())) {
                return true;
            }
        }
        return false;
    }

    public void alterarAssinaturaBtn() {
        setEdicaoAssinatura(true);
        setDisplayListConclusao(true);
        setCurrentTab(0);
    }

    public void cancelarAlteracaoAssinaturaBtn() {
        setEdicaoAssinatura(false);
        setDisplayListConclusao(false);
        getAtaRestritaDTO().getListaAssinaturaSelected().clear();
        getAtaRestritaDTO().getListaAssinaturaSelected().addAll(getListaAssinaturaAdd());
        setCurrentTab(InfoMessageAtaRestritaEnum.CONCLUSAO.getTab());
    }

    public void guardarAssinaturaConclusaoBtn() throws DomainException {
        if (!ataRestritaService.validarPresidenteSecretarioConclusao(getAtaRestritaDTO())) {
            setCurrentTab(0);
            getAtaRestritaDTO().getListaAssinaturaSelected().clear();
            getAtaRestritaDTO().getListaAssinaturaSelected().addAll(getListaAssinaturaAdd());
            JsfMessageUtil.mostraMensagemErro(Constants.AVISO_PRESIDENTE_SECRETARIO_CONCLUSAO);
            return;
        }
        saveDraft();
        setEdicaoAssinatura(false);
        setDisplayListConclusao(false);
        getListaAssinaturaAdd().clear();
        getListaAssinaturaAdd().addAll(getAtaRestritaDTO().getListaAssinaturaSelected());
        setCurrentTab(InfoMessageAtaRestritaEnum.CONCLUSAO.getTab());
        JsfMessageUtil.mostraMensagemSucesso("Assinatura da ata registada com sucesso.");
    }

    private boolean validarAtaAlargada() {
        boolean retorno = true;
        if (getAtaRestritaDTO().getDataRealizacao() == null) {
            setCurrentTab(InfoMessageAtaRestritaEnum.INTRODUCAO.getTab());
            JsfMessageUtil.mostraMensagemErro("Campo data não foi preenchido.");
            retorno = false;
        } else if (ataRestritaService.verificaExisteTrabalho(getAtaRestritaDTO())) {
            setCurrentTab(InfoMessageAtaRestritaEnum.ORDEM_TRABALHO.getTab());
            JsfMessageUtil.mostraMensagemErro("Não foi registado nenhum ponto de trabalho.");
            retorno = false;
        } else if (!ataRestritaService.validarPresidenteSecretarioConclusao(getAtaRestritaDTO())) {
            setCurrentTab(InfoMessageAtaRestritaEnum.CONCLUSAO.getTab());
            JsfMessageUtil.mostraMensagemErro(Constants.AVISO_PRESIDENTE_SECRETARIO_CONCLUSAO);
            retorno = false;
        }
        RequestContext.getCurrentInstance().update("pcjRegistarAtaForm:pcjAtaTabView");
        return retorno;
    }

    public boolean validarDataRealizacao() {
        return getAtaRestritaDTO().getDataRealizacao() != null && getAtaRestritaDTO().getDataRealizacao().after(new Date());
    }

    public void validaDataInicioSuperiorAtual() {
        LoggingHelper.logEntrada(LOGGER, getAtaRestritaDTO().getDataRealizacao());
        if (verificaDataSuperiorAtual(getAtaRestritaDTO().getDataRealizacao())) {
            getAtaRestritaDTO().setDataRealizacao(new Date());
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public boolean verificaDataSuperiorAtual(Date data) {
        if (data.after(new Date())) {
            JsfMessageUtil.mostraMensagemErro("A data não pode ser superior a data atual.");
            return true;
        }
        return false;
    }

    private void inicializarTrabalho() {
        getAtaRestritaDTO().setOrdenacaoAtaRestritaDTO(new OrdenacaoAtaRestritaDTO());
        setExibeBotaoAddTrabalho(false);
        getFilesSingle().clear();
        setViewFileUpload(false);
    }

    private BlocosTrabalhoAtaRestritaEnum determinarBlocoAtual() {
        if (getNomeBloco() != null) {
            setBlocoTrabalho(getNomeBloco().getNome());
            return getNomeBloco();
        }
        JsfMessageUtil.mostraMensagemErro("Nenhum ponto de trabalho foi selecionado. Por favor, escolha uma opção.");
        return null;
    }

    private void mostrarMensagemErro(BlocosTrabalhoAtaRestritaEnum bloco) {
        String mensagem = String.format("O ponto de trabalho %s só pode ser adicionado uma vez.", bloco.getNome());
        JsfMessageUtil.mostraMensagemErro(mensagem);
    }

    private void instanciarClasseBlocoTrabalho(OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO) {
        setNomeBloco(ordenacaoAtaRestritaDTO.getNomeBloco());
        instanciarBlocoSelecionado();
    }

    public void limparBlocoTrabalho() {
        setExibeBotaoAddTrabalho(true);
        setTipoTrabalho(true);
        setNomeBloco(null);
        setStandartWork();
        setEdicaoBlocoTrabalho(false);
    }

    public boolean exibirBtnInicirAta() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && getAtaRestritaDTO().getId() == null;
    }

    public boolean exibirBtnImprimirPresenca() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && isDisplayList() &&
                getAtaRestritaDTO().getId() != null && !isEdicaoAssinatura();
    }

    public boolean exibirBtnCacelarAlteracaoListaPresenca() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && getAtaRestritaDTO().getId() != null && isEdicao();
    }

    public boolean exibirBtnGuardaMinuta() {
        return currentTab == InfoMessageAtaRestritaEnum.INTRODUCAO.getTab() ||
                currentTab == InfoMessageAtaRestritaEnum.ORDEM_TRABALHO.getTab() ||
                (currentTab == InfoMessageAtaRestritaEnum.CONCLUSAO.getTab() && !isEdicaoAssinatura());
    }

    public boolean exibirNovaListaPresenca() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && !isDisplayList() && !isEdicao();
    }

    public boolean exibirEdicaoListaPresenca() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && !isDisplayList() && isEdicao();
    }

    public boolean exibirListaPresenca() {
        return currentTab == InfoMessageAtaRestritaEnum.PRESENCA.getTab() && isDisplayList();
    }

    public boolean exibirBotoesGuardarAssinatura() {
        return isEdicaoAssinatura();
    }

    public boolean exibirBtnConcluir() {
        return currentTab == InfoMessageAtaRestritaEnum.PRE_VISUALIZACAO.getTab();
    }

    public String getAlertMessage() {
        return Constants.ALERTA_TRABALHOS_PENDENTES;
    }

    public ConclusaoDTO getConclusaoDTO() {
        return conclusaoDTO;
    }

    public void setConclusaoDTO(ConclusaoDTO conclusaoDTO) {
        this.conclusaoDTO = conclusaoDTO;
    }

    public ConvidadoPCJDTO getConvidadoDTO() {
        return convidadoDTO;
    }

    public void setConvidadoDTO(ConvidadoPCJDTO convidadoDTO) {
        this.convidadoDTO = convidadoDTO;
    }

    public ConvidadoPCJDTO getConvidadoNovo() {
        return convidadoNovo;
    }

    public void setConvidadoNovo(ConvidadoPCJDTO convidadoNovo) {
        this.convidadoNovo = convidadoNovo;
    }

    public int getCurrentTab() {
        return currentTab;
    }

    public void setCurrentTab(int currentTab) {
        this.currentTab = currentTab;
    }

    public String getDateFormat() {
        return Format.Date.YEAR_MONTH_DAY_HYPHEN_SEPARATOR.value();
    }

    public List<ElementoPresencaRestritaDTO> getElementosPresenca() {
        return elementosPresenca;
    }

    public void setElementosPresenca(List<ElementoPresencaRestritaDTO> elementosPresenca) {
        this.elementosPresenca = elementosPresenca;
    }

    public FolhaPresencaDTO getFolhaPresencaDTO() {
        return folhaPresencaDTO;
    }

    public void setFolhaPresencaDTO(FolhaPresencaDTO folhaPresencaDTO) {
        this.folhaPresencaDTO = folhaPresencaDTO;
    }

    public String getInfoMessage() {
        return InfoMessageAtaRestritaEnum.getDescricaoTab(currentTab);
    }

    public String getInitialText() {
        return Constants.TEXTO_INICIO_ATA;
    }

    public List<ConclusaoDTO> getListaAssinaturaAdd() {
        return listaAssinaturaAdd;
    }

    public void setListaAssinaturaAdd(List<ConclusaoDTO> listaAssinaturaAdd) {
        this.listaAssinaturaAdd = listaAssinaturaAdd;
    }

    public List<ConclusaoDTO> getListaConclusaoDTO() {
        return listaConclusaoDTO;
    }

    public void setListaConclusaoDTO(List<ConclusaoDTO> listaConclusaoDTO) {
        this.listaConclusaoDTO = listaConclusaoDTO;
    }

    public List<ConvidadoPresencaDTO> getListaConvidadosAddEdicao() {
        return listaConvidadosAddEdicao;
    }

    public void setListaConvidadosAddEdicao(List<ConvidadoPresencaDTO> listaConvidadosAddEdicao) {
        this.listaConvidadosAddEdicao = listaConvidadosAddEdicao;
    }

    public List<ConvidadoPCJDTO> getListaConvidadosDTO() {
        return listaConvidadosDTO;
    }

    public void setListaConvidadosDTO(List<ConvidadoPCJDTO> listaConvidadosDTO) {
        this.listaConvidadosDTO = listaConvidadosDTO;
    }

    public String getMensagemDataSuperiorAtual() {
        return mensagemDataSuperiorAtual;
    }

    public void setMensagemDataSuperiorAtual(String mensagemDataSuperiorAtual) {
        this.mensagemDataSuperiorAtual = mensagemDataSuperiorAtual;
    }

    public OrdenacaoAtaRestritaDTO getOrdenacaoAtaRestritaDTO() {
        return ordenacaoAtaRestritaDTO;
    }

    public void setOrdenacaoAtaRestritaDTO(OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO) {
        this.ordenacaoAtaRestritaDTO = ordenacaoAtaRestritaDTO;
    }

    public String getPaginatorPosition() {
        return Configuration.DataTable.PAGINATOR_POSITION.value();
    }

    public String getPaginatorTemplate() {
        return Configuration.DataTable.PAGINATOR_TEMPLATE.value();
    }

    public String getRowsPerPageTemplate() {
        return Configuration.DataTable.ROWS_PER_PAGE_TEMPLATE.value();
    }

    public List<BlocosTrabalhoAtaRestritaEnum> getTiposTrabalhos() {
        return tiposTrabalhos;
    }

    public void setTiposTrabalhos(List<BlocosTrabalhoAtaRestritaEnum> tiposTrabalhos) {
        this.tiposTrabalhos = tiposTrabalhos;
    }

    public List<TrabalhoDTO> getTrabalhosDTO() {
        return trabalhosDTO;
    }

    public void setTrabalhosDTO(List<TrabalhoDTO> trabalhos) {
        this.trabalhosDTO = trabalhos;
    }

    public boolean isDataRealizacaoInvalida() {
        return dataRealizacaoInvalida;
    }

    public void setDataRealizacaoInvalida(boolean dataRealizacaoInvalida) {
        this.dataRealizacaoInvalida = dataRealizacaoInvalida;
    }

    public boolean isDisableTabs() {
        return disableTabs;
    }

    public void setDisableTabs(boolean disableTabs) {
        this.disableTabs = disableTabs;
    }

    public boolean isDisplayList() {
        return displayList;
    }

    public void setDisplayList(boolean displayList) {
        this.displayList = displayList;
    }

    public boolean isDisplayListConclusao() {
        return displayListConclusao;
    }

    public void setDisplayListConclusao(boolean displayListConclusao) {
        this.displayListConclusao = displayListConclusao;
    }

    public boolean isEdicao() {
        return edicao;
    }

    public void setEdicao(boolean edicao) {
        this.edicao = edicao;
    }

    public boolean isEdicaoAssinatura() {
        return edicaoAssinatura;
    }

    public void setEdicaoAssinatura(boolean edicaoAssinatura) {
        this.edicaoAssinatura = edicaoAssinatura;
    }

    public boolean isEdicaoBlocoTrabalho() {
        return edicaoBlocoTrabalho;
    }

    public void setEdicaoBlocoTrabalho(boolean edicaoBlocoTrabalho) {
        this.edicaoBlocoTrabalho = edicaoBlocoTrabalho;
    }

    public boolean isExibeBotaoAddTrabalho() {
        return exibeBotaoAddTrabalho;
    }

    public void setExibeBotaoAddTrabalho(boolean exibeBotaoAddTrabalho) {
        this.exibeBotaoAddTrabalho = exibeBotaoAddTrabalho;
    }

    public boolean isExisteTrabalhoPendente() {
        return existeTrabalhoPendente;
    }

    public void setExisteTrabalhoPendente(boolean existeTrabalhoPendente) {
        this.existeTrabalhoPendente = existeTrabalhoPendente;
    }

    public boolean isNovoConvidado() {
        return novoConvidado;
    }

    public void setNovoConvidado(boolean novoConvidado) {
        this.novoConvidado = novoConvidado;
    }

    public boolean isRadioHorizontal() {
        return radioHorizontal;
    }

    public void setRadioHorizontal(boolean radioHorizontal) {
        this.radioHorizontal = radioHorizontal;
    }

    public boolean isTipoTrabalho() {
        return tipoTrabalho;
    }

    public void setTipoTrabalho(boolean tipoTrabalho) {
        this.tipoTrabalho = tipoTrabalho;
    }

    public boolean isViewFileUpload() {
        return viewFileUpload;
    }

    public void setViewFileUpload(boolean viewFileUpload) {
        this.viewFileUpload = viewFileUpload;
    }

    private void setStandartWork() {
        setBlocoTrabalho("bloco-base");
    }

    public Long getIdBlockRemove() {
        return idBlockRemove;
    }

    public void setIdBlockRemove(Long idBlockRemove) {
        this.idBlockRemove = idBlockRemove;
    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> getListaComunicacoes() {
        return listaComunicacoes;
    }

    public void setListaComunicacoes(List<BlocoComunicacaoRestritaParaRegistoDTO> listaComunicacoes) {
        this.listaComunicacoes = listaComunicacoes;
    }

//    public ComunicacaoDTO getComunicacaoDTO() {
//        return comunicacaoDTO;
//    }
//
//    public void setComunicacaoDTO(ComunicacaoDTO comunicacaoDTO) {
//        this.comunicacaoDTO = comunicacaoDTO;
//    }

    public BlocoGeralRestritaDTO getBlocoGeralRestritaDTO() {
        return blocoGeralRestritaDTO;
    }

    public void setBlocoGeralRestritaDTO(BlocoGeralRestritaDTO blocoGeralRestritaDTO) {
        this.blocoGeralRestritaDTO = blocoGeralRestritaDTO;
    }

    public BlocoComunicacaoRestritaParaRegistoDTO getBlocoComunicacaoRestrita() {
        return blocoComunicacaoRestrita;
    }

    public void setBlocoComunicacaoRestrita(BlocoComunicacaoRestritaParaRegistoDTO blocoComunicacaoRestrita) {
        this.blocoComunicacaoRestrita = blocoComunicacaoRestrita;
    }
}
