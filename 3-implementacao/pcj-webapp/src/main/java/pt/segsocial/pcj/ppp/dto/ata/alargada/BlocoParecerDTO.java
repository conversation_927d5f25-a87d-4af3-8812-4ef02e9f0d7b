package pt.segsocial.pcj.ppp.dto.ata.alargada;

import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;

public class BlocoParecerDTO extends BlocoBaseDTO {

    private String assunto;

    private String observacao;

    private String textoIntrodutorio;

    private DocumentoDTO documentoPCJ;

    private ComissaoPCJDTO comissao;

    private Boolean ataExterna;

    public BlocoParecerDTO() {
    }

    public static class Builder {
        private Long id;
        private String assunto;
        private String observacao;
        private String textoIntrodutorio;
        private DocumentoDTO documentoPCJ;
        private ComissaoPCJDTO comissao;
        private Boolean ataExterna;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder assunto(String assunto) {
            this.assunto = assunto;
            return this;
        }

        public Builder observacao(String observacao) {
            this.observacao = observacao;
            return this;
        }

        public Builder textoIntrodutorio(String textoIntrodutorio) {
            this.textoIntrodutorio = textoIntrodutorio;
            return this;
        }

        public Builder documentoPCJ(DocumentoDTO documentoPCJ) {
            this.documentoPCJ = documentoPCJ;
            return this;
        }

        public Builder comissao(ComissaoPCJDTO comissao) {
            this.comissao = comissao;
            return this;
        }

        public Builder ataExterna(Boolean ataExterna) {
            this.ataExterna = ataExterna;
            return this;
        }

        public BlocoParecerDTO build() {
            BlocoParecerDTO blocoParecerDTO = new BlocoParecerDTO();
            blocoParecerDTO.setId(this.id);
            blocoParecerDTO.setAssunto(this.assunto);
            blocoParecerDTO.setObservacao(this.observacao);
            blocoParecerDTO.setTextoIntrodutorio(this.textoIntrodutorio);
            blocoParecerDTO.setDocumentoPCJ(this.documentoPCJ);
            blocoParecerDTO.setComissao(this.comissao);
            blocoParecerDTO.setAtaExterna(this.ataExterna);
            return blocoParecerDTO;
        }
    }

    public DocumentoDTO getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentoDTO documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTextoIntrodutorio() {
        return textoIntrodutorio;
    }

    public void setTextoIntrodutorio(String textoIntrodutorio) {
        this.textoIntrodutorio = textoIntrodutorio;
    }

    public ComissaoPCJDTO getComissao() {
        return comissao;
    }

    public void setComissao(ComissaoPCJDTO comissao) {
        this.comissao = comissao;
    }

    public Boolean isAtaExterna() {
        return ataExterna;
    }

    public void setAtaExterna(Boolean ataExterna) {
        this.ataExterna = ataExterna;
    }
}
