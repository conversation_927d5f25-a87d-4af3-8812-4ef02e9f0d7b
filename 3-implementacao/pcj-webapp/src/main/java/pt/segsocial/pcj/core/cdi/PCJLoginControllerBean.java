package pt.segsocial.pcj.core.cdi;

import com.ocpsoft.pretty.faces.annotation.URLAction;
import org.apache.commons.collections.CollectionUtils;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import pt.segsocial.fraw.api.security.Login;
import pt.segsocial.pcj.core.service.LoginService;
import pt.segsocial.pcj.pae.bean.AbstractBean;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.PerfilDTO;
import pt.segsocial.pcj.ppp.dto.entidadeexterna.EntidadeExternaDTO;
import pt.segsocial.pcj.ppp.dto.entidadeexterna.FiltroPesquisaEntidadeExternaDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.EntidadeExterna;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilEntidade;
import pt.segsocial.pcj.ppp.service.EntidadeExternaService;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.faces.model.SelectItem;
import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;

@Named(value = "pcjLoginController")
@Stateful(name = "pcjLoginController")
@LocalBean
@ViewAccessScoped
public class PCJLoginControllerBean extends AbstractBean {

    @Inject
    private LoginService loginService;

    @Inject
    private PCJLoginBean loginBean;

    @Inject
    private EntidadeExternaService entidadeExternaService;

    private Elemento elemento;
    private EntidadeExterna entidadeExterna;
    private List<SelectItem> listCPCJ = new ArrayList<>();
    private Login login;
    private boolean cpcjElemento = true;
    private PerfilDTO perfilDTO = new PerfilDTO();

    @URLAction(mappingId = PCJSubsystem.OP_LOGIN_CPCJ, onPostback = false)
    public String onLoginCPCJ() {
        if (listCPCJ.isEmpty()) {
            criarSelectCPCJ();
        }
        login = loginBean.getLogin();
        return "";
    }

    private void criarSelectCPCJ() {
        List<Elemento> elementos = loginBean.obterElementos(Long.valueOf(loginBean.getNiss()));
        if (CollectionUtils.isNotEmpty(elementos)) {
            addElementosToListCPCJ(elementos);
        } else {
            List<EntidadeExternaDTO> entidadesExternas = buscarEntidadesExternas();
            if (CollectionUtils.isNotEmpty(entidadesExternas)) {
                setCpcjElemento(false);
                addEntidadesExternasToListCPCJ(entidadesExternas);
            }
        }
    }

    private List<EntidadeExternaDTO> buscarEntidadesExternas() {
        FiltroPesquisaEntidadeExternaDTO pesquisa = new FiltroPesquisaEntidadeExternaDTO();
        UtilizadorPCJDTO utilizadorPCJDTO = new UtilizadorPCJDTO();
        utilizadorPCJDTO.setNiss(String.valueOf(loginBean.getNiss()));
        pesquisa.setUtilizadorPCJ(utilizadorPCJDTO);
        return entidadeExternaService.buscarPorFiltro(pesquisa);
    }

    private void addElementosToListCPCJ(List<Elemento> elementos) {
        for (Elemento elementoField : elementos) {
            listCPCJ.add(new SelectItem(elementoField, elementoField.getComissao().getNome()));
        }
    }

    private void addEntidadesExternasToListCPCJ(List<EntidadeExternaDTO> entidadesExternas) {
        for (EntidadeExternaDTO entidadeExternaField : entidadesExternas) {
            for (ComissaoPCJDTO comissaoPCJppp : entidadeExternaField.getComissoes()) {
                listCPCJ.add(new SelectItem(entidadeExternaField, comissaoPCJppp.getNome()));
            }
        }
    }

    public void addRolePerfilCPCJ() {
        //TODO: Permissoes temporarias
        //Aqui tenho a CPCJ selecionada
        //Aqui eu tenho o elemento
        //Busca na entidade Permissao Temporaria
        //Adiciona ao pergilDTO a lista de permissoes temporarias

        if (elemento != null) {
            PerfilElemento perfilElemento = obterPerfilElemento(elemento);
            login.addRoles(perfilElemento.getPerfil());
            loginBean.criarPerfilElemento(perfilElemento);
        } else if (entidadeExterna != null) {
            PerfilEntidade perfilEntidade = obterPerfilEntidade(entidadeExterna);
            login.addRoles(perfilEntidade.getPerfil());
            loginBean.criarPerfilEntidade(perfilEntidade);
        }
        pcjSubsystem.redirectToHome(login);
    }

    public PerfilElemento obterPerfilElemento(Elemento elemento) {
        return loginService.verificaPerfilElemento(elemento);
    }

    public PerfilEntidade obterPerfilEntidade(EntidadeExterna entidadeExterna) {
        return loginService.verificaPerfilEntidadeExterna(entidadeExterna);
    }

    public List<SelectItem> getListCPCJ() {
        return listCPCJ;
    }

    public void setListCPCJ(List<SelectItem> listCPCJ) {
        this.listCPCJ = listCPCJ;
    }

    public Elemento getElemento() {
        return elemento;
    }

    public void setElemento(Elemento elemento) {
        this.elemento = elemento;
    }

    public EntidadeExterna getEntidadeExterna() {
        return entidadeExterna;
    }

    public void setEntidadeExterna(EntidadeExterna entidadeExterna) {
        this.entidadeExterna = entidadeExterna;
    }

    public void setCpcjElemento(boolean cpcjElemento) {
        this.cpcjElemento = cpcjElemento;
    }

    public boolean isCpcjElemento() {
        return cpcjElemento;
    }

    public void setPerfilDTO(PerfilDTO perfilDTO) {
        this.perfilDTO = perfilDTO;
    }

    public PerfilDTO getPerfilDTO() {
        return perfilDTO;
    }
}
