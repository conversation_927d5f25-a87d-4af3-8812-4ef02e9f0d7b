package pt.segsocial.pcj.pae.bean;

import ch.qos.logback.classic.Level;
import com.ocpsoft.pretty.faces.annotation.URLAction;
import com.ocpsoft.pretty.faces.annotation.URLActions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.joda.time.LocalDate;
import org.primefaces.component.tabview.TabView;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.TabChangeEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.cdi.grs.dominio.DominioHolder;
import pt.segsocial.fraw.api.cdi.grs.dominio.DominioValue;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.cdi.CDIUtils;
import pt.segsocial.fraw.jsf.util.JsfUtil;
import pt.segsocial.id.api.dto.PessoaSingularDTO;
import pt.segsocial.id.api.exception.IDException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJLoginBean;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.service.RegistoLogService;
import pt.segsocial.pcj.core.util.*;
import pt.segsocial.pcj.pae.bean.service.*;
import pt.segsocial.pcj.pae.bean.wizard.RegistarPaeWizardContext;
import pt.segsocial.pcj.pae.enums.*;
import pt.segsocial.pcj.pae.handler.ReportHandler;
import pt.segsocial.pcj.pae.jpa.dao.*;
import pt.segsocial.pcj.pae.jpa.entity.*;
import pt.segsocial.pcj.pae.vo.ComprovativoVO;
import pt.segsocial.pcj.ppp.dto.PerfilDTO;
import pt.segsocial.pcj.ppp.dto.log.RegistoLogDTO;
import pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum;
import pt.segsocial.pcj.ppp.jpa.dao.CompetenciaTerritorialDAO;
import pt.segsocial.pcj.ppp.jpa.dao.PerfilElementoDAO;
import pt.segsocial.pcj.ppp.jpa.dao.TerritorioPCJDAO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.CompetenciaTerritorial;
import pt.segsocial.pcj.ppp.jpa.entity.PerfilElemento;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.mapper.MoradaMapper;

import javax.annotation.PostConstruct;
import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static pt.segsocial.pcj.pae.enums.MotivoInvalidezDocumento.DOCUMENTO_ILEGIVEL;
import static pt.segsocial.pcj.pae.enums.MotivoInvalidezDocumento.DOCUMENTO_INCORRETO;
import static pt.segsocial.pcj.pae.enums.TipoLogEnum.ATRIBUIR_NOVO_PRESIDENTE;
import static pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum.CONSULTAR_PROCESSO;


/**
 * Classe que define o bean da p?gina de consulta do processo
 */

@Named(value = "pcjConsultarProcessoBean")
@LocalBean
@ViewAccessScoped
@Stateful
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class ConsultarProcessoBean extends AbstractBean implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsultarProcessoBean.class);
    private static final String PENDENTE_ASSINATURA = "PENDENTE_ASSINATURA";
    private static final String AGUARDANDO_ASSINATURA = "AGUARDANDO_ASSINATURA";
    private static final String DECISAO_ASSINADA = "DECISAO_ASSINADA";
    private static final String DECISAO_ASSINADA_EXTERNA = "DECISAO_ASSINADA_EXTERNA";


    @Inject
    @DominioValue(value = "INDEFER")
    private DominioHolder dominioHolder;

    @Inject
    private PCJSubsystem pcjSubsystem;

    @Inject
    private ConsultarReavaliacaoBean pcjConsultarReavaliacaoBean;

    @Inject
    private ProcessoPaeBean processoPaeBean;

    @Inject
    private RegistoLogService registoLogService;

    @Inject
    private MoradaMapper moradaMapper;

    private Long idProcesso;
    private MessagesBundle messageProperties;
    private IdentificacaoPaeService identificacao;
    private DocumentoPaeService documentoPaeService;
    private EducacaoPaeService educacaoPaeService;
    private HorarioParticipacaoService horarioParticipacao;
    private HorarioParticipacaoService horarioParticipacaoAlteracao;
    private ParticipacaoPaeService participacaoPaeService;
    private VigilanciaService vigilanciaService;
    private Participacao novaParticapacao;
    private Educacao educacaoAlteracao;
    private InformacaoComplementar informacaoComplementar;
    private String informacoesComplementares;
    private RegistarPaeWizardContext context;
    private PCJLoginBean loginBean;
    private String motivo;
    private MotivoInvalidezDocumento motivoInvalidacaoDocumento;
    private String tabAtiva;
    private Date dataProlongamentoParticipacao;
    private String motivoPrazoIncerto;

    private String nomeGestor;
    private String nomeComissao;
    private ComissaoPCJppp comissaoPCJppp;

    private List<RequerimentoPae> requerimentosAbertos;
    private Map<String, DetalheDominioVO> detalheDominioMap;
    private List<Historico> historicos;
    private ParticipacaoPaeService service;

    private Historico historico;
    private boolean alteracaoEducacao;
    private boolean alteracaoHorarios;
    private boolean alteracaoVigilante;
    private RequerimentoPae novoRequerimentoAlteracaoHorarios;
    private PessoaSingularDTO pessoaSingular;
    private DuracaoParticipacaoEnum prazoRenovacao;
    private VigilanciaPae vigilanteAlteracao;
    private RequerimentoPae novoRequerimentoEducacao;
    private RequerimentoPae novoRequerimentoParticipacao;
    private int activeIndexTabViewConsultarProcesso;
    private Comprovativo comprovativoAssinatura;
    private String estadoComprovativoDecisao;
    private Long idFicheiro;
    private RequerimentoPae requerimentoEducacao;
    private RequerimentoPae requerimentoVigilante;
    private RequerimentoPae requerimentoParticipacao;

    @PostConstruct
    public void init() {
        LoggingHelper.logEntrada();
        String id = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("id");
        if (id != null) {
            idProcesso = Long.parseLong(id);
        }
        LoggingHelper.logSaida();
    }

    @URLActions(actions = {
            @URLAction(mappingId = PCJSubsystem.OP_CONSULTAR_PROCESSO, onPostback = false),
            @URLAction(mappingId = PCJSubsystem.OP_CONSULTAR_DOSSIER, onPostback = false)
    })
    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void onLoadConsultar() throws IDException {
        LoggingHelper.logEntrada();
        try {
            context = new RegistarPaeWizardContext();
            context.setEntityManager(entityManager);
            context.setServiceLocator(paeServiceLocator);
            context.setPcjCache(pcjSubsystem.getPcjCache());
            loginBean = paeServiceLocator.getLoginBean();
            messageProperties = CDIUtils.getCdiBean(MessagesBundle.class);
            popularProcesso();
            participacaoPaeService = new ParticipacaoPaeService(context, moradaMapper);
            service = new ParticipacaoPaeService(context, moradaMapper);
            preencherDadosSolicitacaoNovaParticipacao();

            if (getVigilancia() == null) {
                context.setVigilancia(new VigilanciaPae());
            }

            //clonarEducacao();
            clonarVigilante();
            participacaoPaeService.setRenovacao(true);
            identificacao.popularInformacoesPaiEMae();
            getHorariosParticipacao();
            insereMotivoDefaultDocumentoInvalido();
            carregarDiligencias(this.requerimento.getProcessoPae().getId());
            this.requerimentosAbertos = new RequerimentoPaeDAO(entityManager).findRequerimentosAberto(idProcesso);

            clonarHorarios();
            if (EstadoProcessoEnum.DEFERIDO.equals(requerimento.getProcessoPae().getEstado())) {
                limparDocumentosNaoUsados();
            }
            historico = new Historico();
            pcjSubsystem.getPcjCache().validaParametroIdProcessoParaPesquisa(idProcesso);
            registoLogService.gravarRegistoLog(gerarRegistoLog(this.requerimento.getProcessoPae(), CONSULTAR_PROCESSO));
            CompetenciaTerritorial competenciaTerritorial = new CompetenciaTerritorialDAO(entityManager).findCompetenciasByTerritorio(requerimento.getProcessoPae().getTerritorioPCJ().getId());
            setComissaoPCJppp(null != competenciaTerritorial ? competenciaTerritorial.getComissao() : null);
            preencherComprovativoAssinado();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LoggingHelper.logSaida();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void limparDocumentosNaoUsados() {
        if (!possuiAlteracaoAberta(TipoRequerimentoEnum.ALTERACAO_EDUCACAO)) {
            List<Long> idsFicheiros = new DocumentoPaeDAO(entityManager).findIdDocumentosAlteracaoParaExlcuirStorage(getIdProcesso(), TipoDocumentoEnum.getTipoDocumentoAlteracaoCircunstaciaEducacao());
            apagandoDocumentosFicheiro(idsFicheiros, TipoDocumentoEnum.getTipoDocumentoAlteracaoCircunstaciaEducacao());
        }
        if (!possuiAlteracaoAberta(TipoRequerimentoEnum.ALTERACAO_PARTICIPACAO)) {
            List<Long> idsFicheiros = new DocumentoPaeDAO(entityManager).findIdDocumentosAlteracaoParaExlcuirStorage(getIdProcesso(), TipoDocumentoEnum.getTipoDocumentoAlteracaoCircunstaciaParticipacao());
            apagandoDocumentosFicheiro(idsFicheiros, TipoDocumentoEnum.getTipoDocumentoAlteracaoCircunstaciaParticipacao());
        }
    }

    private void apagandoDocumentosFicheiro(List<Long> idsFicheiros, List<TipoDocumentoEnum> tipos) {

        // apagando documentos da base PCJ
        new DocumentoPaeDAO(entityManager).apagarDocumentosSemRequerimento(getIdProcesso(), tipos);

        // apagando arquivos do ficheiro
        for (Long id : idsFicheiros) {
            try {
                documentoPaeService.delete(id, "PCJ");
            } catch (PCJException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    private void reprovarRequerimentos() {
        for (RequerimentoPae requerimento : this.requerimentosAbertos) {
            LoggingHelper.logEntrada(Level.DEBUG, requerimento);
            requerimento.setEstadoRequerimento(EstadoRequerimentoEnum.REPROVADO);
        }
    }

    protected void popularProcesso() throws IDException {
        LoggingHelper.logEntrada();
        try {
            entityManager.clear();
            requerimento = getRequerimentoConsulta();
            if (requerimento != null) {
                LoggingHelper.logEntrada(Level.DEBUG, requerimento);
                this.requerimento.setIdadeCrianca(obterIdadeCrianca());
                context.setRequerimento(requerimento);
                informacaoComplementar = new InformacaoComplementarDAO(entityManager).findByIdRequerimento(requerimento.getId());
                identificacao = new IdentificacaoPaeService(context, false);
                setParticipacao(carregarDadosParticipacao());
                dataProlongamentoParticipacao = getParticipacao().getDataFimParticipacao();
                prazoRenovacao = getParticipacao().getDuracao();
                context.setParticipacao(getParticipacao());
                setEducacao(carregarDadosEducacao());
                if (getEducacao() != null) {
                    context.setEducacao(getEducacao());
                    educacaoPaeService = new EducacaoPaeService(context);
                    horarioParticipacao = new HorarioParticipacaoService(context, false);
                }

                setVigilancia(carregarDadoVigilante());
                context.setVigilancia(getVigilancia());
                context.setDocumentoAutorizacao(obterDocumentoParticipacao(requerimento.getProcessoPae().getId()));
                documentoPaeService = new DocumentoPaeService(context);
                setDocumentosResumo(documentoPaeService.getDocumentos());
                getHistoricosByProcesso();

                setNomeGestor(this.requerimento.getProcessoPae().getElemento() != null ? getNomeGestor(
                        this.requerimento.getProcessoPae()) : "");

                setNomeComissao(null != comissaoPCJppp ? getComissaoPCJppp().getNome() : "");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LoggingHelper.logSaida(Level.DEBUG, requerimento);
    }

    private Participacao carregarDadosParticipacao() {
        return new ParticipacaoDAO(entityManager).findByIdRequerimento(requerimentoParticipacao.getId());
    }

    private Educacao carregarDadosEducacao() {
        return new EducacaoDAO(entityManager).findByIdRequerimento(requerimentoEducacao.getId());
    }

    private VigilanciaPae carregarDadoVigilante() {
        return new VigilanciaPaeDAO(entityManager).findByIdRequerimento(requerimentoVigilante.getId());
    }

    public String nomeCriancaComNISS() {
        return pessoaSingular.getNomeCompleto();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void preview(Long idFicheiro) {
        try {
            documentoPaeService.preview(idFicheiro);
        } catch (PCJException e) {
            LOGGER.error("[ PCJ ] Erro ao fazer preview do ficheiro: ", e);
            JsfUtil.addErrorMessage(null, "Não foi possível adicionar documento. Tente novamente mais tarde", null);
        }
    }

    public boolean consultaExterna() {
        return paeServiceLocator.getLoginBean().hasPerfilEntidade();
    }

    public boolean consultaInterna() {
        return paeServiceLocator.getLoginBean().hasPerfilGestor()
                || paeServiceLocator.getLoginBean().hasPerfilPresidente()
                || paeServiceLocator.getLoginBean().hasPerfilComissao()
                || paeServiceLocator.getLoginBean().hasPerfilSecretario();
    }

    public boolean renderizarIdentificacaoEstrangeira() {
        return this.getRequerimento().getProcessoPae().getCriancaEstrangeira() != null;
    }

    @Override
    public String labelIdentificacao() {
        return this.getRequerimento().getProcessoPae().getCriancaEstrangeira() != null ? "Documento de identificação" : "NISS";
    }

    @Override
    public String labelCidadao() {
        return getRequerimento().getProcessoPae().getCriancaEstrangeira() != null ? "Nº Documento de Identificação" : "Nº Cartão de Cidadão";
    }

    public boolean podeRevogar() {
        if (null != getParticipacao().getDataFimParticipacao()) {
            return requerimento.getProcessoPae().getEstado().equals(EstadoProcessoEnum.DEFERIDO)
                    && loginBean.hasPerfilGestor() && verificaGestorProcesso(requerimento.getProcessoPae())
                    && getParticipacao().getDataFimParticipacao().compareTo(new Date()) >= 0;
        }

        return requerimento.getProcessoPae().getEstado().equals(EstadoProcessoEnum.DEFERIDO)
                && loginBean.hasPerfilGestor();
    }

    private boolean verificaGestorProcesso(ProcessoPae processoPae) {
        if (processoPae.getElemento() != null) {
            PerfilElemento perfilElemento = new PerfilElementoDAO(entityManager).buscarPerfilElementoPorElemento(processoPae.getElemento());
            return perfilElemento != null && (perfilElemento.getPerfil().equals(PerfilEnum.PRESIDENTE.getDescricao()) ||
                    perfilElemento.getPerfil().equals(PerfilEnum.SECRETARIO.getDescricao()) ||
                    perfilElemento.getPerfil().equals(PerfilEnum.MEMBRO_RESTRITA.getDescricao()));
        }
        return false;
    }

    public String dataFormatada(Date dataDecisao, String hora) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.format(dataDecisao) + " " + hora;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarInformacoesComplementares() throws DomainException {

        if (this.informacaoComplementar == null) {
            this.informacaoComplementar = new InformacaoComplementar();
            this.informacaoComplementar.setRequerimento(getRequerimento());
            this.informacaoComplementar.setInformacao(informacoesComplementares);
            new InformacaoComplementarDAO(entityManager).create(informacaoComplementar);

        } else {
            this.informacaoComplementar.setInformacao(informacoesComplementares);
            new InformacaoComplementarDAO(entityManager).update(informacaoComplementar);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void solicitarAlteracaoHorarios(RequerimentoPae requerimentoPae) {
        try {
            if (null == horarioParticipacao) {
                horarioParticipacao = new HorarioParticipacaoService(context, false);
            }
            horarioParticipacao.regraGuardarInformacoes(requerimentoPae);
            horarioParticipacao.saveHorariosAlteracao(this.novoRequerimentoAlteracaoHorarios);
            paeServiceLocator.getMensagemHandler().enviarMensagemAlteracaoParticipacao(this.requerimento,
                    getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                    getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
            atualizarEstadoProcesso(this.requerimento, EstadoProcessoEnum.DEFERIDO, "Pedido de solicitação de alteração de horários ", null, null);
            novoRequerimentoAlteracaoHorarios = null;
            alteracaoHorarios = false;
            JsfMessageUtil.mostraMensagemSucesso("Pedido de solicitação de alteração de horários realizado com sucesso.");
            redirectProcesso();
        } catch (PCJException | IDException | IOException | DomainException e) {
            LOGGER.error(e.getMessage());
            JsfMessageUtil.mostraMensagemErro(e.getMessage());
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void solicitarAlteracaoEducacao() throws PCJException {

        if (!alterouEducacao()) {
            JsfMessageUtil.mostraMensagemErro("Não foram efetuadas alterações.");
            throw new PCJException();
        }

        try {
            educacaoAlteracao.setRequerimentoPAE(this.novoRequerimentoEducacao);
            educacaoAlteracao.getMorada().setTerritorioPCJ(null);
            if (!educacaoPaeService.isEscolaridadeObrigatoria()) {
                limpandoListas(novoRequerimentoEducacao);
                limparDadosEducacao();
                new RequerimentoPaeDAO(entityManager).create(novoRequerimentoEducacao);
            } else {
                educacaoPaeService.registarRequerimentoAlteracaoEducacao(educacaoAlteracao);
            }
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.DEFERIDO);
            paeServiceLocator.getMensagemHandler().enviarMensagemAlteracaoParticipacao(this.requerimento,
                    getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                    getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
            atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Pedido de solicitação de alteração de educação", null, null);
            JsfMessageUtil.mostraMensagemSucesso("Pedido de solicitação de alteração de educação realizada com sucesso.");
            redirectProcesso();
            setAlteracaoEducacao(false);

        } catch (PCJException | IDException | IOException | DomainException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void limparDadosEducacao() {
        this.educacaoAlteracao = null;

    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void aprovarProcesso() throws PCJException, IDException {
        try {
            verificarDocumentosValidados();
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.DEFERIDO);
            paeServiceLocator.getMensagemHandler().enviarMensagem(this.requerimento, getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());

            aprovarDocumentosNaoObrigatorios();
            ComprovativoVO comprovativoVO = preencherComprovativoDeferimentoIndeferimento(this.requerimento, ReportHandler.COMPROVATIVO_DEFERIMENTO_TEMPLATE_3741, getNumOficio());
            gerarDocumento(comprovativoVO, false);

            atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Autorização de processo", comprovativoVO.getComprovativo(), TipoLogEnum.AUTORIZACAO_PROCESSO);
            getHistoricosByProcesso();
            registoLogService.gravarRegistoLog(gerarRegistoLog(requerimento.getProcessoPae(), TipoRegistoLogEnum.AVALIACAO_PROCESSO));
            AuditoriaRazaoEnum.DEFERIR_PROCESSO_PAE.push();
            redirectProcesso();
            JsfMessageUtil.mostraMensagemSucesso(PcjMessages.getMessage("M_SUCESSO_DEFERIR_PROCESSO"));
        } catch (Exception e) {
            LOGGER.error("ERRO", e);
        }
    }

    private void aprovarDocumentosNaoObrigatorios() {
        for (Documento doc : documentoPaeService.getDocumentos()) {
            if (("-").equalsIgnoreCase(doc.getDocumentoValido())) {
                doc.setAprovado(true);
            }
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void reprovarProcesso() throws PCJException, IDException {
        try {
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.INDEFERIDO);
            paeServiceLocator.getMensagemHandler().enviarMensagem(this.requerimento, getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());

            ComprovativoVO comprovativoVO = preencherComprovativoDeferimentoIndeferimento(this.requerimento, ReportHandler.COMPROVATIVO_INDEFERIMENTO_TEMPLATE_3742, getNumOficio());
            gerarDocumento(comprovativoVO, false);

            atualizarEstadoProcesso(this.requerimento, estadoAnterior, motivo, comprovativoVO.getComprovativo(), TipoLogEnum.NAO_AUTORIZACAO_PROCESSO);
            getHistoricosByProcesso();
            JsfMessageUtil.mostraMensagemSucesso("Pedido de autorização para a participação em Artes e espetáculos rejeitado com sucesso.");
            AuditoriaRazaoEnum.INDEFERIR_PROCESSO_PAE.push();
            redirectProcesso();
        } catch (Exception e) {
            LOGGER.error("ERRO", e);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void arquivarProcesso() throws PCJException, IDException {
        this.getRequerimento().getProcessoPae().setDataFim(new Date());
        EstadoProcessoEnum estadoAnterior = requerimento.getProcessoPae().getEstado();
        requerimento.getProcessoPae().setEstado(EstadoProcessoEnum.ARQUIVADO);
        atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Término do processo", null, TipoLogEnum.TERMINO_PROCESSO);
        paeServiceLocator.getMensagemHandler().enviarMensagemArquivado(this.requerimento,
                getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                getNumeroProcesso(),
                getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
        JsfMessageUtil.mostraMensagemSucesso(Constants.ARQUIVAR_PROCESSO);
        registoLogService.gravarRegistoLog(gerarRegistoLog(requerimento.getProcessoPae(), TipoRegistoLogEnum.TERMINO_PROCESSO));
        AuditoriaRazaoEnum.ARQUIVAR_PROCESSO_PAE.push();
    }

    private RegistoLogDTO gerarRegistoLog(ProcessoPae processoPae, TipoRegistoLogEnum terminoProcesso) {
        PerfilDTO perfilDTO = pcjSubsystem.getLogin().getPerfilDTO();
        RegistoLogDTO registoLogDTO = new RegistoLogDTO();
        registoLogDTO.setUtilizadorPCJDTO(perfilDTO.getUtilizadorPCJDTO());
        registoLogDTO.setDescricao(String.format(terminoProcesso.getDescricao(), getNumeroProcessoByProcesso(processoPae)));

        return registoLogDTO;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void renovarProcesso() {
        try {
            if (dataProlongamentoParticipacao.before(getParticipacao().getDataFimParticipacao()) || dataProlongamentoParticipacao.equals(getParticipacao().getDataFimParticipacao())) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M11186"));
                throw new PCJException();
            }
            RequerimentoPae novoRequerimento = salvarRequerimentoAlteracao(TipoRequerimentoEnum.RENOVACAO);
            novoRequerimento.setLocaisAtividade(clonarMoradaRenovarProcesso(context.getParticipacao().getRequerimentoPAE().getLocaisAtividade()));
            ParticipacaoPaeService service = new ParticipacaoPaeService(context, moradaMapper);
            novaParticapacao = new Participacao();
            service.setNovaPartipacao(novaParticapacao);
            novaParticapacao.setDataFimParticipacao(dataProlongamentoParticipacao);

            novaParticapacao.setMotivoPrazoIncerto(getMotivoPrazoIncerto());
            novaParticapacao.setDuracao(prazoRenovacao);
            popularParticipacaoRenovacao(this.novaParticapacao, getParticipacao());
            service.setRenovacao(true);
            new RequerimentoPaeDAO(entityManager).create(novoRequerimento);
            novaParticapacao.setRequerimentoPAE(novoRequerimento);
            service.salvarRenovacao();
            paeServiceLocator.getMensagemHandler().enviarMensagemRenovacao(this.requerimento,
                    getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                    getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
            dataProlongamentoParticipacao = getParticipacao().getDataFimParticipacao();
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.DEFERIDO);
            atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Pedido de solicitação de prolongamento", null, TipoLogEnum.PEDIDO_PROLONGAMENTO_PROCESSO);
            JsfMessageUtil.mostraMensagemSucesso(PcjMessages.getMessage("M011185"));
            AuditoriaRazaoEnum.RENOVAR_PROCESSO_PAE.push();
        } catch (PCJException | IDException | DomainException e) {
            LOGGER.error("Erro ao tentar renovar o  processo: " + e);
        } finally {
            dataProlongamentoParticipacao = getParticipacao().getDataFimParticipacao();
            motivoPrazoIncerto = null;
            prazoRenovacao = getParticipacao().getDuracao();
        }
    }

    private List<Morada> clonarMoradaRenovarProcesso(List<Morada> moradas) throws DomainException {
        List<Morada> moradasClonadas = new ArrayList<>();

        for (Morada morada : moradas) {
            Morada novaMorada = new Morada();
            novaMorada.setArteria(morada.getArteria());
            novaMorada.setCodigoPostal(morada.getCodigoPostal());
            novaMorada.setDesignacaoPostal(morada.getDesignacaoPostal());
            novaMorada.setLocalidade(morada.getLocalidade());
            novaMorada.setPais(morada.getPais());
            if (morada.getTerritorioPCJ() != null) {

                TerritorioPCJ territorioPCJ = new TerritorioPCJDAO(entityManager).getTerritoryPCJ(morada.getTerritorioPCJ().getCodigoDistrito(), morada.getTerritorioPCJ().getCodigoConcelho(), morada.getTerritorioPCJ().getCodigoFreguesia());
                novaMorada.setTerritorioPCJ(territorioPCJ);
            }
            moradasClonadas.add(novaMorada);

        }

        return moradasClonadas;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void revogarProcesso() {
        try {
            Comprovativo comprovativo = this.requerimento.getComprovativos().iterator().next();
            reprovarRequerimentos();
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.REVOGADO);
            atualizarEstadoProcesso(this.requerimento, estadoAnterior, historico.getLog(), null, TipoLogEnum.REVOGACAO_PROCESSO);
            paeServiceLocator
                    .getMensagemHandler()
                    .enviarMensagemRevogacao(getRequerimento(),
                            getNumeroProcesso(),
                            getNomeCompletoDaCrianca(getRequerimento()),
                            getNumeroIdentificacaoCivil(),
                            historico.getLog());

            ComprovativoVO comprovativoVO = preencherComprovativoRevogacaoProcesso(this.requerimento, getNumOficio());
            comprovativo.setFicheiroAssinado(null);
            comprovativoVO.setComprovativo(comprovativo);
            gerarDocumento(comprovativoVO, false);
            redirectProcesso();
            JsfMessageUtil.mostraMensagemSucesso("Revogação de processo realizada com sucesso.");
            registoLogService.gravarRegistoLog(gerarRegistoLog(requerimento.getProcessoPae(), TipoRegistoLogEnum.REVOGAR_PROCESSO));

        } catch (Exception e) {
            LOGGER.error("Erro ao tentar revogar processo: " + e);
            JsfUtil.addErrorMessage(null, "Não foi possível revogar processo. Tente novamente mais tarde", null);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void solicitarAlteracaoParticipacao() {
        try {

            if (novoRequerimentoParticipacao.getLocaisAtividade().isEmpty()) {
                JsfMessageUtil.mostraMensagemErro("Deve guardar pelo menos um local da atividade.");
                throw new PCJException();
            }
            new RequerimentoPaeDAO(entityManager).create(novoRequerimentoParticipacao);
            novaParticapacao.setRequerimentoPAE(novoRequerimentoParticipacao);
            zerarDescricoes();
            service.setRenovacao(true);
            novaParticapacao.setDuracao(service.isPrazoCerto() ? DuracaoParticipacaoEnum.PRAZO_CERTO : DuracaoParticipacaoEnum.PRAZO_INCERTO);
            if (service.isPrazoCerto()) {
                novaParticapacao.setMotivoPrazoIncerto(null);
            }
            service.salvarRenovacao();

            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.DEFERIDO);
            paeServiceLocator.getMensagemHandler().enviarMensagemAlteracaoParticipacao(this.requerimento,
                    getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                    getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
            dataProlongamentoParticipacao = getParticipacao().getDataFimParticipacao();
            participacaoPaeService.setAlteracaoParticipacao(false);
            atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Pedido de solicitação de alteração de participação",
                    null, TipoLogEnum.ALTERACAO_PARTICIPACAO);
            JsfMessageUtil.mostraMensagemSucesso("Pedido de solicitação de alteração de participação realizado com sucesso.");
            redirectProcesso();
        } catch (PCJException | IDException | IOException | DomainException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void redirectProcesso() throws IOException {
        pcjSubsystem.redirectTo(
                pcjSubsystem.consultarProcessoPae(),
                pcjSubsystem.getParametroId(this.requerimento.getProcessoPae()));
    }

    private void zerarDescricoes() {
        if (!service.isEnvolveAnimal()) {
            novaParticapacao.setDescricaoAnimal(null);
        }
        if (!service.isEnvolvePerigo()) {
            novaParticapacao.setDescricaoPerigo(null);
        }
        if (!service.isEnvolveSubstancia()) {
            novaParticapacao.setDescricaoSubstancia(null);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void solicitarAlteracaoVigilante() {
        try {
            RequerimentoPae novoRequerimento = salvarRequerimentoAlteracao(TipoRequerimentoEnum.ALTERACAO_VIGILANCIA);
            vigilanciaService.regrasVigilancia(novoRequerimento);
            vigilanciaService.setRequerimento(novoRequerimento);
            if (!vigilanciaService.isPossuiVigilante()) {
                apagarDadosVigilanteClonado(this.vigilanteAlteracao);
                apagarDadosVigilanteServiceClonado();

            }
            new RequerimentoPaeDAO(entityManager).create(novoRequerimento);
            vigilanciaService.registar(novoRequerimento, vigilanteAlteracao);
            EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.DEFERIDO);
            paeServiceLocator.getMensagemHandler().enviarMensagemAlteracaoParticipacao(this.requerimento,
                    getElemento(this.requerimento.getProcessoPae().getElemento().getId()).getUtilizadorPCJ().getNiss(),
                    getNumeroProcesso(),
                    getNomeCriancaCompleto(this.requerimento.getProcessoPae().getNissCrianca()), getIdentificacaoParaReport());
            atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Pedido de solicitação de alteração de Vigilante",
                    null, TipoLogEnum.ALTERACAO_VIGILANTE);
            setAlteracaoVigilante(false);
            JsfMessageUtil.mostraMensagemSucesso("Pedido de solicitação de alteração de Vigilante  realizado com sucesso.");
            redirectProcesso();
        } catch (PCJException | IDException | IOException | DomainException e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void apagarDadosVigilanteServiceClonado() {
        vigilanciaService.setPessoaSingularDTO(null);
        vigilanciaService.setVigilantePossuiNiss(false);
        vigilanciaService.setNomeCompleto(null);
    }

    private RequerimentoPae salvarRequerimentoAlteracao(TipoRequerimentoEnum tipoAlteracao) {
        RequerimentoPae novoRequerimento = criarRequerimentoPaeAlteracao(tipoAlteracao);

        if (TipoRequerimentoEnum.ALTERACAO_PARTICIPACAO.equals(tipoAlteracao)) {
            popularLocaisAtividade(novoRequerimento, this.context.getRequerimento());
        }

        return novoRequerimento;
    }

    @NotNull
    private RequerimentoPae criarRequerimentoPaeAlteracao(TipoRequerimentoEnum tipoAltrecao) {
        RequerimentoPae novoRequerimento = new RequerimentoPae();
        novoRequerimento.setTipoRequerimento(tipoAltrecao);
        novoRequerimento.setData(new Date());
        novoRequerimento.setProcessoPae(this.requerimento.getProcessoPae());
        return novoRequerimento;
    }

    public void cancelarAlteracao() throws IOException {
        dataProlongamentoParticipacao = getParticipacao().getDataFimParticipacao();
        motivoPrazoIncerto = null;
        participacaoPaeService.setAlteracaoParticipacao(false);
        this.setAlteracaoEducacao(false);
        this.setAlteracaoHorarios(false);
        this.setAlteracaoVigilante(false);
        redirectProcesso();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void anularProcesso() {
        this.getRequerimento().getProcessoPae().setDataFim(new Date());
        EstadoProcessoEnum estadoAnterior = getEstadoProcesso(EstadoProcessoEnum.ANULADO);
        atualizarEstadoProcesso(this.requerimento, estadoAnterior, "Anulação de processo", null, TipoLogEnum.ANULACAO_PROCESSO);
        JsfMessageUtil.mostraMensagemSucesso("Pedido de autorização para a participação em Artes e espetáculos anulado com sucesso.");
        AuditoriaRazaoEnum.ANULAR_PROCESSO_PAE.push();
    }

    private void verificarDocumentosValidados() throws PCJException {
        for (Documento doc : documentoPaeService.getDocumentos()) {
            if (obrigatoriedade(doc.getTipoDocumento()).equalsIgnoreCase("Sim") &&
                    (doc.getDocumentoValido().equalsIgnoreCase("Inválido") || ("-").equalsIgnoreCase(doc.getDocumentoValido()))) {
                JsfMessageUtil.mostraMensagemErro("Existem documentos não aprovados ou inválidos. ");
                throw new PCJException();
            }
        }
    }

    public Boolean existeContestacaoDecisao() {
        ContestacaoDAO contestacaoDAO = new ContestacaoDAO(entityManager);
        return contestacaoDAO.existeContestacao(requerimento.getProcessoPae());
    }

    public List<DetalheDominioVO> motivosIndeferimentos() {
        if (detalheDominioMap == null) {
            detalheDominioMap = dominioHolder.manual(true).get().getDetalhes();
        }
        return new ArrayList<>(detalheDominioMap.values());
    }

    public boolean mostrarPerfilEntidadeEIndeferido() {
        return loginBean.hasPerfilEntidade() &&
                getRequerimento().getProcessoPae().getEstado() == EstadoProcessoEnum.INDEFERIDO;
    }

    private void insereMotivoDefaultDocumentoInvalido() {
        motivosIndeferimentos();
        if (motivo == null || motivo.isEmpty()) {
            for (Documento doc : requerimento.getProcessoPae().getDocumentos()) {
                if (BooleanUtils.isFalse(doc.isAprovado())) {
                    motivo = detalheDominioMap.get("DOCI").getDesignacao();
                    break;
                }
            }
        }
    }

    public boolean dataFimProcessoMenorQueVinteDias() {
        long diasFimParticipacao = PCJDateUtil.quantidadeDiasFimParticipacao(this.context.getParticipacao().getDataFimParticipacao());
        return diasFimParticipacao < 20;
    }

    public boolean permiteAdicionarInformacoesComplementares() {
        return !loginBean.hasPerfilEntidade() && !loginBean.hasPerfilComissao()
                && this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.EM_ANALISE)
                && isTabShowBtn();
    }

    public boolean permiteConsultarInformacoesComplementares() {
        return !loginBean.hasPerfilEntidade() && !this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.EM_ANALISE) &&
                !this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.POR_ANALISAR) && isTabShowBtn();
    }

    public boolean permiteConsultarInformacoesComplementaresReadOnly() {
        return !loginBean.hasPerfilEntidade() && !this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.EM_ANALISE) &&
                !this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.POR_ANALISAR) && isTabShowBtn() && existeInformacaoComplementar();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void validarInvalidarDocumento(Documento documento, Boolean validarDocumento) {
        Documento doc = new DocumentoPaeDAO(entityManager).findByIdentificadorFicheiro(documento.getIdentificadorFicheiro());
        doc.setAprovado(validarDocumento);
        doc.setMotivoInvalidez(motivoInvalidacaoDocumento);
        motivo = null;
        if (!validarDocumento) {
            insereMotivoDefaultDocumentoInvalido();
        }

        try {
            new DocumentoPaeDAO(entityManager).update(doc);
            documentoPaeService = new DocumentoPaeService(context);
            this.motivoInvalidacaoDocumento = null;
            RequestContext.getCurrentInstance().update("pcjConsultarProcessoPaeForm:pcjTabFragmentos:pcjDataTableDocumentos");
        } catch (DomainException e) {
            LOGGER.error("ERRO", e);
        }
    }

    public List<MotivoInvalidezDocumento> motivosInValidezDocumentos() {
        return Arrays.asList(DOCUMENTO_ILEGIVEL, DOCUMENTO_INCORRETO);
    }

    public void onTabChange(TabChangeEvent event) {
        this.tabAtiva = event.getTab().getId();
        pcjSubsystem.getPcjCache().setActiveIndexTabViewConsultarProcesso(((TabView) event.getSource()).getChildren().indexOf(event.getTab()));
    }

    private int obterIdadeCrianca() {
        if (this.requerimento.getProcessoPae().getCriancaEstrangeira() == null) {
            try {
                if (pessoaSingular == null) {
                    pessoaSingular = paeServiceLocator.getIdDelegate().getPessoaSingular(this.requerimento.getProcessoPae().getNissCrianca());
                }
                return PCJDateUtil.calcularIdade(new LocalDate(pessoaSingular.getDataNascimento()));
            } catch (IDException e) {
                e.printStackTrace();
            }
        }
        return PCJDateUtil.calcularIdade(new LocalDate(this.requerimento.getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getDataNascimento()));
    }

    public boolean mostraAbaDecisao() {
        for (Historico historico : historicos) {
            if (EstadoProcessoEnum.getStatusDecisao().contains(historico.getEstadoAtual())
                    || EstadoProcessoEnum.getStatusDecisao().contains(historico.getEstadoAntigo())) {
                return true;
            }
        }
        return false;
    }

    private boolean entidadeNaoPodeVisualizar(Historico historico) {
        return EstadoProcessoEnum.POR_ANALISAR.equals(historico.getEstadoAntigo()) && EstadoProcessoEnum.EM_ANALISE.equals(historico.getEstadoAtual())
                || (historico.getEstadoAtual().equals(historico.getEstadoAntigo())) && ATRIBUIR_NOVO_PRESIDENTE.equals(historico.getTipoLogEnum());
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void upload(FileUploadEvent event) {
        try {
            documentoPaeService.upload(event);
        } catch (PCJException e) {
            LOGGER.error("[ PCJ ] Erro ao fazer upload storage: ", e);
            JsfUtil.addErrorMessage(null, e.getMessage(), null);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void replace(FileUploadEvent event) {
        try {
            documentoPaeService.replace(event);
        } catch (PCJException e) {
            LOGGER.error("[ PCJ ] Erro ao fazer replace storage: ", e);
            JsfUtil.addErrorMessage(null, "Não foi possível subistituir o documento. Tente novamente mais tarde", null);
        }
    }

    public void clonarEducacao() {
        educacaoAlteracao = new Educacao();
        this.novoRequerimentoEducacao = criarRequerimentoPaeAlteracao(TipoRequerimentoEnum.ALTERACAO_EDUCACAO);
        this.novoRequerimentoEducacao.setIdadeCrianca(this.context.getRequerimento().getIdadeCrianca());
        context.setEducacao(educacaoAlteracao);
        limpandoListas(novoRequerimentoEducacao);
        if (getEducacao() != null) {
            popularParticipacaoEducacao(this.educacaoAlteracao, getEducacao(), novoRequerimentoEducacao, this.getRequerimento());

        }
        educacaoPaeService = new EducacaoPaeService(context);
        this.educacaoPaeService.setRequerimento(novoRequerimentoEducacao);

    }

    private void limpandoListas(RequerimentoPae novoRequerimentoEducacao) {
        novoRequerimentoEducacao.setHorariosEscolares(new ArrayList<HorarioEscolar>());
        novoRequerimentoEducacao.setHorariosParticipacoes(new ArrayList<HorarioParticipacao>());
        novoRequerimentoEducacao.setFerias(new HashSet<FeriasEscolar>());
    }

    public void clonarParticipacao() {

        novoRequerimentoParticipacao = salvarRequerimentoAlteracao(TipoRequerimentoEnum.ALTERACAO_PARTICIPACAO);
        service.setRequerimento(novoRequerimentoParticipacao);

    }

    private void preencherDadosSolicitacaoNovaParticipacao() {
        novaParticapacao = new Participacao();
        service.setNovaPartipacao(novaParticapacao);
        novaParticapacao.setDataFimParticipacao(getParticipacao().getDataFimParticipacao());
        novaParticapacao.setMotivoPrazoIncerto(getParticipacao().getMotivoPrazoIncerto());
        popularParticipacaoRenovacao(this.novaParticapacao, getParticipacao());
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void guardarDecisaoAssinado() {
        try {
            if (idFicheiro == null) {
                idFicheiro = documentoPaeService.uploadStorage(getFileEvent());
            }

            Comprovativo comprovativo = new ComprovativoDAO(entityManager).findByIdFicheiro(getComprovativoAssinatura().getIdentificadorFicheiro());

            comprovativo.setDataGeracao(new Date());
            comprovativo.setFicheiroAssinado(idFicheiro);
            new ComprovativoDAO(entityManager).update(comprovativo);

            new HistoricoDAO(entityManager).create(
                    new Historico.Builder()
                            .processoPae(getRequerimento().getProcessoPae())
                            .data(new Date())
                            .estadoAntigo(getRequerimento().getProcessoPae().getEstado())
                            .estadoAtual(getRequerimento().getProcessoPae().getEstado())
                            .log("Submissão do comprovativo assinado")
                            .responsavel(paeServiceLocator.getLoginBean().getNomeUtilizador())
                            .comprovativo(comprovativo)
                            .tipoLogEnum(null)
                            .build());
            setComprovativoAssinatura(comprovativo);
            getFilesSingle().clear();
            redirectProcesso();
            JsfMessageUtil.mostraMensagemSucesso("Submissão do comprovativo assinado realizado com sucesso.");
        } catch (Exception e) {
            LOGGER.error(e.toString());
        }
    }

    private void preencherComprovativoAssinado() {
        if (getComprovativoAssinatura() == null) {
            Comprovativo maior = null;
            for (Comprovativo c : getRequerimento().getComprovativos()) {
                if (getRequerimento().getTipoRequerimento().equals(TipoRequerimentoEnum.INICIAL)) {
                    if (maior == null || c.getId() > maior.getId()) {
                        maior = c;
                    }
                }
            }
            setComprovativoAssinatura(maior);
        }
        this.estadoComprovativoDecisao = preencherEstadoComprovativoDecisao();
    }

    public String preencherEstadoComprovativoDecisao() {
        if (getComprovativoAssinatura() == null) {
            return "";
        }
        if (hasPerfilEntidade() && getComprovativoAssinatura().getFicheiroAssinado() == null) {
            return AGUARDANDO_ASSINATURA;
        } else if (hasPerfilEntidade() && getComprovativoAssinatura().getFicheiroAssinado() != null) {
            return DECISAO_ASSINADA_EXTERNA;
        } else if (null != getComprovativoAssinatura() && hasPerfilGestor() && getComprovativoAssinatura().getFicheiroAssinado() == null) {
            return PENDENTE_ASSINATURA;
        }
        return DECISAO_ASSINADA;
    }

    public void consultarDocumentoComprovativoDecisao() {
        LoggingHelper.logEntrada();
        try {
            documentoPaeService.preview(getComprovativoAssinatura().getFicheiroAssinado());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LoggingHelper.logSaida(getComprovativoAssinatura().getFicheiroAssinado());
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void removerDocumentoComprovativoDecisao() {
        LoggingHelper.logEntrada();
        try {
            atualizarComprovativo(null);
            redirectProcesso();
            JsfMessageUtil.mostraMensagemSucesso("Comprovativo assinado removido com sucesso.");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LoggingHelper.logSaida(getComprovativoAssinatura().getFicheiroAssinado());
    }

    private void atualizarComprovativo(TipoComprovativoEnum tipo) throws DomainException {
        if (comprovativoAssinatura == null) {
            throw new DomainException("Comprovativo não encontrado");
        }
        Comprovativo comprovativo = new ComprovativoDAO(entityManager).findByIdFicheiro(getComprovativoAssinatura().getIdentificadorFicheiro());
        if (tipo != null) {
            comprovativo.setTipoComprovativo(tipo);
        }
        comprovativo.setFicheiroAssinado(null);
        comprovativo.setDataGeracao(new Date());
        new ComprovativoDAO(entityManager).update(comprovativo);
        setComprovativoAssinatura(comprovativo);
    }

    public boolean possuiAlteracaoAberta(TipoRequerimentoEnum tipoRequerimento) {
        return new RequerimentoPaeDAO(entityManager).findRequerimentoAbertoByTipo(this.getRequerimento().getProcessoPae().getId(), tipoRequerimento) != null;
    }

    public boolean alterouVigilante() {
        return !vigilanciaService.isPossuiVigilante() && new VigilanciaPaeDAO(entityManager).findByIdRequerimento(requerimento.getId()) == null;
    }

    public boolean alterouEducacao() {
        return educacaoPaeService.isEscolaridadeObrigatoria() || CollectionUtils.isNotEmpty(this.context.getRequerimento().getHorariosEscolares());
    }

    public Boolean possuiRequerimentosAbertos() {
        return CollectionUtils.isNotEmpty(getRequerimentosAbertos());
    }

    private void clonarHorarios() {
        horarioParticipacaoAlteracao = new HorarioParticipacaoService(context, false);
        novoRequerimentoAlteracaoHorarios = criarRequerimentoPaeAlteracao(TipoRequerimentoEnum.ALTERACAO_HORARIO);
        novoRequerimentoAlteracaoHorarios.setHorariosParticipacoes(new ArrayList<HorarioParticipacao>());
        novoRequerimentoAlteracaoHorarios.setIdadeCrianca(this.context.getRequerimento().getIdadeCrianca());
        popularHorarioParticipacaos(novoRequerimentoAlteracaoHorarios, this.context.getRequerimento());

    }

    private void clonarVigilante() {
        vigilanciaService = new VigilanciaService(context, true);
        vigilanteAlteracao = new VigilanciaPae();
        PessoaEstrangeira vigilanteEstrangeiro = new PessoaEstrangeira();
        Contacto contato = new Contacto();
        Morada morada = new Morada();
        vigilanteAlteracao.setContacto(contato);
        vigilanteEstrangeiro.setContacto(contato);
        vigilanteEstrangeiro.setMorada(morada);
        vigilanteAlteracao.setPessoaEstrangeira(vigilanteEstrangeiro);
        vigilanciaService.setVigilanciaNacional(vigilanteAlteracao);
        if (getVigilancia() != null && getVigilancia().getDataNascimento() != null) {
            vigilanteAlteracao.setDataNascimento(getVigilancia().getDataNascimento());
        }
        vigilanciaService.setRequerimento(null);
        vigilanciaService.setVigilanciaEstrangeira(vigilanteEstrangeiro);
        VigilanciaPae vigilante;
        vigilante = new VigilanciaPaeDAO(entityManager).findByIdRequerimento(requerimento.getId());
        if (vigilante != null) {
            if (vigilante.getPessoaEstrangeira() != null) {
                clonarDadosPessoais(vigilanteEstrangeiro, vigilante);
                clonarDadosContato(contato, vigilante);
                clonarDadosMorada(morada, vigilante);
                vigilanteEstrangeiro.setMorada(morada);
                vigilanteEstrangeiro.setContacto(contato);
                vigilanteAlteracao.setPessoaEstrangeira(vigilanteEstrangeiro);
                vigilanteAlteracao.setRelacaoVigilante(vigilante.getRelacaoVigilante());
            } else if (vigilante.getNissVigilante() != null) {
                vigilanteAlteracao.setNissVigilante(vigilante.getNissVigilante());
                vigilanteAlteracao.setRelacaoVigilante(vigilante.getRelacaoVigilante());
            }
            context.setVigilancia(vigilanteAlteracao);
        }

    }

    public Boolean mudouOpcaoVigilante() {
        return getVigilancia() == null && (vigilanciaService.getVigilanciaNacional().getId() == null &&
                vigilanciaService.getVigilanciaNacional().getPessoaEstrangeira() == null);
    }

    public void popularLocaisAtividade(RequerimentoPae requerimento, RequerimentoPae requerimentoValido) {
        requerimento.setLocaisAtividade(new ArrayList<Morada>());
        requerimento.getLocaisAtividade().addAll(requerimentoValido.getLocaisAtividade());
    }

    public boolean exibirMensagemAlertaPendenteAssinatura() {
        return consultaInterna() && getEstadoComprovativoDecisao().equals(PENDENTE_ASSINATURA);
    }

    public boolean existeInformacaoComplementar() {
        return informacaoComplementar != null && !informacaoComplementar.getInformacao().isEmpty();
    }

    public int getActiveIndexTabViewConsultarProcesso() {
        return activeIndexTabViewConsultarProcesso;
    }

    public void setActiveIndexTabViewConsultarProcesso(int activeIndexTabViewConsultarProcesso) {
        this.activeIndexTabViewConsultarProcesso = activeIndexTabViewConsultarProcesso;
    }

    public ComissaoPCJppp getComissaoPCJppp() {
        return comissaoPCJppp;
    }

    public void setComissaoPCJppp(ComissaoPCJppp comissaoPCJppp) {
        this.comissaoPCJppp = comissaoPCJppp;
    }

    public Comprovativo getComprovativoAssinatura() {
        return comprovativoAssinatura;
    }

    public void setComprovativoAssinatura(Comprovativo comprovativoAssinatura) {
        this.comprovativoAssinatura = comprovativoAssinatura;
    }

    @Override
    public Date getDataNascimento() throws IDException {
        Date dataNascimento;
        if (getRequerimento().getProcessoPae().getCriancaEstrangeira() == null) {
            dataNascimento = super.getDataNascimento(getRequerimento().getProcessoPae().getNissCrianca());
        } else {
            dataNascimento = getRequerimento().getProcessoPae().getCriancaEstrangeira().getDataNascimento();
        }
        return dataNascimento;
    }

    public Date getDataProlongamentoParticipacao() {
        return dataProlongamentoParticipacao;
    }

    public void setDataProlongamentoParticipacao(Date dataProlongamentoParticipacao) {
        this.dataProlongamentoParticipacao = dataProlongamentoParticipacao;
    }

    @Override
    public String getDescricaoTipoProcesso() {
        LoggingHelper.logEntrada(Level.DEBUG, getRequerimento());
        return getRequerimento().getProcessoPae().isComunicacao() ? TipoProcessoEnum.COMUNICACAO.getDescricao() :
                TipoProcessoEnum.AUTORIZACAO.getDescricao();
    }

    public DocumentoPaeService getDocumentoPaeService() {
        return documentoPaeService;
    }

    public void setDocumentoPaeService(DocumentoPaeService documentoPaeService) {
        this.documentoPaeService = documentoPaeService;
    }

    public String getDtNascimentoCrianca() {
        return PCJDateUtil.dataFormatada(pessoaSingular.getDataNascimento());
    }

    public Educacao getEducacaoAlteracao() {
        return educacaoAlteracao;
    }

    public void setEducacaoAlteracao(Educacao educacaoAlteracao) {
        this.educacaoAlteracao = educacaoAlteracao;
    }

    public EducacaoPaeService getEducacaoPaeService() {
        return educacaoPaeService;
    }

    public void setEducacaoPaeService(EducacaoPaeService educacaoPaeService) {
        this.educacaoPaeService = educacaoPaeService;
    }

    public String getEstado() {
        EstadoProcessoEnum estado = this.getRequerimento().getProcessoPae().getEstado();
        if (consultaInterna() && EstadoProcessoEnum.getStatusDecisao().contains(estado) && getEstadoComprovativoDecisao().equals(PENDENTE_ASSINATURA)) {
            estado = EstadoProcessoEnum.PENDENTE_ASSINATURA;
        } else if (consultaExterna() && EstadoProcessoEnum.POR_ANALISAR.equals(estado)) {
            estado = EstadoProcessoEnum.EM_ANALISE;
        }
        return estado.getDescricao();
    }

    // GETTER SETTER

    public String getEstadoComprovativoDecisao() {
        return estadoComprovativoDecisao;
    }

    public void setEstadoComprovativoDecisao(String estadoComprovativoDecisao) {
        this.estadoComprovativoDecisao = estadoComprovativoDecisao;
    }

    private EstadoProcessoEnum getEstadoProcesso(EstadoProcessoEnum deferido) {
        EstadoProcessoEnum estadoAnterior = requerimento.getProcessoPae().getEstado();
        requerimento.getProcessoPae().setEstado(deferido);
        return estadoAnterior;
    }

    public String getForm() {
        return "pcjConsultarProcessoPaeForm:pcjTabFragmentos";
    }

    public Historico getHistorico() {
        return historico;
    }

    public void setHistorico(Historico historico) {
        this.historico = historico;
    }

    public List<Historico> getHistoricos() {
        return historicos;
    }

    public void setHistoricos(List<Historico> historicos) {
        this.historicos = historicos;
    }

    private void getHistoricosByProcesso() {
        historicos = new HistoricoDAO(entityManager).findByProcesso(requerimento.getProcessoPae().getId());
        if (getLoginBean().hasPerfilEntidade()) {
            List<Historico> historicosEntidade = new ArrayList<>();
            for (Historico historico : getHistoricos()) {
                if (!entidadeNaoPodeVisualizar(historico)) {
                    historicosEntidade.add(historico);
                }
            }
            this.historicos = historicosEntidade;
        }
    }

    public String getHoraFormatada(Date horario) {
        return HorarioUtil.horarioFormatado(horario);
    }

    public HorarioParticipacaoService getHorarioParticipacao() {
        return horarioParticipacao;
    }

    public void setHorarioParticipacao(HorarioParticipacaoService horarioParticipacao) {
        this.horarioParticipacao = horarioParticipacao;
    }

    public HorarioParticipacaoService getHorarioParticipacaoAlteracao() {
        return horarioParticipacaoAlteracao;
    }

    public void setHorarioParticipacaoAlteracao(HorarioParticipacaoService horarioParticipacaoAlteracao) {
        this.horarioParticipacaoAlteracao = horarioParticipacaoAlteracao;
    }

    public Long getIdFicheiro() {
        return idFicheiro;
    }

    public void setIdFicheiro(Long idFicheiro) {
        this.idFicheiro = idFicheiro;
    }

    public Long getIdProcesso() {
        return idProcesso;
    }

    public void setIdProcesso(Long idProcesso) {
        this.idProcesso = idProcesso;
    }

    public IdentificacaoPaeService getIdentificacao() {
        return identificacao;
    }

    public void setIdentificacao(IdentificacaoPaeService identificacao) {
        this.identificacao = identificacao;
    }

    public String getIdentificacaoParaReport() {
        if (getRequerimento().getProcessoPae().getCriancaEstrangeira() == null) {
            return getRequerimento().getProcessoPae().getNissCrianca();
        } else {
            return getRequerimento().getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getNumeroIdentificacao();
        }
    }

    public InformacaoComplementar getInformacaoComplementar() {
        return informacaoComplementar;
    }

    public void setInformacaoComplementar(InformacaoComplementar informacaoComplementar) {
        this.informacaoComplementar = informacaoComplementar;
    }

    public String getInformacoesComplementares() {
        return informacaoComplementar != null ? informacaoComplementar.getInformacao() : " ";
    }

    public void setInformacoesComplementares(String informacoesComplementares) {
        this.informacoesComplementares = informacoesComplementares;
    }

    public PCJLoginBean getLoginBean() {
        return loginBean;
    }

    public void setLoginBean(PCJLoginBean loginBean) {
        this.loginBean = loginBean;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
        insereMotivoDefaultDocumentoInvalido();
    }

    public MotivoInvalidezDocumento getMotivoInvalidacaoDocumento() {
        return motivoInvalidacaoDocumento;
    }

    public void setMotivoInvalidacaoDocumento(MotivoInvalidezDocumento motivoInvalidacaoDocumento) {
        this.motivoInvalidacaoDocumento = motivoInvalidacaoDocumento;
    }

    public String getMotivoPrazoIncerto() {
        return motivoPrazoIncerto;
    }

    public void setMotivoPrazoIncerto(String motivoPrazoIncerto) {
        this.motivoPrazoIncerto = motivoPrazoIncerto;
    }

    @Override
    public String getNissDocumentoIdentificacao() {
        return this.getRequerimento().getProcessoPae().getCriancaEstrangeira() != null ?
                this.getRequerimento().getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getTipoDocumento().getDescricao() :
                this.getRequerimento().getProcessoPae().getNissCrianca();
    }

    public String getNomeComissao() {
        return nomeComissao;
    }

    public void setNomeComissao(String nomeComissao) {
        this.nomeComissao = nomeComissao;
    }

    public String getNomeGestor() {
        return nomeGestor;
    }

    public void setNomeGestor(String nomeGestor) {
        this.nomeGestor = nomeGestor;
    }

    public Participacao getNovaParticapacao() {
        return novaParticapacao;
    }

    public void setNovaParticapacao(Participacao novaParticapacao) {
        this.novaParticapacao = novaParticapacao;
    }

    public RequerimentoPae getNovoRequerimentoAlteracaoHorarios() {
        return novoRequerimentoAlteracaoHorarios;
    }

    public void setNovoRequerimentoAlteracaoHorarios(RequerimentoPae novoRequerimentoAlteracaoHorarios) {
        this.novoRequerimentoAlteracaoHorarios = novoRequerimentoAlteracaoHorarios;
    }

    public RequerimentoPae getNovoRequerimentoEducacao() {
        return novoRequerimentoEducacao;
    }

    public void setNovoRequerimentoEducacao(RequerimentoPae novoRequerimentoEducacao) {
        this.novoRequerimentoEducacao = novoRequerimentoEducacao;
    }

    @Override
    public String getNumeroIdentificacaoCivil() {
        String numeroIdentificacao = " ";
        if (getRequerimento().getProcessoPae().getCriancaEstrangeira() == null) {
            numeroIdentificacao = pessoaSingular.getNumeroIdentificacaoCivil();
        } else {
            numeroIdentificacao = getRequerimento().getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getNumeroIdentificacao();
        }
        return numeroIdentificacao;
    }

    public String getPageInfoMessages() {
        return messageProperties.paeInformativa01();
    }

    public ParticipacaoPaeService getParticipacaoPaeService() {
        return participacaoPaeService;
    }

    public void setParticipacaoPaeService(ParticipacaoPaeService participacaoPaeService) {
        this.participacaoPaeService = participacaoPaeService;
    }

    public ConsultarReavaliacaoBean getPcjConsultarReavaliacaoBean() {
        return pcjConsultarReavaliacaoBean;
    }

    public void setPcjConsultarReavaliacaoBean(ConsultarReavaliacaoBean pcjConsultarReavaliacaoBean) {
        this.pcjConsultarReavaliacaoBean = pcjConsultarReavaliacaoBean;
    }

    public PessoaSingularDTO getPessoaSingular() {
        return pessoaSingular;
    }

    public void setPessoaSingular(PessoaSingularDTO pessoaSingular) {
        this.pessoaSingular = pessoaSingular;
    }

    public DuracaoParticipacaoEnum getPrazoRenovacao() {
        return prazoRenovacao;
    }

    public void setPrazoRenovacao(DuracaoParticipacaoEnum prazoRenovacao) {
        this.prazoRenovacao = prazoRenovacao;
    }

    public String getProcess() {
        return "@this pcjConsultarProcessoPaeForm:pcjTabFragmentos";
    }

    public ProcessoPaeBean getProcessoPaeBean() {
        return processoPaeBean;
    }

    public void setProcessoPaeBean(ProcessoPaeBean processoPaeBean) {
        this.processoPaeBean = processoPaeBean;
    }

    private RequerimentoPae getRequerimentoConsulta() {
        RequerimentoPae requerimentoInicial = null;
        try {
            List<RequerimentoPae> todosRequerimentos =
                    new RequerimentoPaeDAO(entityManager).findRequerimentosTodos(idProcesso);
            for (RequerimentoPae req : todosRequerimentos) {

                if (TipoRequerimentoEnum.INICIAL.equals(req.getTipoRequerimento())) {
                    requerimentoInicial = req;
                    requerimentoEducacao = requerimentoEducacao == null ? req : requerimentoEducacao;
                    requerimentoVigilante = requerimentoVigilante == null ? req : requerimentoVigilante;
                    requerimentoParticipacao = requerimentoParticipacao == null ? req : requerimentoParticipacao;
                } else if (EstadoRequerimentoEnum.APROVADO.equals(req.getEstadoRequerimento())
                        && TipoRequerimentoEnum.ALTERACAO_EDUCACAO.equals(req.getTipoRequerimento())) {
                    requerimentoEducacao = req;
                } else if (EstadoRequerimentoEnum.APROVADO.equals(req.getEstadoRequerimento())
                        && TipoRequerimentoEnum.ALTERACAO_VIGILANCIA.equals(req.getTipoRequerimento())) {
                    requerimentoVigilante = req;
                } else if (EstadoRequerimentoEnum.APROVADO.equals(req.getEstadoRequerimento())
                        && TipoRequerimentoEnum.ALTERACAO_PARTICIPACAO.equals(req.getTipoRequerimento())) {
                    requerimentoParticipacao = req;
                }


            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return requerimentoInicial;

    }

    public RequerimentoPae getRequerimentoVigilante() {
        return requerimentoVigilante;
    }

    public void setRequerimentoVigilante(RequerimentoPae requerimentoVigilante) {
        this.requerimentoVigilante = requerimentoVigilante;
    }

    public List<RequerimentoPae> getRequerimentosAbertos() {
        return requerimentosAbertos;
    }

    public void setRequerimentosAbertos(List<RequerimentoPae> requerimentosAbertos) {
        this.requerimentosAbertos = requerimentosAbertos;
    }

    public ParticipacaoPaeService getService() {
        return service;
    }

    public void setService(ParticipacaoPaeService service) {
        this.service = service;
    }

    public EducacaoPaeService getServiceEducacao() {
        return educacaoPaeService;
    }

    public void setServiceEducacao(EducacaoPaeService educacaoPaeService) {
        this.educacaoPaeService = educacaoPaeService;
    }

    public String getUpdateLocalAtividadeBtn() {
        return "pcjParticipacaoLocalAtividade";
    }

    public VigilanciaService getVigilanciaService() {
        return vigilanciaService;
    }

    public void setVigilanciaService(VigilanciaService vigilanciaService) {
        this.vigilanciaService = vigilanciaService;
    }

    public VigilanciaPae getVigilanteAlteracao() {
        return vigilanteAlteracao;
    }

    public void setVigilanteAlteracao(VigilanciaPae vigilanteAlteracao) {
        this.vigilanteAlteracao = vigilanteAlteracao;
    }

    public boolean isAlteracaoEducacao() {
        return alteracaoEducacao;
    }

    public void setAlteracaoEducacao(boolean alteracaoEducacao) {
        this.alteracaoEducacao = alteracaoEducacao;
    }

    public boolean isAlteracaoHorarios() {
        return alteracaoHorarios;
    }

    public void setAlteracaoHorarios(boolean alteracaoHorarios) {
        this.alteracaoHorarios = alteracaoHorarios;
    }

    public boolean isAlteracaoVigilante() {
        return alteracaoVigilante;
    }

    public void setAlteracaoVigilante(boolean alteracaoVigilante) {
        this.alteracaoVigilante = alteracaoVigilante;
    }

    public boolean isPodeAnularProcesso() {
        return loginBean.hasPerfilEntidade() && (this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.EM_ANALISE) || this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.POR_ANALISAR));
    }

    public boolean isPodeArquivarProcesso() {
        return loginBean.hasPerfilEntidade() && this.getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.DEFERIDO) && (!isAlteracaoEducacao() || !isAlteracaoHorarios() || !isAlteracaoVigilante() || !participacaoPaeService.isAlteracaoParticipacao());
    }

    public Boolean isPodeRejeitarOuAprovar() {
        return isTabShowBtn() && !loginBean.hasPerfilEntidade()
                && getRequerimento().getProcessoPae().getEstado().equals(EstadoProcessoEnum.EM_ANALISE)
                && (getRequerimento().getProcessoPae().getElemento().getId().equals(loginBean.getPerfilDTO().getElementoDTO().getId())
                || paeServiceLocator.getLoginBean().hasPerfilPresidente() || paeServiceLocator.getLoginBean().hasPerfilSecretario());
    }

    public boolean isPodeRenovarProcesso() {
        return (isPodeArquivarProcesso() &&
                !this.getRequerimento().getProcessoPae().isComunicacao() &&
                FrequenciaParticipacaoEnum.VARIAS_ATUACOES.equals(getParticipacao().getFrequenciaParticipacao()) &&
                !possuiAlteracaoAberta(TipoRequerimentoEnum.RENOVACAO) &&
                !dataFimProcessoMenorQueVinteDias()) && (!isAlteracaoEducacao() || !isAlteracaoHorarios() || !isAlteracaoVigilante() || !participacaoPaeService.isAlteracaoParticipacao());
    }

    private boolean isTabShowBtn() {
        return !"pcjTabHistorico".equalsIgnoreCase(tabAtiva) && !"pcjTabDecisao".equalsIgnoreCase(tabAtiva);
    }
}

