package pt.segsocial.pcj.ppp.dto;

import pt.segsocial.pcj.ppp.enums.EstadoSuporteEnum;

import java.util.Date;

public class ResultadoPesquisaPedidoSuporteDTO {

    private Long id;

    private Date dataPedido;

    private String nomeElemento;

    private String nomeCpcj;

    private String assunto;

    private EstadoSuporteEnum estado;

    public Date getDataPedido() {
        return dataPedido;
    }

    public void setDataPedido(Date dataPedido) {
        this.dataPedido = dataPedido;
    }

    public String getNomeElemento() {
        return nomeElemento;
    }

    public void setNomeElemento(String nomeElemento) {
        this.nomeElemento = nomeElemento;
    }

    public String getNomeCpcj() {
        return nomeCpcj;
    }

    public void setNomeCpcj(String nomeCpcj) {
        this.nomeCpcj = nomeCpcj;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public EstadoSuporteEnum getEstado() {
        return estado;
    }

    public void setEstado(EstadoSuporteEnum estado) {
        this.estado = estado;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @Override
    public String toString() {
        return "ResultadoPesquisaPedidoSuporteDTO{" +
                "id=" + id +
                ", nomeElemento='" + nomeElemento + '\'' +
                ", nomeCpcj='" + nomeCpcj + '\'' +
                ", assunto='" + assunto + '\'' +
                ", estado=" + estado +
                ", dataPedido=" + dataPedido +
                '}';
    }
}
