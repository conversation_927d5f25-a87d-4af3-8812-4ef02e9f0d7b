package pt.segsocial.pcj.core.util;

import org.apache.commons.lang3.StringUtils;
import pt.segsocial.id.api.dto.MoradaDTO;
import pt.segsocial.id.api.dto.MoradaEstrangeiraDTO;
import pt.segsocial.id.api.dto.MoradaPortuguesaDTO;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.pae.jpa.entity.Morada;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public final class MoradaUtil implements Serializable {

    private MoradaUtil() {
    }

    public static String formatarMorada(MoradaDTO morada) {
        if (morada != null) {
            if (morada instanceof MoradaPortuguesaDTO) {
                MoradaPortuguesaDTO mp = (MoradaPortuguesaDTO) morada;
                return getString(mp.getArteria(), mp.getCodPostal(), mp.getLocalidade());
            } else if (morada instanceof MoradaEstrangeiraDTO) {
                MoradaEstrangeiraDTO me = (MoradaEstrangeiraDTO) morada;

                String endereco = "";
                if (StringUtils.isNotEmpty(me.getEnderecoLinha1())) {
                    endereco = me.getEnderecoLinha1();
                }
                if (StringUtils.isNotEmpty(me.getEnderecoLinha2())) {
                    endereco += " " + me.getEnderecoLinha2();
                }
                if (StringUtils.isNotEmpty(me.getEnderecoLinha3())) {
                    endereco += " " + me.getEnderecoLinha3();
                }

                String localidade = "";
                if (StringUtils.isNotEmpty(me.getCidade())) {
                    localidade = me.getCidade();
                }
                if (StringUtils.isNotEmpty(me.getRegiao())) {
                    localidade += me.getRegiao();
                }
                if (StringUtils.isNotEmpty(localidade)) {
                    endereco += ", " + localidade;
                }
                if (StringUtils.isNotEmpty(me.getCodigoPostal())) {
                    endereco += ", " + me.getCodigoPostal();
                }
                return endereco;
            }
        }
        return "";
    }

    public static String formatarMoradaEstrangeira(Morada morada, PCJServiceLocator paeServiceLocator) throws PCJException {
        if (morada != null) {
            return getStringMoradaExtrang(morada.getArteria(), morada.getCodigoPostal(),
                    morada.getTerritorioPCJ().getCodigoDistrito(), morada.getTerritorioPCJ().getCodigoConcelho(), morada.getTerritorioPCJ().getCodigoFreguesia(),
                    paeServiceLocator);
        }
        return "";
    }

    @NotNull
    private static String getString(String arteria, String codigoPostal, String localidade) {
        StringBuilder builder = new StringBuilder();
        if (arteria != null) {
            builder.append(arteria).append(", ");
        }
        if(codigoPostal != null) {
            builder.append(MoradaUtil.formatarCodigoPostal(codigoPostal));
        }
        if (localidade != null) {
            builder.append(localidade);
        }

        return builder.toString();
    }

    @NotNull
    private static String getStringMoradaExtrang(String arteria, String codigoPostal, Integer codDistrito, Integer codConselho, Integer codFreguesia, PCJServiceLocator paeServiceLocator) throws PCJException {
        StringBuilder builder = new StringBuilder();
        if (arteria != null) {
            builder.append(arteria).append(", ");
        }
        if (codigoPostal != null) {
            builder.append(codigoPostal).append(", ");
        }
        if (codFreguesia != null) {
            builder.append(getDescricaoMorada(codDistrito, codConselho, codFreguesia, paeServiceLocator)).append(", ");
        }
        if (codConselho != null) {
            builder.append(getDescricaoMorada(codDistrito, codConselho, 0, paeServiceLocator)).append(", ");
        }
        if (codDistrito != null) {
            builder.append(getDescricaoMorada(codDistrito, 0, 0, paeServiceLocator));
        }

        return builder.toString();
    }

    private static String getDescricaoMorada(Integer codigoDistrito, Integer codigoConselho, Integer codigoFreguesia, PCJServiceLocator paeServiceLocator) throws PCJException {
        return paeServiceLocator.getGvrDelegate().getDivisaoAdministrativa(codigoDistrito, codigoConselho , codigoFreguesia);
    }

    public static StringBuilder formatarCodigoPostal(String codPostal) {
        StringBuilder builder = new StringBuilder(codPostal);
        if (codPostal.length() > 3) {
            return builder.insert( builder.length()-3, "-");
        }
        return builder.append(" ");
    }

    public static String formatarMoradaEstrangeiraComNiss(MoradaEstrangeiraDTO moradaEstrangeiraDTO) {
        StringBuilder builder = new StringBuilder();
        if (moradaEstrangeiraDTO.getEnderecoLinha1() != null) {
            builder.append(moradaEstrangeiraDTO.getEnderecoLinha1()).append(", ");
        }

        if (moradaEstrangeiraDTO.getCidade() != null) {
            builder.append(moradaEstrangeiraDTO.getCidade()).append(", ");
        }

        if (moradaEstrangeiraDTO.getCodigoPostal() != null) {
            builder.append(MoradaUtil.formatarCodigoPostal(moradaEstrangeiraDTO.getCodigoPostal())).append(", ");
        }

        if (moradaEstrangeiraDTO.getEstadoMorada() != null) {
            builder.append(moradaEstrangeiraDTO.getEstadoMorada()).append(", ");
        }

        if (moradaEstrangeiraDTO.getRegiao() != null) {
            builder.append(moradaEstrangeiraDTO.getRegiao()).append(", ");
        }

        if (moradaEstrangeiraDTO.getPaisMorada() != null) {
            builder.append(moradaEstrangeiraDTO.getPaisMorada());
        }

        return builder.toString();
    }
}
