package pt.segsocial.pcj.ppp.dto.ata.restrita;

import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;

public class ConvidadoPresencaDTO {

    private Long id;
    private String nome;
    private String cargo;
    private String entidade;
    private String codigoEntidadeParticipante;
    private String detalheEntidadeParticipante;
    private Boolean assinatura;
    private ComissaoPCJDTO comissao;

    public ConvidadoPresencaDTO(Long id, String nome, String cargo, String codigoEntidadeParticipante, String detalheEntidadeParticipante) {
        this.id = id;
        this.nome = nome;
        this.cargo = cargo;
        this.codigoEntidadeParticipante = codigoEntidadeParticipante;
        this.detalheEntidadeParticipante = detalheEntidadeParticipante;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ConvidadoPresencaDTO() { }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(Boolean assinatura) {
        this.assinatura = assinatura;
    }

    public String getCodigoEntidadeParticipante() {
        return codigoEntidadeParticipante;
    }

    public void setCodigoEntidadeParticipante(String codigoEntidadeParticipante) {
        this.codigoEntidadeParticipante = codigoEntidadeParticipante;
    }

    public String getDetalheEntidadeParticipante() {
        return detalheEntidadeParticipante;
    }

    public void setDetalheEntidadeParticipante(String detalheEntidadeParticipante) {
        this.detalheEntidadeParticipante = detalheEntidadeParticipante;
    }
}
