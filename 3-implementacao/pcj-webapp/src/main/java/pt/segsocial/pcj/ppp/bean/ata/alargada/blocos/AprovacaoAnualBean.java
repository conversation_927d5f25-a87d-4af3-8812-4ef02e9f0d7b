package pt.segsocial.pcj.ppp.bean.ata.alargada.blocos;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.ppp.dto.ata.alargada.AtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAprovacaoAnualDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAssuntosGeraisDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.OrdenacaoAtaAlargadaDTO;

public class AprovacaoAnualBean extends BlocoBaseBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(AprovacaoAnualBean.class);

    private AtaAlargadaDTO ataAlargadaDTO;

    private BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO;

    public AprovacaoAnualBean(AtaAlargadaDTO ataAlargadaDTO, BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
        this.blocoAprovacaoAnualDTO = blocoAprovacaoAnualDTO;
        init();
    }

    public void init() {
        getAtaAlargadaDTO().setOrdenacaoAtaAlargadaDTO(new OrdenacaoAtaAlargadaDTO());
        getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setBlocoAssuntoGeral(new BlocoAssuntosGeraisDTO());
    }

    public AtaAlargadaDTO getAtaAlargadaDTO() {
        return ataAlargadaDTO;
    }

    public void setAtaAlargadaDTO(AtaAlargadaDTO ataAlargadaDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
    }

    public BlocoAprovacaoAnualDTO getBlocoAprovacaoAnualDTO() {
        return blocoAprovacaoAnualDTO;
    }

    public void setBlocoAprovacaoAnualDTO(BlocoAprovacaoAnualDTO blocoAprovacaoAnualDTO) {
        this.blocoAprovacaoAnualDTO = blocoAprovacaoAnualDTO;
    }
}
