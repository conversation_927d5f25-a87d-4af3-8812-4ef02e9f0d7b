package pt.segsocial.pcj.ppp.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.jpa.entity.PontoTrabalhoPCJ;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import java.util.Collections;
import java.util.List;

public class PontoTrabalhoPCJDAO extends DAO<PontoTrabalhoPCJ, Long> {

    @Inject
    public PontoTrabalhoPCJDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<PontoTrabalhoPCJ> listarPorIdReuniao(Long idReuniao) {
        try {
            TypedQuery<PontoTrabalhoPCJ> query = getEntityManager().createQuery("Select p from PontoTrabalhoPCJ p where p.convocatoriaAlargada.id = :idReuniao", PontoTrabalhoPCJ.class);
            query.setParameter("idReuniao", idReuniao);

            return query.getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }
}
