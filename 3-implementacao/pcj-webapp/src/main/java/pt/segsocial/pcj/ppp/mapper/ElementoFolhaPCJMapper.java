package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ElementoFolhaPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.ElementoFolhaPCJ;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = ElementoFolhaIdMapper.class)
public interface ElementoFolhaPCJMapper {

    ElementoFolhaPCJDTO toDTO(ElementoFolhaPCJ elementoFolhaPCJ);

    ElementoFolhaPCJ toEntity(ElementoFolhaPCJDTO elementoFolhaPCJDTO);

}
