package pt.segsocial.pcj.ppp.enums;

public enum TipoRegistoLogEnum {

    LOGIN("LOGIN"),
    ACESSO("0069"),
    OPERACAO("0070"),
    REGISTO_ELEMENTO("Registo de elemento com cartão número %s"),
    ALTERACAO_ELEMENTO("Alteração de elemento com cartão número %s"),
    ELIMINAR_ELEMENTO("Eliminação do elemento com cartão número %s"),
    ATIVACAO_ELEMENTO("Ativação do elemento com cartão número %s"),
    CESSAR_ELEMENTO("Cessação do elemento com cartão número %s"),
    REGISTO_UTILIZADOR("Registo de utilizador NISS %s"),
    ELIMINAR_UTILIZADOR("Eliminação do utilizador com cartão número %s"),
    REGISTO_CPCJ("Registo de CPCJ %s"),
    ALTERACAO_CPCJ("Alteração da CPCJ %s"),
    COMPARAR_VERSAO_ELEMENTO("Comparação de versões do elemento com cartão número %s"),

    COMPARAR_VERSAO_UTILIZADOR("Comparação de versões do utilizador"),
    REGISTO_AGRUPAMENTO("Registo do agrupamento de CPCJ %s"),
    ALTERACAO_AGRUPAMENTO(" Alteração do agrupamento de CPCJ  %s"),
    ELIMINACAO_AGRUPAMENTO("Eliminação do agrupamento de CPCJ %s"),
    REGISTO_SALA_CPCJ("Registo de sala %s da CPCJ %s"),
    ALTERACAO_SALA_CPCJ("Alteração de sala %s da CPCJ %s"),
    ELIMINACAO_SALA_CPCJ("Eliminação de sala %s da CPCJ %s"),
    ATIVACAO_UTLIZADOR("Ativação do utilizador NISS %s"),
    CESSACAO_UTILIZADOR("Cessação do utilizador NISS %s"),
    ASSOCIAR_GESTOR("Associação do gestor %s ao processo %s"),
    REGISTO_DILIGENCIA("Registo de diligencia ao processo %s"),
    ALTERACAO_DILIGENCIA("Alteração de diligencia ao processo %s"),
    REGISTO_ENTREVISTA("Registo de entrevista ao processo %s"),
    TERMINO_PROCESSO("Termino do processo do PAE %s"),
    AVALIACAO_PROCESSO("Avaliação do processo %s"),
    REVOGAR_PROCESSO("Revogação do processo %s"),
    REAVALIACAO_PROCESSO("Avaliação do processo %s"),
    CONSULTAR_PROCESSO("Consulta do processo %s"),
    SOLICITACAO_SUPORTE("Solicitação de suporte à comissão nacional"),

    SCHED_INATIVAR_CPCJ("Inativação da CPCJ através do scheduler de Inativar CPCJ."),
    REGISTO_EVENTO("Registo de evento do tipo %s da CPCJ %s."),
    ALTERACAO_EVENTO("Alteração de evento do tipo %s da CPCJ %s."),
    CANCELAR_EVENTO("Cancelamento de evento do tipo %s da CPCJ %s.");


    private final String descricao;
    TipoRegistoLogEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }
}
