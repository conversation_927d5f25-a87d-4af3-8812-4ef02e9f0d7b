package pt.segsocial.pcj.ppp.dto;

import java.io.Serializable;

public class HomepageDTO implements Serializable {

    private String valorTotal;
    private String totalAnalisados;
    private String totalArquivados;
    private String totalSemGestor;
    private String totalEmAnalise;
    private String totalDeferidoPendencia;

    public String getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getTotalAnalisados() {
        return totalAnalisados;
    }

    public void setTotalAnalisados(String totalAnalisados) {
        this.totalAnalisados = totalAnalisados;
    }

    public String getTotalArquivados() {
        return totalArquivados;
    }

    public void setTotalArquivados(String totalArquivados) {
        this.totalArquivados = totalArquivados;
    }

    public String getTotalSemGestor() {
        return totalSemGestor;
    }

    public void setTotalSemGestor(String totalSemGestor) {
        this.totalSemGestor = totalSemGestor;
    }

    public String getTotalEmAnalise() {
        return totalEmAnalise;
    }

    public void setTotalEmAnalise(String totalEmAnalise) {
        this.totalEmAnalise = totalEmAnalise;
    }

    public String getTotalDeferidoPendencia() {
        return totalDeferidoPendencia;
    }

    public void setTotalDeferidoPendencia(String totalDeferidoPendencia) {
        this.totalDeferidoPendencia = totalDeferidoPendencia;
    }

    @Override
    public String toString() {
        return "HomepageDTO{" +
                "valorTotal='" + valorTotal + '\'' +
                ", totalAnalisados='" + totalAnalisados + '\'' +
                ", totalArquivados='" + totalArquivados + '\'' +
                ", totalSemGestor='" + totalSemGestor + '\'' +
                ", totalEmAnalise='" + totalEmAnalise + '\'' +
                ", totalDeferidoPendencia='" + totalDeferidoPendencia + '\'' +
                '}';
    }
}
