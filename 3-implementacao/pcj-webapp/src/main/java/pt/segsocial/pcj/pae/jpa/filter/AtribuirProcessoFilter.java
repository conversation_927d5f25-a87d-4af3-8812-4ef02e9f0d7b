package pt.segsocial.pcj.pae.jpa.filter;

import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;

import java.io.Serializable;

public class AtribuirProcessoFilter implements Serializable {

    private static final long serialVersionUID = -4512348112744807493L;

    private String numeroProcesso;
    private EstadoProcessoEnum estado;
    private Long codigoGestor;
    private ComissaoPCJppp comissaoPCJ;
    private Long codigoServico;
    private Long nissEntidade;

    public String getNumeroProcesso() {
        return numeroProcesso;
    }

    public void setNumeroProcesso(String numeroProcesso) {
        this.numeroProcesso = numeroProcesso;
    }

    public EstadoProcessoEnum getEstado() {
        return estado;
    }

    public void setEstado(EstadoProcessoEnum estado) {
        this.estado = estado;
    }

    public Long getCodigoGestor() {
        return codigoGestor;
    }

    public void setCodigoGestor(Long codigoGestor) {
        this.codigoGestor = codigoGestor;
    }

    public ComissaoPCJppp getComissaoPCJ() {
        return comissaoPCJ;
    }

    public void setComissaoPCJ(ComissaoPCJppp comissaoPCJ) {
        this.comissaoPCJ = comissaoPCJ;
    }

    public Long getCodigoServico() {
        return codigoServico;
    }

    public void setCodigoServico(Long codigoServico) {
        this.codigoServico = codigoServico;
    }

    public Long getNissEntidade() {
        return nissEntidade;
    }

    public void setNissEntidade(Long nissEntidade) {
        this.nissEntidade = nissEntidade;
    }

    public void limpar() {
        this.setNumeroProcesso(null);
        this.setCodigoGestor(null);
    }

    @Override
    public String toString() {
        return "AtribuirProcessoFilter{" +
                "numeroProcesso='" + numeroProcesso + '\'' +
                ", estado=" + estado +
                ", codigoGestor=" + codigoGestor +
                ", codigoServico=" + codigoServico +
                ", nissEntidade=" + nissEntidade +
                '}';
    }
}
