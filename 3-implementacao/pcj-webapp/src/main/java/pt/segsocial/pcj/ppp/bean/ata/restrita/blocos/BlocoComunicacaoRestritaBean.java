package pt.segsocial.pcj.ppp.bean.ata.restrita.blocos;


import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.CriancaJovemStepBean;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.DescricaoSituacaoStepBean;
import pt.segsocial.pcj.ppp.bean.comunicacao.wizard.ParticipanteStepBean;
import pt.segsocial.pcj.ppp.dto.ata.restrita.AtaRestritaDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.BlocoRestritaComunicacaoDTO;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.inject.Inject;
import javax.inject.Named;

@Named(value = "pcjBlocoComunicacaoRestritaBean")
@Stateful(name = "pcjBlocoComunicacaoRestritaBean")
@LocalBean
@ViewAccessScoped
public class BlocoComunicacaoRestritaBean extends BlocoBaseRestritaBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(BlocoComunicacaoRestritaBean.class);

    private BlocoRestritaComunicacaoDTO blocoComunicacaoRestritaDTO;

    private AtaRestritaDTO ataRestritaDTO;

    private String labelComunicacao;

    @Inject
    private CriancaJovemStepBean criancaJovemStepBean;

    @Inject
    private ParticipanteStepBean participanteStepBean;

    @Inject
    private DescricaoSituacaoStepBean descricaoSituacaoStepBean;

    public BlocoComunicacaoRestritaBean() {
        super();
    }

    public BlocoComunicacaoRestritaBean(AtaRestritaDTO ataRestritaDTO, BlocoRestritaComunicacaoDTO dto) {
        this.ataRestritaDTO = ataRestritaDTO;
        this.blocoComunicacaoRestritaDTO = dto;
        init();
    }

    public void init() {
        if(getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().getId() == null) {
            getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().setBlocoComunicacaoRestrita(blocoComunicacaoRestritaDTO);
        } else {
            blocoComunicacaoRestritaDTO = getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().getBlocoComunicacaoRestrita();
        }

    }

    public AtaRestritaDTO getAtaRestritaDTO() {
        return ataRestritaDTO;
    }

    public void setAtaRestritaDTO(AtaRestritaDTO ataRestritaDTO) {
        this.ataRestritaDTO = ataRestritaDTO;
    }

    public BlocoRestritaComunicacaoDTO getBlocoComunicacaoRestritaDTO() {
        blocoComunicacaoRestritaDTO = getAtaRestritaDTO().getOrdenacaoAtaRestritaDTO().getBlocoComunicacaoRestrita();
        return blocoComunicacaoRestritaDTO;
    }

    public void setBlocoComunicacaoRestritaDTO(BlocoRestritaComunicacaoDTO blocoComunicacaoRestritaDTO) {
        this.blocoComunicacaoRestritaDTO = blocoComunicacaoRestritaDTO;
    }

    public String getLabelComunicacao() {
        return labelComunicacao;
    }

    public void setLabelComunicacao(String labelComunicacao) {
        this.labelComunicacao = labelComunicacao;
    }
}
