package pt.segsocial.pcj.ppp.dto.ata.restrita;

public class BlocoRestritaComunicacaoDTO extends BlocoBaseRestritaDTO {

    private String comunicacao;

    private Long idComunicacao;

    private String numeroComunicacao;

    public BlocoRestritaComunicacaoDTO() { }

    public String getComunicacao() {
        return comunicacao;
    }

    public void setComunicacao(String comunicacao) {
        this.comunicacao = comunicacao;
    }

    public Long getIdComunicacao() {
        return idComunicacao;
    }

    public void setIdComunicacao(Long idComunicacao) {
        this.idComunicacao = idComunicacao;
    }

    public String getNumeroComunicacao() {
        return numeroComunicacao;
    }

    public void setNumeroComunicacao(String numeroComunicacao) {
        this.numeroComunicacao = numeroComunicacao;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String comunicacao;
        private Long idComunicacao;
        private String numeroComunicacao;

        public Builder comunicacao(String comunicacao) {
            this.comunicacao = comunicacao;
            return this;
        }

        public Builder idComunicacao(Long idComunicacao) {
            this.idComunicacao = idComunicacao;
            return this;
        }

        public Builder numeroComunicacao(String numeroComunicacao) {
            this.numeroComunicacao = numeroComunicacao;
            return this;
        }

        public BlocoRestritaComunicacaoDTO build() {
            BlocoRestritaComunicacaoDTO blocoRestritaComunicacaoDTO = new BlocoRestritaComunicacaoDTO();
            blocoRestritaComunicacaoDTO.comunicacao = this.comunicacao;
            blocoRestritaComunicacaoDTO.idComunicacao = this.idComunicacao;
            blocoRestritaComunicacaoDTO.numeroComunicacao = this.numeroComunicacao;
            return blocoRestritaComunicacaoDTO;
        }
    }
}
