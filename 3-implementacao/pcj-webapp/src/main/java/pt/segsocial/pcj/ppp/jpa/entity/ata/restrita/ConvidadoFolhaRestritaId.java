package pt.segsocial.pcj.ppp.jpa.entity.ata.restrita;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class ConvidadoFolhaRestritaId implements Serializable {

    @Column(name = "ID_CONVIDADO_PCJ")
    private Long convidadoId;

    @Column(name = "ID_ATA_RESTRITA_PCJ")
    private Long ataRestritaId;

    public ConvidadoFolhaRestritaId() {}

    public ConvidadoFolhaRestritaId(Long convidadoId, Long ataRestritaId) {
        this.convidadoId = convidadoId;
        this.ataRestritaId = ataRestritaId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConvidadoFolhaRestritaId that = (ConvidadoFolhaRestritaId) o;
        return Objects.equals(convidadoId, that.convidadoId) && Objects.equals(ataRestritaId, that.ataRestritaId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(convidadoId, ataRestritaId);
    }
}
