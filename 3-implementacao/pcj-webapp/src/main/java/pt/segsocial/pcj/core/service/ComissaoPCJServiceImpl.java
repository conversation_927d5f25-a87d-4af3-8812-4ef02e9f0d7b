package pt.segsocial.pcj.core.service;

import pt.segsocial.pcj.core.dao.ComissaoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.io.Serializable;

@RequestScoped
public class ComissaoPCJServiceImpl implements Serializable {

    @Inject
    private ComissaoPCJDAO comissaoPCJDAO;

    public ComissaoPCJppp buscaComissaoPorId(Long id) {
        return comissaoPCJDAO.find(id);
    }
}
