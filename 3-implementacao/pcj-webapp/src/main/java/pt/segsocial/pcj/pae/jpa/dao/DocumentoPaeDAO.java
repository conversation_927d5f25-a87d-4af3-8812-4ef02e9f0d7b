package pt.segsocial.pcj.pae.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.pae.jpa.entity.Documento;
import pt.segsocial.pcj.pae.enums.TipoDocumentoEnum;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class DocumentoPaeDAO extends DAO<Documento, Long> {

    public DocumentoPaeDAO(EntityManager entityManager) {
    	super(entityManager);
    }
    
    public List<Documento> findByIdProcessoPae(Long idProcessoPae) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(Documento.FIND_BY_ID_PROCESSO, Documento.class)
                    .setParameter("idProcessoPae", idProcessoPae)
                    .getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }

    public List<Documento> findByDocumentosDeferidosByProcesso(Long idProcessoPae) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(Documento.FIND_DOCUMENTOS_APROVADOS_BY_ID_PROCESSO, Documento.class)
                    .setParameter("idProcessoPae", idProcessoPae)
                    .getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }

    public Documento findByIdentificadorFicheiro(Long identificadorFicheiro) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(Documento.FIND_BY_IDENTIFICADOR_FICHEIRO, Documento.class)
                    .setParameter("identificadorFicheiro", identificadorFicheiro)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public void apagarDocumentosSemRequerimento(Long idProcessoPae, List<TipoDocumentoEnum> tipoDocumentos) {
        try {
            this.getEntityManager()
                    .createQuery("DELETE FROM Documento doc WHERE doc.processo.id = :idProcessoPae and doc.aprovado is null and doc.tipoDocumento in (:tipoDocumentos)")
                    .setParameter("idProcessoPae", idProcessoPae)
                    .setParameter("tipoDocumentos", tipoDocumentos).executeUpdate();
        } catch (NoResultException e) {
            e.printStackTrace();
        }
    }

    public void apagarDocumentosComRequerimento(Long idProcessoPae, List<TipoDocumentoEnum> tipoDocumentos, List<Long> ids) {
        try {
            this.getEntityManager()
                    .createQuery("DELETE FROM Documento doc WHERE doc.processo.id = :idProcessoPae and doc.aprovado = true and doc.tipoDocumento in (:tipoDocumentos) and doc.id not in (:ids)")
                    .setParameter("idProcessoPae", idProcessoPae)
                    .setParameter("tipoDocumentos", tipoDocumentos)
                    .setParameter("ids", ids).executeUpdate();
        } catch (NoResultException e) {
            e.printStackTrace();
        }
    }

    public List<Long> findIdDocumentosAlteracaoParaExlcuirStorage(Long idProcessoPae, List<TipoDocumentoEnum> tipoDocumentos) {
        try {
            return this.getEntityManager()
                    .createQuery("Select doc.identificadorFicheiro FROM Documento doc WHERE doc.processo.id = :idProcessoPae and doc.aprovado is null and doc.tipoDocumento in (:tipoDocumentos)")
                    .setParameter("idProcessoPae", idProcessoPae)
                    .setParameter("tipoDocumentos", tipoDocumentos).getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }


    public List<Documento> findDocumentosAlteracao(Long idProcessoPae, List<TipoDocumentoEnum> tipoDocumentos) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(Documento.FIND_DOCUMENTOS_ALTERACAO, Documento.class)
                    .setParameter("idProcessoPae", idProcessoPae)
                    .setParameter("tipoDocumentos", tipoDocumentos).getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }

    public List<Documento> findByDataUploadAndProlongadoNull(Date dataUpload) {
        try {
            return this.getEntityManager()
                    .createNamedQuery(Documento.FIND_BY_DATA_UPLOAD_AND_PROLONGADO_NULL, Documento.class)
                    .setParameter("dataUpload", dataUpload)
                    .getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }
}
