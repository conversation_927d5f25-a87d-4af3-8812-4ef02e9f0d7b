package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.*;
import pt.segsocial.pcj.ppp.dto.ReuniaoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoRestrita;

@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReuniaoRestritaMapper {

    @Mappings({
            @Mapping(target = "outraPeriodicidade", source = "outroPeriodicidadeReuniaoRestrita"),
            @Mapping(target = "periodicidadeReuniaoRestrita", source = "periodicidadeReuniaoRestrita")
    })
    ReuniaoRestrita toEntidade(ReuniaoDTO reuniaoRestritaDTO);

    @Mappings({
            @Mapping(target = "outraPeriodicidade", source = "outroPeriodicidadeReuniaoRestrita"),
            @Mapping(target = "periodicidadeReuniaoRestrita", source = "periodicidadeReuniaoRestrita")
    })
    void atualizarReuniaoRestritaFromDTO(ReuniaoDTO reuniaoDTO, @MappingTarget ReuniaoRestrita reuniaoRestrita);

}
