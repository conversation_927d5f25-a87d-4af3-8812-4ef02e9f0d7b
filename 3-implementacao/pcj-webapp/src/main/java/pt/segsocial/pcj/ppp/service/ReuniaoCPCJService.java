package pt.segsocial.pcj.ppp.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.MensagemHandler;
import pt.segsocial.pcj.core.service.RegistoLogService;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.dto.*;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaParaRegistoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.FiltroPesquisaComunicacaoRegistoEvento;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.log.RegistoLogDTO;
import pt.segsocial.pcj.ppp.enums.TipoLocalReuniao;
import pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum;
import pt.segsocial.pcj.ppp.jpa.dao.ConvocatoriaAlargadaPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.ReuniaoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.entity.*;
import pt.segsocial.pcj.ppp.mapper.*;
import pt.segsocial.pcj.core.util.PCJDateUtil;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceException;
import java.util.*;

import static pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum.*;
import static pt.segsocial.pcj.core.util.PCJDateUtil.isDataInicialAmanha;

@RequestScoped
public class ReuniaoCPCJService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReuniaoCPCJService.class);

    @Inject @ReuniaoMapperQualifier
    private ReuniaoPCJMapper reuniaoPCJMapper;

    @Inject @ConvocatoriaMapperQualifier
    private ConvocatoriaReuniaoPCJMapper convocatoriaReuniaoPCJMapper;

    @Inject
    private PontoTrabalhoPCJMapper pontoTrabalhoPCJMapper;

    @Inject
    private SalaReuniaoService salaReuniaoService;

    @Inject
    private MembroComissaoService membroComissaoService;

    @Inject
    private LocalReuniaoPCJService localReuniaoPCJService;

    @Inject
    private RegistoLogService registoLogService;

    @Inject
    private PontoTrabalhoPCJService pontoTrabalhoPCJService;

    @Inject
    private PCJServiceDomain pcjServiceDomain;

    @Inject
    private ElementoService elementoService;

    @Inject
    private ReuniaoPCJDAO reuniaoPCJDAO;

    @Inject
    private ConvocatoriaAlargadaPCJDAO convocatoriaAlargadaPCJDAO;

    @Inject
    private MensagemHandler mensagemHandler;

    @Inject
    private BlocoComunicacaoRestritaService blocoComunicacaoRestritaService;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarOuAlterarReuniao(ReuniaoEventoDTO reuniaoEventoDTO, PerfilDTO perfilDTO, TipoLocalReuniao tipoReuniaoSelecionado) throws DomainException {
        LOGGER.info("Salvando novo evento: {}", reuniaoEventoDTO);
        try {
            ReuniaoPCJ reuniaoPCJ = mapearReuniaoPorLocal(reuniaoEventoDTO, tipoReuniaoSelecionado);
            if (reuniaoEventoDTO.reuniaoComissaoAlargada()) {
                processarReuniaoConvocatoriaAlargada(reuniaoPCJ, reuniaoEventoDTO.getPontosTrabalho());
            } else if(reuniaoEventoDTO.reuniaoComissaoRestrita()) {
                processarReuniaoComunicacaoRestrita(reuniaoPCJ, null, reuniaoEventoDTO.getBlocosComunicacaoRestrita());
            }else{
                processarReuniaoGeral(reuniaoPCJ, reuniaoEventoDTO.getMembrosComissao());
            }
            registarLog(reuniaoPCJ, perfilDTO, reuniaoEventoDTO.getId() == null ? REGISTO_EVENTO : ALTERACAO_EVENTO);
            enviarMensagemLembrete(reuniaoPCJ);
            LOGGER.info("Evento registrado com sucesso");
        } catch (PersistenceException e) {
            tratarErroPersistencia(e, "Erro ao salvar/alterar evento");
        } catch (Exception e) {
            tratarErroGenerico(e, "Erro inesperado ao salvar/alterar evento");
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarReuniaoRestritaComunicacao(ReuniaoEventoDTO reuniaoEventoDTO, PerfilDTO perfilDTO, TipoLocalReuniao tipoReuniaoSelecionado) throws DomainException {
        LOGGER.info("Salvando novo evento: {}", reuniaoEventoDTO);
        try {
            ReuniaoPCJ reuniaoPCJ = mapearReuniaoToEntidade(reuniaoEventoDTO, tipoReuniaoSelecionado, reuniaoPCJMapper);
            processarReuniaoComunicacaoRestrita(reuniaoPCJ, reuniaoEventoDTO.getMembrosComissao(), reuniaoEventoDTO.getBlocosComunicacaoRestrita());
            registarLog(reuniaoPCJ, perfilDTO, reuniaoEventoDTO.getId() == null ? REGISTO_EVENTO : ALTERACAO_EVENTO);
            enviarMensagemLembrete(reuniaoPCJ);
            LOGGER.info("Evento da restrita(comunicacao) registrado com sucesso");
        } catch (PersistenceException e) {
            tratarErroPersistencia(e, "Erro ao salvar/alterar evento");
        } catch (Exception e) {
            tratarErroGenerico(e, "Erro inesperado ao salvar/alterar evento");
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void cancelarReuniao(Long idReuniao, PerfilDTO perfilDTO) throws DomainException {
        try {
            ReuniaoPCJ reuniaoPCJ = reuniaoPCJDAO.find(idReuniao);
            if(reuniaoPCJ.isReuniaoRestrita()) {
                LoggingHelper.logEntrada(LOGGER, "Reuniao restrita. Verificando se existem comunicacoes vinculadas.");
                boolean existemComunicacoesVinculadas = blocoComunicacaoRestritaService.existemComunicacoesVinculadas(reuniaoPCJ);
                if(existemComunicacoesVinculadas) {
                    LoggingHelper.logEntrada(LOGGER, "Existem comunicacoes vinculadas a esta reuniao. Cancelando blocos de comunicacao restrita.");
                    cancelarBlocosRestrita(reuniaoPCJ);
                }
            }
            reuniaoPCJ.setAtivo(false);
            reuniaoPCJDAO.update(reuniaoPCJ);
            registarLog(reuniaoPCJ, perfilDTO, CANCELAR_EVENTO);
            enviarMensagemCancelamento(reuniaoPCJ);
            LoggingHelper.logSaida("Cancelamento de reuniao");
        } catch (PersistenceException e) {
            throw new DomainException("Erro ao cancelar evento", e);
        } catch (Exception e) {
            throw new DomainException("Erro inesperado ao cancelar evento", e);
        }
    }

    public ConsultaReuniaoDTO consultarEventoPorId(FiltroPesquisaComunicacaoRegistoEvento filtro) {
        ConsultaReuniaoDTO reuniao = reuniaoPCJDAO.consultarReuniaoPorId(filtro.getIdReuniao());
        reuniao.setModalidade(retornarModalidade(reuniao.getIdModalidade()));
        reuniao.setTipo(retornarTipoReunioes(reuniao.getIdTipo()));
        reuniao.setResponsavel(retornarResponsavel(reuniao.getIdResponsavel().longValue()));
        reuniao.setTipoLocalReuniao(retornarTipoLocalReuniao(reuniao));
        if(reuniao.isReuniaoComissaoRestrita()) {
            reuniao.setBlocosComunicacaoRestrita(recuperarBlocosComunicacaoRestrita(filtro));
            reuniao.setFluxoComunicacao(true);
        }
        return reuniao;
    }

    public ReuniaoEventoDTO buscarReuniaoEventoParaAlteracao(Long idReuniaEvento) {
        ReuniaoPCJ reuniaoPCJ = buscaReuniaoPorId(idReuniaEvento);
        validarReuniao(idReuniaEvento, reuniaoPCJ);
        ReuniaoEventoDTO reuniaoEventoDTO = mapearReuniaoToDTO(reuniaoPCJ);
        preencherPontosTrabalho(reuniaoPCJ, reuniaoEventoDTO);
        return reuniaoEventoDTO;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void atualizarVinculoReuniaoAoRemoverElemento(Elemento elemento) throws DomainException {
        if (elemento == null || elemento.getId() == null) {
            throw new IllegalArgumentException("Elemento e seu ID não podem ser nulos.");
        }

        Long idElementoResponsavel = elemento.getId();
        List<ReuniaoPCJ> reunioes = reuniaoPCJDAO.buscarReunioesPorResponsavel(idElementoResponsavel);

        if (reunioes == null || reunioes.isEmpty()) {
            LOGGER.info("Nenhuma reunião encontrada para o elemento com ID {}", idElementoResponsavel);
            return;
        }

        Map<Long, Elemento> novoResponsavelPorComissao = new HashMap<>();
        List<String> erros = new ArrayList<>();

        for (ReuniaoPCJ reuniao : reunioes) {
            if (reuniao == null || reuniao.getComissao() == null) {
                LOGGER.warn("Reunião ou Comissão nula encontrada. Pulando esta entrada.");
                continue;
            }

            Long comissaoId = reuniao.getComissao().getId();
            Elemento novoResponsavel = novoResponsavelPorComissao.get(comissaoId);

            if (novoResponsavel == null) {
                try {
                    novoResponsavel = elementoService.buscarNovoResponsavelPresidenteOuSecretarioDaCPCJ(idElementoResponsavel, reuniao.getComissao());
                    if (novoResponsavel == null) {
                        String erroMsg = "Não foi possível encontrar um novo responsável para a comissão " + comissaoId;
                        LOGGER.warn(erroMsg);
                        erros.add(erroMsg);
                        continue;
                    }
                    novoResponsavelPorComissao.put(comissaoId, novoResponsavel);
                } catch (Exception e) {
                    String errorMsg = "Erro ao buscar novo responsável para a comissão " + comissaoId + ": " + e.getMessage();
                    LOGGER.error(errorMsg);
                    erros.add(errorMsg);
                    continue;
                }
            }

            try {
                reuniao.setElemento(novoResponsavel);
                reuniaoPCJDAO.update(reuniao);
            } catch (Exception e) {
                String errorMsg = "Erro ao atualizar reunião com ID " + reuniao.getId() + ": " + e.getMessage();
                LOGGER.error(errorMsg);
                erros.add(errorMsg);
            }
        }

        try {
            membroComissaoService.removerVinculo(elemento);
        } catch (Exception e) {
            String errorMsg = "Erro ao remover vinculo do membro com ID " + elemento.getId() + ": " + e.getMessage();
            LOGGER.error(errorMsg);
            erros.add(errorMsg);
        }

        if (!erros.isEmpty()) {
            StringBuilder mensagemDeErro = new StringBuilder("Erros ao atualizar reuniões: ");
            for (String erro : erros) {
                mensagemDeErro.append(erro).append("; ");
            }
            throw new DomainException(mensagemDeErro.toString());
        }
    }

    public ReuniaoPCJ buscaReuniaoPorId(Long idReuniaEvento) {
        return reuniaoPCJDAO.find(idReuniaEvento);
    }

    public List<SalaReuniaoDTO> carregaSalasCPCJ(PerfilDTO perfilDTO) {
        return salaReuniaoService.buscaSalasPorCPCJ(perfilDTO.getElementoDTO().getComissao());
    }

    public List<LocalReuniaoDTO> carregaLocaisCPCJ(PerfilDTO perfilDTO) throws DomainException {
        return localReuniaoPCJService.carregaLocaisCPCJ(perfilDTO.getElementoDTO().getComissao());
    }

    public List<ResultadoPesquisaReuniaoDTO> retornaEventosPorPeridodo(
            FiltroPesquisaReuniaoDTO filtroPesquisaReuniaoDTO) {
        return reuniaoPCJDAO.pesquisaReunioesMapa(filtroPesquisaReuniaoDTO);
    }

    public ResultadoReuniaoSelecionadoMapaDTO consultaReuniaoPorId(Long id) {
        return reuniaoPCJDAO.consultaReuniaoPorId(id);
    }

    public List<ElementoDTO> obterConvocados(Long idReuniao) {
        return membroComissaoService.listaMembrosConvocatorioPorIdReuniao(idReuniao);
    }

    public List<PontoTrabalhoDTO> listaPontosTrabalhoPorReuniao(Long idReuniao) {
        return pontoTrabalhoPCJService.obterListaPontosTrabalhoReuniao(idReuniao);
    }

    public ReuniaoPCJ buscarProximaReuniaoRestritaDoTipoOrdinaria(Long idComissao) {
        return reuniaoPCJDAO.buscarProximaReuniaoRestritaDoTipoOrdinaria(idComissao);
    }

    private void cancelarBlocosRestrita(ReuniaoPCJ reuniaoPCJ) throws DomainException, PersistenceException {
        if(reuniaoPCJ.isReuniaoExtraordinaria()) {
            throw new DomainException("Não é possível cancelar uma reunião extraordinária com blocos de comunicação restrita.");
        }
        LoggingHelper.logEntrada(LOGGER, "Cancelando blocos de comunicacao restrita");
        blocoComunicacaoRestritaService.atualizarBlocosComunicacaoRestritaParaReuniaoCancelada(reuniaoPCJ);
    }

    private ReuniaoPCJ mapearReuniaoToEntidade(ReuniaoEventoDTO reuniaoEventoDTO, TipoLocalReuniao tipoReuniaoSelecionado, ReuniaoPCJMapper reuniaoMapper) throws DomainException {
        switch (tipoReuniaoSelecionado) {
            case MORADA_CPCJ:
                return reuniaoMapper.reuniaoEventoToEntidadeMoradaCpcj(reuniaoEventoDTO)
                        .withLocalReuniao(null);
            case OUTRA_MORADA:
                LocalReuniaoPCJ localReuniao = localReuniaoPCJService.buscaOuCriaByLocalReuniaoId(reuniaoEventoDTO.getLocalReuniao(), reuniaoEventoDTO.getComissao());
                return reuniaoMapper.reuniaoEventoToEntidadeOutraMorada(reuniaoEventoDTO)
                        .withLocalReuniao(localReuniao)
                        .withSala(null);
            default: // SEM LOCAL
                return reuniaoMapper.reuniaoEventoToEntidadeSemLocalDefinido(reuniaoEventoDTO)
                        .withLocalReuniao(null)
                        .withSala(null);
        }
    }

    @SuppressWarnings("unchecked")
    private <T> T salvarOuAtualizarEntidade(T entidade) throws DomainException, PersistenceException {
        if (entidade instanceof ConvocatoriaAlargadaPCJ) {
            ConvocatoriaAlargadaPCJ convocatoria = (ConvocatoriaAlargadaPCJ) entidade;
            if (convocatoria.getId() == null) {
                convocatoriaAlargadaPCJDAO.create(convocatoria);
            } else {
                convocatoriaAlargadaPCJDAO.update(convocatoria);
            }
            return (T) convocatoria;
        } else if (entidade instanceof ReuniaoPCJ) {
            ReuniaoPCJ reuniao = (ReuniaoPCJ) entidade;
            if (reuniao.getId() == null) {
                reuniaoPCJDAO.create(reuniao);
            } else {
                reuniaoPCJDAO.update(reuniao);
            }
            return (T) reuniao;
        } else {
            throw new IllegalArgumentException("Tipo de entidade não suportado: " + entidade.getClass().getName());
        }
    }

    private ReuniaoEventoDTO mapearReuniaoToDTO(ReuniaoPCJ reuniaoPCJ) {
        ReuniaoEventoDTO reuniaoEventoDTO;
        ReuniaoPCJMapper reuniaoMapper = reuniaoPCJ instanceof ConvocatoriaAlargadaPCJ ? convocatoriaReuniaoPCJMapper : reuniaoPCJMapper;
        if (reuniaoPCJ.getSala() != null) {
            reuniaoEventoDTO = reuniaoMapper.reuniaoToReuniaoEventoMoradaCpcj(reuniaoPCJ)
                    .withHorarioDisponivel(new HorarioDisponivel(reuniaoPCJ.getHoraInicio(), reuniaoPCJ.getHoraFim()))
                    .withDuracaoSelecionada(PCJDateUtil.duracaoSelecionada(reuniaoPCJ.getHoraInicio(), reuniaoPCJ.getHoraFim()));
        } else if (reuniaoPCJ.getLocalReuniao() != null) {
            reuniaoEventoDTO = reuniaoMapper.reuniaoToReuniaoEventoOutraMorada(reuniaoPCJ);
        } else {
            reuniaoEventoDTO = reuniaoMapper.reuniaoToReuniaoEventoSemLocalDefinido(reuniaoPCJ);
        }
        return reuniaoEventoDTO;
    }

    private void processarReuniaoConvocatoriaAlargada(ReuniaoPCJ reuniaoPCJ, List<PontoTrabalhoDTO> pontosTrabalho) throws DomainException, PCJException {
        ConvocatoriaAlargadaPCJ convocatoriaAlargadaPCJ = (ConvocatoriaAlargadaPCJ) reuniaoPCJ;
        salvarOuAtualizarEntidade(convocatoriaAlargadaPCJ);
        if (pontosTrabalho != null && !pontosTrabalho.isEmpty()) {
            pontoTrabalhoPCJService.salvarAtualizarPontosTrabalho(pontosTrabalho, convocatoriaAlargadaPCJ);
        }
        membroComissaoService.salvarMembrosComissaoReuniao(convocatoriaAlargadaPCJ);
    }

    private RegistoLogDTO criarLogEvento(ReuniaoPCJ reuniaoPCJ, PerfilDTO perfilDTO, TipoRegistoLogEnum tipoRegisto) {
        RegistoLogDTO registoLogDTO = new RegistoLogDTO();
        registoLogDTO.setDescricao(String.format(tipoRegisto.getDescricao(), reuniaoPCJ.getCodigoTipo(), reuniaoPCJ.getComissao().getNome()));
        registoLogDTO.setUtilizadorPCJDTO(perfilDTO.getUtilizadorPCJDTO());
        return registoLogDTO;
    }

    private void processarReuniaoGeral(ReuniaoPCJ reuniaoPCJ, List<ElementoDTO> membros) throws DomainException {
        salvarOuAtualizarEntidade(reuniaoPCJ);
        membroComissaoService.salvarMembrosComissaoReuniao(reuniaoPCJ, membros);
    }

    private void processarReuniaoComunicacaoRestrita(ReuniaoPCJ reuniaoPCJ, List<ElementoDTO> membros, List<BlocoComunicacaoRestritaParaRegistoDTO> blocosComunicacaoRestrita) throws DomainException {
        processarReuniaoGeral(reuniaoPCJ, membros);
        if(blocosComunicacaoRestrita != null) {
            blocoComunicacaoRestritaService.atualizarBlocosComunicacaoRestrita(blocosComunicacaoRestrita, reuniaoPCJ);
        }
    }

    private void registarLog(ReuniaoPCJ reuniaoPCJ, PerfilDTO perfilDTO, TipoRegistoLogEnum tipoRegistoLog) {
        RegistoLogDTO logDTO = criarLogEvento(reuniaoPCJ, perfilDTO, tipoRegistoLog);
        registoLogService.gravarRegistoLog(logDTO);
    }

    private void enviarMensagemLembrete(final ReuniaoPCJ reuniaoPCJ) {
        if (reuniaoPCJ.getDataInicio() != null && isDataInicialAmanha(reuniaoPCJ.getDataInicio())) {
            try {
                mensagemHandler.processarEnvioMensagemReuniao(reuniaoPCJ);
                LOGGER.info("Mensagem de lembrete enviada para a reunião: {}", reuniaoPCJ.getId());
            } catch (PCJException e) {
                LOGGER.error(String.format("[PCJ] Erro ao enviar lembrete da reuniao id %s", reuniaoPCJ.getId()), e);
            }
        }
    }

    private void enviarMensagemCancelamento(ReuniaoPCJ reuniaoPCJ) {
        mensagemHandler.processarEnvioMensagemCancelamento(reuniaoPCJ);
    }

    private ReuniaoPCJ mapearReuniaoPorLocal(ReuniaoEventoDTO reuniaoEventoDTO, TipoLocalReuniao tipoReuniaoSelecionado) throws DomainException {
        validarTipoReuniao(reuniaoEventoDTO, tipoReuniaoSelecionado);
        ReuniaoPCJMapper reuniaoMapper = reuniaoEventoDTO.reuniaoComissaoAlargada() ? convocatoriaReuniaoPCJMapper : reuniaoPCJMapper;
        return mapearReuniaoToEntidade(reuniaoEventoDTO, tipoReuniaoSelecionado, reuniaoMapper);
    }

    private void preencherPontosTrabalho(ReuniaoPCJ reuniaoPCJ, ReuniaoEventoDTO reuniaoEventoDTO) {
        if (reuniaoPCJ instanceof ConvocatoriaAlargadaPCJ) {
            ConvocatoriaAlargadaPCJ convocatoria = (ConvocatoriaAlargadaPCJ) reuniaoPCJ;
            reuniaoEventoDTO.setTipoConvocatoria(convocatoria.getTipoConvocatoria());
            reuniaoEventoDTO.setPontosTrabalho(pontoTrabalhoPCJMapper.toDTOs(convocatoria.getPontosTrabalho()));
        }
    }

    private TipoLocalReuniao retornarTipoLocalReuniao(ConsultaReuniaoDTO reuniao) {
        if (reuniao.getTituloLocal() != null && reuniao.getNomeSala() == null) {
            return TipoLocalReuniao.OUTRA_MORADA;
        } else if (reuniao.getTituloLocal() == null && reuniao.getNomeSala() != null) {
            return TipoLocalReuniao.MORADA_CPCJ;
        }
        return TipoLocalReuniao.SEM_LOCAL;
    }

    private String retornarModalidade(String idModalidade) {
        Collection<DetalheDominioVO> modalidades = pcjServiceDomain.getDominioHolderModalidadesReuniao().get().getDetalhes().values();
        for (DetalheDominioVO m : modalidades) {
            if (m.getCodigoDetalheDominio().equals(idModalidade)) {
                return m.getDesignacao();
            }
        }
        return idModalidade;
    }

    private String retornarTipoReunioes(String idTipo) {
        Collection<DetalheDominioVO> tipos = pcjServiceDomain.getDominioHolderTiposReuniao().get().getDetalhes().values();
        for (DetalheDominioVO t : tipos) {
            if (t.getCodigoDetalheDominio().equals(idTipo)) {
                return t.getDesignacao();
            }
        }
        return idTipo;
    }

    private List<BlocoComunicacaoRestritaParaRegistoDTO> recuperarBlocosComunicacaoRestrita(FiltroPesquisaComunicacaoRegistoEvento filtro) {
        return blocoComunicacaoRestritaService.recuperarBlocosComunicacaoRestritaPorIdReuniao(filtro);
    }

    private static void validarReuniao(Long idReuniaEvento, ReuniaoPCJ reuniaoPCJ) {
        if (reuniaoPCJ == null) {
            throw new EntityNotFoundException("Reunião com ID " + idReuniaEvento + " não encontrada.");
        }
    }

    private void validarTipoReuniao(ReuniaoEventoDTO reuniaoEventoDTO, TipoLocalReuniao tipoReuniaoSelecionado) throws DomainException {
        if (tipoReuniaoSelecionado == null) {
            throw new DomainException("Tipo de local de reunião inválido: " + reuniaoEventoDTO.getTipo());
        }
    }

    private String retornarResponsavel(Long idResponsavel) {
        return elementoService.findById(idResponsavel).getUtilizadorPCJ().getNome();
    }

    private void tratarErroPersistencia(PersistenceException e, String mensagem) throws DomainException {
        LOGGER.error(mensagem, e);
        throw new DomainException(mensagem, e);
    }

    private void tratarErroGenerico(Exception e, String mensagem) throws DomainException {
        LOGGER.error(mensagem, e);
        throw new DomainException(mensagem, e);
    }
}
