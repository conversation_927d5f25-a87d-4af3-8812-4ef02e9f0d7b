package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ResultadoGerirPedidoSuporteDTO;
import pt.segsocial.pcj.ppp.dto.ResultadoPesquisaPedidoSuporteDTO;
import pt.segsocial.pcj.ppp.dto.SuporteDTO;
import pt.segsocial.pcj.ppp.jpa.entity.Suporte;

import java.util.List;

@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {ComissaoPPPMapper.class, ElementoMapper.class})
public interface SuporteMapper {

    SuporteDTO toDTO(Suporte suporte);
    Suporte toEntity(SuporteDTO suporte);
    List<ResultadoPesquisaPedidoSuporteDTO> toListaPesquisa(List<Suporte> pedidosSuporte);
    @Mappings({
            @Mapping(target = "nomeElemento", source = "elemento.utilizadorPCJ.nome"),
            @Mapping(target = "nomeCpcj", source = "comissao.nome"),
            @Mapping(target = "dataPedido", source = "dataPedidoSuporte")
    })
    ResultadoPesquisaPedidoSuporteDTO toListaPesquisa(Suporte pedidoSuporte);

    @Mappings({
            @Mapping(target = "nomeElemento", source = "elemento.utilizadorPCJ.nome"),
            @Mapping(target = "emailElemento", source = "elemento.email"),
            @Mapping(target = "nomeCpcj", source = "comissao.nome"),
            @Mapping(target = "dataPedido", source = "dataPedidoSuporte"),
            @Mapping(target = "dataConclusao", source = "dataConclusaoSuporte")
    })
    ResultadoGerirPedidoSuporteDTO toConsultaPedidoSuporteDTO(Suporte pedidoSuporte);
}
