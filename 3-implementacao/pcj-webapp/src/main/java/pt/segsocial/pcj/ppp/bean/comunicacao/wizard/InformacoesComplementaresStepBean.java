package pt.segsocial.pcj.ppp.bean.comunicacao.wizard;

import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import pt.segsocial.fraw.api.cdi.grs.dominio.DetalheDominioVO;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.ppp.dto.comunicacao.ComunicacaoComplementoDTO;
import pt.segsocial.pcj.ppp.enums.TipoParticipanteEnum;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Named(value = "pcjInformacoesComplementaresStepBean")
@ViewAccessScoped
public class InformacoesComplementaresStepBean implements Serializable {

    private ComunicacaoComplementoDTO comunicacaoComplementoInput = new ComunicacaoComplementoDTO();

    private List<ComunicacaoComplementoDTO> comunicacaoComplementosDTO = new ArrayList<>();

    private List<DetalheDominioVO> entidadesPP;

    private List<DetalheDominioVO> tipoContactos;

    private List<DetalheDominioVO> parentescosCrianca;

    private List<DetalheDominioVO> relacoesCrianca;

    private boolean ignorarValidacaoInformacaoComplementar = false;

    private int indexEdicao = -1;

    @Inject
    private PCJServiceDomain pcjServiceDomain;

    @PostConstruct
    public void init() {
        comunicacaoComplementoInput = new ComunicacaoComplementoDTO();
        comunicacaoComplementosDTO = new ArrayList<>();
        entidadesPP = new ArrayList<>(pcjServiceDomain.getDominioHolderEntidadePP().get().getDetalhes().values());
        tipoContactos = new ArrayList<>(pcjServiceDomain.getDominioHolderTipoContactos().get().getDetalhes().values());
        parentescosCrianca = new ArrayList<>(pcjServiceDomain.getDominioHolderParentescoCrianca().get().getDetalhes().values());
        relacoesCrianca = pcjServiceDomain.relacoesCriancasOrdenada();
        this.ignorarValidacaoInformacaoComplementar = false;
    }

    public void eliminarSituacao(int index) {
        setIgnorarValidacaoInformacaoComplementar(!this.comunicacaoComplementosDTO.isEmpty());
    }

    public void prepararValidacaoInformacao() {
        this.ignorarValidacaoInformacaoComplementar = false;
    }

    public void alterouOpcaoInformacaoComplementarQuemContactado() {
        if(comunicacaoComplementoInput.getPessoaContactada() == TipoParticipanteEnum.E) {
            comunicacaoComplementoInput.setCodigoParticipanteParentesco(null);
            comunicacaoComplementoInput.setParticipanteFamiliar(false);
        } else {
            comunicacaoComplementoInput.setCodigoEntidadeParticipante(null);
            comunicacaoComplementoInput.setDetalheEntidade(null);
            comunicacaoComplementoInput.setParticipanteFamiliar(true);
        }
    }

    public void adicionarInformacao() {
        if (indexEdicao >= 0) {
            this.comunicacaoComplementosDTO.set(indexEdicao, comunicacaoComplementoInput);
        } else {
            this.comunicacaoComplementosDTO.add(comunicacaoComplementoInput);
        }
        this.comunicacaoComplementoInput = new ComunicacaoComplementoDTO();
        this.indexEdicao = -1;
    }

    public void alterarInformacao(int index) {
        this.indexEdicao = index;
        this.comunicacaoComplementoInput = comunicacaoComplementosDTO.get(index).clone();
    }

    public void eliminarInformacao(int index)  {
        this.comunicacaoComplementosDTO.remove(index);
        if(index == indexEdicao) {
            this.comunicacaoComplementoInput = new ComunicacaoComplementoDTO();
            indexEdicao = -1;
        }
    }

    public void alterouOpcaoParticipanteFamiliar() {
        if(comunicacaoComplementoInput.getParticipanteFamiliar() == null) {
            return;
        }

        if(parentescosCrianca == null || parentescosCrianca.isEmpty()) {
            parentescosCrianca = new ArrayList<>(pcjServiceDomain.getDominioHolderParentescoCrianca().get().getDetalhes().values());
        }
    }

    public List<ComunicacaoComplementoDTO> getComunicacaoComplementosDTO() {
        Collections.sort(comunicacaoComplementosDTO, new Comparator<ComunicacaoComplementoDTO>() {
            @Override
            public int compare(ComunicacaoComplementoDTO o1, ComunicacaoComplementoDTO o2) {
                if (o1.getData() == null && o2.getData() == null) return 0;
                if (o1.getData() == null) return -1;
                if (o2.getData() == null) return 1;
                return o1.getData().compareTo(o2.getData());
            }
        });
        return comunicacaoComplementosDTO;
    }

    public void setComunicacaoComplementosDTO(List<ComunicacaoComplementoDTO> comunicacaoComplementosDTO) {
        this.comunicacaoComplementosDTO = comunicacaoComplementosDTO;
    }

    public ComunicacaoComplementoDTO getComunicacaoComplementoInput() {
        return comunicacaoComplementoInput;
    }

    public void setComunicacaoComplementoInput(ComunicacaoComplementoDTO comunicacaoComplementoInput) {
        this.comunicacaoComplementoInput = comunicacaoComplementoInput;
    }

    public List<DetalheDominioVO> getTipoContactos() {
        return tipoContactos;
    }

    public void setTipoContactos(List<DetalheDominioVO> tipoContactos) {
        this.tipoContactos = tipoContactos;
    }

    public List<DetalheDominioVO> getEntidadesPP() {
        return entidadesPP;
    }

    public void setEntidadesPP(List<DetalheDominioVO> entidadesPP) {
        this.entidadesPP = entidadesPP;
    }

    public boolean isIgnorarValidacaoInformacaoComplementar() {
        return ignorarValidacaoInformacaoComplementar;
    }

    public void setIgnorarValidacaoInformacaoComplementar(boolean ignorarValidacaoInformacaoComplementar) {
        this.ignorarValidacaoInformacaoComplementar = ignorarValidacaoInformacaoComplementar;
    }

    public List<DetalheDominioVO> getParentescosCrianca() {
        return parentescosCrianca;
    }

    public void setParentescosCrianca(List<DetalheDominioVO> parentescosCrianca) {
        this.parentescosCrianca = parentescosCrianca;
    }

    public int getIndexEdicao() {
        return indexEdicao;
    }

    public void setIndexEdicao(int indexEdicao) {
        this.indexEdicao = indexEdicao;
    }

    public List<DetalheDominioVO> getRelacoesCrianca() {
        return relacoesCrianca;
    }

    public void setRelacoesCrianca(List<DetalheDominioVO> relacoesCrianca) {
        this.relacoesCrianca = relacoesCrianca;
    }
}
