package pt.segsocial.pcj.pae.bean;

import ch.qos.logback.classic.Level;
import org.apache.commons.collections.CollectionUtils;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.joda.time.Days;
import org.joda.time.Instant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.pae.enums.EstadoProcessoEnum;
import pt.segsocial.pcj.pae.enums.EstadoRequerimentoEnum;
import pt.segsocial.pcj.pae.enums.TipoLogEnum;
import pt.segsocial.pcj.pae.jpa.dao.RequerimentoPaeDAO;
import pt.segsocial.pcj.pae.jpa.entity.Historico;
import pt.segsocial.pcj.pae.jpa.entity.RequerimentoPae;

import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe que define o bean da página de consulta do processo
 */

@Named(value = "pcjConsultarHistoricoBean")
@LocalBean
@ViewAccessScoped
@Stateful
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class ConsultarHistoricoBean extends AbstractBean implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsultarHistoricoBean.class);

    private boolean dataIndeferimentoMaisDoQueDezDias;

    @Inject
    private ConsultarProcessoBean consultarProcessoBean;

    private List<String> tiposDocumentos = new ArrayList<>();

    private void verificarSeDiasIndeferimentoMaiorQueDez(Date dataIndeferimento) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        dataIndeferimentoMaisDoQueDezDias = Days.daysBetween(Instant.parse(sdf.format(dataIndeferimento) + "T00:00:00.00Z"), Instant.now()).getDays() > 10;
    }

    public String horaFormatada(Date dataAtualizacao) {
        return PCJDateUtil.dataHoraFormatada(dataAtualizacao);
    }

    public String dataFormatada(Date dataDecisao) {
        return PCJDateUtil.dataFormatada(dataDecisao);
    }

    public boolean mostrarMotivo() {
        return getDecisao().getEstadoAtual() == EstadoProcessoEnum.INDEFERIDO ||
                getDecisao().getEstadoAtual() == EstadoProcessoEnum.REVOGADO;
    }

    public boolean exibeLinkObterComprovativoAssinado(Historico historico) {
        LoggingHelper.logEntrada(Level.DEBUG, historico);
        return historico.isDecisaoAssinada();
    }

    public boolean exibeLinkObterComprovativoDeferimento(Historico historico) {
        LoggingHelper.logEntrada(Level.DEBUG, historico);
        return historico.getEstadoAntigo().equals(EstadoProcessoEnum.POR_ANALISAR) &&
                historico.getEstadoAtual().equals(EstadoProcessoEnum.DEFERIDO)
                || historico.getEstadoAntigo().equals(EstadoProcessoEnum.EM_ANALISE) &&
                historico.getEstadoAtual().equals(EstadoProcessoEnum.DEFERIDO);
    }

    public boolean exibeLinkObterComprovativoIndeferimento(Historico historico) {
        LoggingHelper.logEntrada(Level.DEBUG, historico);
        return historico.getEstadoAtual() == EstadoProcessoEnum.INDEFERIDO &&
                historico.getEstadoAntigo() == EstadoProcessoEnum.EM_ANALISE;
    }

    public boolean exibeLinkObterComprovativoAutorizacaoAlteracao(Historico historico) {
        LoggingHelper.logEntrada(Level.DEBUG, historico);
        return TipoLogEnum.AUTORIZA_ALTERACAO_CIRCUNSTANCIA == historico.getTipoLogEnum();
    }

    public boolean exibeLinkObterComprovativoRevogacao(Historico historico) {
        LoggingHelper.logEntrada(Level.DEBUG, historico);
        return TipoLogEnum.REVOGACAO_PROCESSO == historico.getTipoLogEnum();
    }

    public String estadoReavaliacao(String id) {
        if (id == null || id.isEmpty()) {
            return "ERRO";
        }
        RequerimentoPae req = new RequerimentoPaeDAO(entityManager).find(Long.valueOf(id));
        if (req.getEstadoRequerimento() == null) {
            return "DECISAO";
        } else if (req.getEstadoRequerimento().equals(EstadoRequerimentoEnum.REPROVADO)
                || CollectionUtils.isNotEmpty(req.getComprovativos()) && req.getComprovativos().iterator().next().getFicheiroAssinado() != null) {
            return "CONCLUIDO";
        }
        return "ASSINATURA";
    }

    public ConsultarProcessoBean getConsultarProcessoBean() {
        return consultarProcessoBean;
    }

    public void setConsultarProcessoBean(ConsultarProcessoBean consultarProcessoBean) {
        this.consultarProcessoBean = consultarProcessoBean;
    }

    public boolean getDataIndeferimentoMaisDoQueDezDias() {
        return dataIndeferimentoMaisDoQueDezDias;
    }

    public void setDataIndeferimentoMaisDoQueDezDias(Boolean dataIndeferimentoMaisDoQueDezDias) {
        this.dataIndeferimentoMaisDoQueDezDias = dataIndeferimentoMaisDoQueDezDias;
    }

    public String getDataUltimaAtualizacao() {
        return PCJDateUtil.dataFormatada(getHistoricos().get(0).getData());
    }

    public Historico getDecisao() {
        LoggingHelper.logEntrada();
        try {
            for (Historico historico : getHistoricos()) {
                LoggingHelper.logEntrada(Level.DEBUG, historico);
                if ((historico.getEstadoAtual().equals(EstadoProcessoEnum.REVOGADO)
                        || historico.getEstadoAtual().equals(EstadoProcessoEnum.INDEFERIDO))
                        && !historico.getLog().contains("assinado")) {
                    return historico;
                }

                if (historico.getEstadoAntigo().equals(EstadoProcessoEnum.POR_ANALISAR) &&
                        historico.getEstadoAtual().equals(EstadoProcessoEnum.DEFERIDO) ||
                        historico.getEstadoAntigo().equals(EstadoProcessoEnum.EM_ANALISE) &&
                                historico.getEstadoAtual().equals(EstadoProcessoEnum.DEFERIDO)) {
                    verificarSeDiasIndeferimentoMaiorQueDez(historico.getData());
                    return historico;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LoggingHelper.logSaida();
        return null;
    }

    public List<Historico> getHistoricos() {
        return consultarProcessoBean.getHistoricos();
    }

    public void setHistoricos(List<Historico> historicos) {
        consultarProcessoBean.setHistoricos(historicos);
    }

    public String getMotivo(Historico historico) {
        LoggingHelper.logEntrada(historico);
        if ((!historico.getEstadoAntigo().equals(historico.getEstadoAtual()) && historico.getEstadoAtual().equals(EstadoProcessoEnum.INDEFERIDO)) &&
                (EstadoProcessoEnum.EM_ANALISE.equals(historico.getEstadoAntigo()) && EstadoProcessoEnum.INDEFERIDO.equals(historico.getEstadoAtual()))) {
            return "Rejeição de processo";
        }

        if (historico.getEstadoAtual().equals(EstadoProcessoEnum.REVOGADO) && historico.getTipoLogEnum() != null) {
            return "Revogação do processo";
        }
        LoggingHelper.logSaida();
        return historico.getLog();
    }

    public String getPageInfoMessages() {
        return "Para contestar a decisão de indeferimento do processo, deve entregar os documentos que provem que a criança / jovem está apto a participar na atividade registada. ";
    }

    public List<String> getTiposDocumentos() {
        return tiposDocumentos;
    }

    public void setTiposDocumentos(List<String> tiposDocumentos) {
        this.tiposDocumentos = tiposDocumentos;
    }
}