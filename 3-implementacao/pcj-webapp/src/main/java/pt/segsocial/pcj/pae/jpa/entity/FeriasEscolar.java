package pt.segsocial.pcj.pae.jpa.entity;


import org.hibernate.envers.Audited;
import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.pae.enums.TipoPeriodoFeriasEnum;
import pt.segsocial.pcj.core.util.PCJDateUtil;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Audited
@Table(name = "FERIAS_ESCOLARES")
@AttributeOverride(name = "id", column = @Column(name = "ID_FERIAS_ESCOLARES"))
public class FeriasEscolar extends EntityBase implements Serializable {

	private static final long serialVersionUID = 2654645038722871296L;

	@Column(name="TIPO_PERIODO")
	@Enumerated(EnumType.STRING)
	private TipoPeriodoFeriasEnum tipoPeriodo;

	@Column(name="DATA_INICIO_FERIAS")
    @Temporal(TemporalType.DATE)
    private Date dataInicioFerias;
    
    @Column(name="DATA_FIM_FERIAS")
    @Temporal(TemporalType.DATE)
    private Date dataFimFerias;

	@ManyToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "ID_REQUERIMENTO_PAE")
	private RequerimentoPae requerimentoPAE;
    
	public FeriasEscolar() {}

	public TipoPeriodoFeriasEnum getTipoPeriodo() {
		return tipoPeriodo;
	}

	public void setTipoPeriodo(TipoPeriodoFeriasEnum tipoPeriodo) {
		this.tipoPeriodo = tipoPeriodo;
	}

	public Date getDataInicioFerias() {
		return dataInicioFerias;
	}

	public void setDataInicioFerias(Date dataInicioFerias) {
		this.dataInicioFerias = dataInicioFerias;
	}

	public Date getDataFimFerias() {
		return dataFimFerias;
	}

	public void setDataFimFerias(Date dataFimFerias) {
		this.dataFimFerias = dataFimFerias;
	}

	public RequerimentoPae getRequerimentoPAE() {
		return requerimentoPAE;
	}

	public void setRequerimentoPAE(RequerimentoPae requerimentoPAE) {
		this.requerimentoPAE = requerimentoPAE;
	}

	public String dataInicioFormatada() {
		return PCJDateUtil.dataFormatada(dataInicioFerias);
	}

	public String dataFimFormatada() {
		return PCJDateUtil.dataFormatada(dataFimFerias);
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (!(o instanceof FeriasEscolar)) return false;
		if (!super.equals(o)) return false;
		FeriasEscolar that = (FeriasEscolar) o;
		return getTipoPeriodo() == that.getTipoPeriodo() && getDataInicioFerias().equals(that.getDataInicioFerias()) && getDataFimFerias().equals(that.getDataFimFerias()) && getRequerimentoPAE().equals(that.getRequerimentoPAE());
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), getTipoPeriodo(), getDataInicioFerias(), getDataFimFerias(), getRequerimentoPAE());
	}

	public boolean equalsCustom(FeriasEscolar other) {
		if (this == other) return true;
		if (other == null) return false;
		int currentHash = Objects.hash(this.getTipoPeriodo(), this.getDataInicioFerias(), this.getDataFimFerias());
		int otherHash = Objects.hash(other.getTipoPeriodo(), other.getDataInicioFerias(), other.getDataFimFerias());
		return Objects.equals(currentHash, otherHash);
	}

}
