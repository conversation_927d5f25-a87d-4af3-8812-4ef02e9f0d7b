package pt.segsocial.pcj.ppp.bean.ata.alargada.blocos;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoConhecimentoDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.AtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.OrdenacaoAtaAlargadaDTO;

public class ConhecimentoBean extends BlocoBaseBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConhecimentoBean.class);

    private BlocoConhecimentoDTO blocoConhecimentoDTO;

    private AtaAlargadaDTO ataAlargadaDTO;

    public ConhecimentoBean(AtaAlargadaDTO ataAlargadaDTO, BlocoConhecimentoDTO blocoConhecimentoDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
        this.blocoConhecimentoDTO = blocoConhecimentoDTO;
        init();
    }

    public void init() {
        if (getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getId() == null) {
            getAtaAlargadaDTO().setOrdenacaoAtaAlargadaDTO(new OrdenacaoAtaAlargadaDTO());
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setAtaAlargada(getAtaAlargadaDTO());
            //TODO: remover esse trecho devido a carregar da base as informacoes depois de selecionar o bloco
//            blocoConhecimentoDTO = new BlocoConhecimentoDTO();
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setBlocoConhecimento(blocoConhecimentoDTO);
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getBlocoConhecimento().setComissao(ataAlargadaDTO.getComissao());
        } else {
            blocoConhecimentoDTO = getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getBlocoConhecimento();
        }
    }

    public AtaAlargadaDTO getAtaAlargadaDTO() {
        return ataAlargadaDTO;
    }

    public void setAtaAlargadaDTO(AtaAlargadaDTO ataAlargadaDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
    }

    public BlocoConhecimentoDTO getBlocoConhecimentoDTO() {
        //blocoConhecimentoDTO = getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getBlocoConhecimento();
        return blocoConhecimentoDTO;
    }

    public void setBlocoConhecimentoDTO(BlocoConhecimentoDTO blocoConhecimentoDTO) {
        this.blocoConhecimentoDTO = blocoConhecimentoDTO;
    }

    public void adicionarDocumento(DocumentoDTO documentoDTO) {
        blocoConhecimentoDTO.setDocumentoPCJ(documentoDTO);
    }

    public boolean verificaExisteDoc() {
        return blocoConhecimentoDTO.getDocumentoPCJ() != null;
    }

    public DocumentoDTO getDocumentDTO() {
        return blocoConhecimentoDTO.getDocumentoPCJ();
    }

    public void removerDocumentDTO() {
        blocoConhecimentoDTO.setDocumentoPCJ(new DocumentoDTO());
    }
}
