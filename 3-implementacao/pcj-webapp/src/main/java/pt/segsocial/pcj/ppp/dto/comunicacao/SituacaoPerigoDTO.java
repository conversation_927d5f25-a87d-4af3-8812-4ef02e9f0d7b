package pt.segsocial.pcj.ppp.dto.comunicacao;

import java.io.Serializable;
import java.util.Objects;

public class SituacaoPerigoDTO implements Serializable {

    private Long id;

    private String codigoTipologiaPerigo;

    private String codigoSituacaoSinalizada;

    private String codigoSituacaoAtribuida;

    private String outraSituacaoAtribuida;

    private String nuipc;

    private String descricaoFactos;

    private String morada;

    private Boolean intervencaoUrgente;

    public String getCodigoSituacaoAtribuida() {
        return codigoSituacaoAtribuida;
    }

    public void setCodigoSituacaoAtribuida(String codigoSituacaoAtribuida) {
        this.codigoSituacaoAtribuida = codigoSituacaoAtribuida;
    }

    public String getCodigoSituacaoSinalizada() {
        return codigoSituacaoSinalizada;
    }

    public void setCodigoSituacaoSinalizada(String codigoSituacaoSinalizada) {
        this.codigoSituacaoSinalizada = codigoSituacaoSinalizada;
    }

    public String getCodigoTipologiaPerigo() {
        return codigoTipologiaPerigo;
    }

    public void setCodigoTipologiaPerigo(String codigoTipologiaPerigo) {
        this.codigoTipologiaPerigo = codigoTipologiaPerigo;
    }

    public String getDescricaoFactos() {
        return descricaoFactos;
    }

    public void setDescricaoFactos(String descricaoFactos) {
        this.descricaoFactos = descricaoFactos;
    }

    public String getNuipc() {
        return nuipc;
    }

    public void setNuipc(String nuipc) {
        this.nuipc = nuipc;
    }

    public String getOutraSituacaoAtribuida() {
        return outraSituacaoAtribuida;
    }

    public void setOutraSituacaoAtribuida(String outraSituacaoAtribuida) {
        this.outraSituacaoAtribuida = outraSituacaoAtribuida;
    }

    public String getMorada() {
        return morada;
    }

    public void setMorada(String morada) {
        this.morada = morada;
    }

    public Boolean getIntervencaoUrgente() {
        return intervencaoUrgente;
    }

    public void setIntervencaoUrgente(Boolean intervencaoUrgente) {
        this.intervencaoUrgente = intervencaoUrgente;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SituacaoPerigoDTO clone() {
        SituacaoPerigoDTO copia = new SituacaoPerigoDTO();
        copia.setId(this.getId());
        copia.setCodigoTipologiaPerigo(this.getCodigoTipologiaPerigo());
        copia.setCodigoSituacaoSinalizada(this.getCodigoSituacaoSinalizada());
        copia.setCodigoSituacaoAtribuida(this.getCodigoSituacaoAtribuida());
        copia.setOutraSituacaoAtribuida(this.getOutraSituacaoAtribuida());
        copia.setNuipc(this.getNuipc());
        copia.setDescricaoFactos(this.getDescricaoFactos());
        copia.setMorada(this.getMorada());
        copia.setIntervencaoUrgente(this.getIntervencaoUrgente());
        return copia;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        SituacaoPerigoDTO that = (SituacaoPerigoDTO) o;
        return Objects.equals(codigoTipologiaPerigo, that.codigoTipologiaPerigo) &&
                Objects.equals(codigoSituacaoSinalizada, that.codigoSituacaoSinalizada) &&
                Objects.equals(codigoSituacaoAtribuida, that.codigoSituacaoAtribuida) &&
                Objects.equals(outraSituacaoAtribuida, that.outraSituacaoAtribuida) &&
                Objects.equals(intervencaoUrgente, that.intervencaoUrgente);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigoTipologiaPerigo, codigoSituacaoSinalizada, codigoSituacaoAtribuida, outraSituacaoAtribuida, intervencaoUrgente);
    }

    @Override
    public String toString() {
        return "SituacaoPerigoDTO{" +
                "codigoSituacaoAtribuida='" + codigoSituacaoAtribuida + '\'' +
                ", codigoTipologiaPerigo='" + codigoTipologiaPerigo + '\'' +
                ", codigoSituacaoSinalizada='" + codigoSituacaoSinalizada + '\'' +
                ", outraSituacaoAtribuida='" + outraSituacaoAtribuida + '\'' +
                ", nuipc='" + nuipc + '\'' +
                ", descricaoFactos='" + descricaoFactos + '\'' +
                ", morada='" + morada + '\'' +
                ", intervencaoUrgente=" + intervencaoUrgente +
                '}';
    }
}
