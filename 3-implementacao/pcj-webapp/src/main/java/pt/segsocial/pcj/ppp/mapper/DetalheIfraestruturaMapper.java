package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.DetalheInfraestruturaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.DetalheInfraestrutura;

@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DetalheIfraestruturaMapper {

    DetalheInfraestruturaDTO toDTO(DetalheInfraestrutura detalheInfraestrutura);

    DetalheInfraestrutura toEntidade(DetalheInfraestruturaDTO detalheInfraestruturaDTO);

}
