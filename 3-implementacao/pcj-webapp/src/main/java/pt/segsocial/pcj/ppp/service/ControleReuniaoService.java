package pt.segsocial.pcj.ppp.service;

import pt.segsocial.pcj.ppp.jpa.dao.ata.alargada.AtaAlargadaDAO;
import pt.segsocial.pcj.ppp.service.ata.AtaRestritaService;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ControleReuniaoService {

    @Inject
    private AtaAlargadaDAO ataAlargadaDAO;

    @Inject
    private AtaRestritaService ataRestritaService;

    // Cadastrei um evento e não existem eventos anteriores para a CPCJ com ata
    public boolean deveExibirBotaoRegistarAta(Long idReuniao, Long idCpcj) {
        return !naoDeveExibirBotaoRegistarAta(idReuniao, idCpcj);
    }

    public boolean naoDeveExibirBotaoRegistarAta(Long idReuniao, Long idCpcj) {
        return ataAlargadaDAO.existemEventosAnterioresParaCPCJComAtaEstadoDiffConcluidoOuAssinado(idReuniao, idCpcj);
    }

    public Long buscarIdReuniaoExcluindoConcluidoOuAssinadoAlargada(Long idCpcj) {
        return ataAlargadaDAO.buscarIdReuniaoExcluindoConcluidoOuAssinado(idCpcj);
    }

    public boolean verificaAtaEstadosAssConclAlargada(Long idReuniao, Long idComissao) {
        return ataAlargadaDAO.findByIdReuniaoEstadosAssConcl(idReuniao, idComissao);
    }

    public boolean verificaAtaEstadosAssConclRestrita(Long idReuniao, Long idComissao) {
        return ataRestritaService.findByIdReuniaoEstadosAssConcl(idReuniao, idComissao);
    }

    public Long buscarIdReuniaoExcluindoConcluidoOuAssinadoRestrita(Long idCpcj) {
        return ataRestritaService.retornaAtaRegistadaParaReuniaoNaoAssinadaNaoConcluida(idCpcj);
    }

    public boolean existeAtaIniciadaParaReuniao(Long idReuniao) {
        return ataAlargadaDAO.existeAtaIniciadaParaReuniao(idReuniao);
    }

    public boolean naoExisteAtaIniciadaParaReuniao(Long idReuniao) {
        return !existeAtaIniciadaParaReuniao(idReuniao);
    }
}
