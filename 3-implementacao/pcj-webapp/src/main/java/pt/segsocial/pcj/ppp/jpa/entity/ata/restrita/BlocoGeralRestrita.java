package pt.segsocial.pcj.ppp.jpa.entity.ata.restrita;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentosPPP;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;

import javax.persistence.*;

@Entity
@Table(name = "BLOCO_GERAL_RESTRITA", schema = "PCJ")
@AttributeOverride(name = "id", column = @Column(name = "ID_BLOCO_GERAL_RESTRITA"))
public class BlocoGeralRestrita extends EntityBase implements java.io.Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_REUNIAO_PCJ")
    private ReuniaoPCJ reuniao;

    @Column(name = "TITULO")
    private String assunto;

    @Column(name = "OBSERVACAO")
    private String observacao;

    @Column(name = "TEXTO_INTRODUTORIO")
    private String textoIntrodutorio;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "ID_DOCUMENTO_PPP")
    private DocumentosPPP documentoPCJ;

    public BlocoGeralRestrita() {
    }

    private BlocoGeralRestrita(Builder builder) {
        this.documentoPCJ = builder.documentoPCJ;
        this.reuniao = builder.reuniao;
        this.assunto = builder.assunto;
        this.observacao = builder.observacao;
        this.textoIntrodutorio = builder.textoIntrodutorio;
    }

    public static class Builder {
        private DocumentosPPP documentoPCJ;
        private ReuniaoPCJ reuniao;
        private String assunto;
        private String observacao;
        private String textoIntrodutorio;

        public Builder() {
        }

        public String getAssunto() {
            return assunto;
        }

        public void setAssunto(String assunto) {
            this.assunto = assunto;
        }

        public DocumentosPPP getDocumentoPCJ() {
            return documentoPCJ;
        }

        public void setDocumentoPCJ(DocumentosPPP documentoPCJ) {
            this.documentoPCJ = documentoPCJ;
        }

        public String getObservacao() {
            return observacao;
        }

        public void setObservacao(String observacao) {
            this.observacao = observacao;
        }

        public ReuniaoPCJ getReuniao() {
            return reuniao;
        }

        public void setReuniao(ReuniaoPCJ reuniao) {
            this.reuniao = reuniao;
        }

        public String getTextoIntrodutorio() {
            return textoIntrodutorio;
        }

        public void setTextoIntrodutorio(String textoIntrodutorio) {
            this.textoIntrodutorio = textoIntrodutorio;
        }

        public Builder reuniao(ReuniaoPCJ reuniao) {
            this.reuniao = reuniao;
            return this;
        }

        public BlocoGeralRestrita build() {
            return new BlocoGeralRestrita(this);
        }
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public DocumentosPPP getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentosPPP documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTextoIntrodutorio() {
        return textoIntrodutorio;
    }

    public void setTextoIntrodutorio(String textoIntrodutorio) {
        this.textoIntrodutorio = textoIntrodutorio;
    }

    public ReuniaoPCJ getReuniao() {
        return reuniao;
    }

    public void setReuniao(ReuniaoPCJ reuniao) {
        this.reuniao = reuniao;
    }

    public static Builder builder() {
        return new Builder();
    }
}