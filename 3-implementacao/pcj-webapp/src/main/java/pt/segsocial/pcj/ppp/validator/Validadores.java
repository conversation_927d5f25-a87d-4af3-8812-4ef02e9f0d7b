package pt.segsocial.pcj.ppp.validator;

import pt.segsocial.pcj.api.exception.PCJException;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class Validadores {

    public static boolean validaNumeroDocumentoCC(String numeroDocumento) {
        if (numeroDocumento.length() > 9) {
            return validaNumeroCCcompleto(numeroDocumento);
        }
        return validaBI(numeroDocumento);

    }


    /**
     * validação apenas da parte numerica do CC correspondente ao antigo BI
     *
     * @param docNumber
     * @return
     */
    public static boolean validaBI(String docNumber) {
        if (docNumber.length() > 9) {
            return false;
        }
        int number;
        int sum = 0;
        int remainder;
        int check;
        int nifCheck = 0;

        try {
            for (int i = 0; i < 8; i++) {
                number = Integer.parseInt(docNumber.substring(i, i + 1));
                sum += number * (9 - i);
            }
            nifCheck = Character.getNumericValue(docNumber.charAt(8));

        } catch (Exception e) {
            return false;
        }

        remainder = sum % 11;

        // calc check digit
        if (remainder == 0 || remainder == 1) {
            check = 0;
        } else {
            check = 11 - remainder;
        }
        // return result
        return check == nifCheck;
    }


    /**
     * valida numero completo de cartão de cidadão 12 digitos com letras de versão
     *
     * @param numeroDocumento
     * @return
     */
    private static boolean validaNumeroCCcompleto(String numeroDocumento) {
        int sum = 0;
        boolean secondDigit = false;
        if (numeroDocumento == null || numeroDocumento.length() != 12) {
            return false;
        }
        for (int i = numeroDocumento.length() - 1; i >= 0; --i) {
            int valor;
            try {
                valor = getNumberFromCharCC(numeroDocumento.charAt(i));
            } catch (PCJException ex) {
                //entrando aqui existe um caracter n?o alfanumerico no numero n?a sendo portanto v?lido
                return false;
            }
            if (secondDigit) {
                valor *= 2;
                if (valor > 9) {
                    valor -= 9;
                }
            }
            sum += valor;
            secondDigit = !secondDigit;
        }
        return (sum % 10) == 0;
    }


    private static int getNumberFromCharCC(char letter) throws PCJException {
        switch (letter) {
            case '0':
                return 0;
            case '1':
                return 1;
            case '2':
                return 2;
            case '3':
                return 3;
            case '4':
                return 4;
            case '5':
                return 5;
            case '6':
                return 6;
            case '7':
                return 7;
            case '8':
                return 8;
            case '9':
                return 9;
            case 'A':
                return 10;
            case 'B':
                return 11;
            case 'C':
                return 12;
            case 'D':
                return 13;
            case 'E':
                return 14;
            case 'F':
                return 15;
            case 'G':
                return 16;
            case 'H':
                return 17;
            case 'I':
                return 18;
            case 'J':
                return 19;
            case 'K':
                return 20;
            case 'L':
                return 21;
            case 'M':
                return 22;
            case 'N':
                return 23;
            case 'O':
                return 24;
            case 'P':
                return 25;
            case 'Q':
                return 26;
            case 'R':
                return 27;
            case 'S':
                return 28;
            case 'T':
                return 29;
            case 'U':
                return 30;
            case 'V':
                return 31;
            case 'W':
                return 32;
            case 'X':
                return 33;
            case 'Y':
                return 34;
            case 'Z':
                return 35;
            case 'a':
                return 10;
            case 'b':
                return 11;
            case 'c':
                return 12;
            case 'd':
                return 13;
            case 'e':
                return 14;
            case 'f':
                return 15;
            case 'g':
                return 16;
            case 'h':
                return 17;
            case 'i':
                return 18;
            case 'j':
                return 19;
            case 'k':
                return 20;
            case 'l':
                return 21;
            case 'm':
                return 22;
            case 'n':
                return 23;
            case 'o':
                return 24;
            case 'p':
                return 25;
            case 'q':
                return 26;
            case 'r':
                return 27;
            case 's':
                return 28;
            case 't':
                return 29;
            case 'u':
                return 30;
            case 'v':
                return 31;
            case 'w':
                return 32;
            case 'x':
                return 33;
            case 'y':
                return 34;
            case 'z':
                return 35;
        }
        throw new PCJException("Valor inválido no número de documento.");
    }


    //validaçãominima para mail
    public static boolean validaEmail(String email) {
        try {
            return Pattern.compile("^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$").matcher(email).matches();
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean validaNuipc(String nuipc) {

        if (nuipc == null) {
            return false;
        }
        if (nuipc.length() != 16) {
            return false;
        }

        try {

            int i = Integer.parseInt(nuipc.substring(0, 6));
            if (i < 0)
                return false;
            i = Integer.parseInt(nuipc.substring(7, 9));
            if (i < 0)
                return false;
            Integer.parseInt(nuipc.substring(10, 11));
        } catch (NumberFormatException e) {
            return false;
        }

        //validaçãode caracteres que tem de ser maiusculas
        if (!Character.isUpperCase(nuipc.charAt(11)))
            return false;
        if (!Character.isUpperCase(nuipc.charAt(13)))
            return false;
        if (!Character.isUpperCase(nuipc.charAt(14)))
            return false;
        if (!Character.isUpperCase(nuipc.charAt(15)))
            return false;

        //validação caracter maiuscula ou digito excepto 0
        if (!Character.isLetterOrDigit(nuipc.charAt(12)))
            return false;
        if (Character.isAlphabetic(nuipc.charAt(12)) && Character.isLowerCase(nuipc.charAt(12)))
            return false;
        if (Character.isDigit(nuipc.charAt(12)) && Character.digit(nuipc.charAt(12), 10) == 0)
            return false;

        if (nuipc.charAt(6) != '/')
            return false;
        if (nuipc.charAt(9) != '.')
            return false;

        return true;
    }

}
