package pt.segsocial.pcj.ppp.dto;

import java.util.Objects;

public class InfraestruturaDTO {

    private Long id;
    private String nome;
    private long capacidade;
    private boolean statusFuncionamento;
    private String observacao;
    private String tipoSala;

    private Boolean salaAcustica;
    private Boolean equipamentoAudio;
    private Boolean espelhoUnidirecional;
    private Boolean recursosAudicao;
    private Boolean ludicoPedagogico;
    private Boolean utilizacaoExterna;
    private Boolean equipamentoVideo;
    private Boolean salaSegura;
    private Boolean salaAcolhedora;
    private Boolean comportaTodasIdades;
    private Boolean capacidadeImpressao;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public long getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(long capacidade) {
        this.capacidade = capacidade;
    }

    public boolean getStatusFuncionamento() {
        return statusFuncionamento;
    }

    public void setStatusFuncionamento(boolean statusFuncionamento) {
        this.statusFuncionamento = statusFuncionamento;
    }

    public String getEstado(){
        return !statusFuncionamento ? "Inativa" : "Ativa";
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTipoSala() {
        return tipoSala;
    }

    public void setTipoSala(String tipoSala) {
        this.tipoSala = tipoSala;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isStatusFuncionamento() {
        return statusFuncionamento;
    }

    public Boolean getSalaAcustica() {
        return salaAcustica;
    }

    public void setSalaAcustica(Boolean salaAcustica) {
        this.salaAcustica = salaAcustica;
    }

    public Boolean getEquipamentoAudio() {
        return equipamentoAudio;
    }

    public void setEquipamentoAudio(Boolean equipamentoAudio) {
        this.equipamentoAudio = equipamentoAudio;
    }

    public Boolean getEspelhoUnidirecional() {
        return espelhoUnidirecional;
    }

    public void setEspelhoUnidirecional(Boolean espelhoUnidirecional) {
        this.espelhoUnidirecional = espelhoUnidirecional;
    }

    public Boolean getRecursosAudicao() {
        return recursosAudicao;
    }

    public void setRecursosAudicao(Boolean recursosAudicao) {
        this.recursosAudicao = recursosAudicao;
    }

    public Boolean getLudicoPedagogico() {
        return ludicoPedagogico;
    }

    public void setLudicoPedagogico(Boolean ludicoPedagogico) {
        this.ludicoPedagogico = ludicoPedagogico;
    }

    public Boolean getUtilizacaoExterna() {
        return utilizacaoExterna;
    }

    public void setUtilizacaoExterna(Boolean utilizacaoExterna) {
        this.utilizacaoExterna = utilizacaoExterna;
    }

    public Boolean getEquipamentoVideo() {
        return equipamentoVideo;
    }

    public void setEquipamentoVideo(Boolean equipamentoVideo) {
        this.equipamentoVideo = equipamentoVideo;
    }

    public Boolean getSalaSegura() {
        return salaSegura;
    }

    public void setSalaSegura(Boolean salaSegura) {
        this.salaSegura = salaSegura;
    }

    public Boolean getSalaAcolhedora() {
        return salaAcolhedora;
    }

    public void setSalaAcolhedora(Boolean salaAcolhedora) {
        this.salaAcolhedora = salaAcolhedora;
    }

    public Boolean getComportaTodasIdades() {
        return comportaTodasIdades;
    }

    public void setComportaTodasIdades(Boolean comportaTodasIdades) {
        this.comportaTodasIdades = comportaTodasIdades;
    }

    public Boolean getCapacidadeImpressao() {
        return capacidadeImpressao;
    }

    public void setCapacidadeImpressao(Boolean capacidadeImpressao) {
        this.capacidadeImpressao = capacidadeImpressao;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InfraestruturaDTO that = (InfraestruturaDTO) o;
        return Objects.equals(nome, that.nome) &&
                Objects.equals(statusFuncionamento, that.statusFuncionamento);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nome, statusFuncionamento);
    }

    @Override
    public String toString() {
        return "InfraestruturaDTO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", capacidade=" + capacidade +
                ", statusFuncionamento=" + statusFuncionamento +
                ", observacao='" + observacao + '\'' +
                ", tipoSala='" + tipoSala + '\'' +
                ", salaAcustica=" + salaAcustica +
                ", equipamentoAudio=" + equipamentoAudio +
                ", espelhoUnidirecional=" + espelhoUnidirecional +
                ", recursosAudicao=" + recursosAudicao +
                ", ludicoPedagogico=" + ludicoPedagogico +
                ", utilizacaoExterna=" + utilizacaoExterna +
                ", equipamentoVideo=" + equipamentoVideo +
                ", salaSegura=" + salaSegura +
                ", salaAcolhedora=" + salaAcolhedora +
                ", comportaTodasIdades=" + comportaTodasIdades +
                ", capacidadeImpressao=" + capacidadeImpressao +
                '}';
    }
}
