package pt.segsocial.pcj.ppp.jpa.entity;

import org.joda.time.LocalDate;
import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PERFIL_ELEMENTO_PCJ")
@AttributeOverride(name = "id", column = @Column(name = "ID_PERFIL_ELEMENTO_PCJ"))
public class PerfilElemento extends EntityBase {

    @Column(name = "CODIGO_PERFIL", nullable = false)
    private String perfil;

    @ManyToOne
    @JoinColumn(name = "ID_ELEMENTO_PCJ", nullable = false)
    private Elemento elemento;

    @Column(name = "DATA_INICIO_VIGENCIA", nullable = false)
    private Date inicioVigencia;

    @Column(name = "DATA_FIM_VIGENCIA", nullable = false)
    private Date fimVigencia;


    @Override
    public void prePersist() {
        super.prePersist();
        LocalDate dataFimVigencia = LocalDate.fromDateFields(getInicioVigencia());
        dataFimVigencia = dataFimVigencia.plusYears(3);
        setFimVigencia(dataFimVigencia.toDate());
    }

    public String getPerfil() {
        return perfil;
    }

    public void setPerfil(String perfil) {
        this.perfil = perfil;
    }

    public Elemento getElemento() {
        return elemento;
    }

    public void setElemento(Elemento elemento) {
        this.elemento = elemento;
    }

    public Date getInicioVigencia() {
        return inicioVigencia;
    }

    public void setInicioVigencia(Date inicioVigencia) {
        this.inicioVigencia = inicioVigencia;
    }

    public Date getFimVigencia() {
        return fimVigencia;
    }

    public void setFimVigencia(Date fimVigencia) {
        this.fimVigencia = fimVigencia;
    }
}
