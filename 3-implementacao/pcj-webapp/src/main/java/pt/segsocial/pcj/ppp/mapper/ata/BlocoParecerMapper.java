package pt.segsocial.pcj.ppp.mapper.ata;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoParecerDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoParecer;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BlocoParecerMapper {

    BlocoParecer toEntity(BlocoParecerDTO blocoParecerDTO);

    BlocoParecerDTO toDTO(BlocoParecer blocoParecer);

}
