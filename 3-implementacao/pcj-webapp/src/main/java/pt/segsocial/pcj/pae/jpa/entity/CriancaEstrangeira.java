package pt.segsocial.pcj.pae.jpa.entity;


import org.hibernate.envers.Audited;
import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.pae.enums.GrauParentescoEnum;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Audited
@Table(name = "CRIANCA_ESTRANGEIRA")
@AttributeOverride(name = "id", column = @Column(name = "ID_CRIANCA_ESTRANGEIRA"))
@NamedQuery(name = CriancaEstrangeira.FIND_BY_PESSOA_ESTRANGEIRA,
        query = "SELECT c FROM CriancaEstrangeira c WHERE c.pessoaEstrangeira = :pessoaEstrangeira order by c.id desc")
public class CriancaEstrangeira extends EntityBase implements Serializable {

    private static final long serialVersionUID = 3139234403015063877L;
    public static final String FIND_BY_PESSOA_ESTRANGEIRA = "CriancaEstrangeira.findByPessoaEstrangeira";

    @OneToOne
    @JoinColumn(name = "ID_PESSOA_ESTRANGEIRA")
    private PessoaEstrangeira pessoaEstrangeira;

    @OneToMany(mappedBy = "crianca")
    private List<FiliacaoEstrangeira> filiacoes;

    public CriancaEstrangeira() {
    }

    public PessoaEstrangeira getPessoaEstrangeira() {
        return pessoaEstrangeira;
    }

    public void setPessoaEstrangeira(PessoaEstrangeira pessoaEstrangeira) {
        this.pessoaEstrangeira = pessoaEstrangeira;
    }

    public List<FiliacaoEstrangeira> getFiliacoes() {
        return filiacoes;
    }

    public void setFiliacoes(List<FiliacaoEstrangeira> filiacoes) {
        this.filiacoes = filiacoes;
    }

    public Date getDataNascimentoByGrauParentescoEnum(GrauParentescoEnum grauParentescoEnum) {
        FiliacaoEstrangeira result = new FiliacaoEstrangeira();
        for (FiliacaoEstrangeira parentage : filiacoes) {
            if (parentage.getGrauParentesco().equals(grauParentescoEnum)) {
                result = parentage;
                break;
            }
        }
        return result
                .getPessoaEstrangeira()
                .getDataNascimento();
    }

    public Date getDataNascimento() {
        return this.getPessoaEstrangeira()
                .getDataNascimento();
    }

    public String getNumeroIdentificacao() {
        return this.getPessoaEstrangeira().getNumeroIdentificacao();
    }
}