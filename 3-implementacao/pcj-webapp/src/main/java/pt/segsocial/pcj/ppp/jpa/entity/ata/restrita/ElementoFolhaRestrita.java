package pt.segsocial.pcj.ppp.jpa.entity.ata.restrita;

import pt.segsocial.fraw.api.domain.BooleanResult;
import pt.segsocial.fraw.jpa.PersistentDomainObject;
import pt.segsocial.pcj.ppp.jpa.entity.Elemento;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.AtaAlargada;

import javax.persistence.*;

@Entity
@Table(name = "ELEMENTO_FOLHA_RESTRITA")
@NamedQueries({
        @NamedQuery(name = ElementoFolhaRestrita.FIND_BY_ATA_RESTRITA,
                query = " SELECT e FROM ElementoFolhaRestrita e " +
                        " WHERE e.ataRestrita.id = :idAtaRestrita"),
        @NamedQuery(name = ElementoFolhaRestrita.FIND_BY_ID_ELEMENTO_ID_ATA_RESTRITA,
                query = " SELECT e FROM ElementoFolhaRestrita e " +
                        "  WHERE e.elemento.id = :idElemento" +
                        "    AND e.ataRestrita.id = :idAtaRestrita"),
        @NamedQuery(name = ElementoFolhaRestrita.FIND_ASSINADOS,
                query = " SELECT e FROM ElementoFolhaRestrita e " +
                        "  WHERE e.ataRestrita.id = :idAtaRestrita" +
                        "    AND e.assinatura = true")
})
public class ElementoFolhaRestrita extends PersistentDomainObject<ElementoFolhaRestritaId> {

    public static final String FIND_BY_ATA_RESTRITA= "ElementoFolhaRestrita.findByAtaAlargada";
    public static final String FIND_ASSINADOS = "ElementoFolhaRestrita.findAssinados";
    public static final String FIND_BY_ID_ELEMENTO_ID_ATA_RESTRITA = "ElementoFolhaRestrita.findByIdElementoIdAta";

    @EmbeddedId
    private ElementoFolhaRestritaId id;

    @ManyToOne
    @MapsId("elementoId")
    @JoinColumn(name = "ID_ELEMENTO_PCJ")
    private Elemento elemento;

    @ManyToOne
    @MapsId("ataRestritaId")
    @JoinColumn(name = "ID_ATA_RESTRITA_PCJ")
    private AtaRestrita ataRestrita;

    @Column(name = "ASSINATURA")
    private Boolean assinatura;

    public ElementoFolhaRestrita() {
    }

    public ElementoFolhaRestrita(ElementoFolhaRestritaId id, Elemento elemento, AtaRestrita ataRestrita, boolean assinatura) {
        this.id = id;
        this.elemento = elemento;
        this.ataRestrita = ataRestrita;
        this.assinatura = assinatura;
    }

    @Override
    public ElementoFolhaRestritaId getId() {
        return id;
    }

    public void setId(ElementoFolhaRestritaId id) {
        this.id = id;
    }

    public AtaRestrita getAtaRestrita() {
        return ataRestrita;
    }

    public void setAtaRestrita(AtaRestrita ataRestrita) {
        this.ataRestrita = ataRestrita;
    }

    public Elemento getElemento() {
        return elemento;
    }

    public void setElemento(Elemento elemento) {
        this.elemento = elemento;
    }

    public Boolean getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(Boolean assinatura) {
        this.assinatura = assinatura;
    }

    @Override
    public void delete() {
    }

    @Override
    public BooleanResult getIsDeletable() {
        return BooleanResult.TRUE;
    }
}

