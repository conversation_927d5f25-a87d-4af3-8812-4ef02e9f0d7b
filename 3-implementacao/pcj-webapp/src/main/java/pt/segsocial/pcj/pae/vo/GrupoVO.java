package pt.segsocial.pcj.pae.vo;

import pt.segsocial.pcj.pae.enums.NaturezaAtividadeEnum;
import pt.segsocial.pcj.pae.enums.TipoAtividadeEnum;
import pt.segsocial.pcj.core.util.PCJDateUtil;

public class GrupoVO {

    private Long id;
    private String nome;
    private String nissEntidade;
    private String dataInsercao;
    private String idAtividade;
    private String nomeAtividade;
    private String natureza;
    private String tipoAtividade;
    private String dataInicio;
    private String dataTermino;
    private int numeroCrianca;

    private final static String NAO_HA = "-";

    public GrupoVO() {}

    public GrupoVO(GrupoVO.Builder builder) {
        this.setId(builder.id);
        this.nome = builder.nome;
        this.nissEntidade = builder.nissEntidade;
        this.dataInsercao = builder.dataInsercao;
        this.idAtividade = builder.idAtividade;
        this.nomeAtividade = builder.nomeAtividade;
        this.natureza = builder.natureza;
        this.tipoAtividade = builder.tipoAtividade;
        this.dataInicio = builder.dataInicio;
        this.dataTermino = builder.dataTermino;
        this.numeroCrianca = builder.numeroCrianca;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNissEntidade() {
        return nissEntidade;
    }

    public void setNissEntidade(String nissEntidade) {
        this.nissEntidade = nissEntidade;
    }

    public String getDataInsercao() {
        return PCJDateUtil.dataFormatada(dataInsercao);
    }

    public void setDataInsercao(String dataInsercao) {
        this.dataInsercao = dataInsercao;
    }

    public String getIdAtividade() {
        return idAtividade;
    }

    public void setIdAtividade(String idAtividade) {
        this.idAtividade = idAtividade;
    }

    public String getNomeAtividade() {
        return !nomeAtividade.isEmpty() ? nomeAtividade : NAO_HA;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public String getNatureza() {
        return !natureza.isEmpty() ? NaturezaAtividadeEnum.valueOf(natureza).getDescricao() : NAO_HA;
    }

    public void setNatureza(String natureza) {
        this.natureza = natureza;
    }

    public String getTipoAtividade() {
        return !tipoAtividade.isEmpty() ? TipoAtividadeEnum.valueOf(tipoAtividade).getDescricao() : NAO_HA;
    }

    public void setTipoAtividade(String tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }

    public String getDataInicio() {
        return PCJDateUtil.dataFormatada(dataInicio);
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataTermino() {
        return PCJDateUtil.dataFormatada(dataTermino);
    }

    public void setDataTermino(String dataTermino) {
        this.dataTermino = dataTermino;
    }

    public int getNumeroCrianca() {
        return numeroCrianca;
    }

    public void setNumeroCrianca(int numeroCrianca) {
        this.numeroCrianca = numeroCrianca;
    }

    @Override
    public String toString() {
        return "GrupoVO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", nissEntidade='" + nissEntidade + '\'' +
                ", dataInsercao='" + dataInsercao + '\'' +
                ", idAtividade='" + idAtividade + '\'' +
                ", nomeAtividade='" + nomeAtividade + '\'' +
                ", natureza='" + natureza + '\'' +
                ", tipoAtividade='" + tipoAtividade + '\'' +
                ", dataInicio='" + dataInicio + '\'' +
                ", dataTermino='" + dataTermino + '\'' +
                ", numeroCrianca=" + numeroCrianca +
                '}';
    }

    public static class Builder {
        private Long id;
        private String nome;
        private String nissEntidade;
        private String dataInsercao;
        private String idAtividade;
        private String nomeAtividade;
        private String natureza;
        private String tipoAtividade;
        private String dataInicio;
        private String dataTermino;
        private int numeroCrianca;

        public GrupoVO.Builder addId(Long id) {
            this.id = id;
            return this;
        }
        public GrupoVO.Builder addNome(String nome) {
            this.nome = nome;
            return this;
        }

        public GrupoVO.Builder addNissEntidade(String nissEntidade) {
            this.nissEntidade = nissEntidade;
            return this;
        }

        public GrupoVO.Builder addDataInsercao(String dataInsercao) {
            this.dataInsercao = dataInsercao;
            return this;
        }

        public GrupoVO.Builder addIdAtividade(String idAtividade) {
            this.idAtividade = idAtividade;
            return this;
        }

        public GrupoVO.Builder addNomeAtividade(String nomeAtividade) {
            this.nomeAtividade = nomeAtividade;
            return this;
        }

        public GrupoVO.Builder addNatureza(String natureza) {
            this.natureza = natureza;
            return this;
        }

        public GrupoVO.Builder addTipoAtividade(String tipoAtividade) {
            this.tipoAtividade = tipoAtividade;
            return this;
        }

        public GrupoVO.Builder addDataInicio(String dataInicio) {
            this.dataInicio = dataInicio;
            return this;
        }

        public GrupoVO.Builder addDataTermino(String dataTermino) {
            this.dataTermino = dataTermino;
            return this;
        }

        public GrupoVO.Builder addNumeroCrianca(int numeroCrianca) {
            this.numeroCrianca = numeroCrianca;
            return this;
        }

        public GrupoVO build() {
            return new GrupoVO(this);
        }
    }
}
