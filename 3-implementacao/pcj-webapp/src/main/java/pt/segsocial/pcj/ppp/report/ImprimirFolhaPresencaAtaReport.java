package pt.segsocial.pcj.ppp.report;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.service.reporting.*;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.core.util.Constants;
import pt.segsocial.pcj.core.util.PCJNumberUtil;
import pt.segsocial.pcj.ppp.dto.ata.alargada.AtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ConvidadoPresencaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.ElementoPresencaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.FolhaPresencaDTO;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class ImprimirFolhaPresencaAtaReport extends ReportHandler {

    public static final int IMPRIMIR_LISTA_PRESENTES_3749 = 3749;
    private static final int CODIGO = 10000;
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

    @Inject
    PCJServiceLocator pcjServiceLocator;

    public CraftedReport gerarReport(AtaAlargadaDTO ata) throws PCJException, TableException {

        ReportData rd = createObjetoReport(IMPRIMIR_LISTA_PRESENTES_3749, CODIGO);
        ReportParameter param = new ReportParameter();
        String[] partes = ata.getNumeroAtaAlargada().split("/");

        String titulo = "Ata número " + PCJNumberUtil.porExtenso(Integer.parseInt(partes[0])) + " de " +
                PCJNumberUtil.porExtenso(Integer.parseInt(partes[1]));

        param.add(titulo);
        param.add(Constants.NOME_FOLHA_PRESENCA);
        param.add(ata.getNumeroAtaAlargada());
        param.add(sdf.format(new Date()));

        rd.addParametros(param);
        TableParameter tp = new TableParameter();
        tp.newTable("presentes", 2);
        for (Map.Entry<String, String> m : unificarListas(ata.getFolhaPresencaDTO()).entrySet()) {
            ReportParameter param2 = new ReportParameter();
            param2.add(m.getKey());
            param2.add(m.getValue());
            tp.addLine(param2);
        }
        tp.endTable();
        rd.addTabelas(tp);
        return generate(rd);
    }

    private Map<String, String> unificarListas(FolhaPresencaDTO folha) {
        Map<String, String> map = new HashMap<>();
        for (ElementoPresencaDTO e : folha.getElementosDTO()) {
            map.put(e.getNome(), pcjServiceLocator.getGvrDelegate().detalheDominioByCodigo("ENTIDADE", e.getEntidade()));
        }
        for (ConvidadoPresencaDTO e : folha.getConvidadosDTO()) {
            map.put(e.getNome(), e.getEntidade());
        }
        return map;
    }
}