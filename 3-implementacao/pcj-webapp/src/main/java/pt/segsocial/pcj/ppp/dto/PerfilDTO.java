package pt.segsocial.pcj.ppp.dto;

import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.dto.entidadeexterna.EntidadeExternaDTO;
import pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO;

import java.io.Serializable;

public class PerfilDTO implements Serializable {

    private Long id;
    private String codigo;
    private String nome;

    private ElementoDTO elementoDTO;
    private EntidadeExternaDTO entidadeExterna;
    private UtilizadorPCJDTO utilizadorPCJDTO;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ElementoDTO getElementoDTO() {
        return elementoDTO;
    }

    public void setElementoDTO(ElementoDTO elementoDTO) {
        this.elementoDTO = elementoDTO;
    }

    public EntidadeExternaDTO getEntidadeExterna() {
        return entidadeExterna;
    }

    public void setEntidadeExterna(EntidadeExternaDTO entidadeExterna) {
        this.entidadeExterna = entidadeExterna;
    }

    public UtilizadorPCJDTO getUtilizadorPCJDTO() {
        return utilizadorPCJDTO;
    }

    public void setUtilizadorPCJDTO(pt.segsocial.pcj.ppp.dto.utilizador.UtilizadorPCJDTO utilizadorPCJDTO) {
        this.utilizadorPCJDTO = utilizadorPCJDTO;
    }

    @Override
    public String toString() {
        return "PerfilDTO{" +
                "id=" + id +
                ", codigo='" + codigo + '\'' +
                ", nome='" + nome + '\'' +
                ", elementoDTO=" + elementoDTO +
                ", entidadeExterna=" + entidadeExterna +
                ", utilizadorPCJDTO=" + utilizadorPCJDTO +
                '}';
    }
}
