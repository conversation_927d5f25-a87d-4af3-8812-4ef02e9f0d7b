package pt.segsocial.pcj.pae.bean.service;

import org.joda.time.LocalDate;
import pt.segsocial.id.api.exception.IDException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.pae.jpa.entity.RequerimentoPae;

import java.io.Serializable;

public class ConfirmacaoService implements Serializable {

    private static final long serialVersionUID = 1648502155778453008L;
    private final PCJServiceLocator paeServiceLocator;
    private final RequerimentoPae requerimentoPae;
    private final boolean criancaEstrangeira;

    private String nomeComissao;

    public ConfirmacaoService(PCJServiceLocator paeServiceLocator, RequerimentoPae requerimentoPae, String nomeComissao) {
        this.paeServiceLocator = paeServiceLocator;
        this.requerimentoPae = requerimentoPae;
        criancaEstrangeira = requerimentoPae.getProcessoPae().getCriancaEstrangeira() == null;
        this.nomeComissao = nomeComissao;
    }

    public String getNumero() {
        String pae = "PAE ";
        int year = new LocalDate(requerimentoPae.getProcessoPae().getDataInicio()).getYear();
        return pae.concat(String.valueOf(year).concat("/").concat(String.format("%04d", requerimentoPae.getProcessoPae().getNumeroProcesso())));
    }

    public String getNome() throws IDException {
        if (criancaEstrangeira) {
            return paeServiceLocator.getIdDelegate().getNomeCompletoPessoaSingular(requerimentoPae.getProcessoPae().getNissCrianca());
        } else {
            return requerimentoPae.getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getNome();
        }
    }

    public String getTipoDocumento() {
        if (criancaEstrangeira) {
            return "NISS";
        } else {
            return requerimentoPae.getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getTipoDocumento().getDescricao();
        }
    }

    public String getNumeroDocumento() {
        if (criancaEstrangeira) {
            return requerimentoPae.getProcessoPae().getNissCrianca();
        } else {
            return requerimentoPae.getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getNumeroIdentificacao();
        }
    }

    public String getNomeAtividade() {
        return requerimentoPae.getProcessoPae().getAtividadePCJ().getNome();
    }

    public String getComissaoResponsavel() {
        return nomeComissao;
    }
}
