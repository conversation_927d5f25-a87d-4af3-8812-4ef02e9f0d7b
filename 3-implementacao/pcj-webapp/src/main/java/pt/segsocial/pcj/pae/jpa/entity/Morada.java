package pt.segsocial.pcj.pae.jpa.entity;


import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Entity
@Audited
@Table(name = "MORADA")
@AttributeOverride(name = "id", column = @Column(name = "ID_MORADA"))
@NamedQueries({
        @NamedQuery(name = Morada.FIND_CODIGO_CONCELHO_BY_MORADA,
                query = " SELECT m.territorioPCJ.codigoConcelho FROM Morada m " +
                        " WHERE m.territorioPCJ.id = :id ")
})
public class Morada extends EntityBase implements Serializable, Cloneable {

    private static final long serialVersionUID = -1278271749669430145L;

    public static final String FIND_CODIGO_CONCELHO_BY_MORADA = "Morada.findCodigoConcelhoByMorada";

    @Column(name = "ARTERIA")
    private String arteria;

    @Column(name = "CODIGO_POSTAL")
    private String codigoPostal;

    @Column(name = "DESIGNACAO_POSTAL")
    private String designacaoPostal;

    @Column(name = "LOCALIDADE")
    private String localidade;

    @NotAudited
    @ManyToOne(optional = false, fetch = FetchType.LAZY, cascade=CascadeType.ALL)
    @JoinColumn(name = "ID_TERRITORIO_PCJ")
    private TerritorioPCJ territorioPCJ = new TerritorioPCJ();

    @Column(name = "PAIS")
    private String pais;

    @Column(name = "CODIGO_TIPO_INSTALACAO")
    private String tipoInstalacao;

    @Size(max = 100)
    @Column(name = "FREGUESIA", length = 100)
    private String freguesia;

    @Size(max = 100)
    @Column(name = "CONCELHO", length = 100)
    private String concelho;

    @Size(max = 100)
    @Column(name = "DISTRITO", length = 100)
    private String distrito;

    public String getDistrito() {
        return distrito;
    }

    public void setDistrito(String distrito) {
        this.distrito = distrito;
    }

    public String getConcelho() {
        return concelho;
    }

    public void setConcelho(String concelho) {
        this.concelho = concelho;
    }

    public String getFreguesia() {
        return freguesia;
    }

    public void setFreguesia(String freguesia) {
        this.freguesia = freguesia;
    }

    public Morada() {
    }

    public String getArteria() {
        return arteria;
    }

    public void setArteria(String arteria) {
        this.arteria = arteria;
    }

    public String getCodigoPostal() {
        return codigoPostal;
    }

    public void setCodigoPostal(String codigoPostal) {
        this.codigoPostal = codigoPostal;
    }

    public String getDesignacaoPostal() {
        return designacaoPostal;
    }

    public void setDesignacaoPostal(String designacaoPostal) {
        this.designacaoPostal = designacaoPostal;
    }

    public String getLocalidade() {
        return localidade;
    }

    public void setLocalidade(String localidade) {
        this.localidade = localidade;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getTipoInstalacao() {
        return tipoInstalacao;
    }

    public void setTipoInstalacao(String tipoInstalacao) {
        this.tipoInstalacao = tipoInstalacao;
    }

    public TerritorioPCJ getTerritorioPCJ() {
        return territorioPCJ;
    }

    public void setTerritorioPCJ(TerritorioPCJ territorioPCJ) {
        this.territorioPCJ = territorioPCJ;
    }

    public String moradaFormatada() {

        StringBuilder endereFormatado = new StringBuilder();
        if (this.arteria != null) {
            endereFormatado.append(this.arteria).append(", ");
        }
        if (this.codigoPostal != null) {
            endereFormatado.append(this.codigoPostal).append(", ");
        }
        if (this.designacaoPostal != null) {
            endereFormatado.append(this.designacaoPostal).append(", ");
        }
        if (this.localidade != null) {
            endereFormatado.append(this.localidade).append(", ");
        }
        if (this.territorioPCJ != null) {
            endereFormatado.append(this.territorioPCJ.getCodigoDistrito()).append(", ");
            endereFormatado.append(this.territorioPCJ.getCodigoConcelho()).append(", ");
            endereFormatado.append(this.territorioPCJ.getCodigoFreguesia()).append(", ");
        }
        if (this.pais != null) {
            endereFormatado.append(this.pais);
        }

        return endereFormatado.toString();
    }

    @Override
    public String toString() {
        return "Morada{" +
                "id='" + getId() + '\'' +
                ", arteria='" + arteria + '\'' +
                ", codigoPostal='" + codigoPostal + '\'' +
                ", designacaoPostal='" + designacaoPostal + '\'' +
                ", localidade='" + localidade + '\'' +
                ", pais='" + pais + '\'' +
                ", tipoInstalacao='" + tipoInstalacao + '\'' +
                '}';
    }
}