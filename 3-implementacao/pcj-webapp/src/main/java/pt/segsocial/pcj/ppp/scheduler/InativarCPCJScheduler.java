package pt.segsocial.pcj.ppp.scheduler;

import pt.segsocial.fraw.api.cdi.scheduler.Scheduled;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.pae.scheduler.AbstractScheduler;
import pt.segsocial.pcj.ppp.scheduler.ejb.InativarCPCJSchedulerEJB;

import javax.ejb.LocalBean;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;

@LocalBean
@Stateless(name = "pcjInativarCPCJScheduler")
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class InativarCPCJScheduler extends AbstractScheduler {

    @Inject
    private InativarCPCJSchedulerEJB inativarCPCJSchedulerEJB;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    @Scheduled(cronExpression = "0 0 23 * * ?", description = "Inativar CPCJs sem competencia associada", operation = "PCJ-O046", user = "PCJ_SCHED")
    public void InativarCPCJScheduler() throws DomainException {
        if (schedulerEnable) {
            inativarCPCJSchedulerEJB.inativarComissoes();
            inativarCPCJSchedulerEJB.inativarElementos();
            inativarCPCJSchedulerEJB.inativarEntidadesExternas();
        }
    }

}
