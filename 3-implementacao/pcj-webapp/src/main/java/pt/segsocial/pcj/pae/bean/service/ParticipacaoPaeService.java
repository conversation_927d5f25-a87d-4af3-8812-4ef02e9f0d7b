package pt.segsocial.pcj.pae.bean.service;

import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.id.api.exception.IDException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.pae.bean.wizard.RegistarPaeWizardContext;
import pt.segsocial.pcj.pae.jpa.dao.ParticipacaoDAO;
import pt.segsocial.pcj.pae.jpa.dao.RequerimentoPaeDAO;
import pt.segsocial.pcj.pae.jpa.entity.Morada;
import pt.segsocial.pcj.pae.jpa.entity.Participacao;
import pt.segsocial.pcj.pae.jpa.entity.RequerimentoPae;
import pt.segsocial.pcj.pae.enums.DuracaoParticipacaoEnum;
import pt.segsocial.pcj.pae.enums.FrequenciaParticipacaoEnum;
import pt.segsocial.pcj.pae.enums.TipoAtuacaoEnum;
import pt.segsocial.pcj.ppp.jpa.dao.TerritorioPCJDAO;
import pt.segsocial.pcj.ppp.jpa.entity.TerritorioPCJ;
import pt.segsocial.pcj.ppp.mapper.MoradaMapper;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.core.util.PcjMessages;
import pt.segsocial.pcj.core.vo.MoradaVO;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

public class ParticipacaoPaeService implements Serializable {

    private static final long serialVersionUID = 4496156829444543338L;

    private static final int LIMITE_DATA_PARTICIPACAO = 20;
    private static final int IDADE_MINIMA_PARTICIPACAO_ANIMAL = 12;
    public static final int LIMITE_MES_PARTICIPACAO = 9;

    private RegistarPaeWizardContext context;
    private final EntityManager entityManager;
    private final PCJServiceLocator paeServiceLocator;
    private RequerimentoPae requerimento;
    private Participacao participacao;
    private Participacao novaParticipacao;
    private Morada morada;
    private MoradaVO moradaVO;
    private boolean atuacaoUnica = Boolean.TRUE;
    private boolean prazoCerto = Boolean.TRUE;
    private boolean envolveAnimal;
    private boolean envolveSubstancia;
    private boolean envolvePerigo;
    private boolean isRenovacao;
    private boolean alteracaoParticipacao;
    private boolean operacaoGrupo;

    private MoradaMapper moradaMapper;

    public ParticipacaoPaeService(RegistarPaeWizardContext context, MoradaMapper moradaMapper) {
        this.entityManager = context.getEntityManager();
        this.paeServiceLocator = context.getServiceLocator();
        this.requerimento = context.getRequerimento();
        this.participacao = context.getParticipacao();
        this.moradaMapper = moradaMapper;
        this.context = context;

        this.morada = new Morada();
        novaMoradaVO();

        if (this.participacao.getDataInicioParticipacao() == null) {
            this.participacao = new Participacao();
            this.requerimento.getLocaisAtividade().clear();
        }

        if (this.participacao.getDataInicioParticipacao() != null) {
            this.atuacaoUnica = FrequenciaParticipacaoEnum.UNICA == this.participacao.getFrequenciaParticipacao();
            this.prazoCerto = this.participacao.getMotivoPrazoIncerto() == null;
            this.envolveAnimal = this.participacao.getDescricaoAnimal() != null;
            this.envolvePerigo = this.participacao.getDescricaoPerigo() != null;
            this.envolveSubstancia = this.participacao.getDescricaoSubstancia() != null;
        }
    }

    public ParticipacaoPaeService(EntityManager entityManager, PCJServiceLocator paeServiceLocator, RequerimentoPae requerimento) {
        this.entityManager = entityManager;
        this.paeServiceLocator = paeServiceLocator;
        this.requerimento = requerimento;
    }

    private void novaMoradaVO() {
        this.moradaVO = new MoradaVO(paeServiceLocator.getIdDelegate(), paeServiceLocator.getGvrDelegate());
    }

    public void saveOrUpdate() throws PCJException {
        try {
            regrasParticipacao(this.participacao);
            if(!isRenovacao) {
                preencherParticipacao();
                if (participacao.getId() == null) {
                    new ParticipacaoDAO(entityManager).create(participacao);
                } else {
                    new ParticipacaoDAO(entityManager).update(participacao);
                }
            } else {
                new ParticipacaoDAO(entityManager).create(novaParticipacao);
            }
            context.setParticipacao(participacao);
            requerimento = entityManager.contains(requerimento) ? requerimento : entityManager.merge(requerimento);
            new RequerimentoPaeDAO(entityManager).update(requerimento);
        } catch (Exception e) {
            throw new PCJException(e);
        }
    }

    public void salvarRenovacao() throws PCJException, DomainException {
        regrasParticipacao(novaParticipacao);
        new ParticipacaoDAO(entityManager).create(novaParticipacao);
    }

    private void regrasParticipacao(Participacao participacao) throws PCJException {
        LocalDate dataInicioParticipacao = LocalDate.fromDateFields(participacao.getDataInicioParticipacao());
        LocalDate dataFimParticipacao = LocalDate.fromDateFields(participacao.getDataFimParticipacao());
        int monthsBetween = Months.monthsBetween(dataInicioParticipacao, dataFimParticipacao).getMonths();
        long daysBetwenn = Days.daysBetween(LocalDate.now(), dataInicioParticipacao).getDays();

        if (!atuacaoUnica && dataInicioParticipacao.equals(dataFimParticipacao)) {
            JsfMessageUtil.mostraMensagemErro("Para várias atuações a data de início de participação não pode ser igual à data de fim de participação.");
            throw new PCJException();
        }

        if (isRenovacao && this.novaParticipacao.getDataFimParticipacao().before(this.participacao.getDataFimParticipacao())) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M11186"));
            throw new PCJException();
        }

        if (dataFimParticipacao.isBefore(dataInicioParticipacao)) {
            JsfMessageUtil.mostraMensagemErro("A data fim da participação não pode ser inferior à data início da participação.");
            throw new PCJException();
        }

        if (!isRenovacao && daysBetwenn < LIMITE_DATA_PARTICIPACAO) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M011097"));
            throw new PCJException();
        }

        if (monthsBetween >= LIMITE_MES_PARTICIPACAO) {
            JsfMessageUtil.mostraMensagemErro("O período de participação não pode ser superior a 9 meses");
            throw new PCJException();
        }
        // LOCAL PARTICIPAÇÃO(RN062)
        if (requerimento.getLocaisAtividade().isEmpty() && !isRenovacao) {
            JsfMessageUtil.mostraMensagemErro("Deve guardar pelo menos um local da atividade.");
            throw new PCJException();
        }
    }

    public void preencherParticipacao() {
        participacao.setRequerimentoPAE(context.getRequerimento());
        participacao.setFrequenciaParticipacao(isAtuacaoUnica() ? FrequenciaParticipacaoEnum.UNICA : FrequenciaParticipacaoEnum.VARIAS_ATUACOES);
        participacao.setDuracao(isPrazoCerto() ? DuracaoParticipacaoEnum.PRAZO_CERTO : DuracaoParticipacaoEnum.PRAZO_INCERTO);
    }

    public void eliminarMorada(Morada morada) {
        requerimento.getLocaisAtividade().remove(morada);
        novaMoradaVO();
    }

    public void adicionarMorada() throws DomainException {
        morada = moradaMapper.voEntidade(moradaVO);
        if (existeMorada(requerimento.getLocaisAtividade())) {
            JsfMessageUtil.mostraMensagemErro("Local da atividade já adicionado.");
        } else {
            morada = moradaMapper.voEntidade(moradaVO);
            morada.setTerritorioPCJ(getTerritoryPCJ());
            requerimento.getLocaisAtividade().add(morada);
            morada = new Morada();
        }
        novaMoradaVO();
    }

    private boolean existeMorada(List<Morada> moradas) {
        for (Morada moradaAdicionada : moradas) {
            if (moradaAdicionada.getArteria().trim().equalsIgnoreCase(morada.getArteria().trim()) &&
                    moradaAdicionada.getCodigoPostal().equals(morada.getCodigoPostal())) {
                return true;
            }
        }
        return false;
    }
    private TerritorioPCJ getTerritoryPCJ() {
        return new TerritorioPCJDAO(entityManager).getTerritoryPCJPae(
                morada.getTerritorioPCJ().getCodigoDistrito(),
                morada.getTerritorioPCJ().getCodigoConcelho(),
                morada.getTerritorioPCJ().getCodigoFreguesia());
    }

    public boolean mostrarPrazoIncerto() {
        return !participacao.getDuracao().equals(DuracaoParticipacaoEnum.PRAZO_CERTO);
    }

    public List<TipoAtuacaoEnum> getTiposAtuacao() {
        return Arrays.asList(TipoAtuacaoEnum.values());
    }

    public boolean isOutraParticipacaoRendered(Participacao participacao) {
        return participacao.getTipoAtuacao() != null && participacao.getTipoAtuacao().equals(TipoAtuacaoEnum.OUTRA);
    }

    public boolean isAtuacaoUnica() {
        return atuacaoUnica;
    }

    public void setAtuacaoUnica(boolean atuacaoUnica) {
        this.atuacaoUnica = atuacaoUnica;
    }

    public boolean isPrazoCerto() {
        return prazoCerto;
    }

    public void setPrazoCerto(boolean prazoCerto) {
        this.prazoCerto = prazoCerto;
    }

    public Participacao getParticipacao() {
        return participacao;
    }

    public void setParticipacao(Participacao participacao) {
        this.participacao = participacao;
    }

    public Morada getMorada() {
        return morada;
    }

    public void setMorada(Morada morada) {
        this.morada = morada;
    }

    public List<Morada> getLocaisParticipacao() {
        return requerimento.getLocaisAtividade();
    }

    public void setLocaisParticipacao(List<Morada> locaisParticipacao) {
        this.requerimento.setLocaisAtividade(locaisParticipacao);
    }

    public RequerimentoPae getRequerimento() {
        return requerimento;
    }

    public boolean isComunicacao() {
        return requerimento.getProcessoPae().isComunicacao();
    }

    public void setComunicacao(boolean comunicacao) {
        this.requerimento.getProcessoPae().setComunicacao(comunicacao);
    }

    public boolean permiteEnvolverAnimal() {
        LocalDate dataNascimento = null;
        if (requerimento.getProcessoPae().getCriancaEstrangeira() == null) {
            try {
                dataNascimento = new LocalDate(paeServiceLocator.getIdDelegate().getDataNascimento(requerimento.getProcessoPae().getNissCrianca()));
            } catch (IDException e) {
                e.printStackTrace();
            }
        } else {
            dataNascimento = new LocalDate(requerimento.getProcessoPae().getCriancaEstrangeira().getPessoaEstrangeira().getDataNascimento());
        }

        return PCJDateUtil.calcularIdade(dataNascimento) > IDADE_MINIMA_PARTICIPACAO_ANIMAL;
    }

    public boolean isEnvolveAnimal() {
        return envolveAnimal;
    }

    public void setEnvolveAnimal(boolean envolveAnimal) {
        this.envolveAnimal = envolveAnimal;
    }

    public boolean isEnvolveSubstancia() {
        return envolveSubstancia;
    }

    public void setEnvolveSubstancia(boolean envolveSubstancia) {
        this.envolveSubstancia = envolveSubstancia;
    }

    public boolean isEnvolvePerigo() {
        return envolvePerigo;
    }

    public void setEnvolvePerigo(boolean envolvePerigo) {
        this.envolvePerigo = envolvePerigo;
    }

    public void envolveAnimalListener(Participacao participacao) {
        if (!envolveAnimal) {
            participacao.setDescricaoAnimal(null);
        }
    }

    public void envolveSubstanciaListener(Participacao participacao) {
        if (!envolveSubstancia) {
            participacao.setDescricaoSubstancia(null);
        }
    }

    public void envolvePerigoListener(Participacao participacao) {
        if (!envolvePerigo) {
            participacao.setDescricaoPerigo(null);
        }
    }

    public boolean isRenovacao() {
        return isRenovacao;
    }

    public void setRenovacao(boolean renovacao) {
        isRenovacao = renovacao;
    }

    public Participacao getNovaPartipacao() {
        return novaParticipacao;
    }

    public void setNovaPartipacao(Participacao novaPartipacao) {
        this.novaParticipacao = novaPartipacao;
    }

    public void dataParticipacaoListener() {
        participacao.setDataFimParticipacao(participacao.getDataInicioParticipacao());
    }

    public void buscarMorada() {
        moradaVO.buscarMorada();
        morada.setCodigoPostal(moradaVO.getCodigoPostal());
        morada.getTerritorioPCJ().setCodigoDistrito(moradaVO.getCodigoDistrito());
        morada.getTerritorioPCJ().setCodigoConcelho(moradaVO.getCodigoConcelho());
        morada.getTerritorioPCJ().setCodigoFreguesia(moradaVO.getCodigoFreguesia());
    }

    public void verificaCodigoPostal() {
        if (moradaVO.getCodigoPostal() != null && moradaVO.getCodigoPostal().replace("-", "").length() == 7) {
            buscarMorada();
        }
    }

    public boolean isAlteracaoParticipacao() {
        return alteracaoParticipacao;
    }

    public void participacaoListener() {
        participacao.setDataInicioParticipacao(null);
        participacao.setDataFimParticipacao(null);
    }

    public void setAlteracaoParticipacao(boolean alteracaoParticipacao) {
        this.alteracaoParticipacao = alteracaoParticipacao;
    }

    public boolean isParticipacaoGrupo() {
        return null != participacao.getDataInicioParticipacao() && operacaoGrupo;
    }

    public boolean getOperacaoGrupo() {
        return this.operacaoGrupo;
    }

    public void setOperacaoGrupo(boolean operacaoGrupo) {
        this.operacaoGrupo = operacaoGrupo;
    }

    public void setRequerimento(RequerimentoPae requerimento) {
        this.requerimento = requerimento;
    }

    public MoradaVO getMoradaVO() {
        return moradaVO;
    }

    public void setMoradaVO(MoradaVO moradaVO) {
        this.moradaVO = moradaVO;
    }

    public void setMoradaMapper(MoradaMapper moradaMapper) {
        this.moradaMapper = moradaMapper;
    }
}