package pt.segsocial.pcj.ppp.mapper.ata;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.restrita.BlocoGeralRestritaDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.BlocoGeralRestrita;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BlocoGeralRestritaMapper {

    BlocoGeralRestrita toEntity(BlocoGeralRestritaDTO blocoAssuntosGeraisDTO);

    BlocoGeralRestritaDTO toDTO(BlocoGeralRestrita blocoAssuntoGeral);

}
