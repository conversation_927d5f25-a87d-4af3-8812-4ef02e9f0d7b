package pt.segsocial.pcj.ppp.service;

import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.ComissaoPesquisaPCJDTO;
import pt.segsocial.pcj.ppp.dto.NomeCpcjDTO;
import pt.segsocial.pcj.ppp.jpa.dao.ComissaoPCJpppDAO;
import pt.segsocial.pcj.ppp.jpa.dao.CompetenciaTerritorialDAO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.mapper.ComissaoPPPMapper;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceContextType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ComissaoPCJService implements Serializable {

    private static final long serialVersionUID = 1893060198768261654L;

    @PersistenceContext(unitName = PCJSubsystem.PERSISTENCE_UNIT_PCJ, type = PersistenceContextType.EXTENDED)
    private EntityManager entityManager;

    @Inject
    private ComissaoPPPMapper comissaoPPPMapper;

    public List<ComissaoPCJDTO> carregarComissoesAtivas() {
        List<ComissaoPCJppp> comissoes =  new ComissaoPCJpppDAO(entityManager).findAllAtivo();
        return comissaoPPPMapper.toDTOs(comissoes);
    }

    public List<ComissaoPCJDTO> carregarComissoesAtivasDiferenteElemento(String niss) {
        List<ComissaoPCJppp> comissoes = new ComissaoPCJpppDAO(entityManager).findCpcjAtivoNotElemento(niss);
        return comissaoPPPMapper.toDTOs(comissoes);
    }

    public List<ComissaoPCJDTO> carregarComissoesAtivasDiferenteEntidadeExterna(String niss) {
        List<ComissaoPCJppp> comissoesEntidadeExterna = new ComissaoPCJpppDAO(entityManager).findAllComissaoAtivoInEntidadeExterna(niss);
        List<ComissaoPCJppp> comissoesAtivas = new ComissaoPCJpppDAO(entityManager).findAllAtivo();
        List<ComissaoPCJDTO> comissoesDTO = new ArrayList<>();
        for (ComissaoPCJppp comissao: comissoesAtivas) {
            if (comissoesEntidadeExterna.contains(comissao)) {
                continue;
            }
            comissoesDTO.add(comissaoPPPMapper.toDTO(comissao));
        }

        return comissoesDTO;
    }

    public List<ComissaoPCJDTO> carregarComissoesAtivasDiferenteEntidadeExternaPorEmail(String emailPessoal) {
        List<ComissaoPCJppp> comissoesEntidadeExterna = new ComissaoPCJpppDAO(entityManager).findAllComissaoAtivoInEntidadeExterna(emailPessoal);
        List<ComissaoPCJppp> comissoesAtivas = new ComissaoPCJpppDAO(entityManager).findAllAtivo();
        List<ComissaoPCJDTO> comissoesDTO = new ArrayList<>();
        for (ComissaoPCJppp comissao: comissoesAtivas) {
            if (comissoesEntidadeExterna.contains(comissao)) {
                continue;
            }
            comissoesDTO.add(comissaoPPPMapper.toDTO(comissao));
        }

        return comissoesDTO;
    }
    public ComissaoPCJDTO carregarComissaoPorNome(String nome) {
        ComissaoPCJDTO dtoComissao = new ComissaoPCJDTO();
        ComissaoPCJppp comissaoPCJ = new ComissaoPCJpppDAO(entityManager).findByCompleteName(nome);

        if (null != comissaoPCJ) {
            dtoComissao = comissaoPPPMapper.toDTO(comissaoPCJ);
        }
        return dtoComissao;
    }

    public List<NomeCpcjDTO> buscarNomesIdsCpcjComCriteria() {
        return new ComissaoPCJpppDAO(entityManager).buscarNomesIdsCpcjComCriteria();
    }

    public Map<Integer, String> obterCodigosFregesiasExistentes(Integer codDistrito, Integer codConcelho) {
        return new CompetenciaTerritorialDAO(entityManager).obterCodigosFreguesiasComissoes(codDistrito, codConcelho);
    }

    public List<ComissaoPesquisaPCJDTO> buscaCpcjsAtivas() {
        return new ComissaoPCJpppDAO(entityManager).buscaCpcjsAtivas();
    }

    public ComissaoPCJppp carregarComissaoPorId(Long id) {
        return new ComissaoPCJpppDAO(entityManager).find(id);
    }
}