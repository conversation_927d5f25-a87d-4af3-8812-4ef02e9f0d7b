package pt.segsocial.pcj.ppp.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.comparar.BaseCompararVersaoDTO;
import pt.segsocial.pcj.ppp.dto.comparar.UtilizadorVersaoPCJDTO;
import pt.segsocial.pcj.ppp.jpa.entity.UtilizadorVersaoPCJ;

import java.util.List;

@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UtilizadorVersaoPCJMapper {

    UtilizadorVersaoPCJDTO toDTO(UtilizadorVersaoPCJ utilizadorPCJ);

    UtilizadorVersaoPCJ toEntidade(UtilizadorVersaoPCJDTO utilizadorPCJDTO);

    List<UtilizadorVersaoPCJDTO> toDTOs(List<UtilizadorVersaoPCJ> utilizadorVersao);

    List<UtilizadorVersaoPCJ> toEntidades(List<UtilizadorVersaoPCJDTO> utilizadorVersaoDTO);

    List<BaseCompararVersaoDTO> toBaseDTOs(List<UtilizadorVersaoPCJ> utilizadorVersao);

}
