package pt.segsocial.pcj.ppp.jpa.dao.ata.alargada;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoEleicaoPresidente;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import java.io.Serializable;

public class BlocoEleicaoPresidenteDAO extends DAO<BlocoEleicaoPresidente, Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    protected static final Logger LOGGER = LoggerFactory.getLogger(BlocoEleicaoPresidenteDAO.class);

    @Inject
    public BlocoEleicaoPresidenteDAO(EntityManager entityManager) {
        super(entityManager);
    }

}
