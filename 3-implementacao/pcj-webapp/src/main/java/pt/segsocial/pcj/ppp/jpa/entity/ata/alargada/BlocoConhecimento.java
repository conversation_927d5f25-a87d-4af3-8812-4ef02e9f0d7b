package pt.segsocial.pcj.ppp.jpa.entity.ata.alargada;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "BLOCO_CONHECIMENTO")
@AttributeOverride(name = "id", column = @Column(name = "ID_BLOCO_CONHECIMENTO"))
public class BlocoConhecimento extends EntityBase implements Serializable {

    @Column(name = "ASSUNTO", length = 150)
    private String assunto;

    @Column(name = "OBSERVACAO", length = 4000)
    private String observacao;

    @Column(name = "TEXTO_INTRODUTORIO", length = 4000)
    private String textoIntrodutorio;

    @ManyToOne
    @JoinColumn(name = "ID_COMISSAO_PCJ")
    private ComissaoPCJppp comissao;

    @Column(name = "EXECUTADO", columnDefinition = "NUMBER(1,0) DEFAULT 0")
    private Boolean executado;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "ID_DOCUMENTO_PCJ")
    private DocumentoPCJ documentoPCJ;

    @Column(name = "ATA_EXTERNA", columnDefinition = "NUMBER(1,0) DEFAULT 0")
    private Boolean ataExterna;

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getTextoIntrodutorio() {
        return textoIntrodutorio;
    }

    public void setTextoIntrodutorio(String textoIntrodutorio) {
        this.textoIntrodutorio = textoIntrodutorio;
    }

    public ComissaoPCJppp getComissao() {
        return comissao;
    }

    public void setComissao(ComissaoPCJppp comissao) {
        this.comissao = comissao;
    }

    public Boolean getExecutado() {
        return executado;
    }

    public void setExecutado(Boolean executado) {
        this.executado = executado;
    }

    public DocumentoPCJ getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentoPCJ documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }

    public Boolean isAtaExterna() {
        return ataExterna;
    }

    public void setAtaExterna(Boolean ataExterna) {
        this.ataExterna = ataExterna;
    }
}
