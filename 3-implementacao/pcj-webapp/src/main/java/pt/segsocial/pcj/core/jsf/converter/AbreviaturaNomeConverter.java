package pt.segsocial.pcj.core.jsf.converter;

import org.apache.commons.lang3.StringUtils;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 * Converter nomes numa abreviatura com um tamanho fixo
 */
@FacesConverter("pcjAbreviaturaNomeConverter")
public class AbreviaturaNomeConverter implements Converter {
    /**
     * Tamanho default do nome se não especificado valor (já com ...)
     */
    private static final int TAMANHO_LIMITE = 30;

    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String value) {
        String limite = (String) uiComponent.getAttributes().get("limite");

        if (limite != null) {
            return StringUtils.abbreviate(String.valueOf(value), Integer.parseInt(limite));
        } else {
            return StringUtils.abbreviate(value, TAMANHO_LIMITE);
        }
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object value) {
        if (value == null) {
            return null;
        }

        String limite = (String) uiComponent.getAttributes().get("limite");
        if (limite != null) {
            return StringUtils.abbreviate(String.valueOf(value), Integer.parseInt(limite));
        } else {
            return StringUtils.abbreviate(String.valueOf(value), TAMANHO_LIMITE);
        }
    }
}
