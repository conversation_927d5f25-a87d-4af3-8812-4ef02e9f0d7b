package pt.segsocial.pcj.ppp.jpa.dao;

import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.pae.jpa.dao.PCJDao;
import pt.segsocial.pcj.ppp.dto.FiltroPesquisaFinanciamentoDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;
import pt.segsocial.pcj.ppp.jpa.entity.Financiamento;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import java.util.*;

public class FinanciamentoDAO extends PCJDao<Financiamento, Long> {

    public FinanciamentoDAO(EntityManager entityManager) {
        super(entityManager, Financiamento.class);
    }

    public List<Financiamento> findComissaoCpcj(FiltroPesquisaFinanciamentoDTO filtro) {

        try {
            StringBuilder hql = new StringBuilder();
            hql.append("select distinct f from Financiamento f join FETCH f.comissao c join c.morada.territorioPCJ where 1 = 1 ");
            Map<String, Object> parameters = makeWhere(hql, filtro);

            TypedQuery<Financiamento> query = getEntityManager().createQuery(hql.toString(), Financiamento.class);

            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
            return query.getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }

    private Map<String, Object> makeWhere(StringBuilder hql, FiltroPesquisaFinanciamentoDTO filtro) {
        Map<String, Object> parameters = new LinkedHashMap<>();

        if (filtro.getCodigoCpcj() != null) {
            hql.append("AND lower(f.comissao.codigo) like :codigo ");
            parameters.put("codigo", "%" + filtro.getCodigoCpcj().toLowerCase() + "%");
        }

        if (filtro.getNomeCpcj() != null) {
            hql.append("AND lower(f.comissao.nome) like :nome ");
            parameters.put("nome", "%" + filtro.getNomeCpcj().toLowerCase() + "%");
        }

        if (filtro.getDistritoDto() != null) {
            hql.append("AND f.comissao.morada.territorioPCJ.codigoDistrito = :codigoDistrito ");
            parameters.put("codigoDistrito", filtro.getDistritoDto().getCodigo());
        }

        if (filtro.getConcelhoDto() != null) {
            hql.append("AND f.comissao.morada.territorioPCJ.codigoConcelho = :codigoConcelho ");
            parameters.put("codigoConcelho", filtro.getConcelhoDto().getCodigoConcelho());
        }

        if (filtro.getEscalao() != null) {
            hql.append("AND f.escalao = :escalao ");
            parameters.put("escalao", filtro.getEscalao());
        }

        return parameters;
    }

    public void deleteFinanciamentoAnteriores(Long idComissao ) {
        try {
            this.getEntityManager()
                    .createQuery("DELETE FROM Financiamento f WHERE f.comissao.id = :comissao")
                    .setParameter("comissao", idComissao).executeUpdate();
        } catch (NoResultException e) {
            LoggingHelper.logError(e);
        }

    }

    public Financiamento findByComissao(ComissaoPCJppp comissaoPCJppp) {

        try {
            return this.getEntityManager().createNamedQuery(Financiamento.FIND_BY_CODIGO_COMISSAO, Financiamento.class)
                    .setParameter("idComissao", comissaoPCJppp.getId())
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }

    }
}
