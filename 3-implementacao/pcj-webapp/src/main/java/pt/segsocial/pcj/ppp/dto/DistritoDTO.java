package pt.segsocial.pcj.ppp.dto;

import java.io.Serializable;
import java.util.Objects;

public class DistritoDTO implements Serializable {

    private Integer codigo;
    private String nome;

    public DistritoDTO(Integer nivel1, String designacaoDivisaoAdmin) {
        this.codigo = nivel1;
        this.nome = designacaoDivisaoAdmin;
    }

    public DistritoDTO() {

    }

    public DistritoDTO(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DistritoDTO)) return false;
        DistritoDTO that = (DistritoDTO) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }

    @Override
    public String toString() {
        return "DistritoDto{" +
                "codigo=" + codigo +
                ", nome='" + nome + '\'' +
                '}';
    }
}
