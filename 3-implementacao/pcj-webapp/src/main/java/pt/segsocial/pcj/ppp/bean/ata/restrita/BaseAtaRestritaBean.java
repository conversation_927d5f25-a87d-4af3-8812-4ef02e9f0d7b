package pt.segsocial.pcj.ppp.bean.ata.restrita;


import org.apache.commons.collections.CollectionUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceDomain;
import pt.segsocial.pcj.core.util.Constants;
import pt.segsocial.pcj.ppp.bean.AbstractPppBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoBaseRestritaBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoComunicacaoRestritaBean;
import pt.segsocial.pcj.ppp.bean.ata.restrita.blocos.BlocoGeralRestritaBean;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.CustomUploadedFile;
import pt.segsocial.pcj.ppp.dto.DocumentosPPPDTO;
import pt.segsocial.pcj.ppp.dto.PerfilDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.FiltroPesquisaAtaDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.*;
import pt.segsocial.pcj.ppp.enums.BlocosTrabalhoAtaRestritaEnum;
import pt.segsocial.pcj.ppp.jpa.entity.ReuniaoPCJ;
import pt.segsocial.pcj.ppp.report.ImprimirAtaRestritaReport;
import pt.segsocial.pcj.ppp.report.ImprimirFolhaPresencaAtaRestritaReport;
import pt.segsocial.pcj.ppp.service.DocumentoPCJService;
import pt.segsocial.pcj.ppp.service.ElementoService;
import pt.segsocial.pcj.ppp.service.ReuniaoCPCJService;
import pt.segsocial.pcj.ppp.service.ata.AtaRestritaService;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class BaseAtaRestritaBean extends AbstractPppBean {

    private final String NOVO = "Novo";
    private final String tituloAta = Constants.NOME_ATA_RESTRITA;
    public BlocoBaseRestritaBean blocoBaseBean;
    @Inject
    public ImprimirFolhaPresencaAtaRestritaReport imprimirListaReport;
    @Inject
    public ImprimirAtaRestritaReport imprimirAtaRestritaReport;
    @Inject
    public PesquisarAtaRestritaBean pesquisarAtaBean;
    @Inject
    protected PCJServiceDomain pcjServiceDomain;
    @Inject
    protected ReuniaoCPCJService reuniaoCPCJService;
    @Inject
    protected ElementoService elementoService;
    @Inject
    private ConsultarAtaRestritaBean consultarAtaBean;
    @Inject
    private DocumentoPCJService documentoService;
    @Inject
    private AtaRestritaService ataRestritaService;
    @Inject
    private DocumentoPCJService documentoPCJService;

    private String infoMensage;
    private String alertMensage;
    private String blocoTrabalho;
    private AtaRestritaDTO ataRestritaDTO;
    private ReuniaoPCJ reuniao;
    private Long idReuniao;
    private PerfilDTO perfilDTO;
    private Long idComissao;
    private String numeroAta;
    private Long idAtaRestrita;
    private BlocosTrabalhoAtaRestritaEnum nomeBloco;

    /*Blocos Bean*/
    private BlocoGeralRestritaBean blocoGeralRestritaBean;
    private BlocoComunicacaoRestritaBean blocoComunicacaoRestritaBean;

    /* FIM */
    private List<UploadedFile> filesSingle = new ArrayList<>();
    private String sizeLimit = "3000000";
    private String fileLimitSingle = "1";
    private String fileLimitMessageSingle = "<i class='fa fa-times-circle'></i>  Selecione no máximo 1 ficheiros.";
    private String invalidFileMessageSingle = "<i class='fa fa-times-circle'></i>  Formato não suportado. Formatos possíveis: .jpg, .png ou .pdf";
    private String invalidSizeMessageSingle = "<i class='fa fa-times-circle'></i>  Este ficheiro excede o tamanho máximo de 3MB. Insira um ficheiro menor.";
    private String atributoControlaObrigatoriedadeDocumento;

    public void init() {
        this.perfilDTO = pcjSubsystem.getLogin().getPerfilDTO();
        this.idComissao = this.perfilDTO.getElementoDTO().getComissao().getId();
    }

    public void handleFileSingleUpload(FileUploadEvent event) {
        try {
            if (event.getFile().getSize() > Integer.parseInt(sizeLimit)) {
                FacesMessage msgE = new FacesMessage(FacesMessage.SEVERITY_ERROR, fileLimitMessageSingle, null);
                FacesContext.getCurrentInstance().addMessage("errorMessage", msgE);
            }

            if (filesSingle.size() < Integer.parseInt(fileLimitSingle)) {
                filesSingle.add(event.getFile());

                FacesMessage msg = new FacesMessage("O ficheiro " + event.getFile().getFileName() + " foi carregado com sucesso.");
                FacesContext.getCurrentInstance().addMessage(null, msg);
            }

            documentosPPPDTO = documentoService.uploadPPP(event);
            documentosPPPDTO.setTipoDocumento("application/pdf");
            blocoBaseBean.adicionarDocumento(documentosPPPDTO);
        } catch (Exception e) {
            LOGGER.error(e.toString());
        }
    }

    public void verificaRedirectAtualizarPagina() {
        if (getAtaRestritaDTO().getId() != null &&
                CollectionUtils.isEmpty(getAtaRestritaDTO().getElementosFolhaPresenca())) {
            try {
                pesquisarAtaBean.setPesquisaRetornoAta(true);
                FiltroPesquisaAtaDTO novoFiltro = new FiltroPesquisaAtaDTO();
                novoFiltro.setComissao(new ComissaoPCJDTO(getIdComissao()));
                pesquisarAtaBean.setFiltroPesquisa(novoFiltro);
                pcjSubsystem.redirectTo(pcjSubsystem.pesquisarAtas(), null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void verDocumentoSingle() {
        FacesContext fc = FacesContext.getCurrentInstance();
        ExternalContext ec = fc.getExternalContext();
        ec.responseReset();

        ec.setResponseContentType(filesSingle.get(0).getContentType());
        ec.setResponseHeader("Content-Disposition", "inline; filename=\"" + filesSingle.get(0).getFileName() + "\"");
        try {
            OutputStream output = ec.getResponseOutputStream();
            output.write(filesSingle.get(0).getContents());
            output.flush();
        } catch (IOException e) {
            LOGGER.error(e.toString());
        }
        fc.responseComplete();
    }

    public void deleteFileSingle(int i) {
        if (!filesSingle.isEmpty()) {
            filesSingle.remove(i);
        }
    }

    public void carregarFicheiroStorage() {
        if (filesSingle == null) {
            filesSingle = new ArrayList<>();
        }

        try {
            DocumentosPPPDTO documentoDTO = blocoBaseBean.getDocumentDTO();
            if (documentoDTO != null && documentoDTO.getUploadDocumento()) {
                String nomeDocumento = documentoDTO.getNomeDocumento();
                Long identificadorFicheiro = documentoDTO.getIdentificadorFicheiro();

                InputStream inputStream = getPcjServiceLocator().getStorageDelegate().downloadInputStream(identificadorFicheiro);
                CustomUploadedFile uploadedFile = new CustomUploadedFile(inputStream, nomeDocumento);
                filesSingle.add(uploadedFile);
            }

        } catch (PCJException e) {
            LOGGER.error("Erro ao carregar o ficheiro: " +
                    blocoBaseBean.getDocumentDTO().getNomeDocumento() +
                    " com o identificador: " +
                    blocoBaseBean.getDocumentDTO().getIdentificadorFicheiro(), e);
        }

    }

    public List<ElementoPresencaRestritaDTO> obterDescricaoDominio(List<ElementoPresencaRestritaDTO> elementosFolhaPresenca) {
        List<ElementoPresencaRestritaDTO> listaRetorno = new ArrayList<>();
        for (ElementoPresencaRestritaDTO elemento : elementosFolhaPresenca) {
            elemento.setCargo(getValorDominio("CARGO", elemento.getCargo()));
            elemento.setFuncao(getValorDominio("TPELEMENTO", elemento.getFuncao()));
            elemento.setEntidade(getValorDominio("ENTIDADE", elemento.getEntidade()));
            listaRetorno.add(elemento);
        }
        return listaRetorno;
    }

    public String getHifen(String nome, String codigoRepresentante) {
        return null != nome && (nome.equals(NOVO) || nome.isEmpty()) ? "" : " - " + super.getValorDominio("ENTIDADEPP", codigoRepresentante);
    }

    public AtaRestritaDTO buscarAtaAlargada(Long idAtaRestrita) {
        return ataRestritaService.buscarAtaRestrita(idAtaRestrita);
    }

    public String getWidgetVarsAsJSArray() {
        StringBuilder sb = new StringBuilder();
        for (TrabalhosGuardadosDTO tg : getAtaRestritaDTO().getTrabalhosGuardadosDTO()) {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append("\"").append(tg.getAccordionWidgetId()).append("\"");
        }
        return sb.toString();
    }

    public void instanciarBlocoSelecionado(BlocoRestritaComunicacaoDTO bloco) {
        instanciarBloco(bloco);
    }

    public void instanciarBlocoSelecionado() {
        BlocoBaseRestritaDTO blocoDTO = getNomeBloco() != null ? getNomeBloco().criarDTO() : null;
        instanciarBloco(blocoDTO);
    }

    private void instanciarBloco(BlocoBaseRestritaDTO bloco) {
        BlocosTrabalhoAtaRestritaEnum nmBloco = getNomeBloco();
        if (nmBloco == null) {
            return;
        }
        this.blocoBaseBean = nmBloco.criarBean(ataRestritaDTO, bloco);
        nmBloco.accept(this, blocoBaseBean);
    }

    public String consultarDocumento(Long idDocumento) {
        try {
            String generatedUrl = documentoPCJService.previewUrl(idDocumento);
            if (generatedUrl == null || generatedUrl.isEmpty()) {
                FacesContext.getCurrentInstance().addMessage(null, new FacesMessage("URL do documento não encontrada"));
            } else {
                RequestContext.getCurrentInstance().addCallbackParam("documentUrl", generatedUrl);
            }
            return generatedUrl;
        } catch (PCJException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public void redirectToHome() {
        pcjSubsystem.redirectToHome(login.getLogin());
    }

    public ReuniaoPCJ getReuniao() {
        return reuniao;
    }

    public void setReuniao(ReuniaoPCJ reuniao) {
        this.reuniao = reuniao;
    }

    public Long getIdReuniao() {
        return idReuniao;
    }

    public void setIdReuniao(Long idReuniao) {
        this.idReuniao = idReuniao;
    }

    public Long getIdComissao() {
        return idComissao;
    }

    public void setIdComissao(Long idComissao) {
        this.idComissao = idComissao;
    }

    @Override
    public PerfilDTO getPerfilDTO() {
        return perfilDTO;
    }

    public void setPerfilDTO(PerfilDTO perfilDTO) {
        this.perfilDTO = perfilDTO;
    }

    public String getNumeroAta() {
        return numeroAta;
    }

    public void setNumeroAta(String numeroAta) {
        this.numeroAta = numeroAta;
    }

    public Long getIdAtaRestrita() {
        return idAtaRestrita;
    }

    public void setIdAtaRestrita(Long idAtaRestrita) {
        this.idAtaRestrita = idAtaRestrita;
    }

    public String getTituloAta() {
        return tituloAta;
    }

    public String getSizeLimit() {
        return sizeLimit;
    }

    public void setSizeLimit(String sizeLimit) {
        this.sizeLimit = sizeLimit;
    }

    public String getInvalidSizeMessageSingle() {
        return invalidSizeMessageSingle;
    }

    public void setInvalidSizeMessageSingle(String invalidSizeMessageSingle) {
        this.invalidSizeMessageSingle = invalidSizeMessageSingle;
    }

    public String getInvalidFileMessageSingle() {
        return invalidFileMessageSingle;
    }

    public void setInvalidFileMessageSingle(String invalidFileMessageSingle) {
        this.invalidFileMessageSingle = invalidFileMessageSingle;
    }

    public List<UploadedFile> getFilesSingle() {
        return filesSingle;
    }

    public void setFilesSingle(List<UploadedFile> filesSingle) {
        this.filesSingle = filesSingle;
    }

    public String getFileLimitSingle() {
        return fileLimitSingle;
    }

    public void setFileLimitSingle(String fileLimitSingle) {
        this.fileLimitSingle = fileLimitSingle;
    }

    public String getFileLimitMessageSingle() {
        return fileLimitMessageSingle;
    }

    public void setFileLimitMessageSingle(String fileLimitMessageSingle) {
        this.fileLimitMessageSingle = fileLimitMessageSingle;
    }

    public String getBlocoTrabalho() {
        return blocoTrabalho;
    }

    public void setBlocoTrabalho(String blocoTrabalho) {
        this.blocoTrabalho = blocoTrabalho;
    }

    public BlocoGeralRestritaBean getBlocoGeralRestritaBean() {
        return blocoGeralRestritaBean;
    }

    public void setBlocoGeralRestritaBean(BlocoGeralRestritaBean blocoGeralRestritaBean) {
        this.blocoGeralRestritaBean = blocoGeralRestritaBean;
    }

    public BlocoComunicacaoRestritaBean getBlocoComunicacaoRestritaBean() {
        return blocoComunicacaoRestritaBean;
    }

    public void setBlocoComunicacaoRestritaBean(BlocoComunicacaoRestritaBean blocoComunicacaoRestritaBean) {
        this.blocoComunicacaoRestritaBean = blocoComunicacaoRestritaBean;
    }

    public BlocosTrabalhoAtaRestritaEnum getNomeBloco() {
        return nomeBloco;
    }

    public void setNomeBloco(BlocosTrabalhoAtaRestritaEnum nomeBloco) {
        this.nomeBloco = nomeBloco;
    }

    public AtaRestritaDTO getAtaRestritaDTO() {
        return ataRestritaDTO;
    }

    public void setAtaRestritaDTO(AtaRestritaDTO ataRestritaDTO) {
        this.ataRestritaDTO = ataRestritaDTO;
    }

    public String getAtributoControlaObrigatoriedadeDocumento() {
        return atributoControlaObrigatoriedadeDocumento;
    }

    public void setAtributoControlaObrigatoriedadeDocumento(String atributoControlaObrigatoriedadeDocumento) {
        this.atributoControlaObrigatoriedadeDocumento = atributoControlaObrigatoriedadeDocumento;
    }

    public BlocoBaseRestritaBean getBlocoBaseBean() {
        return blocoBaseBean;
    }

    public PCJServiceDomain getPcjServiceDomain() {
        return pcjServiceDomain;
    }
}
