package pt.segsocial.pcj.core.util;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Objects;
import java.util.Random;

public final class PCJNumberUtil implements Serializable {

    public static final int SCALE_MONEY_CALC = 5;
    public static final int SCALE_MONEY = 2;
    public static final int GROUPING_SIZE = 3;
    public static final int SCALE_PERCENTAGE_CALC = 2;
    public static final int SCALE_PERCENTAGE = 2;
    public static final int NUMERO_UM = 1;
    public static final int NUMERO_DOIS = 2;
    public static final int NUMERO_CINCO = 5;
    public static final int NUMERO_VINTE = 20;
    public static final int NUMERO_DEZ = 10;
    public static final int NUMERO_TRINTA = 30;
    public static final int NUMERO_NOVENTA = 90;
    public static final MathContext MONEY = new MathContext(SCALE_MONEY_CALC, RoundingMode.HALF_EVEN);
    public static final MathContext PERCENTAGE = new MathContext(SCALE_MONEY_CALC, RoundingMode.HALF_EVEN);
    private static final long serialVersionUID = 7293054174809055515L;

    public static boolean isNumber(String identificacaoPedido) {
        try {
            Long.parseLong(identificacaoPedido);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String formatMoney(BigDecimal number) {
        Objects.requireNonNull(number);
        StringBuilder value = new StringBuilder();

        BigDecimal obj = number;
        DecimalFormat formatter = getDecimalFormatMoney();
        formatter.applyPattern("#,###,###,##0.00");

        value.append(formatter.format(obj)).append("€");

        return value.toString();
    }

    private static DecimalFormat getDecimalFormatMoney() {
        RoundingMode roundingMode = MONEY.getRoundingMode();

        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator(',');
        symbols.setGroupingSeparator('.');

        DecimalFormat formatter = new DecimalFormat();
        formatter.setRoundingMode(roundingMode);
        formatter.setMaximumFractionDigits(SCALE_MONEY);
        formatter.setDecimalFormatSymbols(symbols);
        formatter.setGroupingUsed(true);
        formatter.setParseBigDecimal(true);
        formatter.setGroupingSize(GROUPING_SIZE);

        return formatter;
    }

    public static String formatPercentage(BigDecimal number) {
        Objects.requireNonNull(number);
        StringBuilder value = new StringBuilder();

        BigDecimal obj = number;
        DecimalFormat formatter = getDecimalFormatPercentage();
        formatter.applyPattern("##0.00");

        value.append(formatter.format(obj)).append("%");

        return value.toString();
    }

    private static DecimalFormat getDecimalFormatPercentage() {
        RoundingMode roundingMode = MONEY.getRoundingMode();

        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator(',');
        symbols.setGroupingSeparator('.');

        DecimalFormat formatter = new DecimalFormat();
        formatter.setRoundingMode(roundingMode);
        formatter.setMaximumFractionDigits(SCALE_PERCENTAGE);
        formatter.setDecimalFormatSymbols(symbols);
        formatter.setGroupingUsed(true);
        formatter.setParseBigDecimal(true);
        formatter.setGroupingSize(GROUPING_SIZE);

        return formatter;
    }

    public static String getRandomComZeroAEsquerda(int min, int max) {
        boolean maxMaiorQue10 = max > 10;
        boolean maxMaiorQue100 = max > 100;

        int numRandom = new Random().nextInt() * (max - min) + min;
        String numFormatado = "" + numRandom;

        if (maxMaiorQue100) {
            if (numRandom < 10) {
                numFormatado = "00" + numRandom;
            } else if (numRandom < 100) {
                numFormatado = "0" + numRandom;
            }
        } else if (maxMaiorQue10 && numRandom < 10) {
                numFormatado = "0" + numRandom;
            }


        return numFormatado;
    }

    public static double round(double x, int scale) {
        return round(x, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * Retirado da biblioteca da apache
     * (https://commons.apache.org/proper/commons-math/javadocs/api-3.3/src-html/org/apache/commons/math3/util/Precision.html#line.374)
     *
     * @param value valor a arredondar
     * @param scale casas decimais pretendidas
     * @return valor arredondado
     */
    public static Double round(Double value, int scale, int scaleMethod) {
        try {
            final double rounded = (new BigDecimal(Double.toString(value))
                    .setScale(scale, scaleMethod))
                    .doubleValue();
            // MATH-1089: negative values rounded to zero should result in negative zero
            return rounded == 0d ? 0d * value : rounded;
        } catch (NumberFormatException ex) {
            if (Double.isInfinite(value)) {
                return value;
            } else {
                return Double.NaN;
            }
        }
    }

    public static String formatMoneyDouble(Double number) {
        Objects.requireNonNull(number);
        StringBuilder value = new StringBuilder();

        DecimalFormat formatter = getDecimalFormatMoney();
        formatter.applyPattern("#,###,###,##0.00");

        value.append(formatter.format(number)).append("€");

        return value.toString();
    }

    public static boolean isZero(double value) {
        return Double.compare(value, 0.0) == 0;
    }

    public static boolean isValidDouble(double value) {
        // Converte o valor para String
        String valueStr = String.format("%.2f", value);

        // Verifica se o número total de dígitos (excluindo ponto decimal) é menor ou igual a 10
        String digitsOnly = valueStr.replace(".", "");
        if (digitsOnly.length() > 10) {
            return false;
        }

        // Verifica se a parte decimal tem no máximo 2 casas decimais
        String[] parts = valueStr.split("\\.");
        return parts.length <= 1 || parts[1].length() <= 2;
    }

    public static String porExtenso(int num) {
        int u, d, c, m;
        String extenso = "";

        String[] unidade = {"", "um", "dois", "três", "quatro", "cinco", "seis", "sete", "oito", "nove"};
        String[] dezena = {"", "dez", "vinte", "trinta", "quarenta", "cinquenta", "sessenta", "setenta", "oitenta", "noventa"};
        String[] dezenaespecial = {"dez", "onze", "doze", "treze", "quatorze", "quinze", "dezesseis", "dezessete", "dezoito", "dezenove"};
        String[] centena = {"", "cem", "duzentos", "trezentos", "quatrocentos", "quinhentos", "seiscentos", "setecentos", "oitocentos", "novecentos"};

        if (num == 0) {
            return "";
        }

        if (num >= 1000) {
            m = num / 1000;
            num %= 1000;

            if (m == 1) {
                extenso = "mil";
            } else {
                extenso = unidade[m] + " mil";
            }

            if (num > 0) {
                extenso += " e ";
            }
        }

        if (num >= 100) {
            c = num / 100;
            num %= 100;

            if (c == 1 && num == 0) {
                extenso += "cem";
            } else {
                extenso += centena[c];
            }

            if (num > 0) {
                extenso += " e ";
            }
        }

        if (num >= 10 && num <= 19) {
            u = num % 10;
            extenso += dezenaespecial[u];
        } else {
            d = num / 10;
            u = num % 10;

            if (d > 0) {
                extenso += dezena[d];
            }

            if (d > 0 && u > 0) {
                extenso += " e ";
            }

            if (u > 0) {
                extenso += unidade[u];
            }
        }

        return extenso.trim();
    }
}
