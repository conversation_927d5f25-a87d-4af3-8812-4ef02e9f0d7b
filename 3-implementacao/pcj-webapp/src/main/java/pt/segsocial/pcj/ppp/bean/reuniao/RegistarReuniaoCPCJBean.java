package pt.segsocial.pcj.ppp.bean.reuniao;


import com.ocpsoft.pretty.faces.annotation.URLAction;
import com.ocpsoft.pretty.faces.annotation.URLBeanName;
import org.apache.deltaspike.core.api.scope.ViewAccessScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.ppp.enums.TipoLocalReuniao;
import pt.segsocial.pcj.core.util.JsfMessageUtil;

import javax.annotation.PostConstruct;
import javax.ejb.LocalBean;
import javax.ejb.Stateful;
import javax.inject.Named;
import java.io.IOException;

import static pt.segsocial.pcj.core.util.Constants.*;

@URLBeanName("pcjRegistarReuniaoBean")
@Named(value = "pcjRegistarReuniaoBean")
@Stateful(name = "pcjRegistarReuniaoBean")
@LocalBean
@ViewAccessScoped
public class RegistarReuniaoCPCJBean extends BaseReuniaoEventoBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(RegistarReuniaoCPCJBean.class);

    @Override
    @PostConstruct
    public void init() {
        super.init();
        tipoReuniaoSelecionado = TipoLocalReuniao.MORADA_CPCJ;
    }

    @URLAction(mappingId = PCJSubsystem.OP_REGISTAR_REUNIAO, onPostback = false)
    public void initRegistarAgenda() {
        carregarModalidadesETipos();
        carregarPontosDeTrabalho();
        carregarDuracoes();
        carregarMembrosComissao(true);
        try {
            carregarSalasEReunioes();
            definirComissao();
        } catch (DomainException e) {
            LOGGER.error("Erro ao carregar reunião para registar: {}", e.getMessage());
            JsfMessageUtil.mostraMensagemErro("Erro ao carregar dados da reunião.");
        }
    }

    public void registarReuniao() {
        LoggingHelper.logEntrada(LOGGER, this.reuniaoEventoDTO);
        try {
            validacaoRegistarReuniao();
            reuniaoCPCJService.salvarOuAlterarReuniao(this.reuniaoEventoDTO, this.getPerfilDTO(), this.tipoReuniaoSelecionado);
            JsfMessageUtil.mostraMensagemSucesso(String.format(EVENTO_SALVO_COM_SUCESSO, "registado"));
            pcjSubsystem.redirectTo(pcjSubsystem.pesquisarReunioes(), null);
        } catch (DomainException e) {
            LOGGER.error(e.getMessage());
            JsfMessageUtil.mostraMensagemErro(String.format(ERRO_AO_SALVAR_UM_NOVO_EVENTO, "registar"));
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
            JsfMessageUtil.mostraMensagemErro(ERRO_AO_REDIRECIONAR_PARA_PESQUISA_DE_EVENTOS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            JsfMessageUtil.mostraMensagemErro(e.getMessage());
        }
    }
}
