package pt.segsocial.pcj.ppp.dto.ata.alargada;

import pt.segsocial.gvr.api.util.StringUtil;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class FiltroPesquisaAtaDTO {

    private Date dataInicio;

    private Date dataFim;

    private ComissaoPCJDTO comissao = new ComissaoPCJDTO();

    private Long idComissao;

    private String tipoAta;

    private String estadoAta;

    private String tipoReuniao;

    public boolean filtrosVaziosOuNulos() {
        return comissao == null && dataInicio == null && dataFim == null
                && StringUtil.isNullOrEmpty(tipoAta) && StringUtil.isNullOrEmpty(estadoAta);
    }

    public boolean existemFiltrosValidos() {
        return !filtrosVaziosOuNulos();
    }

    public void limpar() {
        comissao = new ComissaoPCJDTO();
        dataInicio = null;
        dataFim = null;
        estadoAta = "";
        tipoAta = "";
        tipoReuniao = "";
    }

    @Override
    public String toString() {
        return "FiltroPesquisaAtaDTO{" +
                ", dataInicio=" + dataInicio +
                ", tipoAta='" + tipoAta + '\'' +
                ", estado=" + estadoAta + '\'' +
                ", idComissao=" + idComissao +
                '}';
    }

    public ComissaoPCJDTO getComissao() {
        return comissao;
    }

    public void setComissao(ComissaoPCJDTO comissao) {
        this.comissao = comissao;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getEstadoAta() {
        return estadoAta;
    }

    public void setEstadoAta(String estadoAta) {
        this.estadoAta = estadoAta;
    }

    public Map<String, Object> getFiltrosPesquisaMapeados() {
        Map<String, Object> map = new HashMap<>();
        map.put("dataInicio", getDataInicio());
        map.put("dataFim", getDataFim());
        map.put("idComissao", getIdComissao());
        map.put("tipoAta", getTipoAta());
        map.put("estadoAta", getEstadoAta());
        map.put("tipoReuniao", getTipoReuniao());

        return map;
    }

    public Long getIdComissao() {
        if (this.comissao != null) {
            idComissao = this.comissao.getId();
        }
        return idComissao;
    }

    public void setIdComissao(Long idComissao) {
        this.idComissao = idComissao;
    }

    public String getTipoAta() {
        return tipoAta;
    }

    public void setTipoAta(String tipoAta) {
        this.tipoAta = tipoAta;
    }

    public String getTipoReuniao() {
        return tipoReuniao;
    }

    public void setTipoReuniao(String tipoReuniao) {
        this.tipoReuniao = tipoReuniao;
    }
}


