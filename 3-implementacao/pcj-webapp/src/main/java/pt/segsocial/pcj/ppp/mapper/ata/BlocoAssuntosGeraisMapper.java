package pt.segsocial.pcj.ppp.mapper.ata;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoAssuntosGeraisDTO;
import pt.segsocial.pcj.ppp.jpa.entity.ata.alargada.BlocoAssuntoGeral;


@Mapper(componentModel = "cdi", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BlocoAssuntosGeraisMapper {

    BlocoAssuntoGeral toEntity(BlocoAssuntosGeraisDTO blocoAssuntosGeraisDTO);

    BlocoAssuntosGeraisDTO toDTO(BlocoAssuntoGeral blocoAssuntoGeral);

}
