package pt.segsocial.pcj.pae.jpa.dao;

import pt.segsocial.pcj.pae.jpa.entity.Entrevista;
import pt.segsocial.pcj.pae.jpa.entity.EntrevistaAgregado;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;

public class EntrevistaAgregadoDAO extends PCJDao<EntrevistaAgregado, Long> {

    public EntrevistaAgregadoDAO(EntityManager entityManager) {
        super(entityManager, EntrevistaAgregado.class);
    }

    public EntrevistaAgregado findEntrevistaAgregaboByEntrevistaAndResponsavel(Entrevista entrevista) {
        try {
            return getEntityManager()
                    .createNamedQuery(EntrevistaAgregado.FIND_BY_ENTREVISTA_RESPONSAVEL, EntrevistaAgregado.class)
                    .setParameter("entrevista", entrevista)
                    .getSingleResult();
        } catch(NoResultException e) {
            return null;
        }
    }
}