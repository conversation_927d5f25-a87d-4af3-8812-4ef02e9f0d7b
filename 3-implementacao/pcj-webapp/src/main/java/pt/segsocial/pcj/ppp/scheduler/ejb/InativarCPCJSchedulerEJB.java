package pt.segsocial.pcj.ppp.scheduler.ejb;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.ppp.enums.TipoRegistoLogEnum;
import pt.segsocial.pcj.ppp.jpa.dao.*;
import pt.segsocial.pcj.ppp.jpa.entity.*;

import javax.ejb.LocalBean;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceContextType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@LocalBean
@Stateless(name = "pcjInativarCpcjEJB")
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class InativarCPCJSchedulerEJB implements Serializable {

    private static final String CESSADO_INATIVACAO_CPCJ = "Cessado por inativação da CPCJ";
    private static final Logger LOG = LoggerFactory.getLogger(InativarCPCJSchedulerEJB.class);

    @PersistenceContext(unitName = PCJSubsystem.PERSISTENCE_UNIT_PCJ, type = PersistenceContextType.TRANSACTION)
    private EntityManager entityManager;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void inativarComissoes() throws DomainException {
        List<ComissaoPCJppp> comissoes = new ComissaoPCJpppDAO(entityManager).obterComissoesSemCompetenciaAssociada();
        if (CollectionUtils.isNotEmpty(comissoes)) {
            for (ComissaoPCJppp comissaoPCJppp : comissoes) {
                comissaoPCJppp.setAtivo(false);
                new ComissaoPCJpppDAO(entityManager).update(comissaoPCJppp);
                registarLogCpcj(comissaoPCJppp);
            }
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void inativarElementos() throws DomainException {
        List<Elemento> elementos = new ElementoDAO(entityManager).obterElementosComissoesSemCompetenciaAssociada();
        for (Elemento elemento : elementos) {
            elemento.setAtivo(false);
            elemento.setDataFimVigencia(new Date());
            if(elemento.getCargoMandato() != null) {
                elemento.setDataFimMandato(new Date());
            }
            new ElementoDAO(entityManager).update(elemento);
            inativarPerfilElemento(elemento);
            registarLogElemento(elemento);
            criarElementoVersao(elemento);
            criarHistoricoElemento(elemento);
        }
    }

    private void inativarPerfilElemento(Elemento elemento) throws DomainException {
        PerfilElementoDAO dao = new PerfilElementoDAO(entityManager);
        List<PerfilElemento> perfisElemento = dao.buscarPerfisElementoPorElemento(elemento);
        for (PerfilElemento perfilElemento: perfisElemento) {
            perfilElemento.setFimVigencia(new Date());
            dao.update(perfilElemento);
        }
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void inativarEntidadesExternas() throws DomainException {
        List<Long> idsEntidadesExternas = new EntidadeExternaDAO(entityManager).obterEntidadesMPSemComissao();
        EntidadeExternaDAO dao = new EntidadeExternaDAO(entityManager);
        for (Long idEntidadeExterna : idsEntidadesExternas) {
            EntidadeExterna entidadeExterna = dao.find(idEntidadeExterna);
            entidadeExterna.setAtivo(false);
            dao.update(entidadeExterna);
            registarLogEntidadeExterna(entidadeExterna);
            inativarPerfilEntidade(entidadeExterna);
        }
    }

    private void inativarPerfilEntidade(EntidadeExterna entidadeExterna) throws DomainException {
        PerfilEntidadeDAO dao = new PerfilEntidadeDAO(entityManager);
        List<PerfilEntidade> perfisEntidade = dao.buscarPerfisEntidadePorEntidadeExterna(entidadeExterna);
        for (PerfilEntidade perfilEntidade: perfisEntidade) {
            perfilEntidade.setFimVigencia(new Date());
            dao.update(perfilEntidade);
        }
    }
    private UtilizadorPCJ getUtilizador() {
        return new UtilizadorPCJDAO(entityManager).findUtilizadorSched();
    }

    private void registarLogCpcj(ComissaoPCJppp comissaoPCJppp) {
        RegistoLog registoLog = new RegistoLog();
        registoLog.setUtilizadorPCJ(getUtilizador());
        registoLog.setDescricao(String.format(TipoRegistoLogEnum.SCHED_INATIVAR_CPCJ.getDescricao(), comissaoPCJppp.getNome()));
        registoLog.setDataHora(new Date());
        gravarRegistoLog(registoLog);
    }

    private void registarLogElemento(Elemento elemento) {
        RegistoLog registoLog = new RegistoLog();
        registoLog.setUtilizadorPCJ(getUtilizador());
        registoLog.setNumeroCartao(elemento.getNumeroCartao());
        registoLog.setDataHora(new Date());
        registoLog.setDescricao(String.format(TipoRegistoLogEnum.SCHED_INATIVAR_CPCJ.getDescricao(), elemento.getNumeroCartao()));
        gravarRegistoLog(registoLog);
    }

    public void registarLogEntidadeExterna(EntidadeExterna entidadeExterna) {
        RegistoLog registoLog = new RegistoLog();
        registoLog.setDescricao(String.format(TipoRegistoLogEnum.SCHED_INATIVAR_CPCJ.getDescricao(), entidadeExterna.getTipoEntidadeExterna()));
        registoLog.setUtilizadorPCJ(getUtilizador());
        registoLog.setDataHora(new Date());
        gravarRegistoLog(registoLog);
    }

    public void gravarRegistoLog(RegistoLog registoLog) {
        try {
            new RegistoLogDAO(entityManager).create(registoLog);
        } catch (DomainException e) {
            LOG.error("Nao foi possivel registar o log {}", registoLog);
        }
    }

    private void criarElementoVersao(Elemento elemento) throws DomainException {
        ElementoVersao elementoVersao = new ElementoVersao();
        elementoVersao.setElemento(elemento);
        elementoVersao.setApoioCn(elemento.isApoioCn());
        elementoVersao.setCargoMandato(elemento.getCargoMandato());
        elementoVersao.setEntidade(elemento.getEntidade());
        elementoVersao.setTipoElemento(elemento.getTipoElemento());
        elementoVersao.setValenciaTecnica(elemento.getValenciaTecnica());
        elementoVersao.setNissUtilizadorResponsavel("00000000000");
        elementoVersao.setDataInicioMandato(elemento.getDataInicioMandato());
        elementoVersao.setDataFimMandato(elemento.getDataFimMandato());
        elementoVersao.setDataInicioVigencia(elemento.getDataInicioVigencia());
        elementoVersao.setDataFimVigencia(elemento.getDataFimVigencia());
        elementoVersao.setDataPrimeiraRenovacaoVigencia(elemento.getDataPrimeiraRenovacaoVigencia());
        elementoVersao.setDataRenovacaoMandato(elemento.getDataRenovacaoMandato());
        elementoVersao.setDataSegundaRenovacaoVigencia(elemento.getDataSegundaRenovacaoVigencia());
        elementoVersao.setDataVersao(new Date());
        elementoVersao.setEmail(elemento.getEmail());
        elementoVersao.setHoraSemanal(elemento.getHoraSemanal());
        elementoVersao.setHoraMensal(elemento.getHoraMensal());
        elementoVersao.setDocumentoPCJ((elemento.getDocumentoPCJ() != null && elemento.getDocumentoPCJ().getId() != null) ? new DocumentoPCJDAO(entityManager).find(elemento.getDocumentoPCJ().getId()) : elemento.getDocumentoPCJ());
        elementoVersao.setNomeComissao(elemento.getComissao().getNome());
        elementoVersao.setNomeProfissional("PCJ_SCHED");
        elementoVersao.setOutraValencia(elemento.getOutraValencia());
        elementoVersao.setAtivo(elemento.isAtivo());
        new ElementoVersaoDAO(entityManager).update(elementoVersao);
    }

    private void criarHistoricoElemento(Elemento elemento) throws DomainException {
        HistoricoElemento historicoElemento = new HistoricoElemento();
        historicoElemento.setDescricao(CESSADO_INATIVACAO_CPCJ);
        historicoElemento.setElemento(elemento);
        historicoElemento.setData(new Date());
        new HistoricoElementoDAO(entityManager).create(historicoElemento);
    }
}
