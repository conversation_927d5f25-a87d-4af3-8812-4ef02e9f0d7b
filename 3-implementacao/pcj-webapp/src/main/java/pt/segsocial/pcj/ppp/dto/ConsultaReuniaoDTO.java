package pt.segsocial.pcj.ppp.dto;

import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaParaRegistoDTO;
import pt.segsocial.pcj.ppp.dto.elemento.ElementoDTO;
import pt.segsocial.pcj.ppp.enums.TipoLocalReuniao;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static pt.segsocial.pcj.core.util.Constants.*;

public class ConsultaReuniaoDTO implements Serializable {
    private static final SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

    private BigDecimal id;
    private String idModalidade;
    private String modalidade;
    private String idTipo;
    private String tipo;
    private BigDecimal idResponsavel;
    private String responsavel;
    private String tituloLocal;
    private String descricaoLocal;
    private String nomeSala;
    private Date dataInicio;
    private Date dataFim;
    private String horaInicio;
    private String horaFim;
    private BigDecimal idComissao;
    private String assunto;
    private String descricao;
    private boolean ativo;
    private String tipoConvocatoria;
    private Boolean convocatoriaEnviada;

    private String tempoEstimado;
    private List<ElementoDTO> convocados;
    private TipoLocalReuniao tipoLocalReuniao;
    private List<PontoTrabalhoDTO> pontosTrabalho;
    private List<ElementoDTO> participantes;
    private String responsavelCPCJ;
    private String entidade;
    private List<BlocoComunicacaoRestritaParaRegistoDTO> blocosComunicacaoRestrita;
    private boolean fluxoComunicacao;

    public ConsultaReuniaoDTO() {
    }

    public ConsultaReuniaoDTO(BigDecimal id, String idModalidade, String idTipo, BigDecimal idResponsavel, String tituloLocal, String descricaoLocal,
                              String nomeSala, Date dataInicio, Date dataFim, Timestamp horaInicio, Timestamp horaFim, BigDecimal idComissao, String assunto,
                              String descricao, BigDecimal ativo, String idTipoConvocatoria, Boolean convocatoriaEnviada) {
        this.id = id;
        this.idModalidade = idModalidade;
        this.idTipo = idTipo;
        this.idResponsavel = idResponsavel;
        this.tituloLocal = tituloLocal;
        this.descricaoLocal = descricaoLocal;
        this.nomeSala = nomeSala;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.horaInicio = sdf.format(horaInicio);
        this.horaFim = sdf.format(horaFim);
        this.idComissao = idComissao;
        this.assunto = assunto != null ? assunto : "";
        this.descricao = descricao != null ? descricao : "";
        this.ativo = ativo.equals(BigDecimal.valueOf(1));
        this.tipoConvocatoria = idTipoConvocatoria;
        this.tempoEstimado = calculaTempoEstimado(horaInicio, horaFim);
        this.convocados = new ArrayList<>();
        this.pontosTrabalho = new ArrayList<>();
        this.participantes = new ArrayList<>();
        this.convocatoriaEnviada = convocatoriaEnviada;
    }

    private String calculaTempoEstimado(Timestamp horaInicio, Timestamp horaFim) {
        if (horaInicio != null && horaFim != null) {
            return PCJDateUtil.retornaDiferencaEntreDoisHorarios(horaInicio, horaFim);
        }
        return "-";
    }

    public Boolean reuniaoConvocatoriaEnviada() {
        return convocatoriaEnviada != null && convocatoriaEnviada;
    }

    public Boolean reuniaoConvocatoriaNaoEnviada() {
        return !reuniaoConvocatoriaEnviada();
    }

    public Boolean reuniaoUltrapassada() {
        try {
            return PCJDateUtil.isDataHoraMenorQueAtual(dataFim, horaFim);
        } catch (PCJException e) {
            return false;
        }
    }

    public Boolean reuniaoNaoUltrapassada() {
        return !reuniaoUltrapassada();
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getIdModalidade() {
        return idModalidade;
    }

    public void setIdModalidade(String idModalidade) {
        this.idModalidade = idModalidade;
    }

    public String getIdTipo() {
        return idTipo;
    }

    public void setIdTipo(String idTipo) {
        this.idTipo = idTipo;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public BigDecimal getIdResponsavel() {
        return idResponsavel;
    }

    public void setIdResponsavel(BigDecimal idResponsavel) {
        this.idResponsavel = idResponsavel;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getTituloLocal() {
        return tituloLocal;
    }

    public void setTituloLocal(String tituloLocal) {
        this.tituloLocal = tituloLocal;
    }

    public String getDescricaoLocal() {
        return descricaoLocal;
    }

    public void setDescricaoLocal(String descricaoLocal) {
        this.descricaoLocal = descricaoLocal;
    }

    public String getNomeSala() {
        return nomeSala;
    }

    public void setNomeSala(String nomeSala) {
        this.nomeSala = nomeSala;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public BigDecimal getIdComissao() {
        return idComissao;
    }

    public void setIdComissao(BigDecimal idComissao) {
        this.idComissao = idComissao;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoLocalReuniao getTipoLocalReuniao() {
        return tipoLocalReuniao;
    }

    public void setTipoLocalReuniao(TipoLocalReuniao tipoLocalReuniao) {
        this.tipoLocalReuniao = tipoLocalReuniao;
    }

    public String getTempoEstimado() {
        return tempoEstimado;
    }

    public void setTempoEstimado(String tempoEstimado) {
        this.tempoEstimado = tempoEstimado;
    }

    public List<ElementoDTO> getConvocados() {
        return convocados;
    }

    public void setConvocados(List<ElementoDTO> convocados) {
        this.convocados = convocados;
    }

    public String getTipoConvocatoria() {
        return tipoConvocatoria;
    }

    public void setTipoConvocatoria(String tipoConvocatoria) {
        this.tipoConvocatoria = tipoConvocatoria;
    }

    public List<PontoTrabalhoDTO> getPontosTrabalho() {
        return pontosTrabalho;
    }

    public void setPontosTrabalho(List<PontoTrabalhoDTO> pontosTrabalho) {
        this.pontosTrabalho = pontosTrabalho;
    }

    public List<ElementoDTO> getParticipantes() {
        return participantes;
    }

    public void setParticipantes(List<ElementoDTO> participantes) {
        this.participantes = participantes;
    }

    public String getResponsavelCPCJ() {
        return responsavelCPCJ;
    }

    public void setResponsavelCPCJ(String responsavelCPCJ) {
        this.responsavelCPCJ = responsavelCPCJ;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getConvocatoriaEnviada() {
        return convocatoriaEnviada;
    }

    public void setConvocatoriaEnviada(Boolean convocatoriaEnviada) {
        this.convocatoriaEnviada = convocatoriaEnviada;
    }

    public boolean isReuniaoComissaoAlargada() {
        return (MODALIDADE_ALARGADA.equals(idModalidade) && TIPO_ALARGADA.equals(idTipo));
    }

    public boolean isReuniaoComissaoRestrita() {
        return (MODALIDADE_RESTRITA.equals(idModalidade) && TIPO_RESTRITA.equals(idTipo));
    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> getBlocosComunicacaoRestrita() {
        return blocosComunicacaoRestrita;
    }

    public void setBlocosComunicacaoRestrita(List<BlocoComunicacaoRestritaParaRegistoDTO> blocosComunicacaoRestrita) {
        this.blocosComunicacaoRestrita = blocosComunicacaoRestrita;
    }

    public void setFluxoComunicacao(boolean fluxoComunicacao) {
        this.fluxoComunicacao = fluxoComunicacao;
    }

    public boolean isFluxoComunicacao() {
        return fluxoComunicacao;
    }

    @Override
    public String toString() {
        return "ConsultaReuniaoDTO{" +
                "id=" + id +
                ", modalidade='" + modalidade + '\'' +
                ", idTipo='" + idTipo + '\'' +
                ", tipo='" + tipo + '\'' +
                ", responsavel='" + responsavel + '\'' +
                ", descricaoLocal='" + descricaoLocal + '\'' +
                ", nomeSala='" + nomeSala + '\'' +
                ", idComissao=" + idComissao +
                ", assunto='" + assunto + '\'' +
                ", ativo=" + ativo +
                ", responsavelCPCJ='" + responsavelCPCJ + '\'' +
                '}';
    }
}
