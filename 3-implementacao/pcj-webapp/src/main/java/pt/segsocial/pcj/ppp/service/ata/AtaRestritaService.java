package pt.segsocial.pcj.ppp.service.ata;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.core.util.Constants;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.LoggingHelper;
import pt.segsocial.pcj.core.util.PCJDateUtil;
import pt.segsocial.pcj.ppp.dto.ComissaoPCJDTO;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.ata.restrita.*;
import pt.segsocial.pcj.ppp.dto.comunicacao.BlocoComunicacaoRestritaParaRegistoDTO;
import pt.segsocial.pcj.ppp.dto.comunicacao.FiltroPesquisaComunicacaoRegistoEvento;
import pt.segsocial.pcj.ppp.enums.*;
import pt.segsocial.pcj.ppp.jpa.dao.BlocoComunicacaoRestritaDAO;
import pt.segsocial.pcj.ppp.jpa.dao.ConvidadoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.DocumentoAtaRestritaPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.ReuniaoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.ata.restrita.*;
import pt.segsocial.pcj.ppp.jpa.entity.*;
import pt.segsocial.pcj.ppp.jpa.entity.ata.restrita.*;
import pt.segsocial.pcj.ppp.mapper.*;
import pt.segsocial.pcj.ppp.mapper.ata.BlocoGeralRestritaMapper;
import pt.segsocial.pcj.ppp.mapper.ata.ReuniaoAtaMapper;
import pt.segsocial.pcj.ppp.service.*;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@RequestScoped
public class AtaRestritaService implements Serializable {

    protected static final Logger LOGGER = LoggerFactory.getLogger(AtaRestritaService.class);

    private final String TIPO_CONVIDADO = "CONVIDADO";
    private final String TIPO_ELEMENTO = "ELEMENTO";
    private final String PRESIDENTE = "Presidente";
    private final String SECRETARIO = "Secretário";
    private final String FUNCAO_CONVIDADO = "Convidado";

    @Inject
    private PontoTrabalhoPCJService pontoTrabalhoPCJService;
    @Inject
    private PCJServiceLocator pcjServiceLocator;
    @Inject
    private DocumentoPCJService documentoPCJService;
    @Inject
    private ComissaoPCJService comissaoPCJService;
    @Inject
    private ElementoService elementoService;
    @Inject
    private ReuniaoCPCJService reuniaoCPCJService;

    //DAO
    @Inject
    private AtaRestritaDAO ataRestritaDAO;
    @Inject
    private ElementoFolhaRestritaDAO elementoFolhaRestritaDAO;
    @Inject
    private ConvidadoFolhaRestritaDAO convidadoFolhaRestritaDAO;
    @Inject
    private ReuniaoPCJDAO reuniaoPCJDAO;
    @Inject
    private ConvidadoPCJDAO convidadoPCJDAO;
    @Inject
    private OrdenacaoAtaRestritaDAO ordenacaoAtaRestritaDAO;
    @Inject
    private DocumentoAtaRestritaPCJDAO documentoAtaRestritaPCJDAO;

    //DAO Blocos
    @Inject
    private BlocoComunicacaoRestritaDAO blocoComunicacaoRestritaDAO;
    @Inject
    private BlocoGeralRestritaDAO blocoGeralRestritaDAO;


    //Mapper
    @Inject
    private AtaRestritaMapper ataRestritaMapper;
    @Inject
    private ConvidadoRestritaPCJMapper convidadoRestritaPCJMapper;
    @Inject
    private OrdenacaoAtaRestritaMapper ordenacaoAtaRestritaMapper;
    @Inject
    private ComissaoPPPMapper comissaoPPPMapper;
    @Inject
    private ReuniaoAtaMapper reuniaoMapper;
    @Inject
    private ElementoMapper elementoMapper;
    //Mapper Blocos
    @Inject
    private BlocoGeralRestritaMapper blocoAssuntosGeraisMapper;

    @Inject
    private DocumentoMapper documentoMapper;

    public AtaRestritaDTO buscarAtaRestrita(Long idAtaRestrita) {
        return ataRestritaMapper.toDTO(ataRestritaDAO.find(idAtaRestrita));
    }

    public void salvarFolhaPresenca(AtaRestritaDTO ataRestritaDTO, Long idReuniao, Long idComissao) throws DomainException {
        criarComissaoReuniao(ataRestritaDTO, idReuniao, idComissao);
        criarAta(ataRestritaDTO);
        criarFolhaPresenca(ataRestritaDTO);
        AtaRestrita ataRestrita = ataRestritaMapper.toEntity(ataRestritaDTO);
        if (ataRestritaDTO.getId() != null) {
            ataRestritaDAO.update(ataRestrita);
        } else {
            ataRestritaDAO.create(ataRestrita);
            ataRestritaDTO.setId(ataRestrita.getId());
        }
        persistirFolhaPresenca(ataRestritaDTO, ataRestrita);
        LoggingHelper.logSaida();
    }

    public void saveDraft(AtaRestritaDTO ataRestritaDTO) throws DomainException {
        boolean ataAtualizada = ataRestritaDAO.updateAtaRestrita(ataRestritaMapper.toEntity(ataRestritaDTO));
        if(!ataAtualizada) {
            throw new DomainException("Não foi possível atualizar a ata.");
        }
        LoggingHelper.logSaida();
    }

    public void saveOrUpdateWorkBlock(OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO, List<TrabalhosGuardadosDTO> trabalhosGuardadosDTO) throws DomainException {
        if (ordenacaoAtaRestritaDTO.getId() == null) {
            OrdenacaoAtaRestritaPCJ ordenacaoAtaRestritaPCJ = ordenacaoAtaRestritaMapper.toEntity(ordenacaoAtaRestritaDTO);
            ordenacaoAtaRestritaDAO.create(ordenacaoAtaRestritaPCJ);
            trabalhosGuardadosDTO.add(new TrabalhosGuardadosDTO(ordenacaoAtaRestritaMapper.toDTO(ordenacaoAtaRestritaPCJ)));
            JsfMessageUtil.mostraMensagemSucesso("Ordem de trabalho registado com sucesso.");
        } else {
            ordenacaoAtaRestritaDAO.update(ordenacaoAtaRestritaMapper.toEntity(ordenacaoAtaRestritaDTO));
            JsfMessageUtil.mostraMensagemSucesso("Ordem de trabalho alterado com sucesso.");
        }
        LoggingHelper.logSaida();
    }

    public void removeWorkOrder(OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO, List<TrabalhosGuardadosDTO> trabalhosGuardadosDTO) throws DomainException {
        OrdenacaoAtaRestritaPCJ ordenacaoAtaRestritaPCJ = ordenacaoAtaRestritaMapper.toEntity(ordenacaoAtaRestritaDTO);
        ordenacaoAtaRestritaDAO.remove(ordenacaoAtaRestritaPCJ);
        for (TrabalhosGuardadosDTO trabalho : trabalhosGuardadosDTO) {
            if (trabalho.getOrdenacaoAtaRestrita().getId().equals(ordenacaoAtaRestritaDTO.getId())) {
                trabalhosGuardadosDTO.remove(trabalho);
                break;
            }
        }
    }

    public void criarFolhaPresenca(AtaRestritaDTO ataRestritaDTO, Long idReuniao, Long idComissao) throws DomainException {
        ataRestritaDTO.setFolhaPresencaDTO(new FolhaPresencaDTO());
        ataRestritaDTO.getFolhaPresencaDTO().setElementosDTO(ataRestritaDTO.getElementosSelected());
        ataRestritaDTO.getFolhaPresencaDTO().setConvidadosDTO(ataRestritaDTO.getListaConvidadosAdd());
        salvarFolhaPresenca(ataRestritaDTO, idReuniao, idComissao);
        criarListaConclusao(ataRestritaDTO);
    }

    public void criarListaConclusao(AtaRestritaDTO ataRestritaDTO) {
        ataRestritaDTO.getListaConclusaoDTO().clear();
        ataRestritaDTO.getListaAssinaturaSelected().clear();
        for (ConvidadoPresencaDTO convidadoFolha : ataRestritaDTO.getFolhaPresencaDTO().getConvidadosDTO()) {
            ataRestritaDTO.getListaConclusaoDTO().add(criarConclusaoConvidado(convidadoFolha));
        }
        for (ElementoPresencaRestritaDTO elementoFolha : ataRestritaDTO.getFolhaPresencaDTO().getElementosDTO()) {
            ataRestritaDTO.getListaConclusaoDTO().add(criarConclusaoElemento(elementoFolha));
        }
        selecionarPresidenteOuSecretario(ataRestritaDTO);
    }

    private void selecionarPresidenteOuSecretario(AtaRestritaDTO ataRestritaDTO) {
        for (ConclusaoDTO conclusaoDTO : ataRestritaDTO.getListaConclusaoDTO()) {
            if (conclusaoDTO.getCargo() != null && conclusaoDTO.getCargo().equals(PRESIDENTE)) {
                conclusaoDTO.setAssinatura(true);
                ataRestritaDTO.getListaAssinaturaSelected().add(conclusaoDTO);
                return;
            }
        }
        for (ConclusaoDTO conclusaoDTO : ataRestritaDTO.getListaConclusaoDTO()) {
            if (conclusaoDTO.getCargo() != null && conclusaoDTO.getCargo().equals(SECRETARIO)) {
                conclusaoDTO.setAssinatura(true);
                ataRestritaDTO.getListaAssinaturaSelected().add(conclusaoDTO);
            }
        }
    }

    public AtaRestritaDTO carregarAtaRestrita(Long idReuniao) {
        try {
            AtaRestrita ataRestrita = ataRestritaDAO.findByMeeting(idReuniao);

             if (ataRestrita.getId() != null) {
                AtaRestritaDTO ataRestritaDTO = AtaRestritaDTO.builder()
                        .id(ataRestrita.getId())
                        .estadoAta(ataRestrita.getEstadoAta())
                        .numeroAtaRestrita(ataRestrita.getNumeroAtaRestrita())
                        .reuniaoPCJ(reuniaoMapper.toDTO(ataRestrita.getReuniaoPCJ()))
                        .comissao(comissaoPPPMapper.toDTO(ataRestrita.getComissao()))
                        .dataRealizacao(ataRestrita.getDataRealizacao())
                        .textoIntrodutorio(ataRestrita.getTextoIntrodutorio())
                        .textoConclusivo(ataRestrita.getTextoConclusivo())
                        .convidadosFolhaPresenca(obterConvidadosFolhaPresencaDTO(ataRestrita))
                        .elementosFolhaPresenca(obterElementosFolhaPresencaDTO(ataRestrita))
                        .build();
                carregarListasFolhaPresencaConclusao(ataRestritaDTO);
                carregarTrabalhosGuardadosBaseDTO(ataRestritaDTO);
//                carregarComunicacoes(ataRestritaDTO);
                return ataRestritaDTO;
            }
            return new AtaRestritaDTO();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public List<BlocoComunicacaoRestritaParaRegistoDTO> carregarComunicacoes(ReuniaoPCJ reuniao) {
        LoggingHelper.logEntrada("Carregando comunicacoes para ata restrita");
        if(reuniao == null || reuniao.getId() == null) {
            LoggingHelper.logError(new DomainException("Id da reuniao nao pode ser nulo ao carregar comunicacoes"));
            return new ArrayList<>();
        }

        FiltroPesquisaComunicacaoRegistoEvento.Builder filtro = FiltroPesquisaComunicacaoRegistoEvento.builder().idReuniao(reuniao.getId()).idComissao(reuniao.getComissao().getId());
        if(reuniao.isReuniaoExtraordinaria()) {
            filtro.urgencia(true);
        }
        LoggingHelper.logSaida("Sucesso ao carregar comunicacoes para ata restrita");
        return blocoComunicacaoRestritaDAO.recuperarBlocosComunicacaoRestritaPorFiltro(filtro.build());
    }

    public void gravarConvidado(ConvidadoPCJDTO convidadoPCJDTO) throws DomainException {
        if (convidadoPCJDTO.getId() == null) {
            ConvidadoPCJ convidado = convidadoRestritaPCJMapper.toEntity(convidadoPCJDTO);
            convidadoPCJDAO.create(convidado);
            convidadoPCJDTO.setId(convidado.getId());
        } else {
            convidadoPCJDAO.update(convidadoRestritaPCJMapper.toEntity(convidadoPCJDTO));
        }
    }

    public void persistirConclusao(List<ConclusaoDTO> listaAssinaturaSelected, Long idAtaRestrita) throws DomainException {
        for (ConclusaoDTO conclusaoDTO : listaAssinaturaSelected) {
            conclusaoDTO.setAssinatura(true);
            if (conclusaoDTO.getTipo().equals(TIPO_ELEMENTO)) {
                ElementoFolhaRestrita elementoFolhaRestrita = elementoFolhaRestritaDAO.findByIdElementoIdAtaRestrita(conclusaoDTO.getId(), idAtaRestrita);
                elementoFolhaRestrita.setAssinatura(true);
                elementoFolhaRestritaDAO.update(elementoFolhaRestrita);
            }
            if (conclusaoDTO.getTipo().equals(TIPO_CONVIDADO)) {
                ConvidadoFolhaRestrita convidadoFolhaRestrita = convidadoFolhaRestritaDAO.findByIdElementoIdAtaRestrita(conclusaoDTO.getId(), idAtaRestrita);
                convidadoFolhaRestrita.setAssinatura(true);
                convidadoFolhaRestritaDAO.update(convidadoFolhaRestrita);
            }
        }
    }

    public void guardarAtaFinal(AtaRestritaDTO ataRestritaDTO) throws DomainException {
        ataRestritaDTO.setEstadoAta(EstadoAtaEnum.ASSINATURA);
        ataRestritaDAO.update(ataRestritaMapper.toEntity(ataRestritaDTO));
        LoggingHelper.logSaida();
    }

    public AtaRestritaDTO consultarAtaRestrita(Long idReuniao) {
        AtaRestritaDTO ataRestritaDTO = new AtaRestritaDTO();
        AtaRestrita ataRestrita = ataRestritaDAO.findByMeeting(idReuniao);
        if (ataRestrita.getId() != null) {
            ataRestritaDTO.setId(ataRestrita.getId());
            ataRestritaDTO.setEstadoAta(ataRestrita.getEstadoAta());
            ataRestritaDTO.setNumeroAtaRestrita(ataRestrita.getNumeroAtaRestrita());
            ataRestritaDTO.setReuniaoPCJ(reuniaoMapper.toDTO(ataRestrita.getReuniaoPCJ()));
            ataRestritaDTO.setComissao(comissaoPPPMapper.toDTO(ataRestrita.getComissao()));
            ataRestritaDTO.setDataRealizacao(ataRestrita.getDataRealizacao());
            ataRestritaDTO.setTextoIntrodutorio(ataRestrita.getTextoIntrodutorio());
            ataRestritaDTO.setTextoConclusivo(ataRestrita.getTextoConclusivo());
        }
        return ataRestritaDTO;
    }

    public boolean verificaExisteTrabalho(AtaRestritaDTO ataRestritaDTO) {
        return CollectionUtils.isEmpty(ordenacaoAtaRestritaDAO.findByAtaRestrita(ataRestritaDTO.getId()));
    }

    public boolean validaQtdBlocosConvocatoria(AtaRestritaDTO ataRestritaDTO, BlocosTrabalhoAtaEnum nomeBloco) {
        if (nomeBloco.equals(BlocosTrabalhoAtaEnum.ELEICAO_PRESIDENTE) ||
                nomeBloco.equals(BlocosTrabalhoAtaEnum.DESIGNACAO_SECRETARIO) ||
                nomeBloco.equals(BlocosTrabalhoAtaEnum.COMPOSICAO_RESTRITA) ||
                nomeBloco.equals(BlocosTrabalhoAtaEnum.APROVACAO_RELATORIO_ANUAL) ||
                nomeBloco.equals(BlocosTrabalhoAtaEnum.APROVACAO_ANUAL)) {
            return CollectionUtils.isEmpty(ordenacaoAtaRestritaDAO.findByAtaAndBloco(ataRestritaDTO.getId(), nomeBloco));
        } else {
            return true;
        }
    }

    public ComissaoPCJDTO getComissao(ReuniaoPCJ reuniaoPCJ) {
        return comissaoPPPMapper.toDTO(comissaoPCJService.carregarComissaoPorId(reuniaoPCJ.getComissao().getId()));
    }

    public boolean verificaExisteAtaEstadoAta(Long idReuniao, Long idComissao, EstadoAtaEnum estadoAta) {
        return CollectionUtils.isEmpty(ataRestritaDAO.findByIdReuniaoEstadoAta(idReuniao, idComissao, estadoAta));
    }

    public Long buscarIdReuniaoExcluindoConcluidoOuAssinado(Long idCpcj) {
        return ataRestritaDAO.buscarIdReuniaoExcluindoConcluidoOuAssinado(idCpcj);
    }

    public boolean findByIdReuniaoEstadosAssConcl(Long idReuniao, Long idComissao) {
        return ataRestritaDAO.findByIdReuniaoEstadosAssConcl(idReuniao, idComissao);
    }

    private void carregarTrabalhosGuardadosBaseDTO(AtaRestritaDTO ataRestritaDTO) {
        List<OrdenacaoAtaRestritaPCJ> listaOrdenacao = ordenacaoAtaRestritaDAO.findByAtaRestrita(ataRestritaDTO.getId());
        if (CollectionUtils.isNotEmpty(listaOrdenacao)) {
            ataRestritaDTO.getTrabalhosGuardadosDTO().clear();
            for (OrdenacaoAtaRestritaPCJ ordenacaoAtaRestritaPCJ : listaOrdenacao) {
                OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO = new OrdenacaoAtaRestritaDTO();
                ordenacaoAtaRestritaDTO.setId(ordenacaoAtaRestritaPCJ.getId());
                ordenacaoAtaRestritaDTO.setAtaRestrita(ataRestritaDTO);
                obterBlocosTrabalhos(ordenacaoAtaRestritaPCJ, ordenacaoAtaRestritaDTO);
                ataRestritaDTO.getTrabalhosGuardadosDTO().add(new TrabalhosGuardadosDTO(ordenacaoAtaRestritaDTO));
            }
        }
    }

    private void criarAta(AtaRestritaDTO ataRestritaDTO) {
        if (ataRestritaDTO.getId() == null) {
            ataRestritaDTO.setEstadoAta(EstadoAtaEnum.MINUTA);
            ataRestritaDTO.setNumeroAtaRestrita(obterNumeroAta(ataRestritaDTO));
        }
    }

    private void criarFolhaPresenca(AtaRestritaDTO ataRestritaDTO) {
        ataRestritaDTO.setConvidadosFolhaPresenca(obterConvidadosFolhaRestritaDTO(ataRestritaDTO.getFolhaPresencaDTO().getConvidadosDTO()));
        ataRestritaDTO.setElementosFolhaPresenca(obterElementosFolhaPCJDTO(ataRestritaDTO.getFolhaPresencaDTO().getElementosDTO()));
    }

    private void criarComissaoReuniao(AtaRestritaDTO ataRestritaDTO, Long idReuniao, Long idComissao) {
        ComissaoPCJppp comissao = comissaoPCJService.carregarComissaoPorId(idComissao);
        ReuniaoPCJ reuniaoPCJ = reuniaoCPCJService.buscaReuniaoPorId(idReuniao);
        ataRestritaDTO.setComissao(comissaoPPPMapper.toDTO(comissao));
        ataRestritaDTO.setReuniaoPCJ(reuniaoMapper.toDTO(reuniaoPCJ));
    }

    private void persistirFolhaPresenca(AtaRestritaDTO restritaDTO, AtaRestrita ataRestrita) throws DomainException {
        processarElementosFolhaPresenca(restritaDTO, ataRestrita);
        processarConvidadosFolhaPresenca(restritaDTO, ataRestrita);
    }

    private void processarConvidadosFolhaPresenca(AtaRestritaDTO ataRestritaDTO, AtaRestrita ataRestrita) throws DomainException {
        excluirListaConvidadosFolhaPresenca(ataRestritaDTO.getId());
        for (ConvidadoFolhaRestritaDTO convidadoFolhaDTO : ataRestritaDTO.getConvidadosFolhaPresenca()) {
            saveConvidadoFolha(ataRestrita, convidadoFolhaDTO);
        }
    }

    private void saveConvidadoFolha(AtaRestrita ataRestrita, ConvidadoFolhaRestritaDTO convidadoFolhaDTO) throws DomainException {
        ConvidadoFolhaRestrita convidadoFolhaRestrita = criarConvidadoFolhaRestrita(ataRestrita, convidadoFolhaDTO);
        convidadoFolhaRestritaDAO.update(convidadoFolhaRestrita);
    }

    private void processarElementosFolhaPresenca(AtaRestritaDTO ataRestritaDTO, AtaRestrita ataRestrita) throws DomainException {
        excluirListaElementosFolhaPresenca(ataRestritaDTO.getId());
        for (ElementoFolhaRestritaDTO elementoFolhaDTO : ataRestritaDTO.getElementosFolhaPresenca()) {
            ElementoFolhaRestrita elementoFolhaPCJ = criarElementoFolha(ataRestrita, elementoFolhaDTO);
            elementoFolhaRestritaDAO.update(elementoFolhaPCJ);
        }
    }

    private void excluirListaElementosFolhaPresenca(Long idAtaRestrita) throws DomainException {
        List<ElementoFolhaRestrita> elementosFolha = elementoFolhaRestritaDAO.findByAtaAlargada(idAtaRestrita);
        for (ElementoFolhaRestrita elemento : elementosFolha) {
            elementoFolhaRestritaDAO.remove(elemento);
        }
    }

    private ConvidadoFolhaRestrita criarConvidadoFolhaRestrita(AtaRestrita ataRestrita, ConvidadoFolhaRestritaDTO convidadoFolhaDTO) {
        ConvidadoFolhaRestrita convidadoFolhaRestrita = new ConvidadoFolhaRestrita();
        ConvidadoFolhaRestritaId convidadoFolhaId = new ConvidadoFolhaRestritaId(convidadoFolhaDTO.getIdConvidado(), ataRestrita.getId());
        convidadoFolhaRestrita.setId(convidadoFolhaId);
        convidadoFolhaRestrita.setConvidadoPCJ(convidadoPCJDAO.find(convidadoFolhaDTO.getIdConvidado()));
        convidadoFolhaRestrita.setAtaRestrita(ataRestrita);
        convidadoFolhaRestrita.setAssinatura(false);
        return convidadoFolhaRestrita;
    }

    private void excluirListaConvidadosFolhaPresenca(Long idAtaRestrita) throws DomainException {
        List<ConvidadoFolhaRestrita> convidadosFolha = convidadoFolhaRestritaDAO.findByAtaRestrita(idAtaRestrita);
        for (ConvidadoFolhaRestrita convidado : convidadosFolha) {
            convidadoFolhaRestritaDAO.remove(convidado);
        }
    }

    private ElementoFolhaRestrita criarElementoFolha(AtaRestrita ataRestrita, ElementoFolhaRestritaDTO elementoFolhaDTO) {
        ElementoFolhaRestrita elementoFolhaRestrita = new ElementoFolhaRestrita();
        ElementoFolhaRestritaId elementoFolhaId = new ElementoFolhaRestritaId(elementoFolhaDTO.getIdElemento(), ataRestrita.getId());
        elementoFolhaRestrita.setId(elementoFolhaId);
        elementoFolhaRestrita.setElemento(elementoService.findByElementoBasePorId(elementoFolhaDTO.getIdElemento()));
        elementoFolhaRestrita.setAssinatura(false);
        elementoFolhaRestrita.setAtaRestrita(ataRestrita);
        return elementoFolhaRestrita;
    }

    private String obterNumeroAta(AtaRestritaDTO ataRestritaDTO) {
        ReuniaoPCJ reuniaoPCJ = reuniaoPCJDAO.find(ataRestritaDTO.getReuniaoPCJ().getId());
        String anoReuniao = PCJDateUtil.getYear(reuniaoPCJ.getDataInicio());
        return ataRestritaDAO.obterNumeroSequencialAta(ataRestritaDTO.getComissao().getId(), anoReuniao);
    }

    private List<ElementoFolhaRestritaDTO> obterElementosFolhaPCJDTO(List<ElementoPresencaRestritaDTO> elementosPresencaDTO) {
        List<ElementoFolhaRestritaDTO> elementosFolhaPCJDTO = new ArrayList<>();
        for (ElementoPresencaRestritaDTO elementoPresencaDTO : elementosPresencaDTO) {
            elementosFolhaPCJDTO.add(new ElementoFolhaRestritaDTO(elementoPresencaDTO.getId(), false));
        }
        return elementosFolhaPCJDTO;
    }

    private List<ConvidadoFolhaRestritaDTO> obterConvidadosFolhaRestritaDTO(List<ConvidadoPresencaDTO> convidadosPresencaDTO) {
        List<ConvidadoFolhaRestritaDTO> convidadosFolhaPCJDTO = new ArrayList<>();
        for (ConvidadoPresencaDTO convidadoPresencaDTO : convidadosPresencaDTO) {
            convidadosFolhaPCJDTO.add(new ConvidadoFolhaRestritaDTO(convidadoPresencaDTO.getId(), false));
        }
        return convidadosFolhaPCJDTO;
    }

    private ConclusaoDTO criarConclusaoConvidado(ConvidadoPresencaDTO convidadoFolha) {
        return new ConclusaoDTO(convidadoFolha.getId(),
                convidadoFolha.getNome(),
                FUNCAO_CONVIDADO,
                convidadoFolha.getCargo(),
                convidadoFolha.getEntidade(),
                convidadoFolha.getCodigoEntidadeParticipante(),
                convidadoFolha.getDetalheEntidadeParticipante(),
                TIPO_CONVIDADO,
                false);
    }

    private ConclusaoDTO criarConclusaoElemento(ElementoPresencaRestritaDTO elementoFolha) {
        return new ConclusaoDTO(elementoFolha.getId(),
                elementoFolha.getNome(),
                elementoFolha.getFuncao() != null && !elementoFolha.getFuncao().equals("-") ? pcjServiceLocator.getGvrDelegate().detalheDominioByCodigo("TPELEMENTO", elementoFolha.getFuncao()) : "-",
                elementoFolha.getCargo() != null && !elementoFolha.getCargo().equals("-") ? pcjServiceLocator.getGvrDelegate().detalheDominioByCodigo("CARGO", elementoFolha.getCargo()) : "-",
                pcjServiceLocator.getGvrDelegate().detalheDominioByCodigo("ENTIDADE",
                        elementoFolha.getEntidade()), TIPO_ELEMENTO, false);
    }

    private void obterBlocosTrabalhos(OrdenacaoAtaRestritaPCJ ordenacao, OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO) {
        if (ordenacao.getNomeBloco() != null) {
            ordenacaoAtaRestritaDTO.setNomeBloco(ordenacao.getNomeBloco());
            carregarBlocoTrabalhoDTO(ordenacao.getNomeBloco(), ordenacaoAtaRestritaDTO, ordenacao);
        }
    }

    private void carregarBlocoTrabalhoDTO(BlocosTrabalhoAtaRestritaEnum blocosTrabalhoAtaEnum, OrdenacaoAtaRestritaDTO ordenacaoAtaRestritaDTO, OrdenacaoAtaRestritaPCJ ordenacaoAtaRestritaPCJ) {
        if (blocosTrabalhoAtaEnum.equals(BlocosTrabalhoAtaRestritaEnum.ASSUNTOS_GERAIS)) {
            ordenacaoAtaRestritaDTO.setBlocoGeralRestrita(blocoAssuntosGeraisMapper.toDTO(ordenacaoAtaRestritaPCJ.getBlocoGeralRestrita()));
        }

    }

    private void carregarListasFolhaPresencaConclusao(AtaRestritaDTO ataAlargadaDTO) {
        ataAlargadaDTO.setFolhaPresencaDTO(new FolhaPresencaDTO());
        ataAlargadaDTO.getElementosSelected().addAll(obterElementosPresencaDTO(ataAlargadaDTO));
        ataAlargadaDTO.getListaConvidadosAdd().addAll(obterConvidadosPresencaDTO(ataAlargadaDTO));
        criarListaConclusao(ataAlargadaDTO);
        verificarAssinados(ataAlargadaDTO);
    }

    private void verificarAssinados(AtaRestritaDTO ataRestritaDTO) {
        for (ConclusaoDTO conclusaoDTO : ataRestritaDTO.getListaConclusaoDTO()) {
            if (conclusaoDTO.getTipo().equals(TIPO_ELEMENTO)) {
                for (ElementoFolhaRestritaDTO elementoFolha : ataRestritaDTO.getElementosFolhaPresenca()) {
                    if (conclusaoDTO.getId().equals(elementoFolha.getIdElemento()) && elementoFolha.isAssinatura()) {
                        conclusaoDTO.setAssinatura(true);
                        if (!ataRestritaDTO.getListaAssinaturaSelected().contains(conclusaoDTO)) {
                            ataRestritaDTO.getListaAssinaturaSelected().add(conclusaoDTO);
                        }
                    }
                }
            }
            if (conclusaoDTO.getTipo().equals(TIPO_CONVIDADO)) {
                for (ConvidadoFolhaRestritaDTO convidadoFolha : ataRestritaDTO.getConvidadosFolhaPresenca()) {
                    if (conclusaoDTO.getId().equals(convidadoFolha.getIdConvidado()) && convidadoFolha.isAssinatura()) {
                        conclusaoDTO.setAssinatura(true);
                        ataRestritaDTO.getListaAssinaturaSelected().add(conclusaoDTO);
                    }
                }
            }
        }
    }

    private List<ElementoPresencaRestritaDTO> obterElementosPresencaDTO(AtaRestritaDTO ataRestritaDTO) {
        List<ElementoPresencaRestritaDTO> elementosPresencaDTO = new ArrayList<>();
        for (ElementoFolhaRestritaDTO elementoFolhaDTO : ataRestritaDTO.getElementosFolhaPresenca()) {
            ElementoPresencaRestritaDTO elementoPresencaDTO = new ElementoPresencaRestritaDTO(
                    elementoFolhaDTO.getElemento().getId(),
                    elementoFolhaDTO.getElemento().getUtilizadorPCJ().getNome(),
                    elementoFolhaDTO.getElemento().getCargoMandato(),
                    elementoFolhaDTO.getElemento().getTipoElemento(),
                    elementoFolhaDTO.getElemento().getEntidade()
            );
            elementosPresencaDTO.add(elementoPresencaDTO);
        }
        ataRestritaDTO.getFolhaPresencaDTO().setElementosDTO(elementosPresencaDTO);
        return elementosPresencaDTO;
    }

    private List<ConvidadoPresencaDTO> obterConvidadosPresencaDTO(AtaRestritaDTO ataRestritaDTO) {
        List<ConvidadoPresencaDTO> ConvidadosPresencaDTO = new ArrayList<>();
        for (ConvidadoFolhaRestritaDTO convidadoFolhaDTO : ataRestritaDTO.getConvidadosFolhaPresenca()) {
            ConvidadoPresencaDTO convidadoPresencaDTO = new ConvidadoPresencaDTO(
                    convidadoFolhaDTO.getConvidadoPCJ().getId(),
                    convidadoFolhaDTO.getConvidadoPCJ().getNome(),
                    convidadoFolhaDTO.getConvidadoPCJ().getCargo(),
                    convidadoFolhaDTO.getConvidadoPCJ().getEntidade(),
                    convidadoFolhaDTO.getConvidadoPCJ().getDetalheEntidadeParticipante()
            );
            ConvidadosPresencaDTO.add(convidadoPresencaDTO);
        }
        ataRestritaDTO.getFolhaPresencaDTO().setConvidadosDTO(ConvidadosPresencaDTO);
        return ConvidadosPresencaDTO;
    }

    private List<ConvidadoFolhaRestritaDTO> obterConvidadosFolhaPresencaDTO(AtaRestrita ataRestrita) {
        List<ConvidadoFolhaRestritaDTO> convidadosFolhaRestritaDTO = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ataRestrita.getConvidadosFolhaRestrita())) {
            for (ConvidadoFolhaRestrita convidado : ataRestrita.getConvidadosFolhaRestrita()) {
                if (convidado.getId() != null) {
                    ConvidadoFolhaRestritaDTO convidadoFolhaRestritaDTO = new ConvidadoFolhaRestritaDTO(
                            convidado.getConvidadoPCJ().getId(),
                            convidado.getAtaRestrita().getId(),
                            convidado.getAssinatura(),
                            convidadoRestritaPCJMapper.toDTO(convidado.getConvidadoPCJ()));

                    convidadosFolhaRestritaDTO.add(convidadoFolhaRestritaDTO);
                }
            }
        }
        return convidadosFolhaRestritaDTO;
    }

    private List<ElementoFolhaRestritaDTO> obterElementosFolhaPresencaDTO(AtaRestrita ataRestrita) {
        List<ElementoFolhaRestritaDTO> convidadosFolhaPCJDTO = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ataRestrita.getElementosFolhaRestrita())) {
            for (ElementoFolhaRestrita elemento : ataRestrita.getElementosFolhaRestrita()) {
                if (elemento.getId() != null) {
                    ElementoFolhaRestritaDTO convidadoFolhaPCJDTO = new ElementoFolhaRestritaDTO(
                            elemento.getElemento().getId(),
                            elemento.getAtaRestrita().getId(),
                            elemento.getAssinatura(),
                            elementoMapper.toDTO(elemento.getElemento()));
                    convidadosFolhaPCJDTO.add(convidadoFolhaPCJDTO);
                }
            }
        }
        return convidadosFolhaPCJDTO;
    }

    public void removerConvidado(ConvidadoPresencaDTO convidadoDTO, AtaRestritaDTO ataRestritaDTO) throws DomainException {
        if (convidadoDTO.getId() != null && ataRestritaDTO.getId() != null) {
            ConvidadoFolhaRestrita convidadoAtaRestrita = convidadoFolhaRestritaDAO.findByIdElementoIdAtaRestrita(convidadoDTO.getId(), ataRestritaDTO.getId());
            convidadoFolhaRestritaDAO.remove(convidadoAtaRestrita);
        }
    }

    public DocumentoDTO obterDocumento(Long id) {
        return documentoPCJService.obterDocumento(id);
    }

    public void gravarConvidadoFolhaPresenca(ConvidadoPCJDTO convidadoDTO, AtaRestritaDTO ataRestritaDTO) throws DomainException {
        saveConvidadoFolha(ataRestritaMapper.toEntity(ataRestritaDTO), new ConvidadoFolhaRestritaDTO(convidadoDTO.getId(), false));
    }

    public void salvarDocumentoAta(AtaRestritaDTO ata) throws DomainException {
        boolean assinada = ata.getEstadoAta().equals(EstadoAtaEnum.ASSINATURA);
        try {
            if (ata.getAtaDocumento().getId() == null) {
                ata.setAtaDocumento(documentoPCJService.salvarDocumento(ata.getAtaDocumento()));
            }
            DocumentoAtaRestritaPCJ doc = new DocumentoAtaRestritaPCJ(ata.getId(), ata.getAtaDocumento().getId(), assinada);
            documentoAtaRestritaPCJDAO.create(doc);
        } catch (Exception e) {
            LOGGER.error("[ PCJ ] Erro guarda documento da ata assinada ", e);
        }
        LoggingHelper.logSaida(LOGGER);
    }

    public boolean buscarAtasPendendeAssinatura(ComissaoPCJDTO comissao) {
        return ataRestritaDAO.buscarAtasPendendeAssinatura(comissao);
    }

    public DocumentoAtaRestritaPCJ obterDcoumentoAta(AtaRestritaDTO ata) {
        return documentoAtaRestritaPCJDAO.obterDocumentoAta(ata);
    }

    public boolean validarFolhaPresenca(List<ElementoPresencaRestritaDTO> listaElementosPresenca, AtaRestritaDTO ataRestritaDTO) {
        int qtdMembrosSelect = 0;
        int qtdPresidenteSecretario = 0;

        qtdPresidenteSecretario = verificaQtdPresidenteSecretario(ataRestritaDTO, qtdPresidenteSecretario);
        if (qtdPresidenteSecretario == 0) {
            JsfMessageUtil.mostraMensagemErro(Constants.AVISO_ELEMENTO_PRESIDENTE_SECRETARIO_ATA);
            return false;
        }

        qtdMembrosSelect = verificaQtdMembros(ataRestritaDTO, qtdMembrosSelect);
        if ((qtdMembrosSelect) < ((float) listaElementosPresenca.size() / 2)) {
            JsfMessageUtil.mostraMensagemErro(Constants.AVISO_ELEMENTO_QUORUM_LISTA_ATA);
            return false;
        }
        return true;
    }

    private int verificaQtdPresidenteSecretario(AtaRestritaDTO ataRestritaDTO, int qtdPresidenteSecretario) {
        if (CollectionUtils.isNotEmpty(ataRestritaDTO.getElementosSelected())) {
            for (ElementoPresencaRestritaDTO elementoPresencaDTO : ataRestritaDTO.getElementosSelected()) {
                if (elementoPresencaDTO.getCargo() != null && elementoPresencaDTO.getCargo().equals(CargoMandatoEnum.PRESIDENTE.getCodigo()) ||
                        elementoPresencaDTO.getCargo() != null && elementoPresencaDTO.getCargo().equals(CargoMandatoEnum.SECRETARIO.getCodigo())) {
                    qtdPresidenteSecretario++;
                }
            }
        }
        return qtdPresidenteSecretario;
    }

    private int verificaQtdMembros(AtaRestritaDTO ataAlargadaDTO, int qtdMembros) {
        if (CollectionUtils.isNotEmpty(ataAlargadaDTO.getElementosSelected())) {
            for (ElementoPresencaRestritaDTO elementoPresencaDTO : ataAlargadaDTO.getElementosSelected()) {
                if (elementoPresencaDTO.getFuncao().equals(TipoElementoEnum.MEMBRO.getCodigo()) ||
                    elementoPresencaDTO.getFuncao().equals(TipoElementoEnum.MEMBRO_RESTRITA.getCodigo())) {
                    qtdMembros++;
                }
            }
        }
        return qtdMembros;
    }

    public boolean validarPresidenteSecretarioConclusao(AtaRestritaDTO ataRestritaDTO) {
        if (CollectionUtils.isNotEmpty(ataRestritaDTO.getListaAssinaturaSelected())) {
            for (ConclusaoDTO conclusaoDTO : ataRestritaDTO.getListaAssinaturaSelected()) {
                if (conclusaoDTO.getCargo().equals(PRESIDENTE) ||
                        conclusaoDTO.getCargo().equals(SECRETARIO)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Long retornaAtaRegistadaParaReuniaoNaoAssinadaNaoConcluida(Long idCpcj) {
        AtaRestritaConsultaDTO ataRestritaConsultaDTO = ataRestritaDAO.retornaAtaRegistadaParaReuniaoNaoAssinadaNaoConcluida(idCpcj);
        return ataRestritaConsultaDTO.getId();
    }
}
