package pt.segsocial.pcj.pae.bean.service;

import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.fraw.jsf.util.JsfUtil;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.pae.bean.wizard.RegistarPaeWizardContext;
import pt.segsocial.pcj.pae.jpa.dao.EducacaoDAO;
import pt.segsocial.pcj.pae.jpa.dao.RequerimentoPaeDAO;
import pt.segsocial.pcj.pae.jpa.entity.*;
import pt.segsocial.pcj.pae.enums.DiaSemanaEnum;
import pt.segsocial.pcj.pae.enums.TipoPeriodoFeriasEnum;
import pt.segsocial.pcj.pae.enums.TipoRequerimentoEnum;
import pt.segsocial.pcj.core.util.HorarioUtil;
import pt.segsocial.pcj.core.util.JsfMessageUtil;
import pt.segsocial.pcj.core.util.PcjMessages;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.*;

public class EducacaoPaeService implements Serializable {

    private static final long serialVersionUID = -5445866452213738015L;
    private static final int IDADE_ESCOLARIDADE_OBRIGATORIA = 6;
    private final PCJServiceLocator paeServiceLocator;
    private final Participacao participacao;
    private EntityManager entityManager;
    private RegistarPaeWizardContext context;
    private RequerimentoPae requerimento;
    private Educacao educacao;
    private HorarioEscolar horarioEscolar;
    private FeriasEscolar feriasEscolar;

    private boolean escolaridadeObrigatoria;

    public EducacaoPaeService(RegistarPaeWizardContext context) {
        this.entityManager = context.getEntityManager();
        this.paeServiceLocator = context.getServiceLocator();
        this.requerimento = context.getRequerimento();
        this.educacao = context.getEducacao();
        this.participacao = context.getParticipacao();
        this.context = context;

        this.escolaridadeObrigatoria = checkEscolaridadeObrigatoria() || !requerimento.getHorariosEscolares().isEmpty();

        if (educacao != null && educacao.getMorada() == null) {
            educacao.setMorada(new Morada());
            educacao.setContacto(new Contacto());
        }

        this.horarioEscolar = new HorarioEscolar();
        this.feriasEscolar = new FeriasEscolar();
    }

    public void registarRequerimentoAlteracaoEducacao(Educacao educacao) {
        try {
            validateDate(educacao);
            validarRegrasEducacao(educacao, educacao.getRequerimentoPAE());
            educacao.setAtivo(this.escolaridadeObrigatoria);
            if (educacao.getRequerimentoPAE().getId() == null) {

                for (HorarioEscolar horarioEscolar : educacao.getRequerimentoPAE().getHorariosEscolares()) {
                    horarioEscolar.setRequerimentoPAE(educacao.getRequerimentoPAE());
                }
                if (!educacao.getRequerimentoPAE().getFerias().isEmpty()) {
                    for (FeriasEscolar ferias : educacao.getRequerimentoPAE().getFerias()) {
                        ferias.setRequerimentoPAE(educacao.getRequerimentoPAE());
                    }
                }
                new RequerimentoPaeDAO(entityManager).create(educacao.getRequerimentoPAE());
            }
            new EducacaoDAO(entityManager).create(educacao);
        } catch (DomainException | PCJException e) {
            e.printStackTrace();
        }
    }

    public void registar() throws PCJException {
        try {
            if (this.escolaridadeObrigatoria) {
                validaHorasIguaisEducacaoFerias();
                validateDate(this.educacao);
                validarRegrasEducacao(this.educacao, this.requerimento);
                preencherEducacao(this.educacao);
                requerimento = entityManager.contains(requerimento) ? requerimento : entityManager.merge(requerimento);
                educacao.getMorada().setTerritorioPCJ(null);
                new RequerimentoPaeDAO(entityManager).update(requerimento);
                if (educacao.getId() == null) {
                    new EducacaoDAO(entityManager).create(educacao);
                } else if (educacao.getId() != null) {
                    new EducacaoDAO(entityManager).update(educacao);
                }
            } else {
                for (HorarioEscolar horarioEscolar : new ArrayList<>(requerimento.getHorariosEscolares())) {
                    requerimento.getHorariosEscolares().remove(horarioEscolar);
                }

                if (!requerimento.getFerias().isEmpty()) {
                    for (FeriasEscolar feriasEscolar : new ArrayList<>(requerimento.getFerias())) {
                        requerimento.getFerias().remove(feriasEscolar);
                    }
                }
                if (educacao != null && educacao.getId() != null) {
                    new EducacaoDAO(entityManager).remove(educacao);
                }
                iniciarEducacao();
            }

            context.setEducacao(educacao);
            this.horarioEscolar = new HorarioEscolar();
            this.feriasEscolar = new FeriasEscolar();

        } catch (DomainException e) {
            throw new PCJException(e);
        }
    }

    private void iniciarEducacao() {
        educacao = new Educacao();
        educacao.setMorada(new Morada());
        educacao.setContacto(new Contacto());
    }

    private void validaHorasIguaisEducacaoFerias() throws PCJException {
        if (educacao.getDataInicioAula().equals(educacao.getDataFimAula()) && feriasEscolar.getDataInicioFerias().equals(feriasEscolar.getDataFimFerias())) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_PERIODOS_IGUAIS"));
            throw new PCJException();
        }
    }

    private void validarRegrasEducacao(Educacao educacao, RequerimentoPae requerimento) throws PCJException {
        LocalDate dataInicioAulas = LocalDate.fromDateFields(educacao.getDataInicioAula());
        LocalDate dataFimAulas = LocalDate.fromDateFields(educacao.getDataFimAula());

        if (dataInicioAulas.isAfter(dataFimAulas)) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_DATA_INICIO_MAIOR_DATA_FIM"));
            throw new PCJException();
        }

        if (requerimento.getHorariosEscolares().isEmpty()) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_GUARDAR_UM_DIA"));
            throw new PCJException();
        }
    }

    private void preencherEducacao(Educacao educacao) {
        educacao.setRequerimentoPAE(this.context.getRequerimento());
        educacao.setAtivo(this.escolaridadeObrigatoria);
    }

    public boolean verificarExistePeriodoEscolar(HorarioEscolar horario, RequerimentoPae requerimento) {
        if (horario.regraHorariosIguais(requerimento.getHorariosEscolares())) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_JA_EXISTE_AULA"));
            return false;
        }
        return true;
    }

    public boolean verificarHorariosCorretos(HorarioEscolar horario) {
        if (HorarioUtil.hourOfDay(horario.getHoraEntrada()) < 8 || (horario.getHoraSaida().before(horario.getHoraEntrada()) || horario.getHoraSaida().equals(horario.getHoraEntrada()))) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_HORARIOS_NAO_CORRETOS"));
            return false;
        }
        return true;
    }

    public void validarRegrasPeriodoFerias(FeriasEscolar feriasEscolar) throws PCJException {
        for (FeriasEscolar obj : requerimento.getFerias()) {
            if (feriasEscolar.getDataInicioFerias().getTime() <= obj.getDataFimFerias().getTime()
                    && feriasEscolar.getDataFimFerias().getTime() >= obj.getDataInicioFerias().getTime()) {
                JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_JA_EXISTEM_FERIAS"));
                throw new PCJException();
            }
        }

        LocalDate dataInicioFerias = LocalDate.fromDateFields(feriasEscolar.getDataInicioFerias());
        LocalDate dataFimFerias = LocalDate.fromDateFields(feriasEscolar.getDataFimFerias());

        if (dataInicioFerias.isAfter(dataFimFerias)) {
            JsfMessageUtil.mostraMensagemErro(PcjMessages.getMessage("M_DATA_INICIO_MAIOR_DATA_FIM"));
            throw new PCJException();
        }
    }

    private void validateDate(Educacao educacao) throws PCJException {
        if (educacao != null) {
            Calendar dataParticipacao = Calendar.getInstance();
            dataParticipacao.setTime(participacao.getDataInicioParticipacao());
            int ano = dataParticipacao.get(Calendar.YEAR);

            if (educacao.getDataInicioAula() != null) {
                Calendar dataInicioAula = Calendar.getInstance();
                dataInicioAula.setTime(educacao.getDataInicioAula());
                int initDate = dataInicioAula.get(Calendar.YEAR);
                boolean inicioValido = getIniciAnoValido(initDate, ano);

                if (!inicioValido || initDate > ano) {
                    FacesContext.getCurrentInstance().addMessage(PcjMessages.getMessage("M_ANO_NAO_CORRESPONDE"), JsfUtil.createFacesMessage(FacesMessage.SEVERITY_ERROR, PcjMessages.getMessage("M_ANO_NAO_CORRESPONDE"), "errorMessage"));
                    throw new PCJException();
                }
            }

            if (educacao.getDataFimAula() != null) {
                Calendar dataFimAula = Calendar.getInstance();
                dataFimAula.setTime(educacao.getDataFimAula());
                int endDate = dataFimAula.get(Calendar.YEAR);

                if (endDate < ano) {
                    FacesContext.getCurrentInstance().addMessage(PcjMessages.getMessage("M_DATA_FIM_INFERIOR"), JsfUtil.createFacesMessage(FacesMessage.SEVERITY_ERROR, PcjMessages.getMessage("M_DATA_FIM_INFERIOR"), "errorMessage"));
                    throw new PCJException();
                }
            }
        }
    }

    private boolean getIniciAnoValido(int initDate, int participacaoYear) {
        return initDate == participacaoYear || initDate == participacaoYear - 1;
    }

    public boolean checkEscolaridadeObrigatoria() {
        return this.requerimento.getIdadeCrianca() >= IDADE_ESCOLARIDADE_OBRIGATORIA;
    }

    public void adicionarHorarioEscolar() {
        if (null == horarioEscolar.getDiaSemana() || null == horarioEscolar.getHoraEntrada()
                || null == horarioEscolar.getHoraSaida() || !this.verificarHorariosCorretos(horarioEscolar)) {
            return;
        }

        if (DiaSemanaEnum.TODOS != horarioEscolar.getDiaSemana()) {
            if (!this.verificarExistePeriodoEscolar(horarioEscolar, requerimento)) {
                return;
            }
        } else {
            requerimento.getHorariosEscolares().clear();
            Date horaEntrada = horarioEscolar.getHoraEntrada();
            Date horaSaida = horarioEscolar.getHoraSaida();

            for (DiaSemanaEnum diaUtil : DiaSemanaEnum.diasUteis()) {
                requerimento.getHorariosEscolares().add(new HorarioEscolar.Builder()
                        .horaEntrada(horaEntrada)
                        .horaSaida(horaSaida)
                        .requerimento(requerimento)
                        .diaSemana(diaUtil)
                        .build());
            }
            return;
        }

        horarioEscolar.setRequerimentoPAE(requerimento);
        requerimento.getHorariosEscolares().add(horarioEscolar);
        if (requerimento.getTipoRequerimento().equals(TipoRequerimentoEnum.INICIAL)) {
            this.setRequerimento(requerimento);
        }
        this.setHorarioEscolar(new HorarioEscolar());
    }

    public boolean verificarObrigatorioDiaSemana() {
        return null != horarioEscolar.getHoraEntrada() || null != horarioEscolar.getHoraSaida();
    }

    public boolean verificarObrigatorioEntrada() {
        return null != horarioEscolar.getDiaSemana() || null != horarioEscolar.getHoraSaida();
    }

    public boolean verificarObrigatorioSaida() {
        return null != horarioEscolar.getDiaSemana() || null != horarioEscolar.getHoraEntrada();
    }

    public void eliminarHorarioEscolar(HorarioEscolar horarioEscolar) {
        requerimento.getHorariosEscolares().remove(horarioEscolar);
        for (HorarioParticipacao hp : new ArrayList<>(requerimento.getHorariosParticipacoes())) {
            if (horarioEscolar.getDiaSemana() == hp.getDiaSemana()) {
                requerimento.getHorariosParticipacoes().remove(hp);
            }
        }
    }

    public void adicionarPeriodoFerias() {
        try {
            if (null == feriasEscolar.getDataInicioFerias() || null == feriasEscolar.getDataFimFerias()
                    || null == feriasEscolar.getTipoPeriodo()) {
                return;
            }

            validarRegrasPeriodoFerias(feriasEscolar);
            feriasEscolar.setRequerimentoPAE(requerimento);
            requerimento.getFerias().add(feriasEscolar);
            feriasEscolar = new FeriasEscolar();
        } catch (PCJException e) {
            e.printStackTrace();
        }
    }

    public void eliminarFeriasEscolar(FeriasEscolar feriasEscolar) {
        Set<FeriasEscolar> ferias = new HashSet<>(requerimento.getFerias());
        for (Iterator<FeriasEscolar> iterator = ferias.iterator(); iterator.hasNext(); ) {
            FeriasEscolar current = iterator.next();
            if (current.equalsCustom(feriasEscolar)) {
                iterator.remove();
                break;
            }
        }
        requerimento.setFerias(ferias);
    }

    public List<DiaSemanaEnum> getDiasSemana() {
        return DiaSemanaEnum.filterDiasSemanaToHorarioEscolar();
    }

    public List<TipoPeriodoFeriasEnum> getTiposPeriodoFerias() {
        return Arrays.asList(TipoPeriodoFeriasEnum.values());
    }

    public Educacao getEducacao() {
        return educacao;
    }

    public void setEducacao(Educacao educacao) {
        this.educacao = educacao;
    }

    public EntityManager getEntityManager() {
        return entityManager;
    }

    public PCJServiceLocator getPaeServiceLocator() {
        return paeServiceLocator;
    }

    public RequerimentoPae getRequerimento() {
        return requerimento;
    }

    public void setRequerimento(RequerimentoPae requerimento) {
        this.requerimento = requerimento;
    }

    public HorarioEscolar getHorarioEscolar() {
        return horarioEscolar;
    }

    public void setHorarioEscolar(HorarioEscolar horarioEscolar) {
        this.horarioEscolar = horarioEscolar;
    }

    public List<HorarioEscolar> getHorariosEscolares() {
        if (CollectionUtils.isNotEmpty(requerimento.getHorariosEscolares())) {
            Collections.sort(requerimento.getHorariosEscolares());
        }
        return requerimento.getHorariosEscolares();
    }

    public void setHorariosEscolares(List<HorarioEscolar> horariosEscolares) {
        this.requerimento.setHorariosEscolares(horariosEscolares);
    }

    public FeriasEscolar getFeriasEscolar() {
        return feriasEscolar;
    }

    public void setFeriasEscolar(FeriasEscolar feriasEscolar) {
        this.feriasEscolar = feriasEscolar;
    }

    public Set<FeriasEscolar> getFerias() {
        return requerimento.getFerias();
    }

    public void setFerias(Set<FeriasEscolar> ferias) {
        requerimento.setFerias(ferias);
    }

    public List<FeriasEscolar> getSortedFerias() {
        List<FeriasEscolar> feriasEscolars = new ArrayList<>(requerimento.getFerias());
        Collections.sort(feriasEscolars, new Comparator<FeriasEscolar>() {
            @Override
            public int compare(FeriasEscolar o1, FeriasEscolar o2) {
                return o1.getDataInicioFerias().compareTo(o2.getDataInicioFerias());
            }
        });
        return feriasEscolars;
    }

    public boolean isEscolaridadeObrigatoria() {
        return this.escolaridadeObrigatoria;
    }

    public void setEscolaridadeObrigatoria(boolean escolaridadeObrigatoria) {
        this.escolaridadeObrigatoria = escolaridadeObrigatoria;
    }
}
