package pt.segsocial.pcj.ppp.jpa.entity;

import pt.segsocial.fraw.api.domain.BooleanResult;
import pt.segsocial.fraw.jpa.PersistentDomainObject;

import javax.persistence.*;

@Entity
@Table(name = "DOCUMENTO_ATA_ALARGADA_PCJ")
public class DocumentoAtaAlargadaPCJ extends PersistentDomainObject<DocumentoAtaAlargadaPCJId> {

    @EmbeddedId
    private DocumentoAtaAlargadaPCJId id;

    @ManyToOne
    @JoinColumn(name = "ID_DOCUMENTO_PCJ", insertable = false, updatable = false)
    private DocumentoPCJ documentoPCJ;

    @Column(name = "DOCUMENTO_ASSINADO")
    private boolean documentoAssinado;

    public DocumentoAtaAlargadaPCJ(Long ataAlargadaId, Long documentoId, boolean documentoAssinado) {
        this.id = new DocumentoAtaAlargadaPCJId(ataAlargadaId, documentoId);
        this.documentoAssinado = documentoAssinado;
    }

    public DocumentoAtaAlargadaPCJ() {

    }

    public boolean isDocumentoAssinado() {
        return documentoAssinado;
    }

    public void setDocumentoAssinado(boolean documentoAssinado) {
        this.documentoAssinado = documentoAssinado;
    }

    public DocumentoPCJ getDocumentoPCJ() {
        return documentoPCJ;
    }

    public void setDocumentoPCJ(DocumentoPCJ documentoPCJ) {
        this.documentoPCJ = documentoPCJ;
    }

    @Override
    public void delete() {

    }

    @Override
    public BooleanResult getIsDeletable() {
        return null;
    }

    @Override
    public DocumentoAtaAlargadaPCJId getId() {
        return id;
    }

    public void setId(DocumentoAtaAlargadaPCJId id) {
        this.id = id;
    }
}
