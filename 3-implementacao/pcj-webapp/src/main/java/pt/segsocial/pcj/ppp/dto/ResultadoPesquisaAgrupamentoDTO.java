package pt.segsocial.pcj.ppp.dto;

public class ResultadoPesquisaAgrupamentoDTO {

    private Long id;
    private String nome;
    private String codigoCpcj;
    private int numeroProcessoAtivos;
    private String nomeDistrito;
    private String nomeConcelho;

    public ResultadoPesquisaAgrupamentoDTO(Long id, String nome, String codigo, String nomeDistrito, String nomeConcelho) {
        this.id = id;
        this.nome = nome;
        this.codigoCpcj = codigo;
        this.nomeDistrito = nomeDistrito;
        this.nomeConcelho = nomeConcelho;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoCpcj() {
        return codigoCpcj;
    }

    public void setCodigoCpcj(String codigoCpcj) {
        this.codigoCpcj = codigoCpcj;
    }

    public int getNumeroProcessoAtivos() {
        return numeroProcessoAtivos;
    }

    public void setNumeroProcessoAtivos(int numeroProcessoAtivos) {
        this.numeroProcessoAtivos = numeroProcessoAtivos;
    }

    public String getNomeDistrito() {
        return nomeDistrito;
    }

    public void setNomeDistrito(String nomeDistrito) {
        this.nomeDistrito = nomeDistrito;
    }

    public String getNomeConcelho() {
        return nomeConcelho;
    }

    public void setNomeConcelho(String nomeConcelho) {
        this.nomeConcelho = nomeConcelho;
    }

    @Override
    public String toString() {
        return "ResultPesquisaAgrupamentoDTO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", codigoCpcj='" + codigoCpcj + '\'' +
                ", numeroProcessoAtivos=" + numeroProcessoAtivos +
                ", nomeDistrito='" + nomeDistrito + '\'' +
                ", nomeConcelho='" + nomeConcelho + '\'' +
                '}';
    }
}
