package pt.segsocial.pcj.ppp.dto;

public class ReuniaoDTO {

    private String periodicidade;
    private String motivoPeriodicidadeMaiorDoisMeses;
    private String codigoPeriodoReuniao;
    private String outroPeriodo;
    private String periodicidadeReuniaoRestrita;
    private String outroPeriodicidadeReuniaoRestrita;
    private String razaoSemPeriodicidade;
    private String outroHorario;
    private String outraPeriodicidade;

    public String getPeriodicidade() {
        return periodicidade;
    }

    public void setPeriodicidade(String periodicidade) {
        this.periodicidade = periodicidade;
    }

    public String getMotivoPeriodicidadeMaiorDoisMeses() {
        return motivoPeriodicidadeMaiorDoisMeses;
    }

    public void setMotivoPeriodicidadeMaiorDoisMeses(String motivoPeriodicidadeMaiorDoisMeses) {
        this.motivoPeriodicidadeMaiorDoisMeses = motivoPeriodicidadeMaiorDoisMeses;
    }

    public String getCodigoPeriodoReuniao() {
        return codigoPeriodoReuniao;
    }

    public void setCodigoPeriodoReuniao(String periodo) {
        this.codigoPeriodoReuniao = periodo;
    }

    public String getOutroPeriodo() {
        return outroPeriodo;
    }

    public void setOutroPeriodo(String outroPeriodo) {
        this.outroPeriodo = outroPeriodo;
    }

    public String getPeriodicidadeReuniaoRestrita() {
        return periodicidadeReuniaoRestrita;
    }

    public void setPeriodicidadeReuniaoRestrita(String periodicidadeReuniaoRestrita) {
        this.periodicidadeReuniaoRestrita = periodicidadeReuniaoRestrita;
    }

    public String getOutroPeriodicidadeReuniaoRestrita() {
        return outroPeriodicidadeReuniaoRestrita;
    }

    public void setOutroPeriodicidadeReuniaoRestrita(String outroPeriodicidadeReuniaoRestrita) {
        this.outroPeriodicidadeReuniaoRestrita = outroPeriodicidadeReuniaoRestrita;
    }

    public String getRazaoSemPeriodicidade() {
        return razaoSemPeriodicidade;
    }

    public void setRazaoSemPeriodicidade(String razaoSemPeriodicidade) {
        this.razaoSemPeriodicidade = razaoSemPeriodicidade;
    }

    public String getOutroHorario() {
        return outroHorario;
    }

    public void setOutroHorario(String outroHorario) {
        this.outroHorario = outroHorario;
    }

    public String getOutraPeriodicidade() {
        return outraPeriodicidade;
    }

    public void setOutraPeriodicidade(String outraPeriodicidade) {
        this.outraPeriodicidade = outraPeriodicidade;
    }

    @Override
    public String toString() {
        return "ReuniaoDTO{" +
                "periodicidade='" + periodicidade + '\'' +
                ", motivoPeriodicidadeMaiorDoisMeses='" + motivoPeriodicidadeMaiorDoisMeses + '\'' +
                ", codigoPeriodoReuniao='" + codigoPeriodoReuniao + '\'' +
                ", outroPeriodo='" + outroPeriodo + '\'' +
                ", periodicidadeReuniaoRestrita='" + periodicidadeReuniaoRestrita + '\'' +
                ", outroPeriodicidadeReuniaoRestrita='" + outroPeriodicidadeReuniaoRestrita + '\'' +
                ", razaoSemPeriodicidade='" + razaoSemPeriodicidade + '\'' +
                ", outroHorario='" + outroHorario + '\'' +
                ", outraPeriodicidade='" + outraPeriodicidade + '\'' +
                '}';
    }
}
