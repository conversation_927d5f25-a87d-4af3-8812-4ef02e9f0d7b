package pt.segsocial.pcj.pae.jpa.entity;


import org.hibernate.envers.Audited;
import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Audited
@Table(name = "ENTREVISTA_MOTIVACAO")
@AttributeOverride(name = "id", column = @Column(name = "ID_ENTREVISTA_MOTIVACAO"))
public class EntrevistaMotivacao extends EntityBase implements Serializable {
    private static final long serialVersionUID = 859849238965244099L;

    public static final String FIND_BY_ID_ENTREVISTA_MOTIVACAO = "EntrevistaMotivacao.findByEntrevistaMotivacao";

    @OneToOne(optional = false)
    @JoinColumn(name = "ID_ENTREVISTA")
    private Entrevista entrevista;

    @Column(name="DESCRICAO_MOTIVACAO")
    private String descricaoMotivacao;

    @Column(name="FREQUENCIA_PARTICIPACAO")
    private String frequenciaParticipacao;

    public EntrevistaMotivacao() {
    }

    public Entrevista getEntrevista() {
        return entrevista;
    }

    public void setEntrevista(Entrevista entrevista) {
        this.entrevista = entrevista;
    }

    public String getDescricaoMotivo() {
        return descricaoMotivacao;
    }

    public void setDescricaoMotivo(String descricaoMotivacao) {
        this.descricaoMotivacao = descricaoMotivacao;
    }

    public String getFrequenciaParticipacao() {
        return frequenciaParticipacao;
    }

    public void setFrequenciaParticipacao(String frequenciaParticipacao) {
        this.frequenciaParticipacao = frequenciaParticipacao;
    }

    @Override
    public String toString() {
        return "EntrevistaMotivacao{" +
                "entrevista=" + entrevista +
                ", descricaoMotivacao='" + descricaoMotivacao + '\'' +
                ", frequenciaParticipacao='" + frequenciaParticipacao + '\'' +
                '}';
    }
}
