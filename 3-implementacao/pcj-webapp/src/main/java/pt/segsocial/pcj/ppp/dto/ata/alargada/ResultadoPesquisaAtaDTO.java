package pt.segsocial.pcj.ppp.dto.ata.alargada;

import pt.segsocial.fraw.api.domain.Auditable;
import pt.segsocial.fraw.api.domain.BooleanResult;
import pt.segsocial.fraw.api.domain.Deletable;
import pt.segsocial.fraw.jpa.LongIdentifiable;
import pt.segsocial.pcj.ppp.enums.EstadoAtaEnum;
import pt.segsocial.pcj.ppp.enums.TipoAtaEnum;

import java.io.Serializable;
import java.util.Date;

public class ResultadoPesquisaAtaDTO implements Serializable, LongIdentifiable, Deletable, Auditable {

    private Long id;
    private String numeroAta;
    private Date data;
    private String nomeCpcj;
    private TipoAtaEnum tipoAta;
    private EstadoAtaEnum estado;
    private Long idReuniao;
    private String tipoReuniao;

    public ResultadoPesquisaAtaDTO() {
    }

    public ResultadoPesquisaAtaDTO(Long id, String numeroAta, Date data, String nomeCpcj, String tipoReuniao, String estado, Long idReuniao, String tipoAta) {
        this.id = id;
        this.numeroAta = numeroAta;
        this.data = data;
        this.nomeCpcj = nomeCpcj;
        this.tipoReuniao = tipoReuniao;
        this.estado = EstadoAtaEnum.valueOf(estado.toUpperCase());
        this.idReuniao = idReuniao;
        this.tipoAta = TipoAtaEnum.valueOf(tipoAta.toUpperCase());
    }

    @Override
    public void delete() {

    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public EstadoAtaEnum getEstado() {
        return estado;
    }

    public void setEstado(EstadoAtaEnum estado) {
        this.estado = estado;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getIdReuniao() {
        return idReuniao;
    }

    public void setIdReuniao(Long idReuniao) {
        this.idReuniao = idReuniao;
    }

    @Override
    public BooleanResult getIsDeletable() {
        return null;
    }

    public String getNomeCpcj() {
        return nomeCpcj;
    }

    public void setNomeCpcj(String nomeCpcj) {
        this.nomeCpcj = nomeCpcj;
    }

    public String getNumeroAta() {
        return numeroAta;
    }

    public void setNumeroAta(String numeroAta) {
        this.numeroAta = numeroAta;
    }

    public TipoAtaEnum getTipoAta() {
        return tipoAta;
    }

    public void setTipoAta(TipoAtaEnum tipoAta) {
        this.tipoAta = tipoAta;
    }

    public String getTipoReuniao() {
        return tipoReuniao;
    }

    public void setTipoReuniao(String tipoReuniao) {
        this.tipoReuniao = tipoReuniao;
    }

    @Override
    public boolean isAuditable() {
        return false;
    }
}
