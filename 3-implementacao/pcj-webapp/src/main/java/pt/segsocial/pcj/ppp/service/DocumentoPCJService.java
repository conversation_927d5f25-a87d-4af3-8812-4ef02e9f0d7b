package pt.segsocial.pcj.ppp.service;

import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.api.exception.PCJException;
import pt.segsocial.pcj.core.cdi.PCJServiceLocator;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.ppp.dto.DocumentoDTO;
import pt.segsocial.pcj.ppp.dto.DocumentosPPPDTO;
import pt.segsocial.pcj.ppp.jpa.dao.DocumentoPCJDAO;
import pt.segsocial.pcj.ppp.jpa.dao.DocumentosPPPDAO;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentoPCJ;
import pt.segsocial.pcj.ppp.jpa.entity.DocumentosPPP;
import pt.segsocial.pcj.ppp.mapper.DocumentoMapper;
import pt.segsocial.pcj.ppp.mapper.DocumentosPPPMapper;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceContextType;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Named("pcjUploadFicheiro")
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public class DocumentoPCJService implements Serializable {

    private static final Logger LOG = LoggerFactory.getLogger(DocumentoPCJService.class);

    @PersistenceContext(unitName = PCJSubsystem.PERSISTENCE_UNIT_PCJ, type = PersistenceContextType.EXTENDED)
    private EntityManager entityManager;

    @Inject
    private PCJServiceLocator pcjServiceLocator;

    @Inject
    private DocumentoMapper documentoMapper;

    @Inject
    private DocumentosPPPMapper documentosPPPMapper;

    public DocumentoDTO upload(FileUploadEvent event) throws PCJException {
        UploadedFile uploadedFile = event.getFile();
        return uploadFile(uploadedFile);
    }

    public DocumentosPPPDTO uploadPPP(FileUploadEvent event) throws PCJException {
        UploadedFile uploadedFile = event.getFile();
        return uploadFilePPP(uploadedFile);
    }

    @TransactionAttribute
    public List<DocumentoDTO> uploadMultipleFiles(List<UploadedFile> files) throws PCJException {
        List<DocumentoDTO> multipleFiles = new ArrayList<>();
        for (UploadedFile uploadedFile : files) {
            multipleFiles.add(uploadFile(uploadedFile));
        }
        return multipleFiles;
    }

    @NotNull
    private DocumentoDTO uploadFile(UploadedFile uploadedFile) throws PCJException {
        try {
            Long idFicheiroStorage = pcjServiceLocator.getStorageDelegate().upload(uploadedFile.getFileName(), uploadedFile.getInputstream());
            return getDocumentoDTO(uploadedFile, idFicheiroStorage);
        } catch (Exception e) {
            throw new PCJException("Não foi possível adicionar documento. Tente novamente mais tarde");
        }
    }

    @NotNull
    private DocumentosPPPDTO uploadFilePPP(UploadedFile uploadedFile) throws PCJException {
        try {
            Long idFicheiroStorage = pcjServiceLocator.getStorageDelegate().upload(uploadedFile.getFileName(), uploadedFile.getInputstream());
            return getDocumentosPPPDTO(uploadedFile, idFicheiroStorage);
        } catch (Exception e) {
            throw new PCJException("Não foi possível adicionar documento. Tente novamente mais tarde");
        }
    }

    @NotNull
    private DocumentoDTO getDocumentoDTO(UploadedFile uploadedFile, Long idFicheiroStorage) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        DocumentoDTO documentoDTO = new DocumentoDTO();
        documentoDTO.setDataUpload(df.parse(df.format(new Date())));
        documentoDTO.setIdentificadorFicheiro(idFicheiroStorage);
        documentoDTO.setNomeDocumento(uploadedFile.getFileName());
        documentoDTO.setUploadedFile(uploadedFile);
        documentoDTO.setUploadDocumento(true);
        return documentoDTO;
    }

    @NotNull
    private DocumentosPPPDTO getDocumentosPPPDTO(UploadedFile uploadedFile, Long idFicheiroStorage) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        DocumentosPPPDTO documentoDTO = new DocumentosPPPDTO();
        documentoDTO.setDataUpload(df.parse(df.format(new Date())));
        documentoDTO.setIdentificadorFicheiro(idFicheiroStorage);
        documentoDTO.setNomeDocumento(uploadedFile.getFileName());
        documentoDTO.setUploadedFile(uploadedFile);
        documentoDTO.setUploadDocumento(true);
        return documentoDTO;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public List<DocumentoPCJ> salvaFicheiros(List<UploadedFile> ficheiros) throws PCJException, DomainException {
        List<DocumentoDTO> documentoDTOS = uploadMultipleFiles(ficheiros);
        List<DocumentoPCJ> documentosSalvos = new ArrayList<>();
        for (DocumentoDTO documentoDTO : documentoDTOS) {
            DocumentoPCJ documentoPCJ = salvaFicheiro(documentoDTO);
            documentosSalvos.add(documentoPCJ);
        }
        return documentosSalvos;
    }

    public DocumentoPCJ salvaFicheiro(DocumentoDTO documentoDTO) throws DomainException {
        DocumentoPCJ documentoPCJ = documentoMapper.toEntidade(documentoDTO);
        if (documentoPCJ.getId() == null) {
            new DocumentoPCJDAO(entityManager).create(documentoPCJ);
        }
        return documentoPCJ;
    }

    public void preview(Long idFicheiro) throws PCJException {
        pcjServiceLocator.getStorageDelegate().preview(idFicheiro);
    }

    public String previewUrl(Long idFicheiro) throws PCJException {
        return pcjServiceLocator.getStorageDelegate().previewUrl(idFicheiro);
    }

    public void removeDocumento(DocumentoPCJ documentoPCJ) throws DomainException {
        new DocumentoPCJDAO(entityManager).remove(documentoPCJ);
    }

    public DocumentoDTO obterDocumento(Long id) {
        return documentoMapper.toDTO(getDocumentoPCJ(id));
    }

    public DocumentoPCJ getDocumentoPCJ(Long id) {
        return new DocumentoPCJDAO(entityManager).find(id);
    }

    public DocumentosPPPDTO obterDocumentoPPP(Long id) {
        return documentosPPPMapper.toDTO(getDocumentosPPP(id));
    }

    public DocumentosPPP getDocumentosPPP(Long id) {
        return new DocumentosPPPDAO(entityManager).find(id);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public DocumentoDTO salvarDocumento(DocumentoDTO doc) throws PCJException, DomainException, ParseException {

        if (doc.getId() == null) {
            new DocumentoPCJDAO(entityManager).create(documentoMapper.toEntidade(doc));
        }
        return documentoMapper.toDTO(new DocumentoPCJDAO(entityManager).findBy(doc.getIdentificadorFicheiro()));
    }
}
