package pt.segsocial.pcj.pae.scheduler;

import pt.segsocial.fraw.api.cdi.config.ConfigurationValue;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceContextType;
import java.io.Serializable;

@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
public abstract class AbstractScheduler implements Serializable {

    private static final long serialVersionUID = 1L;

    @Inject
    @ConfigurationValue(
            subsystem = PCJSubsystem.ID, value = "scheduler.enabled", defaultValue = "true",
            applicationContextual = true, softDecayToNonApplicationContextual = true)
    protected Boolean schedulerEnable;

    @PersistenceContext(unitName = PCJSubsystem.PERSISTENCE_UNIT_PCJ, type = PersistenceContextType.TRANSACTION)
    protected EntityManager entityManager;
}
