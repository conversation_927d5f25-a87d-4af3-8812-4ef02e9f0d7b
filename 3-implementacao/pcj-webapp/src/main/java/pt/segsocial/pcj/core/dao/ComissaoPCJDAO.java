package pt.segsocial.pcj.core.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.jpa.entity.ComissaoPCJppp;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

public class ComissaoPCJDAO extends DAO<ComissaoPCJppp, Long> implements Serializable {

    @Inject
    public ComissaoPCJDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<ComissaoPCJppp> findAllAtivo() {
        try {
            return getEntityManager()
                    .createNamedQuery(ComissaoPCJppp.FIND_ALL_ATIVO, ComissaoPCJppp.class)
                    .getResultList();
        } catch (NoResultException e) {
            return Collections.emptyList();
        }
    }
}
