package pt.segsocial.pcj.ppp.jpa.dao;

import pt.segsocial.fraw.jpa.dao.DAO;
import pt.segsocial.pcj.ppp.jpa.entity.SalaReuniao;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SalaReuniaoDAO extends DAO<SalaReuniao, Long> {

    @Inject
    public SalaReuniaoDAO(EntityManager entityManager) {
        super(entityManager);
    }

    public List<SalaReuniao> findByIds(Long idComissao, List<Long> ids) {
        Map<String, Object> parameters = new HashMap<>();
        StringBuilder queryHql = new StringBuilder("select s from SalaReuniao s where s.comissao.id = :idComissao ");
        parameters.put("idComissao", idComissao);
        if(!ids.isEmpty()) {
            queryHql.append(" and s.id not in(:ids) ");
            parameters.put("ids", ids);
        }

        TypedQuery<SalaReuniao> query = this.getEntityManager().createQuery(queryHql.toString(), SalaReuniao.class);

        for (Map.Entry<String, Object> parametro : parameters.entrySet()) {
            query.setParameter(parametro.getKey(), parametro.getValue());
        }

        return query.getResultList();

    }

    public List<SalaReuniao> buscaSalasPorCPCJ(Long idComissao) {
        Map<String, Object> parameters = new HashMap<>();
        String queryHql = "select s from SalaReuniao s where s.comissao.id = :idComissao and s.statusFuncionamento = true";
        parameters.put("idComissao", idComissao);

        TypedQuery<SalaReuniao> query = this.getEntityManager().createQuery(queryHql, SalaReuniao.class);

        for (Map.Entry<String, Object> parametro : parameters.entrySet()) {
            query.setParameter(parametro.getKey(), parametro.getValue());
        }

        return query.getResultList();
    }
}
