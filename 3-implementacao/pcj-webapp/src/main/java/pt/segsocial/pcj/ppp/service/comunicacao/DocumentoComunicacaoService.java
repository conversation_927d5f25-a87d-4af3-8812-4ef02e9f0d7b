package pt.segsocial.pcj.ppp.service.comunicacao;

import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.ppp.dto.comunicacao.DocumentoComunicacaoDTO;
import pt.segsocial.pcj.ppp.jpa.dao.comunicacao.DocumentoComunicacaoDAO;
import pt.segsocial.pcj.ppp.jpa.entity.comunicacao.DocumentoComunicacaoPPP;
import pt.segsocial.pcj.ppp.mapper.comunicacao.DocumentoComunicacaoMapper;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.List;

@RequestScoped
public class DocumentoComunicacaoService {

    @Inject
    private DocumentoComunicacaoDAO documentoComunicacaoDAO;

    @Inject
    private DocumentoComunicacaoMapper documentoComunicacaoMapper;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void salvarDocumentoComunicacao(DocumentoComunicacaoPPP documentoComunicacao) throws DomainException {
        documentoComunicacaoDAO.create(documentoComunicacao);
    }

    public List<DocumentoComunicacaoDTO> listarDocumentosComunicacaoPorIdComunicacao(Long idComunicacao) {
        return documentoComunicacaoMapper.toDtoList(documentoComunicacaoDAO.listarDocumentosComunicacaoPorIdComunicacao(idComunicacao));
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void removerDocumentosComunicacaoPorIdComunicacao(Long idComunicacao) {
        documentoComunicacaoDAO.removerDocumentosComunicacaoPorIdComunicacao(idComunicacao);
    }

}
