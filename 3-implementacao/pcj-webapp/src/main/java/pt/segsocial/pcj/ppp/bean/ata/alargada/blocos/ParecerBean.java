package pt.segsocial.pcj.ppp.bean.ata.alargada.blocos;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.segsocial.pcj.ppp.dto.ata.alargada.AtaAlargadaDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.BlocoParecerDTO;
import pt.segsocial.pcj.ppp.dto.ata.alargada.OrdenacaoAtaAlargadaDTO;

public class ParecerBean extends BlocoBaseBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(ParecerBean.class);

    private BlocoParecerDTO blocoParecerDTO;

    private AtaAlargadaDTO ataAlargadaDTO;

    public ParecerBean(AtaAlargadaDTO ataAlargadaDTO, BlocoParecerDTO blocoParecerDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
        this.blocoParecerDTO = blocoParecerDTO;
        init();
    }

    public void init() {
        getAtaAlargadaDTO().setOrdenacaoAtaAlargadaDTO(new OrdenacaoAtaAlargadaDTO());
        getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setParecer(new BlocoParecerDTO());
        if (getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getId() == null) {
            getAtaAlargadaDTO().setOrdenacaoAtaAlargadaDTO(new OrdenacaoAtaAlargadaDTO());
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setAtaAlargada(getAtaAlargadaDTO());
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setParecer(new BlocoParecerDTO());
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getParecer().setComissao(getAtaAlargadaDTO().getComissao());
            blocoParecerDTO = new BlocoParecerDTO();
            getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().setParecer(blocoParecerDTO);
        } else {
            blocoParecerDTO = getAtaAlargadaDTO().getOrdenacaoAtaAlargadaDTO().getParecer();
        }
    }

    public AtaAlargadaDTO getAtaAlargadaDTO() {
        return ataAlargadaDTO;
    }

    public void setAtaAlargadaDTO(AtaAlargadaDTO ataAlargadaDTO) {
        this.ataAlargadaDTO = ataAlargadaDTO;
    }

    public BlocoParecerDTO getBlocoParecerDTO() {
        return blocoParecerDTO;
    }

    public void setBlocoParecerDTO(BlocoParecerDTO blocoParecerDTO) {
        this.blocoParecerDTO = blocoParecerDTO;
    }
}
