package pt.segsocial.pcj.pae.scheduler.ejb;

import org.apache.commons.collections.CollectionUtils;
import pt.segsocial.fraw.api.domain.DomainException;
import pt.segsocial.pcj.core.cdi.PCJSubsystem;
import pt.segsocial.pcj.pae.jpa.dao.*;
import pt.segsocial.pcj.pae.jpa.entity.*;

import javax.ejb.*;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceContextType;
import java.io.Serializable;
import java.util.List;

@LocalBean
@Stateless(name ="pcjRemoverRascunhosEJB")
@TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
@TransactionManagement
public class RemoverRascunhosEJB implements Serializable {

    private static final long serialVersionUID = 1L;

    @PersistenceContext(unitName = PCJSubsystem.PERSISTENCE_UNIT_PCJ, type = PersistenceContextType.TRANSACTION)
    private EntityManager entityManager;

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void removeRascunhos(ProcessoPae processoPae) throws DomainException {
        processoPae = new ProcessoPaeDAO(entityManager).find(processoPae.getId());
        removeRequerimento(processoPae);
        removeProcesso(processoPae);
    }

    private void removeRequerimento(ProcessoPae processoPae) throws DomainException {
        List<RequerimentoPae> requerimentos = getRequerimentoPae(processoPae);

        if(CollectionUtils.isNotEmpty(requerimentos)) {
            RequerimentoPae requerimentoPae = requerimentos.get(0);
            removeDependenciasRequerimento(requerimentoPae.getId());
            removeListas(requerimentoPae);
            entityManager.remove(requerimentoPae);
        }
    }

    private void removeProcesso(ProcessoPae processoPae) throws DomainException {
        removeDependenciasProcessos(processoPae.getId());
        CriancaEstrangeira criancaEstrangeira = processoPae.getCriancaEstrangeira();
        if (criancaEstrangeira != null) {
            new FiliacaoEstrangeiraDAO(entityManager).removeFiliacao(criancaEstrangeira);
            for (FiliacaoEstrangeira filiacao : criancaEstrangeira.getFiliacoes()) {
                new PessoaEstrangeiraDAO(entityManager).removePessoaEstrangeira(filiacao.getPessoaEstrangeira());
            }
        }
        new GrupoProcessoDAO(entityManager).deleteByProcesso(processoPae);
        entityManager.remove(processoPae);
        if (criancaEstrangeira != null) {
            List<ProcessoPae> processoPaeList = new ProcessoPaeDAO(entityManager).findAllPosRascunhoByCrianca(criancaEstrangeira);
            if (processoPaeList == null || processoPaeList.size() == 0) {
                new CriancaEstrangeiraDAO(entityManager).remove(criancaEstrangeira);
                new PessoaEstrangeiraDAO(entityManager).remove(criancaEstrangeira.getPessoaEstrangeira());
            }
        }
    }

    private void removeListas(RequerimentoPae requerimentoPae) {
        requerimentoPae.setLocaisAtividade(null);
        requerimentoPae.setHorariosEscolares(null);
        requerimentoPae.setFerias(null);
        requerimentoPae.setHorariosParticipacoes(null);
        requerimentoPae.setComprovativos(null);
    }

    private void removeDependenciasProcessos(Long idProcessoPae) throws DomainException {
        removeDocumentos(idProcessoPae);
        removeHistotico(idProcessoPae);
        removeDiligencia(idProcessoPae);
    }

    private void removeDiligencia(Long idProcessoPae) {
        DiligenciaDAO diligenciaDAO = new DiligenciaDAO(entityManager);
        List<Diligencia> diligencias = diligenciaDAO.getDiligenciaByIdProcesso(idProcessoPae);
        for (Diligencia diligencia: diligencias) {
            diligenciaDAO.removeDiligencia(diligencia);
        }
    }

    private void removeHistotico(Long idProcessoPae) throws DomainException {
        HistoricoDAO historicoDAO = new HistoricoDAO(entityManager);
        List<Historico> historicos = historicoDAO.findByProcesso(idProcessoPae);
        for (Historico historico: historicos ) {
            historicoDAO.remove(historico);
        }
    }

    private void removeDocumentos(Long idProcessoPae) throws DomainException {
        DocumentoPaeDAO documentoPaeDAO = new DocumentoPaeDAO(entityManager);
        List<Documento> documentosPae = documentoPaeDAO.findByIdProcessoPae(idProcessoPae);
        for (Documento doc: documentosPae) {
            documentoPaeDAO.remove(doc);
        }
    }

    private void removeDependenciasRequerimento(Long idRequerimento) throws DomainException {
        removeParticipacao(idRequerimento);
        removeVililancia(idRequerimento);
        removeEducacao(idRequerimento);
        removeInfoComplementares(idRequerimento);
        removeFerias(idRequerimento);
        removeHorarioParticipacao(idRequerimento);
        removeHorarioEscolar(idRequerimento);
        removeComprovativo(idRequerimento);
    }

    private void removeComprovativo(Long idRequerimento) throws DomainException {
        ComprovativoDAO comprovativoDAO = new ComprovativoDAO(entityManager);
        List<Comprovativo> comprovativos = comprovativoDAO.findByRequerimento(idRequerimento);
        if(CollectionUtils.isNotEmpty(comprovativos)) {
            for (Comprovativo comprovativo:comprovativos) {
                comprovativoDAO.remove(comprovativo);
            }
        }
    }

    private void removeHorarioEscolar(Long idRequerimento) throws DomainException {
        HorarioEscolarDAO horarioEscolarDAO = new HorarioEscolarDAO(entityManager);
        List<HorarioEscolar> horarioEscolares = horarioEscolarDAO.findByRequerimento(idRequerimento);
        if(CollectionUtils.isNotEmpty(horarioEscolares)){
            for (HorarioEscolar horarioEscolar: horarioEscolares) {
                horarioEscolarDAO.remove(horarioEscolar);
            }
        }
    }

    private void removeHorarioParticipacao(Long idRequerimento) throws DomainException {
        HorarioParticipacaoDAO horarioParticipacaoDAO =  new HorarioParticipacaoDAO(entityManager);
        List<HorarioParticipacao> horarios = new HorarioParticipacaoDAO(entityManager).findByIdRequerimento(idRequerimento);
        for (HorarioParticipacao horarioParticipacao:horarios) {
            horarioParticipacaoDAO.remove(horarioParticipacao);
        }
    }

    private void removeFerias(Long idRequerimento) throws DomainException {
        FeriasEscolarDAO feriasEscolarDAO = new FeriasEscolarDAO(entityManager);
        List<FeriasEscolar> feriasEscolares = feriasEscolarDAO.findByRequerimentoId(idRequerimento);
        for (FeriasEscolar feriasEscolar:feriasEscolares) {
            feriasEscolarDAO.remove(feriasEscolar);
        }
    }

    private void removeInfoComplementares(Long idRequerimento) throws DomainException {
        InformacaoComplementarDAO informacaoComplementarDAO = new InformacaoComplementarDAO(entityManager);
        InformacaoComplementar informacaoComplementar = informacaoComplementarDAO.findByIdRequerimento(idRequerimento);
        if(informacaoComplementar != null) {
            informacaoComplementarDAO.remove(informacaoComplementar);
        }
    }

    private void removeEducacao(Long idRequerimento) throws DomainException {
        EducacaoDAO educacaoDao = new EducacaoDAO(entityManager);
        List<Educacao> educacaoList = educacaoDao.findAllByIdRequerimento(idRequerimento);
        if(educacaoList != null) {
            for (Educacao educacao : educacaoList) {
                educacaoDao.remove(educacao);
            }
        }
    }

    private void removeVililancia(Long idRequerimento) throws DomainException {
        VigilanciaPaeDAO vigilanciaPaeDAO = new VigilanciaPaeDAO(entityManager);
        List<VigilanciaPae> vigilanciaList = vigilanciaPaeDAO.findAllByIdRequerimento(idRequerimento);
        if(vigilanciaList != null) {
            for (VigilanciaPae vigilanciaPae : vigilanciaList) {
                vigilanciaPaeDAO.remove(vigilanciaPae);
            }
        }
    }

    private void removeParticipacao(Long idRequerimento) throws DomainException {
        ParticipacaoDAO participacaoDAO = new ParticipacaoDAO(entityManager);
        List<Participacao> participacaoList = participacaoDAO.findAllByIdRequerimento(idRequerimento);
        if(participacaoList != null) {
            for (Participacao participacao : participacaoList) {
                participacaoDAO.remove(participacao);
            }
        }
    }

    private List<RequerimentoPae> getRequerimentoPae(ProcessoPae processoPae) {
        return new RequerimentoPaeDAO(entityManager).findbyIdProcesso(processoPae);
    }
}
