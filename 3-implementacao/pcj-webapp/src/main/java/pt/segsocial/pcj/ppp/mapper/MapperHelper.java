package pt.segsocial.pcj.ppp.mapper;

import pt.segsocial.pcj.ppp.dto.MembroComissaoIdDTO;
import pt.segsocial.pcj.ppp.jpa.entity.MembroComissaoId;

public class MapperHelper {

    public static MembroComissaoIdDTO map(MembroComissaoId id) {
        if (id == null) return null;
        MembroComissaoIdDTO dto = new MembroComissaoIdDTO();
        dto.setReuniaoId(id.getReuniaoId());
        dto.setElementoId(id.getElementoId());
        return dto;
    }

    public static MembroComissaoId map(MembroComissaoIdDTO dto) {
        if (dto == null) return null;
        return new MembroComissaoId(dto.getReuniaoId(), dto.getElementoId());
    }
}
