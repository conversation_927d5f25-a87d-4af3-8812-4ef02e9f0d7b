package pt.segsocial.pcj.ppp.jpa.entity;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "ENTIDADE_EXTERNA_PCJ")
@AttributeOverride(name = "id", column = @Column(name = "ID_ENTIDADE_EXTERNA_PCJ"))
@NamedQueries({
        @NamedQuery(name = EntidadeExterna.FIND_BY_EMAIL,
                query = " SELECT e FROM EntidadeExterna e " +
                        " WHERE trim(e.email) = :email")

})

public class EntidadeExterna extends EntityBase{

    public static final String FIND_BY_EMAIL = "EntidadeExterna.findByEmail";
    @Column(name = "EMAIL")
    private String email;

    @Column(name = "COD_TIPO_ENTIDADE")
    private String tipoEntidadeExterna;

    @Column(name = "STATUS_ATIVO")
    private Boolean ativo;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "ID_UTILIZADOR_PCJ")
    private UtilizadorPCJ utilizadorPCJ;

    @Column(name = "NUMERO_CARTAO")
    private String numeroCartao;

    @ManyToMany
    @JoinTable(name = "ENTIDADE_COMISSAO_PCJ",
            joinColumns = @JoinColumn(name = "ID_ENTIDADE_EXTERNA_PCJ"), inverseJoinColumns = @JoinColumn(name = "ID_COMISSAO_PCJ"))
    private List<ComissaoPCJppp> comissoes = new ArrayList<>(0);

    public EntidadeExterna() {
        utilizadorPCJ = new UtilizadorPCJ();
    }

    @Override
    public void prePersist() {
        super.prePersist();
        setAtivo(true);
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTipoEntidadeExterna() {
        return tipoEntidadeExterna;
    }

    public void setTipoEntidadeExterna(String tipoEntidadeExterna) {
        this.tipoEntidadeExterna = tipoEntidadeExterna;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public UtilizadorPCJ getUtilizadorPCJ() {
        return utilizadorPCJ;
    }

    public void setUtilizadorPCJ(UtilizadorPCJ utilizadorPCJ) {
        this.utilizadorPCJ = utilizadorPCJ;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public List<ComissaoPCJppp> getComissoes() {
        return comissoes;
    }

    public void setComissoes(List<ComissaoPCJppp> comissoes) {
        this.comissoes = comissoes;
    }
}
