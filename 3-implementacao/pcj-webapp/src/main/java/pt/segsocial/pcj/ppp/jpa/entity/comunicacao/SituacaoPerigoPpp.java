package pt.segsocial.pcj.ppp.jpa.entity.comunicacao;

import pt.segsocial.pcj.pae.jpa.entity.base.EntityBase;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Objects;

@Entity
@Table(name = "SITUACAO_PERIGO_PPP", schema = "PCJ")
@AttributeOverride(name = "id", column = @Column(name = "ID_SITUACAO_PERIGO_PPP"))
@NamedQueries({
        @NamedQuery(name = SituacaoPerigoPpp.FIND_BY_ID_COMUNICACAO,
                query = " SELECT s FROM SituacaoPerigoPpp s " +
                        " WHERE s.comunicacao.id = :idComunicacao")
})
public class SituacaoPerigoPpp extends EntityBase implements java.io.Serializable {

    public static final String FIND_BY_ID_COMUNICACAO = "SituacaoPerigoPpp.findByIdComunicacao";
    private static final long serialVersionUID = -4741265964982131362L;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "ID_COMUNICACAO_PPP", nullable = false)
    private Comunicacao comunicacao;

    @Size(max = 4)
    @NotNull
    @Column(name = "CODIGO_TIPOLOGIA_PERIGO", nullable = false, length = 4)
    private String codigoTipologiaPerigo;

    @Size(max = 4)
    @NotNull
    @Column(name = "CODIGO_SITUACAO_SINALIZADA", nullable = false, length = 4)
    private String codigoSituacaoSinalizada;

    @Size(max = 4)
    @NotNull
    @Column(name = "CODIGO_SITUACAO_ATRIBUIDA", nullable = false, length = 4)
    private String codigoSituacaoAtribuida;

    @Size(max = 150)
    @Column(name = "OUTRA_SITUACAO_ATRIBUIDA", length = 150)
    private String outraSituacaoAtribuida;

    @Size(max = 15)
    @Column(name = "NUIPC", length = 15)
    private String nuipc;

    @Size(max = 4000)
    @NotNull
    @Column(name = "DESCRICAO_FACTOS", nullable = false, length = 4000)
    private String descricaoFactos;

    @Size(max = 150)
    @Column(name = "MORADA", length = 150)
    private String morada;

    @Column(name = "INTERVENCAO_URGENTE")
    private Boolean intervencaoUrgente;

    public Comunicacao getComunicacao() {
        return comunicacao;
    }

    public void setComunicacao(Comunicacao comunicacao) {
        this.comunicacao = comunicacao;
    }

    public String getCodigoTipologiaPerigo() {
        return codigoTipologiaPerigo;
    }

    public void setCodigoTipologiaPerigo(String codigoTipologiaPerigo) {
        this.codigoTipologiaPerigo = codigoTipologiaPerigo;
    }

    public String getCodigoSituacaoSinalizada() {
        return codigoSituacaoSinalizada;
    }

    public void setCodigoSituacaoSinalizada(String codigoSituacaoSinalizada) {
        this.codigoSituacaoSinalizada = codigoSituacaoSinalizada;
    }

    public String getCodigoSituacaoAtribuida() {
        return codigoSituacaoAtribuida;
    }

    public void setCodigoSituacaoAtribuida(String codigoSituacaoAtribuida) {
        this.codigoSituacaoAtribuida = codigoSituacaoAtribuida;
    }

    public String getOutraSituacaoAtribuida() {
        return outraSituacaoAtribuida;
    }

    public void setOutraSituacaoAtribuida(String outraSituacaoAtribuida) {
        this.outraSituacaoAtribuida = outraSituacaoAtribuida;
    }

    public String getNuipc() {
        return nuipc;
    }

    public void setNuipc(String nuipc) {
        this.nuipc = nuipc;
    }

    public String getDescricaoFactos() {
        return descricaoFactos;
    }

    public void setDescricaoFactos(String descricaoFactos) {
        this.descricaoFactos = descricaoFactos;
    }

    public Boolean getIntervencaoUrgente() {
        return intervencaoUrgente;
    }

    public void setIntervencaoUrgente(Boolean intervencaoUrgente) {
        this.intervencaoUrgente = intervencaoUrgente;
    }

    public String getMorada() {
        return morada;
    }

    public void setMorada(String morada) {
        this.morada = morada;
    }

    public SituacaoPerigoPpp copy() {
        SituacaoPerigoPpp copy = new SituacaoPerigoPpp();
        copy.setId(this.getId());
        copy.setComunicacao(this.comunicacao);
        copy.setCodigoTipologiaPerigo(this.codigoTipologiaPerigo);
        copy.setCodigoSituacaoSinalizada(this.codigoSituacaoSinalizada);
        copy.setCodigoSituacaoAtribuida(this.codigoSituacaoAtribuida);
        copy.setOutraSituacaoAtribuida(this.outraSituacaoAtribuida);
        copy.setNuipc(this.nuipc);
        copy.setDescricaoFactos(this.descricaoFactos);
        copy.setMorada(this.morada);
        copy.setIntervencaoUrgente(this.intervencaoUrgente);
        return copy;
    }

    @Override
    public String toString() {
        return "SituacaoPerigoPpp{" + "comunicacao=" + comunicacao + ", codigoTipologiaPerigo=" + codigoTipologiaPerigo + ", codigoSituacaoSinalizada=" + codigoSituacaoSinalizada + ", codigoSituacaoAtribuida=" + codigoSituacaoAtribuida + ", outraSituacaoAtribuida=" + outraSituacaoAtribuida + ", nuipc=" + nuipc + ", descricaoFactos=" + descricaoFactos + ", morada=" + morada + ", intervencaoUrgente=" + intervencaoUrgente + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        SituacaoPerigoPpp that = (SituacaoPerigoPpp) o;
        return Objects.equals(comunicacao, that.comunicacao) &&
                Objects.equals(codigoTipologiaPerigo, that.codigoTipologiaPerigo) &&
                Objects.equals(codigoSituacaoSinalizada, that.codigoSituacaoSinalizada) &&
                Objects.equals(codigoSituacaoAtribuida, that.codigoSituacaoAtribuida) &&
                Objects.equals(outraSituacaoAtribuida, that.outraSituacaoAtribuida) &&
                Objects.equals(nuipc, that.nuipc) &&
                Objects.equals(descricaoFactos, that.descricaoFactos) &&
                Objects.equals(morada, that.morada) &&
                Objects.equals(intervencaoUrgente, that.intervencaoUrgente);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), comunicacao, codigoTipologiaPerigo, codigoSituacaoSinalizada,
                codigoSituacaoAtribuida, outraSituacaoAtribuida, nuipc, descricaoFactos, morada, intervencaoUrgente);
    }
}