# \u00C3\u00A1 = \u00E1	\u00C3\u00A0 = \u00E0	\u00C3\u00A2 = \u00E2	\u00C3\u00A3 = \u00E3	\u00C3\u20AC = \u00E4	\u00C3\u0081 = \u00C1	\u00C3\u0080 = \u00C0	\u00C3\u0082 = \u00C2	\u00C3\u0083 = \u00C3	\u00C3\u0084 = \u00C4
# \u00C3\u00A9 = \u00E9	\u00C3\u0161 = \u00E8	\u00C3\u00AA = \u00EA	\u00C3\u00AA = \u00EA	\u00C3\u0089 = \u00C9	\u00C3\u0088 = \u00C8	\u00C3\u008A = \u00CA	\u00C3\u008B = \u00CB
# \u00C3\u00AD = \u00ED 	\u00C3\u00AC = \u00EC	\u00C3\u00AE = \u00EE	\u00C3\u00AF = \u00EF	\u00C3\u008D = \u00CD	\u00C3\u008C = \u00CC	\u00C3\u008E = \u00CE	\u00C3\u008F = \u00CF
# \u00C3\u00B3 = \u00F3 	\u00C3\u00B2 = \u00F2	\u00C3\u017D = \u00F4	\u00C3\u00B5 = \u00F5	\u00C3\u00B6 = \u00F6 \u00C3\u0093 = \u00D3	\u00C3\u0092 = \u00D2	\u00C3\u0094 = \u00D4	\u00C3\u0095 = \u00D5	\u00C3\u0096 = \u00D6
# \u00C3\u00BA = \u00FA	\u00C3\u00B9 = \u00F9	\u00C3\u00BB = \u00FB	\u00C3\u0152 = \u00FC	\u00C3\u009A = \u00DA	\u00C3\u0099 = \u00D9	\u00C3\u009B = \u00DB
# \u00C3\u00A7 = \u00E7	\u00C3\u0087 = \u00C7	\u00C3\u00B1 = \u00F1	\u00C3\u0091 = \u00D1	& = &	' = '
# á = \u00e1	à = \u00e0	â = \u00e2	ã = \u00e3	ä = \u00e4	Á = \u00c1	À = \u00c0	Â = \u00c2	Ã = \u00c3	Ä = \u00c4
# é = \u00e9	è = \u00e8	ê = \u00ea	ê = \u00ea	É = \u00c9	È = \u00c8	Ê = \u00ca	Ë = \u00cb
# í = \u00ed 	ì = \u00ec	î = \u00ee	ï = \u00ef	Í = \u00cd	Ì = \u00cc	Î = \u00ce	Ï = \u00cf
# ó = \u00f3 	ò = \u00f2	ô = \u00f4	õ = \u00f5	ö = \u00f6 Ó = \u00d3	Ò = \u00d2	Ô = \u00d4	Õ = \u00d5	Ö = \u00d6
# ú = \u00fa	ù = \u00f9	û = \u00fb	ü = \u00fc	Ú = \u00da	Ù = \u00d9	Û = \u00db
# ç = \u00e7	Ç = \u00c7	ñ = \u00f1	Ñ = \u00d1	& = \u0026	' = \u0027  º = \u00b0
M01139=Para ser elig\u00EDvel a um processo de comunica\u00E7\u00E3o a crian\u00E7a deve ter 13 ou mais anos.
M01140=N\u00E3o pode abrir um processo do tipo Comunica\u00E7\u00E3o para uma atividade que a crian\u00E7a tenha participado nos \u00FAltimos 180 dias.
M010958=N\u00FAmero de processo inv\u00E1lido
M010965=Deve carregar no bot\u00E3o "Validar crian\u00E7a" de forma a tentar identificar automaticamente a CPCJ respons\u00E1vel a ser atribu\u00EDda ao processo.
M010966=N\u00E3o foram encontrados resultados para esta pesquisa.
M010968=Para a abertura de um processo de arte e espet\u00E1culo a crian\u00E7a ter\u00E1 de ter uma idade menor que 16 anos.
M011081=O vigilante tem que ter no m\u00EDnimo 18 anos.
M011092=A data de nascimento n\u00E3o pode ser superior \u00E0 data atual.
M011093=A data de nascimento n\u00E3o pode ser superior \u00E0 data de nascimento da crian\u00E7a.
M011097=A participa\u00E7\u00E3o tem que ser comunicada com a anteced\u00EAncia m\u00EDnima de 20 dias ap\u00F3s a data atual.
M11186=A data para renova\u00E7\u00E3o deve ser superior \u00E0 data de fim autorizada.
M011129=J\u00E1 existe um hor\u00E1rio definido para este per\u00EDodo.
M011130=\u00C9 obrigat\u00F3rio a crian\u00E7a ser acompanhada por um vigilante respons\u00E1vel para atividades que envolvam animais, subst\u00E2ncias perigosas ou algum tipo de risco.
M011134=Deve existir pelo menos 1 pausa por dia.
M011174=J\u00E1 existe um elemento do agregado familar com esses dados.
M011176=O per\u00EDodo de participa\u00E7\u00E3o n\u00E3o pode ser superior a metade do per\u00EDodo di\u00E1rio permitido, sem uma pausa.
M011180=Os dados do vigilante n\u00E3o podem ser iguais ao da crian\u00E7a.
M011185=Pedido de renova\u00E7\u00E3o do processo realizado com sucesso.
M011193=O hor\u00E1rio de entrada n\u00E3o pode ser superior ao hor\u00E1rio de sa\u00EDda.
M011194=CPCJ 
M011195=NISS: 
M011196=Preencha todos os campos obrigat\u00F3rios.
M011197=O conjunto NISS/NIF e data de nascimento inserido n\u00E3o pode ser utilizado. Por favor insira outro.
E011198=Para crian\u00E7a acime de 6 anos \u00E9 obrigat\u00F3rio informar o ano de escolaridade, nome do diretor de turma ou professor.
E011199=Informe quem exerce as responsabilidades parentais e o contacto telef\u00F3nico.
E011200=Todos os campos s\u00E3o obrigat\u00F3rios.
E011201=Verifique os campos obrigat\u00F3rios.
E011202=O NISS inserido n\u00E3o pode ser utilizado. Por favor insira outro.
#-------PPP--------
ACEDER_ECRA=Acedendo ecr\u00E3: Ecr\u00E3 %s acedido.
LOGIN=Login na aplica\u00E7\u00E3o.
OPERACAO=Realizada a opera\u00E7\u00E3o %s no ecr\u00E3 %s
MSG008=N\u00E3o encontramos resultados para os termos pesquisados. Revise os dados informados e tente novamente.
MSG020=\u00C9 obrigat\u00F3rio o preenchimento de um(a) %s.
MSG027=A pesquisa realizada ultrapassou o n\u00FAmero de dias permitido de %s dias.
MSG029=N\u00E3o existem vers\u00F5es do utilizador suficientes para comparar.
MSG030=N\u00E3o existem vers\u00F5es do elemento suficientes para comparar.
MSG031=NISS deve ser validado para prosseguir.
MSG032=NISS inserido est\u00E1 inv\u00E1lido, deve ter 11 caracteres.
MSG039=Excede os limites de horas de afeta\u00E7\u00E3o permitidos.
MSG040=As horas de afeta\u00E7\u00E3o preenchidas s\u00E3o inferiores ao recomendado.
MSG041=O e-mail <b> %s </b> j\u00E1 est\u00E1 registado</b>.
MSG042=O e-mail <b> %s </b> j\u00E1 est\u00E1 registado para outro utilizador na <b> %s </b>.
MSG043=Documento de designa\u00E7\u00E3o \u00E9 obrigat\u00F3rio.
MSG044=A fotografia \u00E9 obrigat\u00F3ria
MSG045=N\u00E3o existe nenhuma CPCJ dispon\u00EDvel para registar o elemento com o NISS <b> %s </b>.
MSG049=O elemento com o NISS <b> %s </b> j\u00E1 se encontra registado (ativo ou cessado) na CPCJ <b> %s </b>. Caso se encontre cessado, \u00E9 poss\u00EDvel reativ\u00E1-lo na Gest\u00E3o de Elementos.
MSG051=N\u00E3o \u00E9 poss\u00EDvel designar esse membro como secret\u00E1rio at\u00E9 que a designa\u00E7\u00E3o anterior seja tratada em ATA da Comiss\u00E3o Alargada.
MSG052=O c\u00F3digo postal deve pertencer ao distrito e concelho de registo da comiss\u00E3o.
MSG053=Algumas freguesias j\u00E1 t\u00EAm CPCJ associadas, confirme se pretende continuar com esta altera\u00E7\u00E3o. Esta ser\u00E1 aplicada no momento do registo da CPCJ.
#-----------------
# TODO Substituir pelo c\u00C3\u00B3digo correspondente
M_JA_EXISTEM_FERIAS=J\u00E1 existem f\u00E9rias marcadas para este per\u00EDodo.
M_JA_EXISTE_AULA=J\u00E1 existe um dia de aulas marcado para este per\u00EDodo.
M_HORARIOS_NAO_CORRETOS=Os hor\u00E1rios inseridos n\u00E3o est\u00E3o corretos. Insira um per\u00EDodo entre as 08:00 e as 23:59.
M_GUARDAR_UM_DIA=Deve guardar pelo menos um dia de hor\u00E1rio escolar
M_ESCOLARIDADE_OBRIGATORIA=Deve preencher as informa\u00E7\u00F5es relativas \u00E0 escolaridade obrigat\u00F3ria.
M_PERIODOS_IGUAIS=O per\u00EDodo de f\u00E9rias n\u00E3o pode ser igual ao per\u00EDodo de aulas
M_ANO_NAO_CORRESPONDE=Os anos inseridos n\u00E3o correspondem ao ano da participa\u00E7\u00E3o da crian\u00E7a.
M_DATA_FIM_INFERIOR=A data fim n\u00E3o pode ser inferior ao ano de participa\u00E7\u00E3o.
M_DEVE_GUARDAR_UM_DIA=Deve guardar pelo menos um dia de hor\u00E1rio de participa\u00E7\u00E3o
M_ULTRAPASSOU_LIMITE_AULAS=Ultrapassou o limite de horas de atividade permitido em per\u00EDodo de aulas.
M_ULTRAPASSOU_LIMITE_ATIVIDADE=Ultrapassou o limite de horas de atividade permitido
M_ULTRAPASSOU_LIMITE_PERIODO=Ultrapassou o limite de horas de atividade permitido em per\u00EDodo de 
M_PARTICIPACAO_SUSPENSA=A participa\u00E7\u00E3o da crian\u00E7a deve ser suspensa pelo menos 1 dia por estar em per\u00EDodo de aulas.
M_PAUSA_30_MIN=A pausa \u00E9 de pelo menos 30 minutos, pelo que o intervalo de participa\u00E7\u00E3o dever\u00E1 ser maior
M_HORARIO_ENTRADA_SAIDA=n\u00E3o \u00E9 poss\u00EDvel adicionar mesmo hor\u00E1rio de entrada e sa\u00EDda.
M_MAX_2H_POR_DIA=n\u00E3o \u00E9 poss\u00EDvel adicionar mais de 2 horas por dia para uma crian\u00E7a com idade entre 3 e 7 anos sem escolaridade.
M_HORAS_ENTRE_8_E_20=O hor\u00E1rio de participa\u00E7\u00E3o s\u00F3 pode ser entre as 8h e as 20h.
M_HORAS_ENTRE_8_E_24=O hor\u00E1rio de participa\u00E7\u00E3o s\u00F3 pode ser entre as 8h e as 24h.
M_INTERVALO_1HORA_AULAS=Deve existir um intervalo m\u00EDnimo de 1 hora entre a frequ\u00EAncia da atividade e as aulas.
M_COINCIDIR_AULAS=O hor\u00E1rio de participa\u00E7\u00E3o n\u00E3o pode coincidir com aulas.
M_ATUACAO_UM_DIA_SEMANA=S\u00F3 pode inserir hor\u00E1rios do tipo atua\u00E7\u00E3o para um dia da semana.
M_DATA_INICIO_MAIOR_DATA_FIM=A data inicial n\u00E3o pode ser superior \u00E0 data final.
M_VALIDAR_RESPONSAVEL=Deve carregar o bot\u00E3o 'Validar respons\u00E1vel' para que o NISS ou o NIF seja validado.
M_VALIDAR_OBITO=O conjunto NISS/NIF e data de nascimento inserido n\u00E3o pode ser utilizado. Por favor insira outro.
M_SUCESSO_DEFERIR_PROCESSO=Pedido de autoriza\u00E7\u00E3o para a participa\u00E7\u00E3o em artes e espet\u00E1culos autorizado com sucesso.
M_JA_EXISTE_PARA_PERIODO=J\u00E1 existe um hor\u00E1rio definido para este per\u00EDodo.
M_VALIDAR_AGREGADO=Deve carregar o bot\u00E3o 'Validar agregado' para que o NISS ou o NIF seja validado.
M_FREGUESIA_ASSOCIADA=N\u00E3o \u00E9 poss\u00EDvel avan\u00E7ar sem uma freguesia associada \u00E0 Compet\u00EAncia territorial.
M_NISS_INVALIDO=O NISS inserido \u00E9 inv\u00E1lido, por favor insira um NISS com o formato correto ou altere o tipo de documento.
M_NISS_INVALIDO_2=O NISS inserido \u00E9 inv\u00E1lido, por favor insira um NISS com o formato correto.
M_SUCESSO_ELEMENTO_ENCONTRADO=Foi encontrado o elemento com o NISS inserido, alguns campos foram preenchidos automaticamente.
M_AUX_ELEMENTO_ENCONTRADO=Documento v\u00E1lido, preencha os campos para efetuar o registo.
M_AUX_EMAIL_INVALIDO=E-mail inserido est\u00E1 inv\u00E1lido.
M_UTLIZADOR_EXTERNO_ATIVO=Esta pessoa j\u00E1 tem um registo ativo no sistema como um utilizador externo. Caso necess\u00E1rio consulte a Comiss\u00E3o Nacional
M_EFETUE_PESQUISA=Efetue uma pesquisa para obter resultados
M_SEM_RESULTADO=N\u00F5o foram encontrados resultados para a pesquisa
#Internacionalização; Path: src/main/resources/pcj/messages.properties
botao.cancelar=Cancelar
botao.elminar=Eliminar
botao.limpar=Limpar
botao.pesquisar=Pesquisar
botao.voltar=Voltar
botao.guardar-alteracoes=Guardar altera\u00E7\u00F5es
botao.iniciar-analise=Iniciar an\u00E1lise
botao.retomar-analise=Retomar an\u00E1lise
botao.avancar-analise=Avan\u00E7ar com an\u00E1lise
botao.concluir-analise=Concluir an\u00E1lise
botao.avancar-conclusao=Avan\u00E7ar para conclus\u00E3o
botao.guardar-rascunho=Guardar rascunho
link.acoes=A\u00E7\u00F5es
label.mae=M\u00E3e
label.pai=Pai
label.sim=Sim
label.nao=N\u00E3o
label.pagina=P\u00E1gina
label.ver=Ver
label.consultar=Consultar
label.remover=Remover
label.nome=Nome
label.telefone=Telefone
label.email=E-mail
label.voltar=Voltar
label.observacoes=Observa\u00E7\u00F5es
pp.label.morada=Morada
pp.label.rua=Rua
pp.label.codigo-postal=C\u00F3digo postal
pp.label.localidade=Localidade
pp.label.selecionar-documento=Selecionar documento
pp.label.permitir-documentos=Tamanho m\u00E1ximo do ficheiro: 3MB | Formatos aceites: PNG, JPG, PDF
pp.label.avancada=avan\u00E7ada
# Ata
pp.ata.msg.pesquisa.data-preenchida=Pesquisa por data exige o preenchimento do intervalo de pesquisa.
# PP
pp.analise.comunicacao=An\u00E1lise de comunica\u00E7\u00E3o n.\u00BA%s
pp.registo.comunicacao=Registo de comunica\u00E7\u00E3o
pp.alterar.comunicacao=Altera\u00E7\u00E3o de comunica\u00E7\u00E3o n.\u00BA%s
pp.registo.comunicacao.informacao.message=Registe as informa\u00E7\u00F5es fornecidas na comunica\u00E7\u00E3o, incluindo o participante e as situa\u00E7\u00F5es de perigo.
pp.alterar.comunicacao.informacao.message=Registe as informa\u00E7\u00F5es fornecidas na comunica\u00E7\u00E3o, incluindo o participante e as situa\u00E7\u00F5es de perigo.
pp.analise.comunicacao.informacao.message=Consulte e altere informa\u00E7\u00F5es da comunica\u00E7\u00E3o, incluindo o participante e as situa\u00E7\u00F5es de perigo.
pp.registo.comunicacao.label.resultados=Resultados
pp.comunicacao.crianca.label.nome=Nome da crian\u00E7a/jovem
pp.comunicacao.crianca.botao.registar=Registar crian\u00E7a/jovem
pp.comunicacao.crianca.facet.registar=Registo de Comunica\u00E7\u00E3o Crian\u00E7a
pp.comunicacao.crianca.table.label.crianca=Crian\u00E7a/jovem
pp.comunicacao.crianca.table.label.dataNascimento=Data de nascimento
pp.comunicacao.crianca.table.label.utilizar=Utilizar registo
pp.comunicacao.crianca.table.label.ultima-morada=\u00DAltima morada informada
pp.comunicacao.crianca.form.label.dados-crianca=Dados da crian\u00E7a/jovem
pp.comunicacao.crianca.form.label.data-nascimento-conhecida=Data de nascimento conhecida?
pp.comunicacao.crianca.form.label.informacao-idade=Informa\u00E7\u00E3o de idade
pp.comunicacao.crianca.form.label.genero=Sexo
pp.comunicacao.crianca.form.label.estabelecimento-ensino=Estabelecimento de ensino
pp.comunicacao.pesquisar=Gest\u00E3o de comunica\u00E7\u00F5es
pp.comunicacao.pesquisar.informacao.message=Pesquise e consulte as   comunica\u00E7\u00F5es registadas.
pp.comunicacao.pesquisar.form.label.intervalo=Intervalo de registo
pp.comunicacao.pesquisar.form.label.nome=Nome da crian\u00E7a/jovem
pp.comunicacao.pesquisar.form.label.tipologia-perigo=Tipologia de perigo
pp.comunicacao.pesquisar.form.label.situacao-sinalizada=Situa\u00E7\u00E3o  sinalizada
pp.comunicacao.pesquisar.form.label.participante=Participante
pp.comunicacao.pesquisar.form.label.tipo-comunicacao=Tipo de comunica\u00E7\u00E3o
pp.comunicacao.pesquisar.form.label.mae=M\u00E3e
pp.comunicacao.pesquisar.form.label.pai=Pai
pp.comunicacao.pesquisar.form.label.morada=Morada
pp.comunicacao.pesquisar.form.label.estado=Estado
pp.comunicacao.pesquisar.form.label.transferida=Transferida
pp.comunicacao.pesquisar.form.label.tipo=Tipo
pp.comunicacao.pesquisar.form.label.distrito-ilha=Distrito/Ilha
pp.comunicacao.pesquisar.form.label.concelho=Concelho
pp.comunicacao.pesquisar.form.label.freguesia=Freguesia
pp.comunicacao.pesquisar.form.label.urgencia=Urg\u00eancia da comunica\u00E7\u00E3o
pp.comunicacao.pesquisar.titulo=Lista de comunica\u00E7\u00F5es
pp.comunicacao.pesquisar.label.numero=Número
pp.comunicacao.pesquisar.label.crianca-jovem=Crian\u00E7a/jovem
pp.comunicacao.pesquisar.label.situacoes-reportadas=Situa\u00E7\u00F5es reportadas
pp.comunicacao.pesquisar.label.urgente=Urgente
pp.comunicacao.pesquisar.label.normal=Normal
pp.comunicacao.pesquisar.label.relevancia=Relev\u00e2ncia
pp.comunicacao.crianca.form.label.comunicacao=Comunica\u00E7\u00E3o
pp.comunicacao.crianca.form.label.entrada-horario-cpcj=Deu entrada dentro do hor\u00E1rio da CPCJ?
pp.comunicacao.crianca.table.label.data-entrada=Data de entrada
pp.comunicacao.crianca.table.label.data-registo=Data de registo
pp.comunicacao.crianca.table.label.modalidade-contacto=Modalidade de contacto
pp.comunicacao.crianca.table.label.expediente-associado=Expediente associado
pp.comunicacao.crianca.table.label.documento-comunicacao=Documento de comunica\u00E7\u00E3o
pp.comunicacao.crianca.table.label.dados-participante=Dados do participante
pp.comunicacao.crianca.table.label.comunicacao-anonima=Comunica\u00E7\u00E3o an\u00F3nima
pp.comunicacao.crianca.table.label.tipo-participante=Tipo de participante
pp.comunicacao.crianca.table.label.entidade-participante=Entidade participante
pp.comunicacao.crianca.table.label.detalhes-entidade-participante=Detalhes da entidade participante
pp.comunicacao.crianca.table.label.morada-participante=Morada
pp.comunicacao.crianca.table.label.participante-familiar=\u00C9 familiar
pp.comunicacao.crianca.table.label.parentesco-participante=Parentesco
pp.comunicacao.crianca.table.label.crianca-tipo-relacao=Tipo de rela\u00E7\u00E3o
pp.comunicacao.crianca.table.label.participante-propria-crianca=O participante \u00E9 a pr\u00F3pria crian\u00E7a?
pp.comunicacao.crianca.table.label.situacao-perigo=Situa\u00E7\u00E3o de perigo
pp.comunicacao.crianca.table.label.tipologia-perigo=Tipologia  perigo
pp.comunicacao.crianca.table.label.situacao-sinalizada=Situa\u00E7\u00E3o sinalizada
pp.comunicacao.crianca.table.label.situacao-atribuida=Situa\u00E7\u00E3o atribu\u00EDda a
pp.comunicacao.crianca.table.label.atribuida=Atribu\u00EDda a
pp.comunicacao.crianca.table.label.situacao-atribuida-outro=Outra. Qual?
pp.comunicacao.crianca.table.label.situacao-nuipc=NUIPC
pp.comunicacao.crianca.table.label.situacao-descricao-factos=Descri\u00E7\u00E3o dos factos
pp.comunicacao.crianca.table.button.adicionar-situacao=Adicionar situa\u00E7\u00E3o
pp.comunicacao.crianca.table.button.alterar-situacao=Alterar situa\u00E7\u00E3o
pp.comunicacao.crianca.situacao.facet.situacaes=Situa\u00E7\u00E3o da Crian\u00E7a
pp.comunicacao.crianca.participante.label=Participante
pp.comunicacao.crianca.descricao-situacao.label=Descri\u00E7\u00E3o da Situa\u00E7\u00E3o
pp.comunicacao.crianca.button.alterar-comunicacao=Alterar comunica\u00E7\u00E3o
pp.comunicacao.crianca.form.label.idade=Idade
pp.comunicacao.button.registar-comunicacao=Registar comunica\u00E7\u00E3o
pp.comunicacao.button.iniciar-analise=Iniciar an\u00E1lise
pp.comunicacao.button.remover-comunicacao=Remover comunica\u00E7\u00E3o
pp.comunicacao.button.consultar-comunicacao=Consultar comunica\u00E7\u00E3o
pp.comunicacao.button.crianca.alterar-crianca=Alterar crian\u00E7a
pp.comunicacao.button.concluir-comunicacao=Concluir comunica\u00E7\u00E3o
pp.comunicacao.texto.comunicacao-concluida=An\u00E1lise da comunica\u00E7\u00E3o conclu\u00EDda com sucesso
pp.comunicacao.texto.comunicacao-concluida-sucesso=A an\u00E1lise da comunica\u00E7\u00E3o foi conclu\u00EDda com sucesso. Foi encaminhado para a lista de trabalhos
pendentes da pr\u00F3xima reuni\u00E3o da restrita.
pp.comunicacao.texto.comunicacao-concluida-que-esperar=O que esperar daqui para frente
pp.comunicacao.texto.info.comunicacao-concluir=Conclua a an\u00E1lise da comunica\u00E7\u00E3o, selecionando a a\u00E7\u00E3o a realizar e preenchendo as informa\u00E7\u00F5es associadas.
pp.comunicacao.button.comunicacao-ir-para-gestao=Ir para gest\u00E3o de comunica\u00E7\u00F5es
pp.comunicacao.label.comunicacao-acao-realizar=A\u00E7\u00E3o a ser realizada
pp.comunicacao.button.comunicacao-justificacao=Justifica\u00E7\u00E3o da a\u00E7\u00E3o
pp.comunicacao.label.comunicacao-motivo-tranferencia=Motivo da transfer\u00eacia
pp.comunicacao.label.comunicacao-motivo-arquivamento=Motivo do arquivamento
pp.comunicacao.label.comunicacao-comissao-destino=Comiss\u00E3o de destino
pp.comunicacao.texto.comunicacao-participante-desconhecido=Participante desconhecido?
pp.comunicacao.texto.comunicacao.historico-comunicacaoes=Hist\u00F3rico de comunica\u00E7\u00F5es
pp.comunicacao.label.crianca.informacoes-complementares=Informa\u00E7\u00F5es complementares
pp.comunicacao.label.crianca.adicionar-informacoes-complementares=Adicionar informa\u00E7\u00F5es
pp.comunicacao.informcacoes.complementares.label.tipo-contacto=Tipo de contacto
pp.comunicacao.informcacoes.complementares.label.data=Data
pp.comunicacao.informcacoes.complementares.label.hora=Hora
pp.comunicacao.informcacoes.complementares.label.quem-esta-ser-contactado=Quem est\u00E1 a ser contactado
pp.comunicacao.informcacoes.complementares.label.entidade=Entidade
pp.comunicacao.informcacoes.complementares.label.pessoa-singular=Pessoa singular
pp.comunicacao.informcacoes.complementares.label.descricao=Descri\u00E7\u00E3o
pp.comunicacao.informcacoes.complementares.label.adicionar-informacao=Adicionar informa\u00E7\u00E3o
pp.comunicacao.informcacoes.complementares.label.entidade-contactada=Entidade contactada
pp.comunicacao.informcacoes.complementares.label.detalhes-entidade-contactada=Detalhes da entidade contactada
pp.comunicacao.informcacoes.complementares.label.alterar-informacao=Alterar informa\u00E7\u00E3o
pp.comunicacao.informcacoes.complementares.label.eliminar-informacao=Eliminar informa\u00E7\u00E3o
pp.comunicacao.text.comunicacao-motivo-remessa-ecmij=Comiss\u00E3o de destino a entidade com compet\u00eancia em mat\u00e9ria de inf\u00e2ncia e juventude (art\u00b0 8)
pp.comunicacao.label.comunicacao-motivo-remessa-ecmij=Motivo de remessa
pp.comunicacao.text.comunicacao-motivo-devolucao-ecmij=Devolu\u00E7\u00E3o a entidade com compet\u00eancia em mat\u00e9ria de inf\u00e2ncia e juventude (art\u00b0 8)
pp.comunicacao.label.comunicacao-motivo-devolucao-ecmij=Motivo de devolu\u00E7\u00E3o
pp.comunicacao.text.validacao_idade=A idade n\u00E3o pode ser superior a 17 anos.
pp.comunicacao.text.modal.concluir=A comunica\u00E7\u00E3o ser\u00e1 enviada para delibera\u00E7\u00E3o e n\u00E3o poder\u00e1 efetuar mais altera\u00E7\u00F5es.
pp.comunicacao.crianca.table.label.tipo-relacao=Tipo de rela\u00E7\u00E3o
pp.comunicacao.label.comunicacao-morada-situacao-perigo=Morada da situa\u00E7\u00E3o de perigo
pp.comunicacao.label.comunicacao-necessita-intervencao-urgente=Necessita de interven\u00E7\u00E3o urgente
pp.comunicacao.label.comunicacao-urgente=Urgente
pp.comunicacao.msg.intervencao-urgente=Ap\u00f3s o registo, esta comunica\u00E7\u00E3o ser\u00e1 sinalizada para interven\u00E7\u00E3o urgente.
pp.comunicacao.msg.vinculo.reuniao-restrita=Existe uma ou mais comunica\u00E7\u00F5es sem associa\u00E7\u00E3o a um evento. \u00C9 necess\u00E1rio registar um evento da restrita para atendimento destas comunica\u00E7\u00F5es.
pp.comunicacao.btn.registar.reuniao-restrita=Registar evento
pp.comunicacao.msg.pesquisa.nome-morada=Pesquisa por nome da crian\u00E7\a ou morada exige o preenchimento do intervalo de pesquisa.
pp.comunicacao.evento.restrita.accordion-numero-comunicacao=Comunica\u00E7\u00E3o n.\u00BA%s
pp.comunicacao.evento.registar.evento.message-error-load=Erro no registo do evento
pp.comunicacao.evento.registar.evento.dados-comunicacao-label=Dados da comunica\u00E7\u00E3o n.\u00BA%s





